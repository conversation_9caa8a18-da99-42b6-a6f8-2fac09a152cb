/**
* Test class for InboundChecklistAttachmentHandler class
* 
* <AUTHOR>
* @since   2020-09-11 
*/
@isTest
public class InboundChecklistAttachmentHandlerTest {

        @TestSetup
        static void setup(){

		    Map<String, Schema.RecordTypeInfo> acctTypesByDevName = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName();
            Map<String, Schema.RecordTypeInfo> citTypesByDevName = Schema.SObjectType.Application_Checklist_Item_Template__c.getRecordTypeInfosByDeveloperName();

            //Inserting contact will create admin account 
            Contact c =  (Contact) TestFactory.createSObject(new Contact()); 
            insert c; 

            Account a = (Account)TestFactory.createSObject(new Account(RecordTypeId = acctTypesByDevName.get('Academic_Program').getRecordTypeId(), 
                                                                       Name = 'Degree Program', 
                                                                       Applicant_Opportunity_Record_Type__c = 'Degree_Program_Prospect'));
 			insert a;
            
            hed__Application__c app = new hed__Application__c (hed__Applicant__c   = c.Id, hed__Applying_To__c = a.Id);
            insert app;
            
            Application_Checklist_Item__c aci = new Application_Checklist_Item__c(Name = 'Test ACI', Status__c = 'Incomplete', Request_Upload_from_Applicant__c = true, Application__c = app.Id);
            insert aci;
       }

       
        @isTest
        public static void handleInboundEmailTest(){
            
            Application_Checklist_Item__c aci = [Select Id, Name from Application_Checklist_Item__c Limit 1];

            Messaging.InboundEmail email = new Messaging.InboundEmail() ;
            Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();

            email.fromAddress = '<EMAIL>';
            email.subject = 'Application Related File Item: ' + aci.Id;
            email.plainTextBody = 'Please find attached the files as needed for the application';
            
            Messaging.InboundEmail.BinaryAttachment efa = new Messaging.InboundEmail.BinaryAttachment();
            efa.FileName = 'Certificates.pdf';
            efa.Body = Blob.valueOf('This is the long body of the attachement for the email.');
            efa.mimeTypeSubType = 'text/calendar';
            email.binaryAttachments = new Messaging.InboundEmail.BinaryAttachment[] {efa};
            
            InboundChecklistAttachmentHandler inEmail = new InboundChecklistAttachmentHandler();
            inEmail.handleInboundEmail(email, env);

        }

}