public class UpdateInvitationStatusQueueable implements Queueable, Database.AllowsCallouts {
	private List<Id> attendeeIds;

	public UpdateInvitationStatusQueueable(List<Id> attendeeIds) {
		this.attendeeIds = attendeeIds;
	}

	public void execute(QueueableContext context) {
		List<evt__Attendee__c> attendeesToUpdate = new List<evt__Attendee__c>();
		List<evt__Attendee__c> attendees = [
				SELECT Id, evt__Invitation_Status__c
				FROM evt__Attendee__c
				WHERE Id IN :attendeeIds
		];
		System.debug('Attendees: ' + attendees);
		System.debug('Attendee IDs: ' + attendeeIds);

		for(evt__Attendee__c att : attendees) {
			if (att.evt__Invitation_Status__c == 'Invited') {
				att.evt__Invitation_Status__c = 'Registered';
				attendeesToUpdate.add(att);
			}
		}
		System.debug('Attendees to update: ' + attendeesToUpdate);
		try {
			update attendeesToUpdate;
		} catch (DmlException e) {
			System.debug('Error updating invitation status: ' + e.getMessage());
		}
	}
}