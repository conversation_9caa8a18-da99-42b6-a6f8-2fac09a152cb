@isTest
public with sharing class GroupRegistrationProcessControllerTest {
    @TestSetup
    static void makeData(){
        Account acc = (Account)TestFactory.createSObject(new Account(Name = 'Dept Account'));
        insert acc;
        Contact con = (Contact)TestFactory.createSObject(new Contact(lastName = 'Last Name', firstName = 'First Name',
            hed__WorkEmail__c	= '<EMAIL>'));
        insert con;

        insert new User(
            alias = 'test2',
            communityNickname = 'test123',
            contactId = con.Id, 
            email = '<EMAIL>', 
            emailencodingkey = 'UTF-8', 
            firstName = 'testCommunity2',
            lastName = 'User', 
            userName = '<EMAIL>', 
            profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Student Community User' LIMIT 1].Id, 
            timeZoneSidKey = 'America/Los_Angeles', 
            LocaleSidKey = 'en_US', 
            LanguageLocaleKey = 'en_US'
        );

        String cookieName = String.valueOf(dateTime.now());
        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
        cart.pymt__Cart_UID__c = cookieName;
        insert cart;

        evt__Special_Event__c event = new evt__Special_Event__c();
        event.Name = 'Special event';
        event.State_Province__c = 'NL';
        
        evt__Special_Event__c event2 = new evt__Special_Event__c();
        event2.Name = 'Special event 2';
        event2.State_Province__c = 'NL';
        
        List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
        listOfEventToInsert.add(event);
        listOfEventToInsert.add(event2);

        insert listOfEventToInsert;

        evt__Event_Fee__c fee = new evt__Event_Fee__c();
        fee.Name = 'Standard';
        fee.evt__Event__c = event.Id;
        fee.evt__Amount__c = 5.00;
        fee.evt__Active__c = true;
        fee.evt__Category__c = 'Attendee';
        fee.evt__Order__c = 1;
        fee.Type__c = 'Standard';

        evt__Event_Fee__c fee2 = new evt__Event_Fee__c();
        fee2.Name = 'Student';
        fee2.evt__Event__c = event2.Id;
        fee2.evt__Amount__c = 5.00;
        fee2.evt__Active__c = true;
        fee2.evt__Category__c = 'Attendee';
        fee2.evt__Order__c = 1;
        
        insert new List<evt__Event_Fee__c>{fee, fee2};
            
        Taxing_Authority__c txauth = new Taxing_Authority__c(Name = 'Test tax', Tax_Rate__c	= 15.000000, Tax_Label__c	='HST',State_Province__c='NL',Do_Not_Auto_Apply__c=false,Country__c='CA');
        insert txauth;

        Discount__c programBalance = new Discount__c(Name = 'Program Balance', Available_for__c = 'All', Type__c = 'Event Discount',Percent_Discount__c=10);
        insert programBalance; 
        
        Invoice__c inv = new Invoice__c();
        inv.Invoice_Status__c = 'Open-Personal';
        inv.Type__c = 'Events';
        inv.contact__c = con.Id;
        inv.Tax_Amount__c = 15;
        inv.Gross_Amount__c = 100;
        inv.Total_Amount_SCI__c = 115;
        insert inv;
        
        pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
        pymnt.Name = 'Event Checkout';
        pymnt.pymt__Transaction_Type__c = 'Payment';
        pymnt.pymt__Payment_Type__c	 = 'Credit Card';
        pymnt.pymt__Status__c = 'Online Checkout';
        pymnt.pymt__Contact__c = con.Id;
        pymnt.pymt__Amount__c = 90;
        pymnt.Gross_Amount__c = 100;
        pymnt.pymt__Tax__c = 10;
        pymnt.pymt__Discount__c = 20;
        pymnt.Type__c = 'Event Registration';
        pymnt.pymt__Payment_Processor__c = 'Global Pay';
        pymnt.invoice__c = inv.Id;
        insert pymnt;

        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event.Name;
        cartItem.Special_Event__c = event.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = con.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        cartItem.pymt__Shopping_Cart__c = cart.Id;
        cartItem.Discount__c = programBalance.Id;
        cartItem.Invoice__c = inv.Id;
        cartItem.pymt__Payment__c = pymnt.Id;

        pymt__Shopping_Cart_Item__c cartItem2 = new pymt__Shopping_Cart_Item__c();
        cartItem2.Name = event2.Name;
        cartItem2.Special_Event__c = event2.Id;
        cartItem2.pymt__Quantity__c =1;
        cartItem2.pymt__Contact__c = con.id;
        cartItem2.pymt__Unit_Price__c = 10.00;
        cartItem2.pymt__Shopping_Cart__c = cart.Id;
        cartItem2.Discount__c = programBalance.Id;
         insert new List<pymt__Shopping_Cart_Item__c>{cartItem, cartItem2};
             
        evt__Attendee__c gstatndy = new evt__Attendee__c();
        gstatndy.evt__Reg_Email__c = '<EMAIL>';
        gstatndy.evt__Reg_Postal_Code__c = '850103';
        gstatndy.evt__Reg_Country__c = 'USA';
        gstatndy.Relationship__c = 'Brother';
        gstatndy.evt__Reg_Title__c = 'Developer';
        gstatndy.evt__Reg_Company__c = 'Huron';
        gstatndy.evt__Reg_State__c = 'CA';
        gstatndy.evt__Reg_Last_Name__c = 'User1';
        gstatndy.evt__Reg_Street__c = '23rd cross';
        gstatndy.evt__Reg_First_Name__c = 'Test';
        gstatndy.Shopping_Cart_Item__c = cartItem.Id;
        gstatndy.evt__Event__c = cartItem.Special_Event__c;
        gstatndy.evt__Event_Fee__c = cartItem.Event_Fee__c;
        insert gstatndy; 
        
        Subscription__c sc = new Subscription__c();
        sc.Name = 'Upcoming events';
        insert sc;
        
        evt__Session__c testSession1 = new evt__Session__c();
        testSession1.Name = 'Test Session1';
        testSession1.Start_Date__c = Date.newInstance(2021, 12, 9); 
        testSession1.Start_Time__c= Time.newInstance(9,30,0,0);
        testSession1.End_Date__c = Date.newInstance(2021, 12, 9);
        testSession1.End_Time__c= Time.newInstance(21,30,0,0);
        testSession1.evt__Event__c = event.Id;

        evt__Session__c testSession2 = new evt__Session__c();
        testSession2.Name = 'Test Session2';
        testSession2.Start_Date__c = Date.newInstance(2021, 12, 9); 
        testSession2.Start_Time__c= Time.newInstance(9,30,0,0);
        testSession2.End_Date__c = Date.newInstance(2021, 12, 9);
        testSession2.End_Time__c= Time.newInstance(21,30,0,0);
        testSession2.evt__Event__c = event2.Id;
        
        insert new List<evt__Session__c>{testSession1,testSession2};
            
        evt__Attendee__c atndynew = new evt__Attendee__c();
        atndynew.evt__Reg_First_Name__c = con.FirstName;
        atndynew.evt__Reg_Last_Name__c = con.LastName;
        atndynew.evt__Reg_Phone__c = con.MobilePhone;
        atndynew.evt__Reg_Email__c = con.Email;
        atndynew.evt__Reg_Street__c = con.MailingStreet;
        atndynew.evt__Reg_City__c = con.MailingCity;
        atndynew.evt__Reg_State__c = con.MailingState;
        atndynew.evt__Reg_Postal_Code__c = con.MailingPostalCode;
        atndynew.evt__Reg_Country__c = con.MailingCountry;
        atndynew.evt__Contact__c = con.Id !=null ? con.Id : null;
        atndynew.Shopping_Cart_Item__c = cartItem.Id;
        atndynew.evt__Event__c = cartItem.Special_Event__c;
        atndynew.evt__Event_Fee__c = cartItem.Event_Fee__c;
        atndynew.evt__Payment__c = pymnt.Id;
        insert atndynew;
    }

    @isTest 
    static void testCreateAttendee() {
        evt__Special_Event__c event = [SELECT Id, Name, State_Province__c FROM evt__Special_Event__c WHERE Name = 'Special Event'];
        Contact testcontact = [SELECT Id, Name FROM Contact WHERE hed__WorkEmail__c	= '<EMAIL>'];
        evt__Attendee__c attendee = [SELECT Id, evt__Reg_First_Name__c, evt__Reg_Last_Name__c, evt__Reg_Phone__c,
                                    evt__Reg_Email__c,evt__Reg_Company__c, evt__Reg_Title__c, evt__Reg_Country__c,
                                    evt__Reg_State__c, evt__Reg_City__c, evt__Reg_Street__c, evt__Reg_Postal_Code__c,
                                    Accessibility_Requirements__c, Accessibility_Requirements_Other__c
                                    FROM evt__Attendee__c WHERE evt__Reg_First_Name__c = 'First Name'];
        evt__Event_Fee__c fee  = [SELECT Id , Name, Type__c, evt__Amount__c FROM evt__Event_Fee__c WHERE Name = 'Standard'];
        Discount__c discount = [SELECT Id FROM Discount__c WHERE Name = 'Program Balance'];

        Test.startTest();
            pymt__PaymentX__c payment = GroupRegisterationProcessController.createAttendeeRecord(event, testcontact, new List<evt__Attendee__c>{attendee}, fee, discount, 'Huron', 'Developer');
        Test.stopTest();

        System.assertNotEquals(payment, null, 'Payment should not be null');
    }
}