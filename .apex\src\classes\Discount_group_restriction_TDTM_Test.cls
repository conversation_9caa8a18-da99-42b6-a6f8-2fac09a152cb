/**
* @description    Test class for Discount_group_restriction_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2022-01-22
* @modified 2022-01-22
*/

@isTest
public class Discount_group_restriction_TDTM_Test {

    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for TestScoreCalculator_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('Discount_group_restriction_TDTM', 'pymt__Shopping_Cart_Item__c', 'AfterInsert;AfterUpdate;AfterDelete;AfterUndelete', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

         //Test Student record: 
         Contact c =  (Contact) TestFactory.createSObject(new Contact(discount_group__c = 'EP Alumni')); 
         //Inserting contact will create admin account 
         insert c; 
    
        //Test discount records 
        List<Discount__c> discountsToInsert = new List<Discount__c>(); 
        discountsToInsert.add(new Discount__c(Name = '10% off discount', Percent_Discount__c = 10, Max_Usage__c = 10, Code__c= 'ROTMAN10', Automatically_Apply__c= false, Discount_Group__c= 'EP Alumni;')); 
        discountsToInsert.add(new Discount__c(Name = '$100 off discount', Dollar_Discount__c = 100, Max_Usage__c = 20, Code__c='ROTMAN100', Automatically_Apply__c= false, Discount_Group__c= 'UofT Alumni;'));
        discountsToInsert.add(new Discount__c(Name = '$1000 off discount', Dollar_Discount__c = 1000, Max_Usage__c = 20, Code__c='ROTMAN1000', Automatically_Apply__c= false)); 
        insert discountsToInsert; 

        //Test Payment record: 
        pymt__PaymentX__c payment = new pymt__PaymentX__c(Name='Test Payment', pymt__contact__c = c.Id, Type__c = 'EP Program Payment', pymt__Status__c = 'Online Checkout'); 
        insert payment; 

        List<pymt__Shopping_Cart_Item__c> items = new List<pymt__Shopping_Cart_Item__c>(); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #1', pymt__contact__c = c.Id, pymt__Unit_Price__c = 9000, pymt__Quantity__c = 1, type__c = 'EP Program Balance', pymt__Payment__c = payment.Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #2', pymt__contact__c = c.Id, pymt__Unit_Price__c = 12000, pymt__Quantity__c = 1, type__c = 'EP Program Balance', pymt__Payment__c = payment.Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #3', pymt__contact__c = c.Id, pymt__Unit_Price__c = 10000, pymt__Quantity__c = 1, type__c = 'EP Program Balance', pymt__Payment__c = payment.Id));
        insert items; 
        }

    /*static testMethod void validateMatchingDiscountCodes() {
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, pymt__contact__c, pymt__contact__r.Discount_Group__c FROM pymt__Shopping_Cart_Item__c WHERE Type__c = 'EP Program Balance' LIMIT 1]; 
        Discount__c test_discount = [SELECT ID, Discount_Group__c FROM Discount__c WHERE Name = '$100 off discount' LIMIT 1]; 

        system.debug('Shopping Cart Item ID ' + item + ' Item Discount ' + item.discount__c + ' Contact ' + item.pymt__contact__c + ' Contact Discount Groups ' + item.pymt__contact__r.Discount_Group__c +  '  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Test discount ' + test_discount );

        item.discount__c = test_discount.id;

        update item;

        item = [SELECT ID, Discount__c, pymt__contact__c, pymt__contact__r.Discount_Group__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :item.Id LIMIT 1]; 

        system.debug('Post update Shopping Cart Item ID ' + item + ' Post Update Test discount ' + test_discount + ' Discount Discount Group ' + test_discount.Discount_Group__c + ' Contact ' + item.pymt__contact__c + ' Contact Discount Group: ' + item.pymt__contact__r.Discount_Group__c);

        
        System.assertEquals(test_discount.id, item.discount__c); 
    }*/
    
    static testMethod void validateMatchingDiscountCodes() {
        try

			{
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, pymt__contact__c, pymt__contact__r.Discount_Group__c FROM pymt__Shopping_Cart_Item__c WHERE Type__c = 'EP Program Balance' LIMIT 1]; 
        Discount__c test_discount = [SELECT ID, Discount_Group__c FROM Discount__c WHERE Name = '10% off discount' LIMIT 1]; 

        system.debug('Shopping Cart Item ID ' + item + ' Item Discount ' + item.discount__c + ' Contact ' + item.pymt__contact__c + ' Contact Discount Groups ' + item.pymt__contact__r.Discount_Group__c +  '  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Test discount ' + test_discount );

        item.discount__c = test_discount.id;

        Test.startTest();
        update item;
        Test.stopTest();

        item = [SELECT ID, Discount__c, pymt__contact__c, pymt__contact__r.Discount_Group__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :item.Id LIMIT 1]; 

        system.debug('Post update Shopping Cart Item ID ' + item + ' Post Update Test discount ' + test_discount + ' Discount Discount Group ' + test_discount.Discount_Group__c + ' Contact ' + item.pymt__contact__c + ' Contact Discount Group: ' + item.pymt__contact__r.Discount_Group__c);

		//throw MyException('An exception should have been thrown by the trigger but was not.'); // 1. If we get to this line it means an error was not added and the test class should throw an exception here. 2. MyException class extends Exception.
		System.assertEquals(test_discount.id, item.discount__c); 
			}

			catch(Exception e)

			{

			Boolean expectedExceptionThrown =  e.getMessage().contains('ERROR_MESSAGE');
			system.debug('Error Message? = ' + expectedExceptionThrown);
			System.AssertEquals(expectedExceptionThrown, false);

            }}

    
    static testMethod void validateNotMatchingDiscountCodes() {
        try

			{
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, pymt__contact__c, pymt__contact__r.Discount_Group__c FROM pymt__Shopping_Cart_Item__c WHERE Type__c = 'EP Program Balance' LIMIT 1]; 
        Discount__c test_discount = [SELECT ID, Discount_Group__c FROM Discount__c WHERE Name = '$100 off discount' LIMIT 1]; 

        system.debug('Shopping Cart Item ID ' + item + ' Item Discount ' + item.discount__c + ' Contact ' + item.pymt__contact__c + ' Contact Discount Groups ' + item.pymt__contact__r.Discount_Group__c +  '  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Test discount ' + test_discount );

        item.discount__c = test_discount.id;

        Test.startTest();
        update item;
        Test.stopTest();

        item = [SELECT ID, Discount__c, pymt__contact__c, pymt__contact__r.Discount_Group__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :item.Id LIMIT 1]; 

        system.debug('Post update Shopping Cart Item ID ' + item + ' Post Update Test discount ' + test_discount + ' Discount Discount Group ' + test_discount.Discount_Group__c + ' Contact ' + item.pymt__contact__c + ' Contact Discount Group: ' + item.pymt__contact__r.Discount_Group__c);

		//throw MyException('An exception should have been thrown by the trigger but was not.'); // 1. If we get to this line it means an error was not added and the test class should throw an exception here. 2. MyException class extends Exception.

			}

			catch(Exception e)

			{

			Boolean expectedExceptionThrown =  e.getMessage().contains('ERROR_MESSAGE');
			system.debug('Error Message? = ' + expectedExceptionThrown);
			System.AssertEquals(expectedExceptionThrown, true);

            }}
}