/**
* Helper Class that does contact name matching
* and staging table field swapping as needed. 
* See <PERSON><PERSON><PERSON> ticket 'UTR-319'
*
*Related TDTM Class: Contact_Trigger_TDTM
*
* <AUTHOR> 
* @since   2020-03-05 
*/

public class ContactNameMatchingAndStaging {
	
    //constructor
    public ContactNameMatchingAndStaging(){}
    
/**
 * <AUTHOR> 
 * @since   2020-03-05 
 *
 *
 * This method takes the Trigger.old and Trigger.New lists of records in beforeUpdate context
 * and checks given contact fields and matches/updates accordingly. Finnaly we update the needed fields
 * and set those values to the new list "afterList"
 *  
 * 
 * @param beforeList This is the list of Trigger.old
 * @param afterList This is the list of Trigger.new
 * @param i This is the given index for whatever loop the function is called in
 *  
 * 
 */
    public void contactNameMatching(List<Contact> beforeList, List<Contact> afterList, Integer i){
        
              //first name logic
                if((afterList[i].FirstName != beforeList[i].FirstName) && afterList[i].Preferred_First_Name__c == null){
                    afterList[i].Matching_Preferred_First_Name__c = afterList[i].FirstName;
                    system.debug('ENTERED HERE');
                    system.debug(afterList[i].FirstName);
                }
                else if((beforeList[i].Preferred_First_Name__c != null) && (afterList[i].Preferred_First_Name__c != beforeList[i].Preferred_First_Name__c)){
                    afterList[i].Matching_Preferred_First_Name__c = afterList[i].Preferred_First_Name__c;
                }
                
                //last name logic
                if((beforeList[i].LastName != afterList[i].LastName) && afterList[i].Former_Last_Name__c == null){
                    afterList[i].Matching_Former_Last_Name__c = afterList[i].LastName;
                }
                else if((beforeList[i].Former_Last_Name__c != null) && (afterList[i].Former_Last_Name__c != beforeList[i].Former_Last_Name__c)){
                     afterList[i].Matching_Former_Last_Name__c = afterList[i].Former_Last_Name__c;
                }
    }

/**
 * <AUTHOR> Cott 
 * @since   2020-03-05 
 *
 *
 * This method takes the Trigger.old and Trigger.New lists of records in beforeUpdate context
 * and checks the staging type field on contact and swaps/adds values to type picklist as needed
 * Finally clears the staging type field to empty string.
 * 
 * @param beforeList This is the list of Trigger.old
 * @param afterList This is the list of Trigger.new
 * @param stagingTableTypes This the Map representation of the staging table and values. See associated TDTM class for this Map
 * @param stagingTypeName The key value within the stagingTableTypes Map that populated the Staging Type field on contact
 * @param index This is the given index for whatever loop the function is called in
 *  
 * 
 */
        public void contactStagingTypePopulator(List<Contact> beforeList, List<Contact> afterList, Map<String, List<String>> stagingTableTypes, String stagingTypeName, Integer index){
            	 
                 //check if Staging Type field on contact is equal to the value
                 if(afterList[index].Staging_Type__c == stagingTypeName){
                    String strPickListValue = afterList[index].Type__c;
                    List<string> lstPicklistValues = strPickListValue.split(';');				//multipicklist seperated by semi-colon
                    List<String> valuesToRemove = stagingTableTypes.get(stagingTypeName);		//values to remove from multipicklist based on key
                    //iterate over list of values associated with key to be removed
                    for(String s : valuesToRemove){
                        if(lstPicklistValues.contains(s)){
                        	lstPicklistValues.remove(lstPicklistValues.indexOf(s));
                    	}
                    }
                    //check if intended value already exists in multi-select picklist to avoid dupliactes
                    if(!(lstPicklistValues.contains(stagingTypeName))){
                        lstPicklistValues.add(stagingTypeName);
                    }
                    
                    afterList[index].Type__c = String.join(lstPicklistValues,';');				//rejoin values for multipicklist
                    afterList[index].Staging_Type__c = '';										//clear Staging Type field to empty string
                }
        }
}