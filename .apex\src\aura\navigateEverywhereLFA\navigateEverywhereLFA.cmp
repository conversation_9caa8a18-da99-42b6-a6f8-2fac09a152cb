<!--
    navigateToRecord Lightning Flow Action
    
	Eric Smith - 9/19/19
	https://success.salesforce.com/ProfileView?u=005300000095v5FAAQ
	https://github.com/ericrsmith35

    Created to allow for the redirection at the end of a Flow to a Record page in either Edit or View mode.
	
	This component uses lightning:navigation rather than the deprecated force:navigateToSObject that is in 
	the Navigate to SObject component.

	Parameters include defining the Object Name, Record Id and Mode (View or Edit)
-->
<aura:component implements="lightning:availableForFlowActions">
    <lightning:pageReferenceUtils aura:id="pageRefUtils"/>
    <aura:attribute name="destinationRecordId" type="String"/>
    <aura:attribute name="destinationName" type="String"/>
    <aura:attribute name="destinationAction" type="String"/>
    <aura:attribute name="destinationType" type="String"/>
    <aura:attribute name="destinationActionFilter" type="String"/>
    <aura:attribute name="destinationUrl" type="String" />
    <aura:attribute name="relationshipName" type="String" />
    <aura:attribute name="defaultVals" type="String" />
    <lightning:navigation aura:id="navService"/>
</aura:component>