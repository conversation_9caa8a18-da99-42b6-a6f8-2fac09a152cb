public class ShoppingCart_Controller {
    
    public string spEventId {get;set;}
    public MAP<string,cartWrapper> ListItems{get;set;}
    public MAP<string,cartWrapper> cart{get;set;} 
    
    public MAP<string,cartWrapper> getSpecialEvents(){
        system.debug('id--> '+ spEventId);
        if(spEventId != Null){
            
        
        MAP<string,cartWrapper> ListItems = new MAP<string,cartWrapper>{};
         evt__Special_Event__c events = [SELECT Id, Name,evt__Short_Description__c,
                                         (SELECT  evt__Amount__c From  evt__Event_Fees__r) 
                                         FROM evt__Special_Event__c 
                                         WHERE Id=: spEventId //'a1V4c0000000bW5EAI' 
                                        ];
        
        cartWrapper cartW = new cartWrapper();
        cartw.event = events;
        cartw.qty = 1; 
        cartw.total = events.evt__Event_Fees__r[0].evt__Amount__c * cartw.qty;
   		ListItems.put(string.valueOf(events.Id),cartW);
            
        return ListItems;
            }
        else return NUll;
    }
    
    public void RemoveFromCart(){
        system.debug('spEventId='+spEventId);
        if(ListItems!= null && ListItems.containsKey(spEventId)){
            ListItems.remove(spEventId);
        }
        
    }
    
    
    public class cartWrapper{
        //public string description{get;set;}
        //public double price{get;set;}
        //public string item{get;set;}
        public integer qty{get;set;}
        public double total{get;set;}
        public evt__Special_Event__c event{get;set;}
    }

}