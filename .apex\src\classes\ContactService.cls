/**
* @description   Service class for Contact Object 
* <AUTHOR>
* @version        1.0 
* @created 2020-03-03
* @modified 2020-03-03
*/ 
public inherited sharing class ContactService {
    
    /**
     * @description query specific fields of contact records 
     * @return List<Contact> list of contact records
     * @param contactIds set of contact IDs
     * @param fieldsToQuery set of api names of fields to query 
     */
    public static List<Contact> queryFieldsInContacts(Set<Id> contIds, Set<String> fieldsToQuery){
        return Database.query('SELECT ' + String.join(new List<String>(fieldsToQuery), ',') + ' FROM Contact WHERE ID IN :contIds ' ); 

    }
}