/**
* TDTM class to update document_type__c on all files inserted with name: $DT_Type
* Insert new contentDocumentLinks for all newly inserted contentDocumentLink where document type = 'Resume' and linkedEntityId is an application record
*
* <AUTHOR>
* @created   2020-07-27
* @modified  2021-02-19
*/

global class CongaDocumentType_TDTM extends hed.TDTM_Runnable{
    public class customException extends Exception{}
    public static final Map<String, String> appDocTypeToAPIFieldMap = new Map<String, String>{'Financial Accounting Syllabus' => 'Financial_Accounting_Syllabus_ID__c', 
                                                                                                'Investments Syllabus'  => 'Investments_Syllabus_ID__c', 
                                                                                                'Financial Derivatives Syllabus' => 'Financial_Derivatives_Syllabus_ID__c', 
                                                                                                'Foundations of Finance Syllabus' => 'Foundations_of_Finance_Syllabus_ID__c', 
                                                                                                'MBA Spike Photos' => 'Spike_Photo_ContentDocumentID__c', 
                                                                                                'Resume' => 'Resume_ContentDocumentId__c', 
                                                                                                'Computational Proficiency' => 'Computational_Proficiency_ID__c', 
                                                                                                'Quantitative Proficiency' => 'Quantitative_Proficiency_ID__c',
                                                                                                'Unofficial Transcript' => 'Unofficial_Transcript_ContentDocumentID__c',
                                                                                                'Rotman Signed Offer Letter Confirmation' => 'Confirmation_Letter_ContentDocumentId__c',
                                                                                                'CPA PEP Prerequisite' => 'CPA_PEP_Prerequisite_ContentDocumentID__c'};
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of  records from trigger new 
     * @param oldList the list of  records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();
        Map<Id,hed__Application__c> appsToUpdate = new Map<Id, hed__Application__c>(); 
        //AFTER INSERT CONTEXT: 
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
            Set<Id> cdIds = new Set<Id>(); 
            Set<Id> appIds = new Set<Id>(); 
            for(ContentDocumentLink cdl : (List<ContentDocumentLink>)newList){
                cdIds.add(cdl.ContentDocumentId); 
                //Group all application Ids 
                if(cdl.linkedEntityId.getSObjectType() == Schema.hed__Application__c.SObjectType){
                    appIds.add(cdl.linkedEntityId); 
                }
            }
            //Query for all contentVersions where title begins with $DT_ and map contentDocument to contentVersion
            Map<Id, ContentVersion> contentDocToVersionMap = new Map<Id, ContentVersion>(); 
            List<contentVersion> cvList = [SELECT Id, Title, ContentDocumentId, document_type__c 
                                            FROM ContentVersion WHERE ContentDocumentId IN :cdIds 
                                            AND ((Title LIKE '$DT_%' AND Document_type__c = null)
                                            OR Document_Type__c != null)]; 
            if(cvList.size() > 0){
                for(ContentVersion cv : cvList){
                    contentDocToVersionMap.put(cv.ContentDocumentId, cv); 
                }
            }
            //Map application Id to application record: 
            Map<Id, hed__Application__c> appMap = new Map<Id, hed__Application__c>(); 
            if(appIds.size() > 0) appMap = new Map<Id, hed__Application__c>([SELECT ID, hed__Applicant__c, Resume_ContentDocumentId__c FROM hed__Application__c WHERE Id IN :appIds]); 

            //List of contentversion to update and contentDocumentLink to insert: 
            List<ContentVersion> cvToUpdate = new List<ContentVersion>();
            List<ContentDocumentLink> cdlToInsert = new List<ContentDocumentLink>(); 
            //List of applications to update: 
            List<SObject> recsToUpdate = new List<SObject>(); 
            //Set of duplicate application IDs for files
            Set<Id> dupeRecIds = new Set<Id>();
            
            //update document type, title, and create contentDocumentLink 
            for(ContentDocumentLink cdl : (List<ContentDocumentLink>)newList){
                if(contentDocToVersionMap.containsKey(cdl.ContentDocumentId)){
                    ContentVersion cv = contentDocToVersionMap.get(cdl.ContentDocumentId); 
                    Id newRecId = cdl.linkedEntityId;
                    
                    //Check to see if document type needs to be mapped
                    if(cv.document_type__c == null){
                        String docName = cv.title.split('\\.')[0]; 
                        if(!String.isBlank(docName.substring(4))){
                            cv.document_type__c = docName.subString(4);  
                            cv.title = docName.subString(4); 
                            cvToUpdate.add(cv); 
                        }
                    } 
                    if(appDocTypeToAPIFieldMap.containsKey(cv.document_type__c) && cdl.linkedEntityId.getSObjectType() == Schema.hed__Application__c.SObjectType ){
                        //if document type = resume and linked record is an application, create cdl for applicant: 
                        if(appMap.containsKey(cdl.linkedEntityId) && cv.document_type__c == 'Resume'){
                            cdlToInsert.add(new ContentDocumentLink(ContentDocumentId = cdl.ContentDocumentId, LinkedEntityId = appMap.get(cdl.linkedEntityId).hed__Applicant__c ));
                        }
                        //Update contentDocument field on the application: 
                        hed__Application__c app = appsToUpdate.containsKey(cdl.linkedEntityId) ? appsToUpdate.get(cdl.linkedEntityId) : new hed__Application__c(Id = cdl.linkedEntityId); 
                        app.put(appDocTypeToAPIFieldMap.get(cv.document_type__c), cdl.ContentDocumentId); 
                        appsToUpdate.put(app.Id, app); 
                    }else if (cv.document_type__c == 'Unofficial Transcript'  && cdl.linkedEntityId.getSObjectType() == Schema.hed__Affiliation__c.SObjectType) {
                        if(!dupeRecIds.contains(newRecId)){
                            recsToUpdate.add(new hed__Affiliation__c(Transcript_ContentDocumentId__c = cdl.ContentDocumentId, Id = cdl.linkedEntityId)); 
                            dupeRecIds.add(newRecId);
                        } 
                    } else if (cv.document_type__c == 'Test Score' && cdl.linkedEntityId.getSObjectType() == Schema.hed__Test__c.SObjectType) {
                        if(!dupeRecIds.contains(newRecId)){
                            recsToUpdate.add(new hed__Test__c(Test_ContentDocumentId__c = cdl.ContentDocumentId, Id = cdl.linkedEntityId)); 
                            dupeRecIds.add(newRecId);
                        }                    
                    } 
                }       
            }

            try { insert cdlToInsert; } catch ( Exception e ) { system.debug('Error linking resume to applicant, ' + e.getMessage() ); }
            
            if(cvToUpdate.size() > 0) update cvToUpdate;//dmlWrapper.objectsToUpdate.addAll(cvToUpdate); 
            if(appsToUpdate.size() > 0) recsToUpdate.addAll(appsToUpdate.values()); 
            
            try{
                if(recsToUpdate.size() > 0) 
                    update recsToUpdate;
            }
            catch (Exception e) {
                String execptionMsg = 'This component only accepts one file attachment. Please consolidate the files into one main file attachment and try again.';
                AuraHandledException exe = new AuraHandledException(execptionMsg);
                system.debug('exeception message length: ' + exe.getMessage().length());
                exe.setMessage('\n' + execptionMsg);
                system.debug('exeception: ' + exe);
                throw exe;
            }
             
            //if(recsToUpdate.size() > 0) update recsToUpdate; //dmlWrapper.objectsToUpdate.addAll(recsToUpdate); 
        }
        return dmlWrapper; 
    }
}