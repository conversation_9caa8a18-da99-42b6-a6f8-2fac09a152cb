@isTest
public class TestScoreDateStampDupRulesApex_TDTMTest{
    @testSetup
    static void testSetup () {
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('TestScoreDateStampDupRulesApex_TDTM', 'Test_Score__c', 'BeforeInsert;BeforeUpdate', 2.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Create test contact:
        Contact c = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student'));
        insert c;
		
        //Create test Account:
        Account a = (Account)TestFactory.createSObject(new Account(Name='TestAccount'));
        insert a;
        
        //Create Test records: 
        List<hed__Test__c> workHistoriesToInsert = new List<hed__Test__c>(); 
        workHistoriesToInsert.add(new hed__Test__c(hed__Test_Type__c = 'GRE', hed__Contact__c = c.Id, hed__Test_Date__c = Date.Today().adddays(3))); 
        //workHistoriesToInsert.add(new hed__Test__c(hed__Test_Type__c = 'GRE', hed__Contact__c = c.Id, hed__Test_Date__c = Date.Today().adddays(3))); 
        insert workHistoriesToInsert;
    }
    @isTest 
    static void testScoreDateStampTest() {
        //Create Test Score records
        List<hed__Test_Score__c> testScoreLst = new List<hed__Test_Score__c>();
        List<hed__Test__c> tstLst = [Select Id,hed__Test_Type__c,hed__Contact__c from hed__Test__c Limit 1];
        testScoreLst.add(new hed__Test_Score__c(hed__Test__c = tstLst[0].Id,hed__Subject_Area__c = 'Verbal Reasoning'));
        //testScoreLst.add(new hed__Test_Score__c(hed__Test__c = tstLst[1].Id,hed__Subject_Area__c = 'Verbal Reasoning'));
        Test.StartTest();
        insert testScoreLst;
        System.assertEquals(tstLst[0].hed__Contact__c,[SELECT id,Contact__c from hed__Test_Score__c].Contact__c);
        Test.StopTest();
    }
}