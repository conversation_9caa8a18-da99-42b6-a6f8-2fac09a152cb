/**
* TDTM class for Contact that does contact name field matching
* and staging table field swapping as needed. 
* See <PERSON><PERSON><PERSON> ticket 'UTR-319'
* 
*
*Related Helper Class: ContactNameMatchingAndStaging
*
* <AUTHOR> 
* @since   2020-03-05 
*/

global class Contact_StagingHandler_TDTM extends hed.TDTM_Runnable{
	// the Trigger Handler’s Run method we must provide
   global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist,
        hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {

        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        
        //map representing Staging type field as key and values to be removed from type as value
        Map<String, List<String>> stagingTableTypes = new  Map<String, List<String>>{
            'EP Graduate' => new List<String>{'EP Student'},  
            'EP Registrant' => new List<String>{'EP Applicant'}, 
            'EP Alumni' => new List<String>{'EP Registrant'}, 
            'Alumni' => new List<String>{'Student'},  
            'Former Student' => new List<String>{'Student'},  
            'Student' => new List<String>{'Applicant', 'Prospect'},
            'Applicant' => new List<String>{'Prospect'},
            'Business Relationship' => new List<String>{''},
            'Enrolled' => new List<String>{'Student'},
            'Faculty' => new List<String>{''},
            'Prospect' => new List<String>{''}
        };
         
		if ( triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate || triggerAction == hed.TDTM_Runnable.Action.BeforeInsert ) {

            List<Contact> beforeList = (List<Contact>)oldlist;		//Trigger.old
            List<Contact> afterList  = (List<Contact>)newlist;		//Trigger.new
            
            //iterate over records
            for(Integer i = 0 ; i < newlist.size() ; i ++){
                
                if ( triggerAction == hed.TDTM_Runnable.Action.BeforeInsert )
                    StagingTransformService.contactNameMatching( afterList[i] );	//helper class method
                
                else 
                    StagingTransformService.contactNameMatching( beforeList[i], afterList[i] );	//helper class method

                if ( afterList[i].Staging_Type__c != null ) {
                    Set<String> conTypes = afterList[i].Type__c == null ? new Set<String>() : new Set<String>(afterList[i].Type__c.split(';'));
                    conTypes.add(afterList[i].Staging_Type__c);
                    afterList[i].Type__c = StagingTransformService.removeExludedFromMultiselect( String.join( new List<String>(conTypes),';' ), stagingTableTypes );
                    afterList[i].Staging_Type__c = null;
                
                } else if ( afterList[i].Type__c != null && (triggerAction == hed.TDTM_Runnable.Action.BeforeInsert || afterList[i].Type__c != beforeList[i].Type__c) ) {
                    afterList[i].Type__c = StagingTransformService.removeExludedFromMultiselect( afterList[i].Type__c , stagingTableTypes );
                    afterList[i].Staging_Type__c = null;
            
                }
            }
        }

        return dmlWrapper; 
   }
}