public class GlobalPayConnect {
    public static Global_Pay_Integration_Setting__mdt globalPaySettings;
    public static String gpAccountName;
    
    //Get the Global Pay variables from metadata record
    public static void populateGlobalPaySettings(){
        globalPaySettings = [SELECT Access_Token_Endpoint__c, account_name__c, appId__c, appKey__c, 
                             Payment_Endpoint__c, X_Gp_Version__c FROM Global_Pay_Integration_Setting__mdt 
                             WHERE DeveloperName = 'Global_Pay_Settings'];
    }


    //Process Card Payment   
    public static System.HTTPResponse processPayment(pymt__PaymentX__c clientPymt, String cardNo, 
                                      String cvd, String expYr, String expMo, String accountName) 
    {
        System.debug('GPC accountName '+accountName);
        System.debug('processPayment');
        System.debug('processPayment - clientPymt '+clientPymt);
        try{
            String token = generateAccessToken();
            System.debug('token '+token);
            if(token != NULL && String.isNotBlank(token)){
                String jsonString = generateJSONForPayment(clientPymt, cardNo, cvd, expYr, expMo, accountName);
                System.debug('jsonString+++ '+jsonString);
                // Creating an instance of HTTP class
                Http tempHttp = new Http();
                
                // Creating an instance of HTTPRequest class
                HTTPRequest request = new HTTPRequest();
                
                // Setting up the endpoint and method for the HTTP request
                request.setEndpoint(globalPaySettings.Payment_Endpoint__c);
                request.setHeader('Content-Type', 'application/json');
                request.setHeader('Accept', 'application/json');
                request.setHeader('Accept-Encoding', 'gzip');
                request.setHeader('X-Gp-Version', globalPaySettings.X_Gp_Version__c);
                request.setHeader('Authorization', token);
                request.setMethod('POST');
                request.setBody(jsonString);
                
                // Hitting the API and getting the response
                HTTPResponse response = tempHttp.send(request);
                return response;
            }
            else
                return null;
        }
        catch(Exception e){            
            System.debug('Exception :: '+e.getMessage());
            throw e;
        }        
    }
    public static String generateJSONForPayment(pymt__PaymentX__c clientPymt, String cardNo, 
                                                String cvd, String expYr, String expMo, String accountName) 
    {
        String amt = '0';
        System.debug('GenerateJSON accountName '+accountName);
        String jsonString = '{"account_name": "{accName}", "channel" : "CNP", "type" : "SALE", "capture_mode" : "AUTO", "amount": "{amount}","currency": "{currency}","country": "{country}", "reference" : "{reference}", "payment_method": {"name": "{contactName}","entry_mode": "ECOM","card": {"number": "{cardNumber}","expiry_month": "{expiryMonth}","expiry_year": "{expiryYear}","cvv": "{CVV}","cvv_indicator": "PRESENT"}}}';
        System.debug('B3 formatAmount '+clientPymt);
        System.debug('B4 formatAmount '+clientPymt.pymt__Amount__c);
        if (clientPymt.pymt__Amount__c != null)
            amt = formatAmount(clientPymt.pymt__Amount__c);
        System.debug('amt '+amt);
        String country = 'CA';
    
        //jsonString = jsonString.replace('{accName}', globalPaySettings.account_name__c);
        //jsonString = jsonString.replace('{amount}', String.valueOf(clientPymt.pymt__Amount__c));
        jsonString = jsonString.replace('{accName}', accountName);
        jsonString = jsonString.replace('{amount}', amt);
        //jsonString = jsonString.replace('{country}', clientPymt.pymt__Billing_Country__c);
        jsonString = jsonString.replace('{country}', country);
        jsonString = jsonString.replace('{currency}', 'CAD');
        jsonString = jsonString.replace('{reference}', clientPymt.Id);
        jsonString = jsonString.replace('{contactName}', clientPymt.pymt__Billing_First_Name__c+' '+clientPymt.pymt__Billing_Last_Name__c);
        jsonString = jsonString.replace('{cardNumber}', cardNo);
        jsonString = jsonString.replace('{expiryMonth}', expMo);
        jsonString = jsonString.replace('{expiryYear}', expYr);
        jsonString = jsonString.replace('{CVV}', cvd);

        System.debug('evan json: ' + jsonString);
        return jsonString;
    }


    //Amount should be in smallest unit currency.
    //https://developer.globalpay.com/data-formats
    public static String formatAmount(Decimal amt) {
        System.debug('formatAmount '+amt);
        String str =  String.valueOf(amt * 100);
        System.debug('str '+str);
        if(str.contains('.'))
            str = str.split('\\.')[0];
        return str;
    }
    public static System.HTTPResponse processRefund(String trnID, Decimal amountToRefund) {
        System.debug('processRefund trnID '+trnID);
        System.debug('processRefund amountToRefund '+amountToRefund);
        try{            
            String token = generateAccessToken();
            String amtToRefund = formatAmount(amountToRefund);
            System.debug('token '+token);
            if(token != NULL && String.isNotBlank(token)){
                String jsonString = '{"amount": "'+amtToRefund+'"}';
                System.debug('processRefund jsonString '+jsonString);
                // Creating an instance of HTTP class
                Http tempHttp = new Http();
                
                // Creating an instance of HTTPRequest class
                HTTPRequest request = new HTTPRequest();
                
                // Setting up the endpoint and method for the HTTP request
                request.setEndpoint(globalPaySettings.Payment_Endpoint__c+'/'+trnID+'/refund');
                request.setHeader('Content-Type', 'application/json');
                request.setHeader('Accept', 'application/json');
                request.setHeader('Accept-Encoding', 'gzip');
                request.setHeader('X-Gp-Version', globalPaySettings.X_Gp_Version__c);
                request.setHeader('Authorization', token);
                request.setMethod('POST');
                request.setBody(jsonString);
                // Hitting the API and getting the response
                HTTPResponse response = tempHttp.send(request);
                System.debug('response :: '+response);
                System.debug('responseBody: '+response.getBody());
                return response;
            }
            else
                return null;
        }
        catch(Exception e){            
            System.debug('Exception :: '+e.getMessage());
            throw e;
        }        
    }

    public static String generateAccessToken() {
        System.debug('generateAccessToken');
        try{
            populateGlobalPaySettings();
            String jsonString = generateJSONForAccessToken();
            System.debug('generateAccessToken jsonString '+jsonString);
            System.debug('generateAccessToken1 '+globalPaySettings.Access_Token_Endpoint__c);
            System.debug('generateAccessToken2 '+globalPaySettings.X_Gp_Version__c);
            // Creating an instance of HTTP class
            Http tempHttp = new Http();
            
            // Creating an instance of HTTPRequest class
            HTTPRequest request = new HTTPRequest();
            
            // Setting up the endpoint and method for the HTTP request
            request.setEndpoint(globalPaySettings.Access_Token_Endpoint__c);
            request.setHeader('Content-Type', 'application/json');
            request.setHeader('X-Gp-Version', globalPaySettings.X_Gp_Version__c);
            request.setMethod('POST');
            request.setBody(jsonString);
            // Hitting the API and getting the response
            HTTPResponse response = tempHttp.send(request);
            System.debug('response :: '+response);
            System.debug('responseBody: '+response.getBody());
            if(response.getStatusCode() == 200){
                GP_TokenResponseWrapper wrapper = (GP_TokenResponseWrapper) JSON.deserialize(response.getBody(), GP_TokenResponseWrapper.class);
                System.debug('wrapper '+wrapper);
                System.debug('wrapper.token '+wrapper.token);
                return wrapper.type + ' ' +wrapper.token;
            }
            else
                return null;   
        }
        catch(Exception e){
            System.debug('Exception :: '+e.getMessage());
            return null;
        }        
    }
    public static String generateJSONForAccessToken() {
        String nonceVal = String.valueOf(System.now());
        
        String appId = globalPaySettings.appId__c;
        String appKey = globalPaySettings.appKey__c;
        
        String key = nonceVal + appKey;
        Blob digest = Crypto.generateDigest('SHA-512', Blob.valueOf(key));
        String secretKey = EncodingUtil.convertToHex(digest);
        //For Production use only
        //String jsonString = '{"app_id": "' + appId + '","nonce": "' + nonceVal + '","secret": "'+secretKey+'","grant_type": "client_credentials", "interval_to_expire":"10_MINUTES"}';
        //For UAT use only
        String jsonString = '{"app_id": "NZOF9fCkU7eQzwKAogwHJTM1Z8rC3oC9","nonce": "2029-03-14T13:24:10.832Z","secret": "888851A74441C759F18BE9F886539BD912F28A5DCDAC4A1AAD98F041C7ED907C1A1B90815D1A1F102160029E06B0D3E3FD1E190C2E2DB0F5C41BDA52599D529E","grant_type": "client_credentials"}';
        return jsonString;
    }
   
}