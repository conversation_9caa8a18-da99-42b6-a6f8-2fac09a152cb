({
    getPaymentWrapper: function(component, event, helper) {
        //console.log('***In helper');
        var a= component.get("v.recordId");
        //console.log('***In helper'+a);
        //call apex class method
        var action = component.get('c.getPaymentDetails');
        action.setParams({
            pid : a//component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            //store state of response
            var state = response.getState();
            if (state === "SUCCESS") {
                console.log("resp--> "+response.getReturnValue());
                //set response value in wrapperList attribute on component.
                component.set('v.paymentCardWrapper', response.getReturnValue());
            }
        });
        $A.enqueueAction(action);
    },
    getVoidPayment: function(component, event, helper){
        var wrapper = component.get('v.paymentCardWrapper');
        
        var action = component.get('c.getVoidPayment');
        action.setParams({
            cardDetails : component.get("v.paymentCardWrapper"),
            pid : component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            var ReceiptId = response.getReturnValue().response.receipt.ReceiptId;
            console.log('ReceiptId: '+ReceiptId)
            if (ReceiptId === null || ReceiptId === "null") {
                 component.find('notifLib').showNotice({
                    "variant": "error",
                    "header": "Error",
                    "message": response.getReturnValue().response.receipt.Message,
                    closeCallback: function() {
                       $A.get("e.force:refreshView").fire();
                    }
                });
            }
            else{
                component.find('notifLib').showNotice({
                    "variant": "Success",
                    "header": "Payment Voided Successful!",
                    "message": 'Payment Voided: '+response.getReturnValue().response.receipt.ReferenceNum,
                    closeCallback: function() {
                       $A.get("e.force:refreshView").fire();
                    }
                });
            } 
        
        });
        $A.enqueueAction(action);
    },
})