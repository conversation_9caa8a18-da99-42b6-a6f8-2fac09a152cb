@isTest
private class CalendarService_TEST {
	@isTest
	static void testCreateInvite() {
		Test.startTest();
		Blob result1 = CalendarService.createInvite(DateTime.now(), DateTime.now().addHours(1), 'Meeting Title', 'Meeting Summary', 'Meeting Location');
		Test.stopTest();
	}

	@isTest
	static void testCreateEvent() {
		RecordType stdEvntRT = [Select Id, Name from RecordType Where SObjectType = 'evt__Special_Event__c' and Name = 'Standard Event'];

		List<evt__Special_Event__c> eList = new List<evt__Special_Event__c>();
		eList.add( new evt__Special_Event__c (RecordTypeId = stdEvntRT.Id, Name = 'Test Event1', evt__Disable_Conflict_Checking__c = False,
				Start_Date__c = Date.newInstance(2025,12,12),
				End_Date__c = Date.newInstance(2025,12,12),
				Start_Time__c = '1:00 PM',
				End_Time__C   = '3:00 PM',
				evt__Start__c = Datetime.newInstanceGmt(2025,12,12,1,0,0), evt__End__c = DateTime.NewInstance(2025,12,12,3,0,0)));
		insert eList;

		List<Contact> cList = new List<Contact>();
		Contact c1 = (Contact)TestFactory.createSObject(new Contact());
		cList.add(c1);
		Insert cList;

		List<evt__Attendee__c> atnList = new List<evt__Attendee__c>();
		atnList.add(new evt__Attendee__c(evt__Event__c = eList[0].Id, evt__Contact__c = cList[0].Id));
		insert atnList;

		List<evt__Session__c> sList = new List<evt__Session__c>();
		sList.add(new evt__Session__c(evt__Event__c = eList[0].Id, Name = 'Test Session1', Start_Date__c = Date.newInstance(2021, 12, 9), Start_Time__c= Time.newInstance(9,30,0,0), End_Date__c = Date.newInstance(2021, 12, 9),
				End_Time__c= Time.newInstance(21,30,0,0)));
		insert sList;

		List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
		saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id, evt__Status__c = 'Waitlisted'));
		insert saList;

		Test.startTest();
		List<Event> result2 = CalendarService.createEvent(saList, 'Start_Date__c', 'End_Date__c', 'Owner__c', 'Contact__c', 'Location__c', 'Description__c');
		Test.stopTest();
	}

	@isTest
	static void testFindEvent() {
		RecordType stdEvntRT = [Select Id, Name from RecordType Where SObjectType = 'evt__Special_Event__c' and Name = 'Standard Event'];

		List<evt__Special_Event__c> eList = new List<evt__Special_Event__c>();
		eList.add( new evt__Special_Event__c (RecordTypeId = stdEvntRT.Id, Name = 'Test Event1', evt__Disable_Conflict_Checking__c = False,
				Start_Date__c = Date.newInstance(2025,12,12),
				End_Date__c = Date.newInstance(2025,12,12),
				Start_Time__c = '1:00 PM',
				End_Time__C   = '3:00 PM',
				evt__Start__c = Datetime.newInstanceGmt(2025,12,12,1,0,0), evt__End__c = DateTime.NewInstance(2025,12,12,3,0,0)));
		insert eList;

		List<Contact> cList = new List<Contact>();
		Contact c1 = (Contact)TestFactory.createSObject(new Contact());
		cList.add(c1);
		Insert cList;

		List<evt__Attendee__c> atnList = new List<evt__Attendee__c>();
		atnList.add(new evt__Attendee__c(evt__Event__c = eList[0].Id, evt__Contact__c = cList[0].Id));
		insert atnList;

		List<evt__Session__c> sList = new List<evt__Session__c>();
		sList.add(new evt__Session__c(evt__Event__c = eList[0].Id, Name = 'Test Session1', Start_Date__c = Date.newInstance(2021, 12, 9), Start_Time__c= Time.newInstance(9,30,0,0), End_Date__c = Date.newInstance(2021, 12, 9),
				End_Time__c= Time.newInstance(21,30,0,0)));
		insert sList;

		List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
		saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id, evt__Status__c = 'Waitlisted'));
		insert saList;

		Test.startTest();
		List<Event> result3 = CalendarService.findEvent(saList, 'Start_Date__c', 'End_Date__c', 'Owner__c', 'Contact__c', 'Location__c', 'Description__c');
		Test.stopTest();
	}
}