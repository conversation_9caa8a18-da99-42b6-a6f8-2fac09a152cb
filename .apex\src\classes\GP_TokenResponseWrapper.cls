public class GP_TokenResponseWrapper {

	public String token;
	public String type;
	public Scope scope;
	public String app_id;
	public String app_name;
	public String time_created;
	public Integer seconds_to_expire;
    public String interval_to_expire;
    public String email;
        
	public class Scope {
		public String merchant_id;
		public String merchant_name;
		public List<Accounts> accounts;
	}

	public class Accounts {
		public String id;
		public String name;
		public List<String> permissions;
	}

	public static GP_TokenResponseWrapper parse(String json) {
		return (GP_TokenResponseWrapper) System.JSON.deserialize(json, GP_TokenResponseWrapper.class);
	}
}