public class myEmailScheduler implements Schedulable {
	private final List<String> recipientEmails;
	private final String senderName;
	private final String replyTo;
	private final String subject;
	private final String htmlBody;
	private final String eventId;

	public myEmailScheduler(List<String> recipientEmail,
							String subject,
							String body,
							String senderName,
							String replyTo,
							String eventId) {
		this.recipientEmails = recipientEmail;
		this.subject = subject;
		this.htmlBody = body;
		this.senderName = senderName;
		this.replyTo = replyTo;
		this.eventId = eventId;
		System.debug('@-0-2-1recipientEmails: ' + recipientEmails);
		System.debug('@-0-2-2subject: ' + subject);
		System.debug('@-0-2-3htmlBody: ' + htmlBody);
		System.debug('@-0-2-4senderName: ' + senderName);
		System.debug('@-0-2-5replyTo: ' + replyTo);
		System.debug('@-0-2-6eventId: ' + eventId);
	}

	public void execute(SchedulableContext ctx) {
		String orgWideId = '';
		OrgWideEmailAddress[] owea = new OrgWideEmailAddress[]{};
		String supportEmail;
		evt__Special_Event__c myevent = [SELECT Support_Email__c FROM evt__Special_Event__c WHERE Id = :eventId];
		if (myevent.Support_Email__c != null) {
			supportEmail = myevent.Support_Email__c;
			owea = [SELECT Id FROM OrgWideEmailAddress WHERE Address = :supportEmail];
		}
		if (owea.size() > 0) {
			orgWideId = owea[0].Id;
		} else {
			owea = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'Rotman Events'];
			if (owea.size() > 0) {
				orgWideId = owea[0].Id;
			}
		}

		List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
		for (String mainRecipient : this.recipientEmails) {
			Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
			if (orgWideId != '') {
				email.setOrgWideEmailAddressId(orgWideId);
			}
			email.setToAddresses(new List<String>{mainRecipient});
			email.setReplyTo(this.replyTo);
			email.setSubject(this.subject);
			email.setHtmlBody(this.htmlBody);
			emails.add(email);
		}
		if (!Test.isRunningTest()) {
			Messaging.sendEmail(emails);
		}
	}
}