/**
* @description    Territory Assignment Logic for Opportunity Object
* <AUTHOR>
* @version        1.0 
* @created 2020-03-12
* @modified 2021-01-25
*/
public class OpportunityTerritoryAssignmentLogic {
    /**
     * @description Helper class to help capture territory2Id, its priority, and whether there are more 
     * territories with same priority assigned to the account.
     */
    private class Territory2Priority {
        public Id territory2Id { get; set; }
        public Integer priority { get; set; }
        public Id objectId {get; set;}
        public String programCode {get; set;}
 
        Territory2Priority(Id territory2Id, Integer priority, Id objectId, String programCode){
            this.territory2Id = territory2Id;
            this.priority = priority;
            this.objectId = objectId;
            this.programCode = programCode; 
        }
    }

    /**
     * @description Assigns opportunity territory and recruitment officer to a list of opportunities 
     * @param acctIdToProgramCode Map of opportunity's parent account to program account's program code
     * @param oppsToAssign List of opportunities to assign territories and ROs to 
     */
    public static List<Opportunity> oppTerrAndROAssignment(Map<Id, String> acctIdToProgramCode, List<Opportunity> oppsToAssign ){
        System.debug('01: '+acctIdToProgramCode);
        System.debug('02: '+oppsToAssign);
        //List of Opportunities updated from territory assignment
        List<Opportunity> oppsToUpdate = new List<Opportunity>(); 
        //Set of assigned territories 
        Set<Id> assignedTerritoryIds = new Set<Id>(); 
    
        //Map each Account Id to territory of highest priority: 
        Map<Id,Territory2Priority> acctIdToTerritoryMap = getAccountMaxPriorityTerritory(acctIdToProgramCode); 
        //Reset Territory Flag: 
        //Boolean resetTerritory = false;
        Boolean resetTerritory = false;

        //Assign each opportunity a territory:
        System.debug('1: '+oppsToAssign);
        System.debug('2: '+acctIdToTerritoryMap);

        for(Opportunity opp :oppsToAssign){
            if(acctIdToTerritoryMap.containsKey(opp.AccountId)){
                Id territoryToAssign = acctIdToTerritoryMap.get(opp.AccountId).territory2Id; 
                //Update territory if territory values differ 
                //if(opp.territory2Id != territoryToAssign){
                    opp.territory2Id = territoryToAssign; 
                    oppsToUpdate.add(opp); 
                    assignedTerritoryIds.add(territoryToAssign); 
                //}
            }
            else{ //null out territory if related account does not have any related territories 
                resetTerritory = true; 
            }
            //Reset territory field 
            if(resetTerritory){
                if(opp.territory2Id != null){
                    opp.territory2Id = null;
                    oppsToUpdate.add(opp); 
                }
                resetTerritory = false; 
            }
            /*
            System.debug('04: '+opp);
            if(opp.territory2Id != null){
                opp.territory2Id = null;
                oppsToUpdate.add(opp);
            }else{
                oppsToUpdate.add(opp);
            }
             */
        }
        System.debug('03: '+oppsToUpdate);

        //Of the ROs on the territory which was selected, assign the RO with the fewest Oppty Recruitment_Officer__c: 
        if(oppsToUpdate.size() > 0){
            //Query for recruitment officer user assignments:
            System.debug('05==> '+assignedTerritoryIds);
            List<UserTerritory2Association> userAssignments = queryUserAssignments(assignedTerritoryIds, new Set<String>{'Recruitment Officer', 'Assistant Director'});
            System.debug('4==> '+userAssignments);
            //Map users to their level of efforts: 
            Map<Id, Decimal> userToEffortMap = getUserToLevelOfEffortMap(userAssignments);
            System.debug('5==> '+userToEffortMap);
            //Map Territory Ids to a Map of Roles to List of Users in that role: 
            Map<Id, Map<String, List<Id>>> terIdToUsersMap = mapTerritoryToUsers(userAssignments);
            System.debug('6==> '+terIdToUsersMap);
            //Iterate through list of opportunities whose territory fields were either updated or nulled out: 
            for(Opportunity opp:oppsToUpdate){    
                //If territory field is blank, null out assigned Recruitment Officer lookups: 
                if(opp.territory2Id == null){
                    opp.Recruitment_Officer__c = null; 
                    opp.Assistant_Director__c = null;
                }
                else{
                    if(terIdToUsersMap != null && terIdToUsersMap.containsKey(opp.territory2Id)){
                        Id roUser = null;
                        Id adUser = null;
                        //Assign Recruitment Officer users
                        if(terIdToUsersMap.get(opp.territory2Id).containsKey('Recruitment Officer')) roUser = getUserForLookup(terIdToUsersMap.get(opp.Territory2Id).get('Recruitment Officer'), userToEffortMap);

                        //Assign Assistant Director users
                        if(terIdToUsersMap.get(opp.territory2Id).containsKey('Assistant Director')) adUser = getUserForLookup(terIdToUsersMap.get(opp.Territory2Id).get('Assistant Director'), userToEffortMap);

                        //Assign Recruitment Officer and update assigned user's total level of efforts: 
                        if(roUser != null && (opp.Recruitment_Officer__c == null || opp.Recruitment_Officer__c != null && opp.Recruitment_Officer__c != roUser)){ 
                            opp.Recruitment_Officer__c = roUser; 
                            //update level of effort on assigned user: 
                            userToEffortMap.put(roUser, userToEffortMap.get(roUser) + opp.Level_of_Effort__c); 
                        }
                        if(adUser != null && (opp.Assistant_Director__c == null || opp.Assistant_Director__c != null && opp.Assistant_Director__c != adUser)){
                            opp.Assistant_Director__c = adUser;
                            //update level of effort on assigned user:
                            userToEffortMap.put(adUser, userToEffortMap.get(adUser) + opp.Level_of_Effort__c);
                        }
                    }
                    //If there are no assigned ADs or ROs for the territory, null out Recruitment_Officer__c: 
                    else if(terIdToUsersMap == null || !terIdToUsersMap.containsKey(opp.territory2Id)){
                        //null out all fields: 
                        if(opp.Recruitment_Officer__c != null) opp.Recruitment_Officer__c = null; 
                        if(opp.Assistant_Director__c != null) opp.Assistant_Director__c = null;
                    }
                }    
            }
        }
        System.debug('3: '+oppsToUpdate);
        return oppsToUpdate;
    }

    /**
    * @description Query assigned territoryIds in active model for given accountId and 
    * map accountId to a map of Program Code to Territory
    * @return Map of account Id to Territory2Priority
    * @param acctIdToProgramCode  Map of opportunity's parent account to program account's program code
    */
    public static Map<Id,Territory2Priority> getAccountMaxPriorityTerritory(Map<Id, String> acctIdToProgramCode){
        
        List<Territory2Priority> territories = new List<Territory2Priority>(); 
       Map<Id,Territory2Priority> accountMaxPriorityTerritory = new Map<Id,Territory2Priority>();

        // Get the active territory model Id
        Id activeModelId = getActiveModelId();
        if(activeModelId != null){

            //List of account territory assignrments from parent account: 
            List<ObjectTerritory2Association> territoryAssignments = [Select ObjectId, Territory2Id, Territory2.Territory2Type.Priority, Territory2.Program_Code__c FROM ObjectTerritory2Association 
                                                                        WHERE objectId IN :acctIdToProgramCode.keySet() 
                                                                        AND Territory2.Territory2ModelId = :activeModelId];
            
            Map<Id, Id> parentTerritoryIdsToActId = new Map<Id, Id>(); 
            for(ObjectTerritory2Association ota : territoryAssignments){
                parentTerritoryIdsToActId.put(ota.Territory2Id, ota.ObjectId); 
                if(ota.Territory2.Territory2Type.Priority != null){
                    territories.add(new Territory2Priority(ota.Territory2Id,ota.Territory2.Territory2Type.priority, ota.objectId, ota.Territory2.Program_Code__c));
                }
            }

             //Query for related children territory records: 
             List<Territory2> childrenTerritories = [SELECT Id, Program_Code__c, Territory2Type.Priority, ParentTerritory2Id FROM Territory2 
                                                        WHERE ParentTerritory2Id IN :parentTerritoryIdsToActId.KeySet() 
                                                        AND Program_Code__c != null];
             for(Territory2 child : childrenTerritories){
                territories.add(new Territory2Priority(child.Id,child.Territory2Type.priority, parentTerritoryIdsToActId.get(child.ParentTerritory2Id), child.Program_Code__c));
             }

            //Map account to top priority territory: 
            for(Territory2Priority t: territories){
                //only consider territories where program code matches the program code of the program account
                if(t.programCode == acctIdToProgramCode.get(t.objectId)){
                    Territory2Priority tp = accountMaxPriorityTerritory.get(t.ObjectId);
                    if((tp == null) || (t.priority < tp.priority)) accountMaxPriorityTerritory.put(t.ObjectId, t);
                }
            }
        }
        return accountMaxPriorityTerritory;
    }

    /**
     * @description Get the Id of the Active Territory Model.
     * If none exists, return null.
     * @return Id the Id of the active territory model 
     */
    private static Id getActiveModelId() {
        List<Territory2Model> models = [Select Id from Territory2Model where State = 'Active'];
        Id activeModelId = null;
        if(models.size() == 1){
            activeModelId = models.get(0).Id;
        }
        return activeModelId;
    }

    /**
     * @description query for a list of active user assignments
     * @return List<UserTerritory2Association> List of Active User Assignments of Assistant Director and/or Recruitment Officer roles 
     * @param territoryIds set of territory Ids 
     */
    public static List<UserTerritory2Association> queryUserAssignments(set<Id> territoryIds, Set<String> userRoles){
        //List of Assistant Director and Recruitment Officer user assignments 
        List<UserTerritory2Association> userAssignments = [SELECT ID, UserId, Territory2Id, RoleInTerritory2 
                                                            FROM UserTerritory2Association 
                                                            WHERE UserId != null 
                                                            AND Territory2Id IN :territoryIds
                                                            AND RoleInTerritory2 IN :userRoles
                                                            AND isActive = true];
        return userAssignments; 
    }

    /**
     * @description map territory Ids to a map of user roles and list of users with that role
     * @return Map<Id, Map<String, List<Id>>> Map of Territory Id to a Map of User Role to a List of users assigned with that role 
     * @param userAssignments list of user assignments 
     */
    public static Map<Id, Map<String, List<Id>>> mapTerritoryToUsers(List<UserTerritory2Association> userAssignments){
        //Map of Territory Ids to a Map of User Roles to a List of Users with the same related roles: 
        Map<Id, Map<String, List<Id>>> territoryToRolesMap = new Map<Id, Map<String, List<Id>>>(); 

        for(UserTerritory2Association uta :userAssignments){
            if(territoryToRolesMap.containsKey(uta.Territory2Id)){
                if(territoryToRolesMap.get(uta.Territory2Id).containsKey(uta.RoleInTerritory2)){
                    //group users in the same territory with the same role 
                    territoryToRolesMap.get(uta.Territory2Id).get(uta.RoleInTerritory2).add(uta.UserId); 
                }
                else{
                    territoryToRolesMap.get(uta.Territory2Id).put(uta.RoleInTerritory2, new List<Id>{uta.UserId}); 
                }
            }
            //create new key-value pairs:
            else{
                Map<String, List<Id>> RoleToUsers = new Map<String, List<Id>>(); 
                RoleToUsers.put(uta.RoleInTerritory2, new List<Id>{uta.UserId}); 
                territoryToRolesMap.put(uta.Territory2Id, RoleToUsers); 
            }
        }
        return territoryToRolesMap;  

    }
    /**
     * @description Map users to their level of efforts from the Opportunity
     * @return Map<Id, Decimal> Map of users to their total Level of Efforts 
     * @param userAssignments List of user territory assignments 
     */
    public static Map<Id, Decimal> getUserToLevelOfEffortMap(List<UserTerritory2Association> userAssignments){
        //maps user to their level of effort 
        Map<Id, Decimal> userToEffortMap = new Map<Id, Decimal>(); 

        //List of user Ids: 
        set<Id> userIds = new set<id>(); 
        for(UserTerritory2Association uta :userAssignments){
            userIds.add(uta.userId); 
        }
        //Query for all related opportunities: 
        List<User> userList = [SELECT ID, 
                                (SELECT Id, Level_of_Effort__c FROM Opportunities_to_Review__r WHERE Level_of_Effort__c != null LIMIT 200),
                                (SELECT ID, Level_of_Effort__c FROM OpportunitiesRO__r WHERE Level_Of_Effort__c != null LIMIT 200)
                                FROM User WHERE Id IN :userIds];

        //sum up the total level of effort per user 
        for(User u :userList){
            Decimal levelOfEffort = 0; 
            if(u.Opportunities_to_Review__r.size() >0){
                for(Opportunity opp :u.Opportunities_to_Review__r){
                    levelOfEffort += opp.Level_of_Effort__c; 
                }
            }
            if(u.OpportunitiesRO__r.size() > 0){
                for(Opportunity opp :u.OpportunitiesRO__r){
                    levelOfEffort += opp.Level_Of_Effort__c; 
                }
            }
            userToEffortMap.put(u.Id, levelOfEffort);
        }
        return userToEffortMap; 
    }

    /**
     * @description Get the User with the lowest total level of efforts
     * @return Id the user ID of the user with the lowest total level of efforts
     * @param users List of user Ids 
     * @param usersToEffortMap map of userIds to total Level of Efforts 
     */
    public static Id getUserForLookup(List<Id> users, Map<Id, Decimal> usersToEffortMap){
        Decimal lowestEffortScore = null; 
        Id assignedUser = null; 
        for(Id userId :users){
            if(lowestEffortScore == null){
                lowestEffortScore = usersToEffortMap.get(userId); 
                assignedUser = userId; 
            }
            else{
                if(usersToEffortMap.get(userId) < lowestEffortScore){
                    lowestEffortScore = usersToEffortMap.get(userId); 
                    assignedUser = userId; 
                }
            }
        }
        return assignedUser; 
    }
}