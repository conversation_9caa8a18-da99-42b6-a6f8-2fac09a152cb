/**
 * @description Clear shopping cart items when payment is set to invoice
 * <AUTHOR>
 * @version 1.0
 * @created 2021-07-23
 * @modified 2021-07-23
 */
global without sharing class ClearShoppingCartItems_TDTM extends hed.TDTM_Runnable {
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Object
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();
        Map<Id, List<pymt__Shopping_Cart_Item__c>> pidToSCIMap = new Map<Id, List<pymt__Shopping_Cart_Item__c>>(); 
        

        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert) {
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                if(sci.pymt__Payment__c != null && sci.pymt__Shopping_Cart__c != null){
					mapSCIsToPayment(pidToSCIMap, sci);                     
                } 
            }
            
        }else if(triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList); 
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                if(sci.pymt__Payment__c != null && sci.pymt__Shopping_Cart__c != null && oldMap.get(sci.Id).pymt__Payment__c != sci.pymt__Payment__c ){
                    mapSCIsToPayment(pidToSCIMap, sci); 
                }
            }
        }
        
        if(pidToSCIMap.size() > 0){
            Map<Id, pymt__PaymentX__c> paymentMap = new Map<Id, pymt__PaymentX__c>([SELECT ID FROM pymt__PaymentX__c WHERE pymt__Status__c = 'Invoice' AND Id IN: pidToSCIMap.keySet()]); 
            for(Id paymentID : pidToSCIMap.keySet()){
                for(pymt__Shopping_Cart_Item__c sci : pidToSCIMap.get(paymentId)){
                    if(paymentMap.containsKey(sci.pymt__Payment__c)){
                        sci.pymt__Shopping_Cart__c = null; 
                    }
            	}
            }
            
        }
        return dmlWrapper; 
    }
    
    private static void mapSCIsToPayment(Map<Id, List<pymt__Shopping_Cart_Item__c>> pidToSCIMap, pymt__Shopping_Cart_Item__c sci){
        if(pidToSCIMap.containsKey(sci.pymt__Payment__c)){
            pidToSCIMap.get(sci.pymt__Payment__c).add(sci); 
        }else{
            pidToSCIMap.put(sci.pymt__Payment__c, new List<pymt__Shopping_Cart_Item__c>{sci}); 
        }
    }
}