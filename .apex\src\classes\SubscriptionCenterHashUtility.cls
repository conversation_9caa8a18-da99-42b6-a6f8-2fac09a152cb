public class SubscriptionCenterHashUtility {
  
    @testVisible private static final String ALGORITHM = 'SHA1'; // Name of hash algorithm to use
    @testVisible private static final String HASH_SEED = 'Kyle is really cool'; // Hash seed to use
    
    // Map of hash fields with hashed field as key
    private static final Map<String, String> HASH_FIELDS = new Map<String, String>{'Id' => 'HASH_Id__c'};
    
    // Calculates and stores hash of specified fields on Contact records
    public static List<Contact> calculateHash(List<Contact> contacts) {
        
        Map<Id, Contact> updatedContacts = new Map<Id, Contact>();
        
        // Loop over contacts and fields to calculate hash for each field
        for (Contact c:contacts) {
            
            // Loop over fields
            for (String field:HASH_FIELDS.keySet()) {
                
                // Calculate hash if field is not blank
                if (String.isNotBlank((String)c.get(field))) {
                    
                    // Calculate hash
                    String hash = EncodingUtil.convertToHex(Crypto.generateDigest(ALGORITHM, Blob.valueOf((String)c.get(field) + HASH_SEED)));
                    system.debug('--- hash value: ' + hash);
                    
                    // Put hash in hash field if it was blank before or if it has changed and add to update list
                    if (String.isBlank((String)c.get(HASH_FIELDS.get(field))) || !hash.equals((String)c.get(HASH_FIELDS.get(field)))) {
                    c.put(HASH_FIELDS.get(field), hash);
                        updatedContacts.put(c.Id, c);
                    }
                    
                } else {
                    
                    // Put null in hash field if it isn't already blank and source field is null
                    if (String.isNotBlank((String)HASH_FIELDS.get(field))) {
                      c.put(HASH_FIELDS.get(field), null);
                        updatedContacts.put(c.Id, c);
                    }
                }
            }
        }
        
        return updatedContacts.values(); // Return list of updated contacts
    }
    
    // Calculates and stores hash of specified fields on Lead records
    public static List<Lead> calculateHash_Lead(List<Lead> contacts) {
        
        Map<Id, Lead> updatedContacts = new Map<Id, Lead>();
        
        // Loop over contacts and fields to calculate hash for each field
        for (Lead c : contacts) {
            
            // Loop over fields
            for (String field:HASH_FIELDS.keySet()) {
                
                // Calculate hash if field is not blank
                if (String.isNotBlank((String)c.get(field))) {
                    
                    // Calculate hash
                    String hash = EncodingUtil.convertToHex(Crypto.generateDigest(ALGORITHM, Blob.valueOf((String)c.get(field) + HASH_SEED)));
                    
                    // Put hash in hash field if it was blank before or if it has changed and add to update list
                    if (String.isBlank((String)c.get(HASH_FIELDS.get(field))) || !hash.equals((String)c.get(HASH_FIELDS.get(field)))) {
                    c.put(HASH_FIELDS.get(field), hash);
                        updatedContacts.put(c.Id, c);
                    }
                    
                } else {
                    
                    // Put null in hash field if it isn't already blank and source field is null
                    if (String.isNotBlank((String)HASH_FIELDS.get(field))) {
                      c.put(HASH_FIELDS.get(field), null);
                        updatedContacts.put(c.Id, c);
                    }
                }
            }
        }
        
        return updatedContacts.values(); // Return list of updated contacts
    }
}