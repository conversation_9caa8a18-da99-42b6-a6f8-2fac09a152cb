/** 
* @description    When shopping cart is saved this checks to see if contact has used discount code before
* <AUTHOR> 
* @version        1.0 
* @created        3-JAN-2022
*/
global class Discount_Usage_duplicate_check_TD<PERSON> extends hed.TDTM_Runnable{

    public static final String ERROR_MESSAGE = 'Discount may not be used more than once';

    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Course Enrollment records from trigger new 
     * @param oldList the list of Course Enrollment records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, Before Update)
     * @param objResult the describe for Course Enrollment
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
         
        set<string> dbDiscountUsageSet = new set<string>();
        boolean result1;
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
           
    
        
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert || triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){
            
        	 //for each database record create dbDiscountUsage to compare (discount + contact) into single string
            for(pymt__Shopping_Cart_Item__c dbDiscountUsage : [SELECT discount__c, discount__r.Automatically_Apply__c, discount_secondary__c, pymt__Contact__c
                                                                 FROM pymt__Shopping_Cart_Item__c 
                                                                WHERE Discount__c != ''
                                                                  AND pymt__Contact__c != ''
                                                                  AND discount__r.Automatically_Apply__c != true
                                                                  AND pymt__Payment_Completed__c = true]){
        
                String dbOwnerSet = dbDiscountUsage.pymt__Contact__c; //Get db contact (string)
                String dbDiscountSet = dbDiscountUsage.discount__c;//Get DiscountID (String)
                String dbSecondaryDiscountSet = dbDiscountUsage.discount_secondary__c;//Get SecondaryDiscountID (String)
                dbDiscountUsageSet.add(dbOwnerSet + dbDiscountSet);//Combine string into single string
                if(dbSecondaryDiscountSet != null){dbDiscountUsageSet.add(dbOwnerSet + dbSecondaryDiscountSet);}//2nd Discount is treated as a useage
                //system.debug();

                }

    
            //Compare newEnrollmentSet to dbenrollment string
            for(pymt__Shopping_Cart_Item__c newDiscountUsage : (List<pymt__Shopping_Cart_Item__c>)newlist){
        
                String CartContact = newDiscountUsage.pymt__Contact__c;//Get new contact (string)
                String newDiscountInput = (newDiscountUsage.Discount__c);//Get DiscountID (String)
                result1 = dbDiscountUsageSet.contains(CartContact + newDiscountInput);//Boolean result if strings match
                if(result1 && newDiscountInput != ''){
            
                    newDiscountUsage.addError(ERROR_MESSAGE);}
        
            }  
        }
        return null;
    }
}