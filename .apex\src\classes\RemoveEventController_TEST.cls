@IsTest
private class RemoveEventController_TEST {
	@TestSetup
	private static void testDataSetup() {
		User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		System.runAs(adminUser){
			Calendar_View__c testCalendrViewObject = new Calendar_View__c();
			testCalendrViewObject.Link_to_Calendar_Item__c =
					'https://dev01-rotman.cs138.force.com/events/s/special-event/{!itemId}';
			testCalendrViewObject.Name = 'Events Home Page Calendar View';
			insert testCalendrViewObject;

			Store_Configuration__c testStoreConfig = new Store_Configuration__c(
					Name = 'Events',
					Link_to_Cart__c = '/events/s/shopping-cart/{!cartId}'
			);
			insert testStoreConfig;

			Store_SObject_Configuration__c sobj = new Store_SObject_Configuration__c();
			sobj.Name = 'Event';
			sobj.Store_Configuration__c = testStoreConfig.Id;
			sobj.SObject_API_Name__c = 'evt__Special_Event__c';
			sobj.Shopping_Cart_Lookup_to_Object__c = 'Special_Event__c';
			sobj.Enable_One_Click_Checkout__c = true;
			sobj.Enforce_Cart_Item_Uniqueness__c = true;
			sobj.Amount_Field_API_Name__c = 'Price__c';
			sobj.Field_Indicating_Availability_for_Cart__c = 'Available_for_Registration__c';
			sobj.Include_Quantity_Field__c = false;
			insert sobj;

			Store_SObject_Mapping__c sobjm1 = new Store_SObject_Mapping__c(
					Default_Value__c = 'Event Registration',
					Cart_Field_API_Name__c = 'Type__c',
					Target_Object_Field_API_Name__c = 'Taxable__c',
					Store_SObject_Configuration__c = sobj.Id
			);
			insert sobjm1;

			Store_SObject_Mapping__c sobjm2 = new Store_SObject_Mapping__c(
					Default_Value__c = '',
					Cart_Field_API_Name__c = 'Shipping_Country__c',
					Target_Object_Field_API_Name__c = 'Country__c',
					Store_SObject_Configuration__c = sobj.Id
			);
			insert sobjm2;

			Taxing_Authority__c tax1 = new Taxing_Authority__c(
					Name = 'Ontario',
					State_Province__c = 'ON',
					Country__c = 'CA',
					Tax_Rate__c = 13
			);
			insert tax1;

			Id fulltimeId = ApplicationService.FTandSpecializedRTId;
			Account testAccount = new Account(Name = 'Test Account');
			insert testAccount;

			Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>',
					AccountId = testAccount.Id);
			insert testContact;

			String cookieName = String.valueOf(dateTime.now());

			pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
			cart.pymt__Cart_UID__c = cookieName;
			cart.Store_Configuration__c = testStoreConfig.Id;
			insert cart;

			hed__Term__c trm = new hed__Term__c(hed__Account__c = testAccount.Id, name = 'Spring 2020');
			insert trm;

			Cohort__c cohort = new Cohort__c(
					Name = 'FTMBA - 2025',
					Start_Term__c = trm.Id,
					Program__c = testAccount.Id,
					Orientation_Date__c = Date.valueOf('2024-04-09 09:30:40'),
					Key__c = 'FTMBA - 2025'
			);
			insert cohort;

			Program_Term_Availability__c pta = new Program_Term_Availability__c(
					Active__c = true,
					Program__c = testAccount.Id,
					Cohort__c = cohort.Id,
					Program_Start_Date__c = Date.valueOf('2022-12-09 10:15:30'),
					Program_End_Date__c = Date.valueOf('2024-04-09 09:30:40'),
					Term__c = trm.Id
			);
			insert pta;

			hed__Application__c app = new hed__Application__c(
					Program_Term_Availability__c = pta.Id,
					hed__Application_Status__c = 'In Progress',
					RecordTypeId = fulltimeId,
					hed__Applying_To__c = testAccount.Id,
					hed__Term__c = trm.Id
			);
			insert app;

			evt__Special_Event__c event = new evt__Special_Event__c();
			event.Name = 'Special event';
			event.Price__c =
					'$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
			event.Start_Local__c = Date.today().addDays(30);
			event.End_Local__c = Date.today().addDays(31);
			event.Start_Date__c = Date.today().addDays(30);
			event.End_Date__c = Date.today().addDays(31);
			event.evt__Event_Time_Zone__c = '(GMT-05:00) Eastern Standard Time (America/Toronto)';
			event.evt__Registration_Deadline__c = Date.today().addDays(29);
			event.evt__By_Invitation__c = false;
			event.Venue_Type__c = 'In-Person';
			event.evt__Publish_To__c = 'Public Events';
			event.evt__Event_Type__c = 'Session Event';
			event.evt__Status__c = 'Published';
			event.evt__Max_Attendees__c = 100;
			event.Tags__c = 'Strategic Communications';
			event.Thumbnail_Image__c =
					'<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';

			List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
			listOfEventToInsert.add(event);

			insert listOfEventToInsert;

			evt__Event_Fee__c fee = new evt__Event_Fee__c();
			fee.Name = 'special event fee';
			fee.evt__Event__c = listOfEventToInsert[0].Id;
			fee.evt__Amount__c = 0.0;
			fee.evt__Active__c = true;
			fee.Type__c = 'Standard';

			insert fee;

			evt__Session__c session = new evt__Session__c();
			session.Name = 'Session 1';
			session.evt__Event__c = listOfEventToInsert[0].Id;
			session.evt__Max_Attendees__c = 100;
			insert session;

			pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
			item.Name = 'Test Item';
			item.pymt__Contact__c = testContact.Id;
			item.Event_Fee__c = fee.Id;
			item.pymt__Unit_Price__c = 0.0;
			item.Event_Discount_Amount__c = 0.0;
			item.pymt__Quantity__c = 1;
			item.type__c = 'Event Registration';
			item.pymt__Shopping_Cart__c = cart.Id;
			item.Special_Event__c = listOfEventToInsert[0].Id;

			insert item;

			evt__Attendee__c at = new evt__Attendee__c();
			at.evt__Event_Fee__c = fee.Id;
			at.evt__Contact__c = testContact.Id;
			at.evt__Invitation_Status__c = 'Registered';
			at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
			at.Shopping_Cart_Item__c = item.Id;
			at.evt__Event__c = listOfEventToInsert[0].Id;
			at.evt__Reg_Email__c = '<EMAIL>';
			at.evt__Reg_First_Name__c = 'John';
			at.evt__Reg_Last_Name__c = 'Doe';
			at.Industry__c = 'Technology';

			insert at;

			shopping_cart_item_details__c testCartItemDetail = new shopping_cart_item_details__c();
			testCartItemDetail.SC_event__c = listOfEventToInsert[0].Id;
			testCartItemDetail.Event_Fee__c = fee.Id;
			testCartItemDetail.Contact__c = testContact.Id;
			testCartItemDetail.Shopping_Cart_Item__c = item.Id;
			testCartItemDetail.Attendee__c = at.Id;
			testCartItemDetail.Item_Unit_Price__c = 5.0;
			testCartItemDetail.Item_Quantity__c = 1.0;
			testCartItemDetail.Item_Discount_Amount__c = 0.0;
			testCartItemDetail.Item_Gross_Amount__c = 5.0;
			testCartItemDetail.Item_Tax_Amount__c = 0.0;
			testCartItemDetail.Item_Total_Amount__c = 5.0;

			insert testCartItemDetail;

			Subscription__c testSubscription = new Subscription__c(Name = 'Upcoming events', Subscription_ID__c = 'Test1', Active__c = true, Group__c = 'Recruitment and Admissions');
			insert testSubscription;
		}
	}

	@IsTest
	static void testDeleteCartItemsAndCart() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		pymt__Shopping_Cart__c testShoppingCart = new pymt__Shopping_Cart__c();
		testShoppingCart.Cart_Key__c = '2398410734';
		insert testShoppingCart;

		pymt__Shopping_Cart_Item__c testCartItem = new pymt__Shopping_Cart_Item__c();
		testCartItem.Name = 'testCartItem';
		testCartItem.Special_Event__c = testEvent.Id;
		testCartItem.pymt__Shopping_Cart__c = testShoppingCart.Id;
		insert testCartItem;

		Test.startTest();
		String cartInfo = RemoveEventController.deleteCartItemsAndCart(testCartItem.Id, testShoppingCart.Id);
		Test.stopTest();
	}

	@isTest
	static void testGetCart1() {
		Store_Configuration__c testStoreConfig = [SELECT Id FROM Store_Configuration__c LIMIT 1];
		Contact testUser = [SELECT Id FROM Contact LIMIT 1];

		Test.startTest();
		Map<String, String> cartInfo = RemoveEventController.getCart(testStoreConfig.Id, '');
		Test.stopTest();
	}

	@isTest
	static void testGetCart2() {
		Store_Configuration__c testStoreConfig = [SELECT Id FROM Store_Configuration__c LIMIT 1];
		Contact testUser = [SELECT Id FROM Contact LIMIT 1];
		String cookieId = '2398410734';
		pymt__Shopping_Cart__c testCart = [SELECT Id, Cart_Key__c FROM pymt__Shopping_Cart__c LIMIT 1];
		testCart.Cart_Key__c = cookieId;
		update testCart;


		pymt__Shopping_Cart_Item__c item = [SELECT Id, Special_Event__c, pymt__Payment_Completed__c, Item_Available__c FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		item.pymt__Shopping_Cart__c = testCart.Id;
		item.pymt__Payment_Completed__c = false;
		update item;

		System.debug('item: ' + item);
		System.debug('testCart: ' + testCart);
		System.debug('testUser: ' + testUser);


		Test.startTest();
		Map<String, String> cartInfo = RemoveEventController.getCart(testStoreConfig.Id, cookieId);
		Test.stopTest();
	}

	@IsTest
	private static void getCartConfigs() {
		String cookieId = '2398410734';
		Id scId = [SELECT Id FROM Store_Configuration__c Limit 1].Id;
		Contact con = [SELECT Id FROM Contact LIMIT 1];

		insert new User(
				alias = 'test2',
				communityNickname = 'test123',
				contactId = con.Id,
				email = '<EMAIL>',
				emailencodingkey = 'UTF-8',
				firstName = 'test2',
				lastName = 'User',
				userName = '<EMAIL>',
				profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Applicant Community User' LIMIT 1].Id,
				timeZoneSidKey = 'America/Los_Angeles',
				LocaleSidKey = 'en_US',
				LanguageLocaleKey = 'en_US'
		);
		User commUser = [SELECT Id FROM User WHERE contactId = :con.Id LIMIT 1];

		Map<String, String> responseLoggedInNoCookie;
		Map<String, String> responseLoggedInWithCookie;

		System.runAs(commUser) {
			responseLoggedInNoCookie = RemoveEventController.getCart(scId, '');
			responseLoggedInWithCookie = RemoveEventController.getCart(scId, cookieId);
		}
		//Map<String, String> responseGuestNoCookie = CartNewController.getCart(scId, '');
		//Map<String, String> responseGuestWithCookie = CartNewController.getCart(scId, cookieId);
	}

	@isTest
	static void testDeleteCartItemsAndCart1() {
		Store_SObject_Configuration__c testStoreConfig = [SELECT Id FROM Store_SObject_Configuration__c LIMIT 1];
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		pymt__Shopping_Cart__c testShoppingCart = new pymt__Shopping_Cart__c();
		testShoppingCart.Cart_Key__c = '2398410734';
		insert testShoppingCart;

		pymt__Shopping_Cart_Item__c testCartItem = new pymt__Shopping_Cart_Item__c();
		testCartItem.Name = 'testCartItem';
		testCartItem.Special_Event__c = testEvent.Id;
		testCartItem.pymt__Shopping_Cart__c = testShoppingCart.Id;
		insert testCartItem;

		Test.startTest();
		String testUrl = RemoveEventController.deleteCartItemsAndCart1(testShoppingCart.Id, testEvent.Id);
		Test.stopTest();
	}

	@isTest
	static void testGetCartItemConfig() {
		Test.startTest();
		//Map<String, String> mapEvents = RemoveEventController.getEventOptOutURL(null);
		Test.stopTest();
	}

	@isTest
	public static void testGetEventOptOutURL1() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		testEvent.OptOutURL__c = '';
		update testEvent;

		Test.startTest();
		Map<String, String> mapEvents = RemoveEventController.getEventOptOutURL(testEvent.Id);
		Test.stopTest();
	}

	@isTest
	public static void testGetEventOptOutURL2() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		testEvent.OptOutURL__c = 'https://www.google.com';
		update testEvent;

		Test.startTest();
		Map<String, String> mapEvents = RemoveEventController.getEventOptOutURL(testEvent.Id);
		Test.stopTest();
	}

	@isTest
	static void testGetEventFeeList1() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		Contact con = [SELECT Id, Type__c FROM Contact LIMIT 1];
		con.Type__c = 'Applicant';
		update con;

		insert new User(
				alias = 'test2',
				communityNickname = 'test123',
				contactId = con.Id,
				email = '<EMAIL>',
				emailencodingkey = 'UTF-8',
				firstName = 'test2',
				lastName = 'User',
				userName = '<EMAIL>',
				profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Applicant Community User' LIMIT 1].Id,
				timeZoneSidKey = 'America/Los_Angeles',
				LocaleSidKey = 'en_US',
				LanguageLocaleKey = 'en_US'
		);

		User commUser = [SELECT Id FROM User WHERE Username = '<EMAIL>'];

		Test.startTest();
		System.runAs(commUser){
			con.Type__c = 'Applicant';
			update con;
			List<evt__Event_Fee__c> result1 = RemoveEventController.getEventFeeList(testEvent.Id);
			con.Type__c = 'Faculty';
			update con;
			List<evt__Event_Fee__c> result2 = RemoveEventController.getEventFeeList(testEvent.Id);
			con.Type__c = 'Student';
			update con;
			List<evt__Event_Fee__c> result3 = RemoveEventController.getEventFeeList(testEvent.Id);
			con.Type__c = 'Alumni';
			update con;
			List<evt__Event_Fee__c> result4 = RemoveEventController.getEventFeeList(testEvent.Id);
			con.Type__c = '';
			update con;
			List<evt__Event_Fee__c> result5 = RemoveEventController.getEventFeeList(testEvent.Id);
		}
		Test.stopTest();
	}

	@isTest
	static void testGetEventFeeList2() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];


		Test.startTest();
		List<evt__Event_Fee__c> result = RemoveEventController.getEventFeeList(testEvent.Id);
		Test.stopTest();
	}

	@isTest
	static void testSearchAttendees() {
		Test.startTest();
		List<evt__Attendee__c> result = RemoveEventController.searchAttendees('John');
		Test.stopTest();
	}

	@isTest
	static void testCreateAttendeeRecords1() {
		pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
		pymt__Shopping_Cart_Item__c testShoppingCartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Event_Fee__c testEventFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		Contact con = [SELECT Id FROM Contact LIMIT 1];

		String cardName = 'John Doe';
		String cardNo = '****************';
		String cvv = '123';
		String expMonth = '12';
		String expYear = '2026';
		String accountName = 'transaction_processing';
		String loggedInContactId = con.Id;
		String contactFName = 'John';
		String contactLName = 'Doe';
		String contactEmail = '<EMAIL>';

		List<evt__Attendee__c> listOfAttendees = new List<evt__Attendee__c>();
		evt__Attendee__c attendee = new evt__Attendee__c();
		attendee.evt__Event__c = testEvent.Id;
		attendee.evt__Invitation_Status__c = 'Registered';
		attendee.evt__Reg_First_Name__c = 'Evan';
		attendee.evt__Reg_Last_Name__c = 'Doe';
		attendee.evt__Reg_Street__c = '123 Main St';
		attendee.evt__Reg_City__c = 'San Francisco';
		attendee.evt__Reg_State__c = 'CA';
		attendee.evt__Reg_Country__c = 'US';
		attendee.evt__Reg_Postal_Code__c = '94105';
		attendee.evt__Reg_Email__c = '<EMAIL>';
		attendee.Shopping_Cart_Item__c = testShoppingCartItem.Id;
		attendee.evt__Event_Fee__c = testEventFee.Id;
		attendee.evt__Contact__c = con.Id;
		insert attendee;
		listOfAttendees.add(attendee);

		pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
		pymnt.Name = 'payment_' + string.valueOf(datetime.now());
		pymnt.pymt__Transaction_Id__c = 'TRN_ReJjB6G4EQPgkBtL4ZTI9aGUMtAp8w_00000NnKzIAK';
		pymnt.pymt__Transaction_Type__c = 'Payment';
		pymnt.Type__c = 'Event Registration';
		pymnt.pymt__Transaction_Id__c = '123456';
		pymnt.pymt__Status__c = 'Completed';
		pymnt.pymt__Amount__c = 10;
		pymnt.pymt__Payment_Type__c = 'Credit Card';
		pymnt.pymt__Payment_Processor__c = 'Global Pay';
		pymnt.pymt__Billing_First_Name__c = 'TEST';
		pymnt.pymt__Billing_Last_Name__c = 'kite';
		pymnt.pymt__Billing_Street__c = '123 Main St';
		pymnt.pymt__Billing_City__c = 'San Francisco';
		pymnt.pymt__Billing_State__c = 'CA';
		pymnt.pymt__Billing_Country__c = 'USA';
		pymnt.evt__Event__c = testEvent.Id;
		pymnt.pymt__Card_Type__c = 'Visa';
		pymnt.pymt__Contact__c = con.Id;
		insert pymnt;

		Decimal subTotal = 100.00;
		Decimal discount = 0.00;
		String discountId = '';
		Integer currentUsage = 1;
		Decimal tax = 0.00;
		Decimal total = 100.00;
		Boolean isPayment = true;
		String eventId = testEvent.Id;
		List<EventFeeCartWrapper> listOfEventFees = new List<EventFeeCartWrapper>();
		EventFeeCartWrapper efcw = new EventFeeCartWrapper();
		efcw.eventFeeId = testEventFee.Id;
		listOfEventFees.add(efcw);
		Integer quantity = 1;
		Subscription__c sc = new Subscription__c();
		sc.Name = 'Upcoming events';
		insert sc;
		String[] listOfSubscription = new String[]{'Upcoming events'};
		List<UploadAttendeeFileWrapper> listOfFiles = new List<UploadAttendeeFileWrapper>();
		UploadAttendeeFileWrapper aufw = new UploadAttendeeFileWrapper();

		evt__Session__c testSession = [SELECT Id FROM evt__Session__c LIMIT 1];
		List<SessionAssignmentWrapper> listOfSessions =	new List<SessionAssignmentWrapper>();
		SessionAssignmentWrapper esaw = new SessionAssignmentWrapper();
		esaw.sessionId = testSession.Id;
		esaw.eventId = testEvent.Id;
		esaw.eventTypeId = testEventFee.Id;
		esaw.attendeeId = attendee.Id;
		esaw.attendeeEmail = '<EMAIL>';
		listOfSessions.add(esaw);

		Test.startTest();
		Test.setMock(HttpCalloutMock.class, new CongaMergeMock());
		pymt__PaymentX__c result =
				RemoveEventController.createAttendeeRecords(testShoppingCart.Id, cardName, cardNo, cvv, expMonth,
						expYear, accountName, loggedInContactId, contactFName, contactLName, contactEmail,
						listOfAttendees, pymnt, subTotal, discount, discountId, currentUsage, tax, total, isPayment,
						eventId, listOfEventFees, quantity, listOfSubscription, listOfFiles, listOfSessions);
		Test.stopTest();

	}

	@isTest
	static void testCreateAttendeeRecords2() {
		pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Event_Fee__c testEventFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];
		evt__Session__c testSession = [SELECT Id FROM evt__Session__c LIMIT 1];

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = testContact.Id;
		item.Event_Fee__c = testEventFee.Id;
		item.pymt__Unit_Price__c = 0.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';
		item.pymt__Shopping_Cart__c = testShoppingCart.Id;
		item.Special_Event__c = testEvent.Id;
		item.pymt__Payment_Completed__c = false;

		insert item;

		String cardName = '';
		String cardNo = '';
		String cvv = '';
		String expMonth = '';
		String expYear = '';
		String accountName = '';
		String loggedInContactId = null;
		String contactFName = 'John';
		String contactLName = 'Doe';
		String contactEmail = '<EMAIL>';

		List<evt__Attendee__c> listOfAttendees = new List<evt__Attendee__c>();
		evt__Attendee__c attendee = new evt__Attendee__c();
		attendee.evt__Event__c = testEvent.Id;
		attendee.evt__Invitation_Status__c = 'Registered';
		attendee.evt__Reg_First_Name__c = 'Evan';
		attendee.evt__Reg_Last_Name__c = 'Doe';
		attendee.evt__Reg_Email__c = '<EMAIL>';
		attendee.Shopping_Cart_Item__c = item.Id;
		attendee.evt__Event_Fee__c = testEventFee.Id;
		insert attendee;
		listOfAttendees.add(attendee);

		pymt__PaymentX__c pymnt = new pymt__PaymentX__c();

		Decimal subTotal = 0.00;
		Decimal discount = 0.00;
		String discountId = '';
		Integer currentUsage = 1;
		Decimal tax = 0.00;
		Decimal total = 0.00;
		Boolean isPayment = true;
		String eventId = testEvent.Id;
		List<EventFeeCartWrapper> listOfEventFees = new List<EventFeeCartWrapper>();
		EventFeeCartWrapper efcw = new EventFeeCartWrapper();
		efcw.eventFeeId = testEventFee.Id;
		efcw.eventId = testEvent.Id;
		efcw.price = 1.00;
		efcw.quantity = 1;
		efcw.tax = 0.50;
		listOfEventFees.add(efcw);

		Integer quantity = 1;
		String[] listOfSubscription = new String[]{'Upcoming events'};
		List<UploadAttendeeFileWrapper> listOfFiles = new List<UploadAttendeeFileWrapper>();
		UploadAttendeeFileWrapper aufw = new UploadAttendeeFileWrapper();
		aufw.attendeeEmail = '<EMAIL>';
		aufw.eventFeeId = testEventFee.Id;
		aufw.file = 'test';
		aufw.fileName = 'testFile';
		listOfFiles.add(aufw);

		List<SessionAssignmentWrapper> listOfSessions =	new List<SessionAssignmentWrapper>();
		SessionAssignmentWrapper esaw =	new SessionAssignmentWrapper();
		esaw.eventId = testEvent.Id;
		esaw.sessionId = testSession.Id;
		listOfSessions.add(esaw);

		Test.startTest();
		Test.setMock(HttpCalloutMock.class, new CongaMergeMock());
		pymt__PaymentX__c result =
				RemoveEventController.createAttendeeRecords(testShoppingCart.Id, cardName, cardNo, cvv, expMonth,
						expYear, accountName, loggedInContactId, contactFName, contactLName, contactEmail,
						listOfAttendees, pymnt, subTotal, discount, discountId, currentUsage, tax, total, isPayment,
						eventId, listOfEventFees, quantity, listOfSubscription, listOfFiles, listOfSessions);
		Test.stopTest();

	}

	@isTest
	static void testCreateAttendeeRecords3() {
		pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Event_Fee__c testEventFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];

		Discount__c testDiscount = new Discount__c();
		testDiscount.Code__c = 'TESTCODE';
		testDiscount.Event__c = testEvent.Id;
		testDiscount.At_Limit__c = false;
		testDiscount.Current_Usage__c = 2;
		testDiscount.Max_Usage__c = 10;
		testDiscount.Dollar_Discount__c = 1;
		testDiscount.Taxable__c = true;
		insert testDiscount;

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = testContact.Id;
		item.Event_Fee__c = testEventFee.Id;
		item.pymt__Unit_Price__c = 0.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';
		item.pymt__Shopping_Cart__c = testShoppingCart.Id;
		item.Special_Event__c = testEvent.Id;
		item.pymt__Payment_Completed__c = false;

		insert item;

		String cardName = '';
		String cardNo = '';
		String cvv = '';
		String expMonth = '';
		String expYear = '';
		String accountName = '';
		String loggedInContactId = testContact.Id;
		String contactFName = 'John3';
		String contactLName = 'Doe3';
		String contactEmail = '<EMAIL>';

		Account acc = new Account();
		acc.Name = 'Test Account';
		insert acc;

		Subscription__c subscr = new Subscription__c(Name = 'Upcoming events', Subscription_ID__c = 'testsubscr');
		insert subscr;

		hed__Term__c term = [SELECT Id FROM hed__Term__c LIMIT 1];

		List<evt__Attendee__c> listOfAttendees = new List<evt__Attendee__c>();
		evt__Attendee__c attendee = new evt__Attendee__c();
		attendee.evt__Event__c = testEvent.Id;
		attendee.evt__Invitation_Status__c = 'Registered';
		attendee.evt__Reg_First_Name__c = 'Evan';
		attendee.evt__Reg_Last_Name__c = 'Doe';
		attendee.evt__Reg_Email__c = '<EMAIL>';
		attendee.Shopping_Cart_Item__c = item.Id;
		attendee.evt__Event_Fee__c = testEventFee.Id;
		attendee.Academic_Program__c = acc.Id;
		attendee.Academic_Term__c = term.Id;
		attendee.Accessibility_Requirements__c = 'Other';
		attendee.Accessibility_Requirements_Other__c = 'Other';
		attendee.Dietary_Restrictions__c = 'Other';
		attendee.Dietary_Restictions_Other__c = 'Other';
		attendee.evt__Is_Primary__c = TRUE;
		insert attendee;
		listOfAttendees.add(attendee);

		pymt__PaymentX__c pymnt = new pymt__PaymentX__c();

		Decimal subTotal = 0.00;
		Decimal discount = 1.00;
		String discountId = testDiscount.Id;
		Integer currentUsage = 1;
		Decimal tax = 0.00;
		Decimal total = 0.00;
		Boolean isPayment = true;
		String eventId = testEvent.Id;
		List<EventFeeCartWrapper> listOfEventFees = new List<EventFeeCartWrapper>();
		EventFeeCartWrapper efcw = new EventFeeCartWrapper();
		efcw.eventFeeId = testEventFee.Id;
		efcw.eventId = testEvent.Id;
		efcw.price = 1.00;
		efcw.quantity = 1;
		efcw.tax = 0.50;
		listOfEventFees.add(efcw);

		Integer quantity = 1;
		String[] listOfSubscription = new String[]{'Upcoming events'};
		List<UploadAttendeeFileWrapper> listOfFiles = new List<UploadAttendeeFileWrapper>();
		UploadAttendeeFileWrapper aufw = new UploadAttendeeFileWrapper();
		aufw.attendeeEmail = '<EMAIL>';
		aufw.eventFeeId = testEventFee.Id;
		aufw.file = 'test';
		aufw.fileName = 'testFile';
		listOfFiles.add(aufw);

		List<SessionAssignmentWrapper> listOfSessions =	new List<SessionAssignmentWrapper>();
		SessionAssignmentWrapper esaw =	new SessionAssignmentWrapper();

		Test.startTest();
		Test.setMock(HttpCalloutMock.class, new CongaMergeMock());
		pymt__PaymentX__c result =
				RemoveEventController.createAttendeeRecords(testShoppingCart.Id, cardName, cardNo, cvv, expMonth,
						expYear, accountName, loggedInContactId, contactFName, contactLName, contactEmail,
						listOfAttendees, pymnt, subTotal, discount, discountId, currentUsage, tax, total, isPayment,
						eventId, listOfEventFees, quantity, listOfSubscription, listOfFiles, listOfSessions);
		Test.stopTest();

	}

	@isTest
	static void testCreateNewContact() {
		String loggedContactId = '';
		String contactFName = 'John';
		String contactLName = 'Doe';
		String contactEmail = '<EMAIL>';

		Test.startTest();
		String result1 =
				RemoveEventController.createNewContact(loggedContactId, contactFName, contactLName, contactEmail);
		Test.stopTest();
	}

	@isTest
	static void testGetCouponCode() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		Discount__c testDiscount = new Discount__c();
		testDiscount.Code__c = 'TESTCODE';
		testDiscount.Event__c = testEvent.Id;
		testDiscount.At_Limit__c = false;
		testDiscount.Current_Usage__c = 0;
		testDiscount.Max_Usage__c = 10;
		testDiscount.Dollar_Discount__c = 5;
		testDiscount.Taxable__c = true;
		insert testDiscount;

		Test.startTest();
		Discount__c result = RemoveEventController.getCouponCode('TESTCODE', testevent.Id);
		Test.stopTest();
	}

	@isTest
	static void testGetShoppingCartItems() {
		pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];

		Test.startTest();
		List<EventCartWrapperNew> result =
				RemoveEventController.getShoppingCartItems(testShoppingCart.Id, testEvent.Id);
		Test.stopTest();
	}

	@isTest
	static void testCartEvents() {
		pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
		pymt__Shopping_Cart_Item__c testShoppingCartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];

		Test.startTest();
		//List<evt__Special_Event__c> result = RemoveEventController.cartEvents(testShoppingCart.Id, testShoppingCartItem.Id);
		Test.stopTest();
	}

	@isTest
	static void testGetCartItems() {
		pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];

		Test.startTest();
		List<pymt__Shopping_Cart_Item__c> result =
				RemoveEventController.getCartItems(testShoppingCart.Id, testEvent.Id);
		Test.stopTest();
	}

	@isTest
	static void testCheckAttendee() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		List<evt__Attendee__c> testAttendeeList = [SELECT Id, Name, evt__Reg_Email__c FROM evt__Attendee__c LIMIT 1];

		Test.startTest();
		List<evt__Attendee__c> result = RemoveEventController.checkAttendee(testAttendeeList, testEvent.Id);
		Test.stopTest();
	}

	@IsTest
	static void testProcessPayment() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];
		pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
		pymt__Shopping_Cart_Item__c testShoppingCartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];

		pymt__PaymentX__c pymt = new pymt__PaymentX__c();
		pymt.Name = 'payment_' + string.valueOf(datetime.now());
		pymt.Order_Id__c = 'Payment:' + string.valueOf(datetime.now());
		pymt.pymt__Transaction_Type__c = 'Payment';
		pymt.pymt__Status__c = 'Scheduled';
		pymt.pymt__Amount__c = 1000;
		pymt.pymt__Payment_Type__c = 'Credit Card';
		pymt.pymt__Billing_First_Name__c = 'TEST';
		pymt.pymt__Billing_Last_Name__c = 'kite';
		pymt.pymt__Billing_Street__c = '123 Main St';
		pymt.pymt__Billing_City__c = 'Toronto';
		pymt.pymt__Billing_State__c = 'ON';
		pymt.evt__Event__c = testEvent.Id;
		pymt.pymt__Contact__c = testContact.Id;
		insert pymt;

		testShoppingCartItem.pymt__Payment__c = pymt.Id;
		update testShoppingCartItem;

		String cardNo = '****************';
		String cvd = '123';
		String expYr = '2026';
		String expMo = '04';
		String accountName = 'transaction_processing';
		String shoppingCartId = testShoppingCart.Id;

		Test.startTest();
		Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
		Map<String, String> responseMap =
				RemoveEventController.processPayment(pymt, cardNo, cvd, expYr, expMo, accountName, shoppingCartId);
		Test.stopTest();
	}

	@isTest
	static void testGetCardType() {
		String cardType1 = 'VISA';
		String cardType2 = 'MASTERCARD';
		String cardType3 = 'DINERS';
		String cardType4 = 'DISCOVER';
		String cardType5 = 'AMEX';
		String actualCardType1 = RemoveEventController.getCardType(cardType1);
		String actualCardType2 = RemoveEventController.getCardType(cardType2);
		String actualCardType3 = RemoveEventController.getCardType(cardType3);
		String actualCardType4 = RemoveEventController.getCardType(cardType4);
		String actualCardType5 = RemoveEventController.getCardType(cardType5);
	}

	@isTest
	static void testEventSessionAssignmentWrapper() {
		Test.startTest();
		SessionAssignmentWrapper wrapper =
				new SessionAssignmentWrapper();
		wrapper.sessionId = 'session123';
		wrapper.eventId = 'event123';
		wrapper.eventTypeId = 'type123';
		wrapper.attendeeId = 'attendee123';
		wrapper.attendeeEmail = '<EMAIL>';
		Test.stopTest();
		System.debug('---wrapper ' + wrapper);
	}

	@isTest
	static void testattendeeUploadFileWrapper() {
		Test.startTest();
		UploadAttendeeFileWrapper wrapper = new UploadAttendeeFileWrapper();
		wrapper.attendeeId = '001XXXXXXXXXXXXXXX';
		wrapper.attendeeEmail = '<EMAIL>';
		wrapper.eventFeeId = 'a0XXXXXXXXXXXXXXX';
		wrapper.file = 'Test File Content';
		wrapper.fileName = 'Test File.txt';
		Test.stopTest();
		System.debug('---wrapper ' + wrapper);
	}

	@IsTest
	static void getObjectConfigs() {
		String cookieId = '2398410734';
		Store_Configuration__c sc1 = [SELECT Id FROM Store_Configuration__c Limit 1];
		Contact con = [SELECT Id FROM Contact LIMIT 1];
		Store_SObject_Configuration__c soc1 = [SELECT Id, Store_Configuration__c FROM Store_SObject_Configuration__c LIMIT 1];
		pymt__Shopping_Cart__c cart = [SELECT Id, Cart_Key__c, Store_Configuration__c FROM pymt__Shopping_Cart__c LIMIT 1];
		cart.Cart_Key__c = cookieId + '.' + soc1.Store_Configuration__c;
		cart.Store_Configuration__c = soc1.Store_Configuration__c;
		update cart;

		pymt__Shopping_Cart_Item__c cartItem = [SELECT Id, Name, pymt__Shopping_Cart__c, pymt__Payment__c FROM pymt__Shopping_Cart_Item__c LIMIT 1];

		Map<String, String> responseLoggedInNoCookie;
		Map<String, String> responseLoggedInWithCookie;

		responseLoggedInNoCookie = RemoveEventController.getCartItemConfig(cartItem.Id, soc1.Id, '');
		responseLoggedInWithCookie = RemoveEventController.getCartItemConfig(cartItem.Id, soc1.Id, cookieId);
	}

	@IsTest
	static void testAddToCart1() {
		UserRole userrole = [Select Id, DeveloperName From UserRole Where DeveloperName = 'Site_Administrators' Limit 1];
		User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		adminUser.UserRoleId = userRole.Id;
		update adminUser;
		System.debug('---admin ' + adminUser);

		System.runAs(adminUser){
			String cookieId = '2398410734';
			Store_Configuration__c sc1 = [SELECT Id FROM Store_Configuration__c Limit 1];
			Store_SObject_Configuration__c soc1 = new Store_SObject_Configuration__c (
					Name = 'Events',
					SObject_API_Name__c = 'evt__Special_Event__c',
					Enforce_Cart_Item_Uniqueness__c = true,
					Include_Quantity_Field__c = true,
					Enable_One_Click_Checkout__c = true,
					Store_Configuration__c = sc1.Id
			);
			insert soc1;
			Contact con = [SELECT Id FROM Contact LIMIT 1];
			evt__Event_Fee__c fee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
			fee.evt__Amount__c = 100;
			update fee;

			insert new User(
					alias = 'test2',
					communityNickname = 'test123',
					contactId = con.Id,
					email = '<EMAIL>',
					emailencodingkey = 'UTF-8',
					firstName = 'test2',
					lastName = 'User',
					userName = '<EMAIL>',
					profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Applicant Community User' LIMIT 1].Id,
					timeZoneSidKey = 'America/Los_Angeles',
					LocaleSidKey = 'en_US',
					LanguageLocaleKey = 'en_US'
			);
			User commUser = [SELECT Id FROM User WHERE Username = '<EMAIL>'];
			evt__Special_Event__c event = [select Id from evt__Special_Event__c where Name = 'Special event'];
			Test.startTest();
			System.runAs(commUser) {
				RemoveEventController.addToCart(event.Id, soc1.Id, cookieId, fee);
			}
			test.stopTest();
		}
	}

	@IsTest
	static void testAddToCart2() {
		Store_Configuration__c sc1 = [SELECT Id FROM Store_Configuration__c Limit 1];
		Store_SObject_Configuration__c soc1 = new Store_SObject_Configuration__c (
				Name = 'Events',
				SObject_API_Name__c = 'evt__Special_Event__c',
				Enforce_Cart_Item_Uniqueness__c = true,
				Include_Quantity_Field__c = true,
				Enable_One_Click_Checkout__c = true,
				Store_Configuration__c = sc1.Id
		);
		insert soc1;
		evt__Event_Fee__c fee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		fee.evt__Amount__c = 100;
		update fee;

		evt__Special_Event__c event = [select Id from evt__Special_Event__c limit 1];
		Test.startTest();
		RemoveEventController.addToCart(event.Id, soc1.Id, '', null);
		test.stopTest();
	}

	@isTest
	static void testGetEventCartWrapper() {
		Test.startTest();
		pymt__Shopping_Cart_Item__c shoppingCartItem = new pymt__Shopping_Cart_Item__c();
		List<evt__Session__c> listOfEventSession = new List<evt__Session__c>();
		List<evt__Attendee__c> listOfGuests = new List<evt__Attendee__c>();
		List<String> listofEventFee = new List<String>();

		EventCartWrapperNew wrapper = new EventCartWrapperNew();
		wrapper.shoppingcartItem = shoppingCartItem;
		wrapper.listOfEventSession = listOfEventSession;
		wrapper.listOfGuests = listOfGuests;
		wrapper.listofEventFee = listofEventFee;

		wrapper.showDiscount = true;
		wrapper.showAddGuests = false;
		wrapper.showZeroDiscount = true;
		wrapper.totalAmount = 113;
		wrapper.originalSubTotal = 100;
		wrapper.subTotal = 100;
		wrapper.estimatedTax = 13;
		wrapper.discountAmount = 0;
		wrapper.taxPercentage = 13;
		wrapper.discountPercent = 0;
		wrapper.taxable = true;
		wrapper.discountCodeMessage = 'Test Discount Code Message';
		wrapper.couponCode = 'Test Discount Code';
		wrapper.showPicklist = true;
		wrapper.showDonationCheckBox = false;
		wrapper.showDonationSection = false;
		wrapper.donationAmount = 0;
		wrapper.event_image = 'Test Image';
	}

	@isTest
	static void testHasPermissionSet() {
		PermissionSet ps = new PermissionSet(
				Label = 'Test Permission Set',
				Name = 'Test_Permission_Set'
		);
		insert ps;
		// Create a test user
		List<User> usersToInsert1 = (List<User>) TestFactory.createSObjectList(new User(), 2);
		insert usersToInsert1;

		// Assign a permission set to the test user
		PermissionSet permSet = [SELECT Id FROM PermissionSet WHERE Name = 'Test_Permission_Set' LIMIT 1];
		PermissionSetAssignment permSetAssign = new PermissionSetAssignment(AssigneeId = usersToInsert1[0].Id, PermissionSetId = permSet.Id);
		insert permSetAssign;

		// Run the test as the test user
		System.runAs(usersToInsert1[0]) {
			Test.startTest();
			Boolean hasPerm = RemoveEventController.hasPermissionSet('Test_Permission_Set');
			Test.stopTest();
			System.assertEquals(hasPerm, true, 'User should have the permission set');
		}
	}

	@isTest
	static void testGetEventDiscounts() {
		// Create test data
		evt__Special_Event__c testEvent = [SELECT Id, Universal_Discount_Code__c FROM evt__Special_Event__c LIMIT 1];
		testEvent.Universal_Discount_Code__c = 'code1';
		update testEvent;

		Discount__c testDiscount1 = new Discount__c(Event__c = testEvent.Id, Code__c = 'code1', Combinable_Discount__c = false,  Current_Usage__c = 0, Start_Date__c = Date.today(), End_Date__c = Date.today().addDays(1));
		insert new List<Discount__c>{testDiscount1};

		List<RemoveEventController.DiscountWrapper> result = RemoveEventController.getEventDiscounts(testEvent.Id);

		System.assertEquals(2, result.size(), 'Expected 1 discounts to be returned');
	}


	@isTest
	static void testDiscountWrapperPositive() {
		RemoveEventController.DiscountWrapper discount = new RemoveEventController.DiscountWrapper(
				'001',
				'SUMMER2023',
				'Percentage',
				'20',
				true,
				true,
				Date.today(),
				Date.today().addDays(30)
		);
	}
}