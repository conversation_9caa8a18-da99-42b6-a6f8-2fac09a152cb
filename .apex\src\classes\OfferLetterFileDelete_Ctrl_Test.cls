/**
 * Created by <PERSON><PERSON> on 2022-05-09.
 */

@IsTest
private class OfferLetterFileDelete_Ctrl_Test {
    @TestSetup
    static void testSetup (){
        //Create an account, Term and Program Term availability test records
        Account a = (Account) TestFactory.createSObject(new Account());
        insert a;
        hed__Term__c t = new hed__Term__c(Name = 'Spring 2020', hed__Account__c=a.Id);
        insert t;
        Program_Term_Availability__c pta = new Program_Term_Availability__c(Program__c = a.Id, Term__c = t.Id);
        insert pta;

        //Inserting contact
        Contact c = (Contact) TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student',Email='<EMAIL>'));
        insert c;

        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new CongaMergeMock());
        //Create Application and child ACI records
        hed__Application__c app = new hed__Application__c( hed__Application_Status__c ='Offer', RecordTypeId = ApplicationService.ExecutiveProgramsRTId,
                hed__Applying_To__c = a.Id, hed__Term__c = t.Id, hed__Applicant__c = c.Id );
        insert app;
        Application_Checklist_Item__c aci = new Application_Checklist_Item__c(Name='Test ACI', Application__c=app.Id);
        insert aci;
        Test.stopTest();

        //Create a ContentDocument and link it to Application
        Map<String, Object> cvMap1 = new Map<String, Object>{
                'title'=>'Offer_Letter.pdf',
                'PathOnClient' => '/Offer_Letter.pdf',
                'origin'  => 'H',
                'ContentLocation' => 'S',
                'versiondata' => Blob.valueOf('Test Offer Letter Pdf.')
        };
        ContentVersion cv = (ContentVersion) TestFactory.createSObject(new ContentVersion(), cvMap1);
        insert cv;
        ContentVersion cv1 = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id];
        ContentDocumentLink cdl = new ContentDocumentLink(ContentDocumentId=cv1.ContentDocumentId, LinkedEntityId=app.Id);
        insert cdl;
    }

    @IsTest
    static void testFetchContent() {
        Test.startTest();
        hed__Application__c testApp = [SELECT Id FROM hed__Application__c LIMIT 1];
        List<ContentDocument> testLstCd = OfferLetterFileDelete_Ctrl.fetchContentDocument(testApp.Id);
        Test.stopTest();
        System.assert(true, testLstCd.size()==1);
    }

    @IsTest
    static void testDelOfferLetter() {
        hed__Application__c myApp= [SELECT Id FROM hed__Application__c WHERE hed__Application_Status__c = 'Offer'];
        ContentDocumentLink myCdl = [SELECT ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId = :myApp.Id];
        Test.startTest();
        String myOL = OfferLetterFileDelete_Ctrl.delOfferLetter(myCdl.ContentDocumentId);
        System.debug('+++'+myOL);
        Test.stopTest();
        System.assertEquals('SUCCESS', myOL);
    }
}