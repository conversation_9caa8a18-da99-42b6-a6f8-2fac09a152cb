/**
 * @description Determines the taxes to apply to shopping cart items when they are created or updated
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-25
 * @modified 2020-08-25
 */
global class TaxService_TDTM extends hed.TDTM_Runnable{

    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        List<pymt__Shopping_Cart_Item__c> itemsToCalculate = new List<pymt__Shopping_Cart_Item__c>(); 

        //BEFORE INSERT CONTEXT: 
        if(triggerAction ==  hed.TDTM_Runnable.Action.BeforeInsert){
            //Only apply taxes to new shopping cart items
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList)
                if( sci.pymt__Taxable__c && !sci.pymt__Payment_Completed__c )
                    itemsToCalculate.add(sci); 


        }else if(triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList);
            //Recalculate taxes on all taxable items where the address has changed or taxable status has changed
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList)
                if( sci.pymt__Taxable__c && !sci.pymt__Payment_Completed__c && (sci.Shipping_State_Province__c != oldMap.get(sci.Id).Shipping_State_Province__c || sci.Shipping_Country__c != oldMap.get(sci.Id).Shipping_Country__c || sci.Shipping_Postal_Code__c != oldMap.get(sci.Id).Shipping_Postal_Code__c || !oldMap.get(sci.Id).pymt__Taxable__c ) )
                    itemsToCalculate.add(sci); 
                else if ( !sci.pymt__Taxable__c && oldMap.get(sci.Id).pymt__Taxable__c && !sci.pymt__Payment_Completed__c )
                    sci.Taxing_Authority__c = null;


        }

        if ( itemsToCalculate.size() > 0 )
            applyTaxes( itemsToCalculate );

        return dmlWrapper; 
    }

    /**
     * @description calculate unit price with discount and increment discount usage
     * @param itemsToCalculate Shopping Cart Items to tax
     */
    private static void applyTaxes( List<pymt__Shopping_Cart_Item__c> itemsToCalculate ){
        
        List<String> taxKeys    = new List<String>{':'}; //Get all possible taxing authorities for the shopping cart items
        Map<String, Id> taByKey = new Map<String, Id>();
        taxKeys.add( 'CA' + ':' + 'ON');
        taxKeys.add( 'CA' + ':');
        //Go through and add the possible tax keys for the taxing authority
        for ( pymt__Shopping_Cart_Item__c sci : itemsToCalculate ) {
            taxKeys.add( sci.Shipping_Country__c + ':' + sci.Shipping_State_Province__c );
            taxKeys.add( sci.Shipping_Country__c + ':' );
        }

        //Get all taxing authorities
        List<Taxing_Authority__c> tas = [ SELECT Id, Tax_Key__c, Postal_Codes__c, Categories__c FROM Taxing_Authority__c WHERE Tax_Key__c IN :taxKeys AND Do_Not_Auto_Apply__c = false ];

        //Construct tax map
        for ( Taxing_Authority__c ta : tas ) {

            //Need to get list of zips/categories that this applies to
            List<String> zips       = ta.Postal_Codes__c == null ? new List<String>{''} : ta.Postal_Codes__c.split( ';' );
            List<String> categories = ta.Categories__c == null ? new List<String>{''} : ta.Categories__c.split( ';' );

            //Loop through all zips and categories to determine tax rates
            for ( String zip : zips )
                for ( String cat : categories )
                    taByKey.put( ta.Tax_Key__c + ':' + zip + ':' + cat, ta.Id );
            
        }
        System.debug('tabykey' + taByKey);
        for ( pymt__Shopping_Cart_Item__c sci : itemsToCalculate ) 
            sci.Taxing_Authority__c = getCode( sci, taByKey );

    }

    /**
     * @description calculate unit price with discount and increment discount usage
     * @param sci Shopping Cart item to tax
     * @param taByKey Map of taxing authorities organized by associated key
     * @return Id of the taxing authority if found, null otherwise
     */
    private static Id getCode ( pymt__Shopping_Cart_Item__c sci, Map<String, Id> taByKey ) {

        for ( String country : new List<String>{ sci.Shipping_Country__c, '' } ) {
            for ( String state : new List<String>{ sci.Shipping_State_Province__c, '' } ) { 
                for ( String zip : new List<String>{ sci.Shipping_Postal_Code__c, '' } ) {
                    for ( String cat : new List<String>{ '' } ) {

                        String key = country + ':' + state + ':' + zip + ':' + cat;
                        if (country != 'CA'){
                            return taByKey.get('CA' + ':' + 'ON' + ':' + '' +':' + ''); 
                        }
                        if ( taByKey.containsKey( key ) ){
                            return taByKey.get( key );
                        }
                    }
                }
            }
        }
        // NullPointerException e = new NullPointerException();
        // e.setMessage('Taxing Authority not found!');
        // throw e;           
        return null;

    }

}