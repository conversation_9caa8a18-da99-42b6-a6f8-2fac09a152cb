/**
* @description Class for Sending Email 
* <AUTHOR>
* @version 1.0
* @created 3-JUNE-2020
* @modified 3-JUNE-2020
* UTR-1198
*/
global class EmailService {
    
    global class SendEmailRequest {
        @InvocableVariable(required=true)
        global String emailTemplateName;
        
        @InvocableVariable(required=true)
        global String emailId;  //ContactId, UserId or External Email        
        
        
        @InvocableVariable
        global String ccAddresses;      //comma separated List of emailIds -- optional
        
        @InvocableVariable
        global String subject;
        //Added by Ved
        @InvocableVariable
        global String orgWideEmail;              
        
        
        @InvocableVariable
        global DateTime eventStartDate;
        
        @InvocableVariable
        global DateTime eventEndDate;
        
        @InvocableVariable
        global String eventSubject;
        
        @InvocableVariable
        global String eventTitle;
        
        @InvocableVariable
        global String eventLocation;
        
        @InvocableVariable
        global ID targetRecordId; // any SObject Id
        
        @InvocableVariable
        global String documentType; //(if attaching a document)           
        
        
        public List<Messaging.EmailFileAttachment> AttsList = new List<Messaging.EmailFileAttachment>();
        
    }
    
    
    @InvocableMethod(label='Send Emails')
    global static void sendEmails(List<SendEmailRequest> requests) {
        
        
        // Map target record to document type
        Map<Id, String> objRecordDocTypeMap = new Map<Id, String>();
        
        Set<Id> objIdSet = new Set<Id>();
        for(SendEmailRequest request : requests){
            if(request.documentType != null && request.targetRecordId != null){
                objRecordDocTypeMap.put(request.targetRecordId, request.documentType);
                
                if(!objIdSet.contains(request.targetRecordId))
                    objIdSet.add(request.targetRecordId); 
            }
        }
        
        if(objIdSet.size()>0){
            //Query for all ContentDocumentLinks related to target record and have the latest version
            List<ContentDocumentLink> CDLList = [
                SELECT  ID,CONTENTDOCUMENTID, LINKEDENTITYID, 
                CONTENTDOCUMENT.LatestPublishedVersion.document_type__c, CONTENTDOCUMENT.LatestPublishedVersion.FileType, 
                CONTENTDOCUMENT.LatestPublishedVersion.versionData, CONTENTDOCUMENT.LatestPublishedVersion.Title
                FROM CONTENTDOCUMENTLINK 
                WHERE CONTENTDOCUMENT.LatestPublishedVersion.isLatest=true and linkedentityid IN :objIdSet
                order by contentdocument.lastmodifieddate desc
            ];
            
            Map<Id, ContentDocumentLink> objRecordCDLMap = new Map<Id, ContentDocumentLink>();
            //Filter ContentDocumentLinks for the matching request document type
            for(ContentDocumentLink cdl: CDLList){
                if(cdl.CONTENTDOCUMENT.LatestPublishedVersion.document_type__c == objRecordDocTypeMap.get(cdl.LinkedEntityId)){
                    if (!objRecordCDLMap.keyset().contains(cdl.LinkedEntityId)){
                        objRecordCDLMap.put(cdl.LinkedEntityId, cdl);
                    }
                }
            }
            for(SendEmailRequest request : requests){
                // Create the email attachment
                Messaging.EmailFileAttachment efa = new Messaging.EmailFileAttachment();
                efa.setFileName((objRecordCDLMap.get(request.targetRecordId)).CONTENTDOCUMENT.LatestPublishedVersion.Title+'.'+(objRecordCDLMap.get(request.targetRecordId)).CONTENTDOCUMENT.LatestPublishedVersion.FileType);
                efa.setBody((objRecordCDLMap.get(request.targetRecordId)).CONTENTDOCUMENT.LatestPublishedVersion.versionData);
                request.AttsList.add(efa);
            }
        }
        
        
        List<Messaging.Singleemailmessage> emailList = new List<Messaging.Singleemailmessage>();
        for (SendEmailRequest request : requests) {
            Messaging.Singleemailmessage em = createEmail(request);
            if(em != null)
                emailList.add(em);
        }
        
        system.debug('Email List Size: '+ emailList.size());
        if(emailList.size() >0)
            try{
                
                Messaging.sendEmail(emailList);
                
            } catch(EmailException e){
                HandleCustomException hc= new HandleCustomException();
                hc.log(e);
                
            } catch (Exception e) {
                System.debug('Exception caught '+e.getMessage());
            }
        
        
    }
    
    
    global static Messaging.Singleemailmessage createEmail(SendEmailRequest request)
    {
        
        EmailTemplate emailTemplate = [select Id, Name, Subject, HtmlValue, Body from EmailTemplate where developerName =: request.emailTemplateName];
        
        String emailStr = (request.emailId).deleteWhitespace();
        List<String> toAddrList = new List<String>();
        toAddrList = emailStr.split(',');
        
        List<String> ccAddrList = new List<String>();
        if(request.ccAddresses != null){
            emailStr = (request.ccAddresses).deleteWhitespace();
            ccAddrList = emailStr.split(',');
        }
        
        System.debug('toAddrList = '+ toAddrList);
        System.debug('ccAddrList = '+ ccAddrList);
        
        //build the email message
        Messaging.Singleemailmessage email = new Messaging.Singleemailmessage();
        
        //if(request.emailId instanceOf Id) { //if it is ContactId or User Id
        //Updated on 10/19/2021 if it is not EmailId and it is  ContactId or User Id
        if(!Pattern.matches('([a-zA-Z0-9_\\-\\.]+)@((\\[a-z]{1,3}\\.[a-z]{1,3}\\.[a-z]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})',request.emailId) && request.emailId instanceof Id){  //if it is ContactId or User Id
            system.debug('If Con/User request.emailId : '+request.emailId);
            email.setTemplateId(emailTemplate.Id);            
            email.setTargetObjectId(request.emailId);            
            if(request.targetRecordId != null) 
                email.setWhatId(request.targetRecordId);
            if(ccAddrList != null)
                email.setCcAddresses(ccAddrList);
            
        } else {                            //if it is external email address
            system.debug('If Ext Email request.emailId : '+request.emailId);
            email = Messaging.renderStoredEmailTemplate(emailTemplate.id, null, request.targetRecordId);
            email.setToAddresses(toAddrList);
            email.setTreatTargetObjectAsRecipient(false);           
            if(ccAddrList != null)
                email.setCcAddresses(ccAddrList);
        }
        
        
        email.setOrgWideEmailAddressId(request.orgWideEmail);
        email.setSaveAsActivity(false);
        SYSTEM.DEBUG(request);
        
        if(request.eventStartDate != null && request.eventEndDate != null && (request.eventSubject != '' || request.eventTitle != '')){
            Messaging.EmailFileAttachment eventInvite = new Messaging.EmailFileAttachment();   
            eventInvite.filename = 'EventInvite.ics'; 
            eventInvite.ContentType = 'text/calendar; charset=utf-8; method=REQUEST';     
            eventInvite.inline = true;     
            eventInvite.body = CalendarService.createInvite(request.eventStartDate, request.eventEndDate, request.eventTitle, request.eventSubject, request.eventLocation);   
            (request.AttsList).add(eventInvite);
        }
        
        if((request.AttsList).size() >0)
            email.setFileAttachments(request.AttsList);
        
        if(!(request.emailId instanceOf Id) && request.targetRecordId == null){ // external email but TargetObject id is null... then can't create email
            email = new Messaging.Singleemailmessage();
            system.debug('TARGETRECORDID IS MANDATORY WITH EXTERNAL EMAIL ID');
        }
        SYSTEM.DEBUG(email);
        return email;
        
        
    }
    
}