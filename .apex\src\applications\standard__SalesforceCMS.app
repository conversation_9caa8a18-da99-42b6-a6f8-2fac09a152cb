<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Salesforce CMS</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Active Student Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <tabs>standard-CmsAuthorHome</tabs>
    <tabs>standard-CmsChannel</tabs>
    <tabs>standard-CmsWorkspaces</tabs>
    <tabs>standard-CmsExperiences</tabs>
    <tabs>sfal__SuccessPlanTemplate__c</tabs>
    <tabs>evt__Session_Assignment__c</tabs>
    <tabs>Discount__c</tabs>
    <tabs>Cohort__c</tabs>
    <tabs>Calendar_View__c</tabs>
    <uiType>Lightning</uiType>
    <workspaceConfig>
        <mappings>
            <tab>Calendar_View__c</tab>
        </mappings>
        <mappings>
            <tab>Cohort__c</tab>
        </mappings>
        <mappings>
            <tab>Discount__c</tab>
        </mappings>
        <mappings>
            <tab>evt__Session_Assignment__c</tab>
        </mappings>
        <mappings>
            <tab>sfal__SuccessPlanTemplate__c</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsAuthorHome</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsChannel</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsExperiences</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsWorkspaces</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
