/**
* @description    Test class for FileShareService_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-06-11
* @modified 2020-06-11
*/
@isTest
public class FileShareService_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for EP_DepositHandler_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('FileShareService_TDTM', 'ContentVersion', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 
		
        Account acct = new Account(Name='TEST_ACCT');
        insert acct;
        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        insert c; 
        
        ContentVersion cv = new ContentVersion();
        cv.title='Test title';
        cv.VersionData=blob.valueof('New Bitmap Image.bmp');
        cv.PathOnClient = 'Penguins.jpg';
        cv.Document_Type__c = 'Receipt';
			
        insert cv;
		List<ContentDocument> documents = [SELECT Id, Title, LatestPublishedVersionId FROM ContentDocument];
        
        //create ContentDocumentLink  record 
        ContentDocumentLink cdl = New ContentDocumentLink();
        cdl.LinkedEntityId = acct.id;
        cdl.ContentDocumentId = documents[0].Id;
        cdl.shareType = 'V';
        insert cdl;
    }

    /**
     * @description Update Shopping Cart Items
     * 
     *  
     */
    @isTest 
    public static void updateSCI(){
        List<ContentVersion> documents = [SELECT Id,ContentDocumentId FROM ContentVersion];
        Id cdID = documents[0].ContentDocumentId;
        List<ContentDocumentLink> documentLnks = [SELECT Id,Visibility FROM ContentDocumentLink where ContentDocumentId =:cdID];
        System.assertEquals('AllUsers', documentLnks[0].Visibility);
    }
}