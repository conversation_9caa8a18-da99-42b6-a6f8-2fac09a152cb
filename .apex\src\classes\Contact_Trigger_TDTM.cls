/**
* TDTM class for Contact that does contact name field matching
* and staging table field swapping as needed. 
* See <PERSON><PERSON><PERSON> ticket 'UTR-319'
* 
*
*Related Helper Class: ContactNameMatchingAndStaging
*
* <AUTHOR> 
* @since   2020-03-05 
*/

global class Contact_Trigger_TDTM extends hed.TDTM_Runnable{
	// the Trigger Handler’s Run method we must provide
   global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist,
        hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {

        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        
        //msp representing Staging type field as key and values to be removed from type as value
        Map<String, List<String>> stagingTableTypes = new  Map<String, List<String>>();
            stagingTableTypes.put('Alumni', new List<String>{'Student'} );  
            stagingTableTypes.put('Applicant', new List<String>{'Prospect'} );
            stagingTableTypes.put('Business Relationship', new List<String>{''} );
            stagingTableTypes.put('Enrolled', new List<String>{'Student'} );
            stagingTableTypes.put('Faculty', new List<String>{''} );
            stagingTableTypes.put('Prospect', new List<String>{''} );
            stagingTableTypes.put('Student', new List<String>{'Applicant', 'Prospect'} );
         
            system.debug('TEST FOR TEST CLASS 1');
		if (triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate) {
           system.debug('TEST FOR TEST CLASS 2');
            List<Contact> beforeList = oldlist;		//Trigger.old
            List<Contact> afterList = newlist;		//Trigger.new
         	ContactNameMatchingAndStaging cnmas = new ContactNameMatchingAndStaging();		//Helper Class
            
            //iterate over records
            for(Integer i = 0 ; i < newlist.size() ; i ++){
                
                cnmas.contactNameMatching(beforeList, afterList, i);	//helper class method
                
                //for each key in keyset execute method
                for(String stageValue : stagingTableTypes.keySet()){       
                    cnmas.contactStagingTypePopulator(beforeList, afterList, stagingTableTypes, stageValue, i);
                }                             
            }
       }
            
       return dmlWrapper; 
   }
}