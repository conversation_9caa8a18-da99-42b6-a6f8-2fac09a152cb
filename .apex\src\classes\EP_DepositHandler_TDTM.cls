/**
 * @description When an 'EP Deposit' is completed, migrates all remaining shopping cart items into a new shopping cart for the user
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-26
 * @modified 2020-08-26 <EMAIL>
 */
global class EP_DepositHandler_TDTM extends hed.TDTM_Runnable{

    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 

        List<pymt__Shopping_Cart_Item__c> scis          = new List<pymt__Shopping_Cart_Item__c>();
        Set<Id> conIds                                  = new Set<Id>();
        Set<pymt__Shopping_Cart__c> cartsToUpsert       = new Set<pymt__Shopping_Cart__c>();
        Set<pymt__Shopping_Cart_Item__c> scisToUpsert   = new Set<pymt__Shopping_Cart_Item__c>();

        Id scId;
        Id invoiceId;

        //Check for new program deposits been paid
        if ( triggerAction == hed.TDTM_Runnable.Action.BeforeInsert )
            for ( pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList ) 
                if ( sci.pymt__Payment_Completed__c && sci.Type__c == 'EP Program Deposit' && sci.pymt__Payment__c != null && sci.pymt__Quantity__c != null && sci.pymt__Unit_Price__c != null )
                    scis.add( sci );

        //Check for program deposits which have just been paid
        if ( triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate ) { 
        
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap     = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList);
        
            for ( pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList ) 
                if ( sci.pymt__Payment_Completed__c && !oldMap.get(sci.Id).pymt__Payment_Completed__c && sci.Type__c == 'EP Program Deposit' && sci.pymt__Payment__c != null && sci.pymt__Quantity__c != null && sci.pymt__Unit_Price__c != null )
                    scis.add( sci );
        }

        for ( pymt__Shopping_Cart_Item__c sci : scis ) {
                    
            //Get the shopping cart Id and invoice Id when the first item is found
            if ( scId == null ) {
                try { 
                        List<Invoice__c> inv = [SELECT Id
                                                FROM Invoice__c
                                                WHERE Contact__c = :sci.pymt__Contact__c AND Invoice_Status__c = 'Open-Personal' LIMIT 1];
                        scId = [ SELECT Id FROM Store_Configuration__c WHERE Name = 'EP Balance' ].Id; 
                        if(inv.size() > 0) {
                            invoiceId = inv[0].Id;
                        } 
                } catch (Exception e) { 
                    for ( pymt__Shopping_Cart_Item__c s : (List<pymt__Shopping_Cart_Item__c>)newList ){ s.addError( 'Could not find Store Configuration "EP Balance". Store configuration must exist to complete EP deposit' ); } return dmlWrapper; 
                }
            }
            
            conIds.add( sci.pymt__Contact__c ); 

            cartsToUpsert.add(
                new pymt__Shopping_Cart__c (
                    Cart_Key__c             = sci.pymt__Contact__c + '.' + scId,
                    pymt__contact__c        = sci.pymt__Contact__c,
                    Store_Configuration__c  = scId
                )
            );
            // scisToUpsert.add(
            //     new pymt__Shopping_Cart_Item__c (
            //         Deposit__c                          = sci.Id,
            //         Name                                = 'Program Deposit',
            //         pymt__Unit_Price__c                 = sci.pymt__Unit_Price__c  * -1,
            //         pymt__Quantity__c                   = sci.pymt__Quantity__c,
            //         pymt__Shopping_Cart__r              = new pymt__Shopping_Cart__c ( Cart_Key__c = sci.pymt__Contact__c + '.' + scId ), 
            //         pymt__Taxable__c                    = false,
            //         Type__c                             = 'Deposit Credit',
            //         pymt__Contact__c                    = sci.pymt__Contact__c,
            //         Invoice__c                          = invoiceId
            //     )
            // );
        }


        if ( conIds.size() > 0 ) {

            List<pymt__Shopping_Cart_Item__c> allScis =   [ SELECT Id, pymt__Contact__c
                                                            FROM pymt__Shopping_Cart_Item__c
                                                            WHERE pymt__Shopping_Cart__r.Store_Configuration__r.Name = 'EP Courses' AND pymt__Shopping_Cart__r.pymt__Contact__c IN :conIds AND pymt__Payment_Completed__c = false AND Id NOT IN :newList ]; 

            for ( pymt__Shopping_Cart_Item__c sci : allScis ) 
                scisToUpsert.add(
                    new pymt__Shopping_Cart_Item__c (
                        Id                                  = sci.Id,
                        Deposit_Paid__c                     = true,
                        pymt__Shopping_Cart__r              = new pymt__Shopping_Cart__c ( Cart_Key__c = sci.pymt__Contact__c + '.' + scId ),
                        void_payment__c                     = false
                    )
                );    


            database.upsert( new List<pymt__Shopping_Cart__c>(cartsToUpsert), pymt__Shopping_Cart__c.FIELDS.Cart_Key__c );
            upsert new List<pymt__Shopping_Cart_Item__c>(scisToUpsert);

        }

        return dmlWrapper; 

    }
}