global class SubscriptionCreationBatch implements Database.Batchable<sObject>, Database.Stateful {
	global Map<String, String> conTosub = new Map<String, String>();

	global SubscriptionCreationBatch(Map<String, String> mapSubscriptions) {
		this.conTosub = mapSubscriptions;
		System.debug('03-0-1-0== '+this.conTosub);
		System.debug('03-0-1-1== '+this.conTosub.keySet());
	}

	global Database.QueryLocator start(Database.BatchableContext BC) {
		return Database.getQueryLocator(
				[SELECT Id, Subscription__c, Subscription__r.Name, Subscription__r.Active__c, Subscription__r.Description__c, Contact__c, Subscribed_Date__c, Subscription_Status__c, Subscription_Name__c, Contact_Email__c FROM Subscription_Membership__c Limit 1]);
	}

	global void execute(Database.BatchableContext BC, List<Subscription_Membership__c> scope) {
		List<Subscription_Membership__c> lstSubscription = new List<Subscription_Membership__c>();

		System.debug('03-0-1-2== '+scope);

		Map<String, Contact> emailToContactMap = new Map<String, Contact>();
		Integer i = 0;
		while (i < 1000000) {
			i++;
		}
		for (Contact con : [SELECT Id, Email FROM Contact WHERE Email IN :this.conTosub.keySet() LIMIT 199]) {
			emailToContactMap.put(con.Email, con);
		}
		System.debug('03-0-2== '+emailToContactMap);

		for (String subKey : this.conTosub.keySet()) {
			String subValue = this.conTosub.get(subKey);
			List<String> splitValues = subValue.split(';');
			System.debug('03-0-2-1== '+subKey);
			System.debug('03-0-2-2== '+splitValues);
			if (splitValues.size() > 1) {
				for (String val : splitValues) {
					List<Subscription_Membership__c> sm = [SELECT Id FROM Subscription_Membership__c WHERE Contact_Email__c = :subKey AND Subscription__r.Name = :val];
					System.debug('03-0-2-31== '+sm);
					if (sm.size() == 0) {
						Subscription__c sub = [SELECT Id, Name FROM Subscription__c WHERE Name = :val];
						System.debug('03-0-2-34== '+sub);
						if (sub != null && emailToContactMap.size() > 0) {
							Subscription_Membership__c smNew = new Subscription_Membership__c();
							smNew.Subscription__c = sub.Id;
							smNew.Contact__c = emailToContactMap.get(subKey).Id;
							smNew.Subscribed_Date__c = Date.today();
							smNew.Subscription_Status__c = 'Subscribed';
							lstSubscription.add(smNew);
						}
					}
				}
			} else if (splitValues.size() == 1) {
				List<Subscription_Membership__c> sm = [SELECT Id FROM Subscription_Membership__c WHERE Contact_Email__c = :subKey AND Subscription__r.Name = :subValue];
				System.debug('03-0-2-32== '+sm);
				System.debug('03-0-2-33== '+splitValues);
				if (sm.size() == 0) {
					Subscription__c sub = [SELECT Id, Name FROM Subscription__c WHERE Name = :splitValues[0]];
					System.debug('03-0-2-4== '+sub);
					if (sub != null && emailToContactMap.size() > 0) {
						Subscription_Membership__c smNew = new Subscription_Membership__c();
						smNew.Subscription__c = sub.Id;
						smNew.Contact__c = emailToContactMap.get(subKey).Id;
						smNew.Subscribed_Date__c = Date.today();
						smNew.Subscription_Status__c = 'Subscribed';
						lstSubscription.add(smNew);
					}
				}
			}
		}

		System.debug('03-0-3== '+lstSubscription);
		if (lstSubscription.size() > 0) {
			Database.SaveResult[] insertResults = Database.insert(lstSubscription, false);
			for (Database.SaveResult sr : insertResults) {
				if (sr.isSuccess()) {
					System.debug('03-0-4 Subscription member created successfully');
				} else {
					System.debug('03-0-5 Subscription member creation error: ' + sr.getErrors()[0].getMessage());
				}
			}
		}
	}

	global void finish(Database.BatchableContext BC) {
		System.debug('03-0-6 Subscription member creation Batch job completed');
	}
}