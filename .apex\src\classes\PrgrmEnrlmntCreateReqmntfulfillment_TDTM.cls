/** 
* @description    At the time of creation of program, create ‘Planned’ requirement fulfillment for every consequential requirement.
* <AUTHOR> 
* @version        1.0 
*/
global class PrgrmEnrlmntCreateReqmntfulfillment_TDTM extends hed.TDTM_Runnable{
	/**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Interaction records from trigger new 
     * @param oldList the list of Interaction records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc...)
     * @param objResult the describe for Interactions 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
    	hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        Map<Id,hed__Program_Enrollment__c> oldMap = new Map<Id,hed__Program_Enrollment__c>();
        
        //Map<Id, hed__Program_Plan__c> IdToProgramPlanMap = new Map<Id, hed__Program_Plan__c>();
		List<Plan_Requirement_Fulfillment__c> reqmntFullflmntLstToInsert = new List<Plan_Requirement_Fulfillment__c>();
        List<hed__Program_Enrollment__c> pgrmEnrollmentLst = (List<hed__Program_Enrollment__c>) newList;
		//AFTER INSERT CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
        	reqmntFullflmntLstToInsert = RollupService.createPlanRequirementFullFillment(pgrmEnrollmentLst);
        }
        //AFTER UPDATE CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            pgrmEnrollmentLst = new List<hed__Program_Enrollment__c>();
            for(hed__Program_Enrollment__c pgrenrlmnt : (List<hed__Program_Enrollment__c>) oldlist){
                oldMap.put(pgrenrlmnt.Id,pgrenrlmnt);
            }
            for(hed__Program_Enrollment__c pgrenrlmntNew : (List<hed__Program_Enrollment__c>) newList){
                System.Debug('<<<<<NEW>>>>>'+pgrenrlmntNew.hed__Program_Plan__c);
                System.Debug('<<<<<OLD>>>>>'+oldMap.get(pgrenrlmntNew.Id).hed__Program_Plan__c);
                if(pgrenrlmntNew.hed__Program_Plan__c != oldMap.get(pgrenrlmntNew.Id).hed__Program_Plan__c){
                    pgrmEnrollmentLst.add(pgrenrlmntNew);
                }
            }
            reqmntFullflmntLstToInsert = RollupService.createPlanRequirementFullFillment(pgrmEnrollmentLst);
        }
        System.Debug('<<<<<reqmntFullflmntLstToInsert>>>>>'+reqmntFullflmntLstToInsert);
		dmlWrapper.objectsToInsert.addAll(reqmntFullflmntLstToInsert);        
        return dmlWrapper;
    }
}