/**
 * Created by <PERSON><PERSON> on 2022-06-16.
 */

@IsTest
private class ReRunTerritory_Test {

    @testSetup
    static void testSetup(){
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig();
        tokens.add(new hed.TDTM_Global_API.TdtmToken('ProgramTermAvailibility_TDTM', 'Program_Term_Availability__c', 'AfterUpdate', 1.00));
        hed.TDTM_Global_API.setTdtmConfig(tokens);

        Id fulltimeId = ApplicationService.FTandSpecializedRTId;
        Account a = new Account(Name='TestAccount');
        insert a;
        hed__Term__c trm = new hed__Term__c(hed__Account__c=a.Id,name='Spring 2020');
        insert trm;
        Program_Term_Availability__c pta = new Program_Term_Availability__c(
                Active__c = True,
                Program__c = a.Id,
                Term__c = trm.Id
        );
        insert pta;
        hed__Application__c app = new hed__Application__c(
                Program_Term_Availability__c = pta.Id,
                hed__Application_Status__c = 'In Progress',
                RecordTypeId = fulltimeId,
                hed__Applying_To__c = a.Id,
                hed__Term__c = trm.Id
        );
        insert app;
    }

    @IsTest
    static void testUpdateRunterritory() {
        Account acc = [SELECT Id, Run_Territory_Assignment__c FROM Account LIMIT 1];
        Test.startTest();
            ReRunTerritory_Ctrl.reRunTerritoryAssignment(acc.Id);
        Test.stopTest();
        Account acc1 = [SELECT Id, Run_Territory_Assignment__c FROM Account WHERE Id = :acc.Id];
        System.assertEquals(true, acc1.Run_Territory_Assignment__c);
    }
}