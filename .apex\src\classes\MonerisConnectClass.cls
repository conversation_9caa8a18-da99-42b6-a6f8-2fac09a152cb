global without sharing class MonerisConnectClass {
    ///BUTTON window.showModalDialog("/apex/MonerisRefundPage?id={!Opportunity.Id}","width=400; height=200;");
    //Void_Transaction
    //public String store_id = 'store5';
   //public String api_token = 'yesguy';
    //public String ecr_no = '66013455';
    //public string cardNo{get; set;}
    //public string expMonth{get; set;}
    //public string expYear{get; set;}
   @AuraEnabled public CardWrapperClass cardDetails{get;set;}
    //public string cvd{get; set;}
    //String ecr_no = 66011091;
    public	String processing_country_code = 'CA';
    //private final static String TEST_SERVER = 'https://esqa.moneris.com/mpg/index.php';
    public static string TEST_SERVER1 = 'https://esqa.moneris.com/gateway2/servlet/MpgRequest';
    public static Gateway_Setting__c gateSetting = new Gateway_Setting__c();
    public String currentRecordId {get;set;}
   /* public MonerisConnectClass(){
        currentRecordId = apexpages.currentpage().getparameters().get('pid') ;
        system.debug('currentRecordId== '+currentRecordId);
        cardDetails = new CardWrapperClass();
        pymt__PaymentX__c pym = [SELECT pymt__Transaction_Id__c, pymt__Amount__c From pymt__PaymentX__c WHERE Id =: currentRecordId];
        Gateway_Setting__c gs = [Select id,API_token__c,Endpoint__c,Store_Id__c From Gateway_Setting__c LIMIT 1];
        cardDetails.Amount = pym.pymt__Amount__c;
        //cardDetails.tnx = gs.pymt__Transaction_Id__c;
        //cardDetails.orderid = gs.Order_Id__c;
    }*/
   /* public pageReference onSubmitPayment(){
        system.debug('cardNo=> '+cardDetails);
        system.debug('year MOnth= '+apexpages.currentpage().getparameters().get('expYear')+ apexpages.currentpage().getparameters().get('expMonth'));
        
        //system.debug('month-->'+ System.currentPageReference().getParameters().get('expMonth'));
        //system.debug('year-->'+ apexpages.currentpage().getparameters().get('expYear'));
        cardDetails.expireDate= apexpages.currentpage().getparameters().get('expYear')+ apexpages.currentpage().getparameters().get('expMonth');
        system.debug('Expiredate ='+cardDetails.expireDate);
        monerisPurchase(cardDetails);
        return Null; //currentRecordId = apexpages.currentpage().getparameters().get('expYear') ;
    }
    public pageReference onSubmitRefund(){
        system.debug('cardNo=> '+cardDetails);
        monerisCardRefund(cardDetails);
        //currentRecordId = apexpages.currentpage().getparameters().get('id') ;
        return Null;
    }*/
    @AuraEnabled
    public static string monerisPurchase(CardWrapperClass card){
        gateSetting = [SELECT id,API_token__c,Store_Id__c,Endpoint__c FROM Gateway_Setting__c LIMIT 1];
        HttpRequest  mpgReq = new HttpRequest();
        system.debug('gateSetting Pur==>'+gateSetting);
        HttpRequest req = new HttpRequest();
        //req.setEndpoint(TEST_SERVER1);
        if(gateSetting.Endpoint__c != Null){
            req.setEndpoint(gateSetting.Endpoint__c);
        }
        else{
            req.setEndpoint(TEST_SERVER1);
        }
        req.setMethod('POST');
        //  string username = 'demouser';
        //   string password = 'password';
        //   string store_id = 'store3';
        //   Blob headerValue = Blob.valueOf(username + ':' + password+ ':' +store_id);
        String reqBody = MonerisRequestXML.PurchaseStructure(card);
        
        Blob body = Blob.valueOf(reqBody);
        req.setHeader('content-Type', 'application/json');
        req.setBody(reqBody);
        System.debug(req.getBody());
        Http http = new Http();
        HttpResponse res = http.send(req);
         System.debug(res.getStatus()); 
        System.debug(res.getBody()); 
        return res.getBody();
    }
    public static string monerisCardRefund(CardWrapperClass card){
        gateSetting = [SELECT id,API_token__c,Store_Id__c,Endpoint__c FROM Gateway_Setting__c LIMIT 1];
        HttpRequest  mpgReq = new HttpRequest();
        system.debug('gateSetting refund==>'+gateSetting);
        HttpRequest req = new HttpRequest();
        req.setEndpoint(TEST_SERVER1);
        req.setMethod('POST');
        //  string username = 'demouser';
        // string password = 'password';
        // string store_id = 'store3';
        // Blob headerValue = Blob.valueOf(username + ':' + password+ ':' +store_id);
        String reqBody = MonerisRequestXML.RefundStructure(card);
        
        Blob body = Blob.valueOf(reqBody);
        req.setHeader('content-Type', 'application/json');
        req.setBody(reqBody);
        System.debug(req.getBody());
        Http http = new Http();
        HttpResponse res = http.send(req);
        System.debug(res.getBody()); 
        return res.getBody();
    }
    webservice static string monerisPurchaseVoid(string order, string tnx){
        system.debug(order+ ' -- '+ tnx);
        gateSetting = [SELECT id,API_token__c,Store_Id__c,Endpoint__c FROM Gateway_Setting__c LIMIT 1];
        HttpRequest  mpgReq = new HttpRequest();
        system.debug('gateSetting Void==>'+gateSetting);
        HttpRequest req = new HttpRequest();
        req.setEndpoint(TEST_SERVER1);
        req.setMethod('POST');
        String reqBody = MonerisRequestXML.VoidStructure(order, tnx);
        req.setHeader('content-Type', 'application/json');
        req.setBody(reqBody);
        System.debug(req.getBody());
        Http http = new Http();
        HttpResponse res = http.send(req);
        System.debug(res.getStatus()); 
        System.debug(res.getBody()); 
        
        return res.getBody();
       
    }
}