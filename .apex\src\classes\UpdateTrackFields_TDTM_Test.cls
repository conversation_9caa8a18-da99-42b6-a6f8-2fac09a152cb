/**
* @description    Test class for UpdateTrackFields_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-10-23
*/
@isTest
public class UpdateTrackFields_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for TestScoreCalculator_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('UpdateTrackFields_TDTM', 'pymt__Shopping_Cart_Item__c', 'BeforeInsert;BeforeUpdate', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

         //Test Student record: 
         Contact c =  (Contact) TestFactory.createSObject(new Contact()); 
         //Inserting contact will create admin account 
         insert c; 
    
        //Test discount records 
        List<Discount__c> discountsToInsert = new List<Discount__c>(); 
        discountsToInsert.add(new Discount__c(Name = '10% off discount', Percent_Discount__c = 10, Max_Usage__c = 10, Code__c= 'ROTMAN10')); 
        discountsToInsert.add(new Discount__c(Name = '$100 off discount', Dollar_Discount__c = 100, Max_Usage__c = 20, Code__c='ROTMAN100')); 
        insert discountsToInsert; 

        //Test Payment record: 
        List<pymt__PaymentX__c> payments = new List<pymt__PaymentX__c>();
        payments.add(new pymt__PaymentX__c(Name='Test Payment1', pymt__contact__c = c.Id, Type__c = 'Event Registration', pymt__Status__c = 'Online Checkout')); 
        payments.add(new pymt__PaymentX__c(Name='Test Payment2', pymt__contact__c = c.Id, Type__c = 'Event Registration', pymt__Status__c = 'Online Checkout')); 
        insert payments; 

        List<pymt__Shopping_Cart_Item__c> items = new List<pymt__Shopping_Cart_Item__c>(); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #1', Discount__c = discountsToInsert[0].Id, pymt__Unit_Price__c = 9000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payments[0].Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #2', Discount__c= discountsToInsert[1].Id, pymt__Unit_Price__c = 12000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payments[0].Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #3', Discount__c = discountsToInsert[0].Id, pymt__Unit_Price__c = 10000, pymt__Quantity__c = 1, type__c = 'Event Registration'));
        insert items; 
    }

    /**
     * @description test insertion of shopping cart items with no related payment record
     * Assert current usage was not updated
     */
    @isTest
    public static void testInsertSCI(){
        Discount__c discount = [SELECT ID, Name, Percent_Discount__c, Current_Usage__c FROM Discount__c WHERE Percent_Discount__c != null LIMIT 1]; 
        List<pymt__PaymentX__c> payments = [SELECT ID, Name FROM pymt__PaymentX__c];

        List<pymt__Shopping_Cart_Item__c> items = new List<pymt__Shopping_Cart_Item__c>(); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #1', Discount__c = discount.Id, pymt__Unit_Price__c= 7000, pymt__Quantity__c = 1, type__c = 'Event Registration')); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #2', Discount__c= discount.Id, pymt__Unit_Price__c= 8000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payments[0].Id)); 

        Test.startTest(); 
            insert items; 
        Test.stopTest(); 

        System.Assert(items[0].Discount_Amount_Paid__c == items[0].Discount_Amount__c);
    }

     /**
     * @description test removing payment on existing shopping cart item 
     */
    @isTest
    public static void testUpdateSCI_NoPayment(){
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, discount__r.Current_Usage__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Type__c = 'Event Registration' AND pymt__Payment__c != null LIMIT 1]; 

        Test.startTest(); 
            update new pymt__Shopping_Cart_Item__c(Id = item.Id, pymt__Payment__c = null);  
        Test.stopTest(); 

    }

     /**
     * @description test updating payment on existing shopping cart item 
     */
    @isTest
    public static void testUpdateSCI(){
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, discount__r.Current_Usage__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Type__c = 'Event Registration' AND pymt__Payment__c != null LIMIT 1]; 
        List<pymt__PaymentX__c> payments = [SELECT ID, Name FROM pymt__PaymentX__c];

        Test.startTest(); 
            update new pymt__Shopping_Cart_Item__c(Id = item.Id, pymt__Payment__c = payments[1].Id);  
        Test.stopTest(); 

    }
    
}