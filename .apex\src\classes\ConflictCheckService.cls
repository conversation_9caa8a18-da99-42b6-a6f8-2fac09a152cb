/**
 * @description check for time conflicts with other event attendee records
 * <AUTHOR>
 * @version 1.0
 * @created 03-AUG-2020
 * @modified 03-AUG-2020
 */
public class ConflictCheckService {

    public static Boolean hasConflicts(List<SObject> CheckForConflict, String startDateField, String endDateField){
        
        Map<DateTime, Integer> dateMap = new Map<DateTime, Integer>();

        //Create a map determining when potentially conflicting events start and end, thereby allowing the counting of concurrent events
        for(SObject rec : CheckForConflict){

            if ( dateMap.containsKey( (DateTime)rec.get(startDateField) ) )
                dateMap.put((DateTime)rec.get(startDateField), dateMap.get( (DateTime)rec.get(startDateField) ) + 1);
            else 
                dateMap.put((DateTime)rec.get(startDateField), 1);

            if ( dateMap.containsKey( (DateTime)rec.get(endDateField) ) )
                dateMap.put((DateTime)rec.get(endDateField), dateMap.get( (DateTime)rec.get(endDateField) ) - 1);
            else                 
                dateMap.put((DateTime)rec.get(endDateField), -1);

        }
        System.debug('DEBUG AttendeeConflictHandler_TDTM dateMap '+dateMap);

        List<DateTime> dateList = new List<DateTime>( dateMap.keySet() );
        dateList.sort();
        Integer numConcurrentActivities = 0;
        System.debug('DEBUG AttendeeConflictHandler_TDTM dateList '+dateList);
        //Loop through map to count concurrent events, return error if one is exceeded
        for(DateTime d : dateList){
        	numConcurrentActivities += dateMap.get(d);
            if(numConcurrentActivities >1)
                return true;
        }
        return false;
    }
}