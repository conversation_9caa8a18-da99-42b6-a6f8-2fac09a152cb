<aura:component implements="flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" 
                access="global" controller="PaymentTerminalController">
    <aura:handler name="init" value="{!this}" action="{!c.initData}"/>
    <aura:attribute name="recordId" type="Id" />
    <aura:attribute name="paymentCardWrapper" type="object"/>
   <!-- <aura:if isTrue="{!v.paymentCardWrapper.pymt.pymt__Status__c=='Completed'}">
        <div>
            <p>
                <h2>
                    Payment is Completed!!!
                </h2>
            </p>
        </div>
    </aura:if> -->
   <!-- <aura:if isTrue="{!v.paymentCardWrapper.pymt.pymt__Status__c!='Completed'}"> -->
        <div aura:id="mainModal" class="" id="modal-content-id-1" >
            
            <lightning:recordViewForm recordId="{!v.recordId}" objectApiName="pymt__PaymentX__c">
                <lightning:notificationsLibrary aura:id="notifLib"/>
                
                <lightning:helptext content="" class="slds-hidden"></lightning:helptext><br/>
                
                <label for="payment">Payment: {!v.paymentCardWrapper.pymt.Name}</label> <br/>
              <!--   <lightning:outputField aura:id="payment" 
                                       fieldName="Name" 
                                       value="{!v.paymentCardWrapper.pymt.Name}"
                                       /> -->
                
               <label for="date">Date: {!v.paymentCardWrapper.pymt.pymt__Date__c}</label> <br/>
              <!--   <lightning:outputField aura:id="date" variant="label-hidden"
                                       fieldName="pymt__Date__c" 
                                       value="{!v.paymentCardWrapper.pymt.pymt__Date__c}"
                                       /> -->
                <label for="totalAmount">Total Amount: {!v.paymentCardWrapper.pymt.pymt__Amount__c}</label> <br/>
             <!--   <lightning:outputField aura:id="totalAmount" 
                                       fieldName="pymt__Amount__c" 
                                       value="{!v.paymentCardWrapper.pymt.pymt__Amount__c}"
                                       /> -->
                <label for="">Status: {!v.paymentCardWrapper.pymt.pymt__Status__c}</label><br/>
              <!--  <lightning:outputField aura:id="status" variant="label-hidden"
                                       fieldName="pymt__Status__c" 
                                       value="{!v.paymentCardWrapper.pymt.pymt__Status__c}"
                                       /> -->
                <label for="Processor">Processor: {!v.paymentCardWrapper.pymt.pymt__Payment_Processor__c}</label><br/>
            <!--    <lightning:outputField aura:id="Processor" variant="label-hidden"
                                       fieldName="pymt__Payment_Processor__c" 
                                       value="{!v.paymentCardWrapper.pymt.pymt__Payment_Processor__c}"
                                       />-->
            </lightning:recordViewForm>
            
            <lightning:button class="slds-m-top_small" variant="brand" type="submit" name="PurchaseCall" onclick="{!c.onVoidSubmit}" label="Void Payment" />
            
        </div>
   
</aura:component>