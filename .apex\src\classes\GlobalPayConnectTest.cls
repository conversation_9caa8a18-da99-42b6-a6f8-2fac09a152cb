@isTest
public class GlobalPayConnectTest {
    @testSetup
    static void testSetup () {

        Contact con = new Contact ( 
            FirstName = 'TEST',
            LastName  = 'Applicant'
        );
        
        insert con;
        
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        pymt.Name = 'payment_'+string.valueOf(datetime.now());
        pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
        pymt.pymt__Transaction_Type__c = 'Payment';
        pymt.pymt__Status__c = 'Scheduled';
        pymt.pymt__Amount__c = 1000;
        pymt.pymt__Payment_Type__c ='Credit Card';
        pymt.pymt__Billing_First_Name__c ='TEST';
        pymt.pymt__Billing_Last_Name__c ='kite';
        insert pymt;   
    }
    @isTest
    static void testCase1() {
        pymt__PaymentX__c clientPymt = [SELECT Id, Name, pymt__Billing_First_Name__c,pymt__Billing_Last_Name__c, pymt__Transaction_Type__c,pymt__Status__c,pymt__Amount__c,pymt__Payment_Type__c FROM pymt__PaymentX__c LIMIT 1]; 
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
        GlobalPayConnect.processPayment(clientPymt, '', '', '', '', '');
        Test.stopTest();

    }
    @isTest
    static void testCase2() {
        pymt__PaymentX__c clientPymt = [SELECT Id, Name, pymt__Billing_First_Name__c,pymt__Billing_Last_Name__c, pymt__Transaction_Type__c,pymt__Status__c,pymt__Amount__c,pymt__Payment_Type__c FROM pymt__PaymentX__c LIMIT 1]; 
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
        GlobalPayConnect.processRefund('TRX123', 50);
        Test.stopTest();

    }
}