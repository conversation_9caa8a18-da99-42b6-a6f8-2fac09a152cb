({
    //Fetch payment details
    doInit: function(component, event, helper) {
        let pymtId = component.get("v.recordId");
        console.log('pymtId '+pymtId);
        if(pymtId)
            helper.fetchPaymentDetails(component, event, pymtId);
    },
    //Close popup
    closePopup : function(component, event, helper) {
        $A.get("e.force:closeQuickAction").fire();
    },
    //Handles refund
    handleRefund : function(component, event, helper) {
        var allValid = component.find('fieldId').reduce(function (validSoFar, inputCmp) {
            inputCmp.showHelpMessageIfInvalid();
            return validSoFar && !inputCmp.get('v.validity').valueMissing;
        }, true);
        
        if (allValid) {
            let RefundAmount = parseFloat(component.get("v.RefundAmount"));
            let adminFee = parseFloat(component.get("v.adminFee"));
            let RefundReason = component.get("v.RefundReason");
            let pymtRec = component.get("v.paymentObj");
            let totalAmount = RefundAmount + adminFee;
            let actualAmout = pymtRec.pymt__Amount__c;
            let maxRefund = parseFloat(component.get("v.maxRefund"));
                        
            console.log('RefundAmount '+RefundAmount);
            console.log('adminFee '+adminFee);
            console.log('RefundReason '+RefundReason);
            console.log('actualAmout '+actualAmout);
            console.log('totalAmount '+totalAmount);
            console.log('maxRefund '+maxRefund);
            if(RefundAmount <= 0  || adminFee < 0)
                return;
            
            if(totalAmount > maxRefund){
                helper.showToastMessage('Error', 'Sum of Refund Amount and Administration Fee should be less than or equal to max refund allowed: '+maxRefund, 'error');
                return;
            }
            else{
                helper.processRefundHelper(component, RefundAmount, adminFee, RefundReason);
            }
            
        } 
        else {
            return;
        }
    }
})