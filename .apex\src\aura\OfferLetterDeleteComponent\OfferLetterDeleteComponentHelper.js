/**
 * Created by <PERSON><PERSON> on 2022-05-06.
 */

({
    delSelected : function(component, event) {
        let toBeDeleted = event.getSource().get("v.name");
        let listOfOffers = component.get("v.lstContentDoc");
        let newListOfOffers = [];

        const action = component.get("c.delOfferLetter");
        action.setParams({
            olRecId: toBeDeleted
        });
        action.setCallback(this, function (a) {
            const state = a.getState();
            if (state === "SUCCESS") {
                for (let i = 0; i < listOfOffers.length; i++) {
                    let tempRecord = Object.assign({}, listOfOffers[i]);
                    if (tempRecord.Id !== toBeDeleted) {
                        newListOfOffers.push(tempRecord);
                    }
                }
                for (let i = 0; i < newListOfOffers.length; i++) {
                    newListOfOffers[i].Id = i + 1;
                }
                component.set("v.lstContentDoc", newListOfOffers);
                $A.get('e.force:refreshView').fire();
                this.showToast("Offer Letter Delete", "success", "The Offer Letter File is deleted Successfully" );
            } else if (state === "ERROR") {
                const errors = action.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        alert(errors[0].message);
                    }
                }
            } else if (state === "INCOMPLETE") {
                alert('No response from server or client is offline.');
            }
        });
        $A.enqueueAction(action);
    },
    showToast: function (title, type, message) {
        const toastEvent = $A.get("e.force:showToast");
        if (toastEvent) {
            toastEvent.setParams({
                "title": title,
                "type": type,
                "message": message,
                "mode": "dismissible",
                "duration": 9000
            }).fire();
        } else {
            alert(message);
        }
    },

});