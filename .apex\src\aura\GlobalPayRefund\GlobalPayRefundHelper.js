({
    //Fetch app details
    fetchPaymentDetails : function(component, event, recId) {
        
        component.set("v.showSpinner", true);
        var action = component.get("c.getPaymentDetails"); 
        action.setParams({
            "pymtId" : recId
        });
        action.setCallback(this, function(response){
            component.set("v.showSpinner", false);
            var state = response.getState();
            let msg = '';
            if (state === "SUCCESS") {
                let paymentObj = response.getReturnValue();
                component.set("v.paymentObj", paymentObj);
                let trnType = paymentObj.pymt__Transaction_Type__c;
                let actualAmt = paymentObj.pymt__Amount__c;
                let amtPaid = paymentObj.Amount_Paid__c;
                let amtRefunded = paymentObj.Amount_Refunded__c;
                let maxRefund = amtPaid - amtRefunded;
                if(maxRefund < 0)
                    maxRefund = 0;
                component.set("v.maxRefund", maxRefund);
                
                if(trnType == 'Refund'){
                    msg = 'Please navigate to parent payment record to initiate refund.';
                }
                else if(trnType == 'Payment' && amtPaid == 0){
                    msg = 'You have not paid anything to initiate refund!';
                }
                    else if(trnType == 'Payment' && amtRefunded > 0 && maxRefund == 0){
                        msg = 'Maximum refund possible is already processed!';
                    }
                        else if(trnType == 'Payment' && maxRefund > 0){
                            msg = '';
                            component.set("v.disableBtn", false);
                            component.set("v.showRefundCMP", true);                    
                        }
                console.log('paymentObj '+JSON.stringify(paymentObj));
            }
            else if (state === "ERROR") {                
                let errors = response.getError();                
                if (errors && errors[0] && errors[0].message) 
                    this.showToastMessage('Error', errors[0].message, 'error');
                else 
                    this.showToastMessage('Error', 'We\'re sorry, but we cannot complete your refund. Please refresh this page and try again.', 'error');
            }
            component.set("v.messageToDisplay", msg);
        }); 
        $A.enqueueAction(action);
    },
    //processRefund
    processRefundHelper: function(component, RefundAmount, adminFee, RefundReason) {
        component.set("v.showSpinner", true);
        let recId = component.get("v.recordId");        
        var action = component.get("c.processRefund"); 
        action.setParams({
            "pymtId" : recId,
            "amountToRefund" : RefundAmount,
            "adminFee" : adminFee,
            "refundReason" : RefundReason,
        });
        action.setCallback(this, function(response){
            component.set("v.showSpinner", false);
            var state = response.getState();
            let msg = '';
            if (state === "SUCCESS") {
                let result = response.getReturnValue();
                if(result && result.Status == 'SUCCESS'){
                    this.showToastMessage('Success', 'Your refund has been processed successfully!', 'success');
                    $A.get('e.force:refreshView').fire();
                    $A.get("e.force:closeQuickAction").fire();                    
                }
                else if(result && result.Status == 'ERROR' && result.ErrMessage){
                    this.showToastMessage('Error', result.ErrMessage, 'error');
                }
                    else
                    {
                        this.showToastMessage('Error', 'We\'re sorry, but we cannot complete your refund. Please refresh this page and try again.', 'error');                   
                    }
            }
            else if (state === "ERROR") {                
                let errors = response.getError();                
                if (errors && errors[0] && errors[0].message) 
                    this.showToastMessage('Error', errors[0].message, 'error');
                else 
                    this.showToastMessage('Error', 'We\'re sorry, but we cannot complete your refund. Please refresh this page and try again.', 'error');
            }
            component.set("v.messageToDisplay", msg);
        }); 
        $A.enqueueAction(action);
    },
    showToastMessage : function(titleParam, messageParam, typeParam) {
        var toastEvent = $A.get("e.force:showToast");
        toastEvent.setParams({
            title : titleParam,
            message: messageParam,
            duration:' 5000',
            key: 'info_alt',
            type: typeParam,
            mode: 'pester'
        });
        toastEvent.fire();
    },                   
})