/**
* @description    Test class for SyncAddresses_TDTM & AccountTerritoryAssignment_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-04-06
* @modified 2020-04-06
*/
@isTest
public class SyncAddresses_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
         //retrieve default EDA trigger handler
         List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
         //create trigger handler for SyncAddresses_TDTM class (for account)
         tokens.add(new hed.TDTM_Global_API.TdtmToken('SyncAddresses_TDTM', 'Account', 'BeforeInsert', 2.00)); 
         //create trigger handler for SyncAddresses_TDTM class (for contact)
         tokens.add(new hed.TDTM_Global_API.TdtmToken('SyncAddresses_TDTM', 'Contact', 'AfterUpdate', 1.00)); 
         //create trigger handler for AccountTerritoryAssignment_TDTM class
         tokens.add(new hed.TDTM_Global_API.TdtmToken('AccountTerritoryAssignment_TDTM', 'Account', 'AfterUpdate', 1.00)); 
         //Create trigger handler for OpportunityAssignAD_TDTM class
         tokens.add(new hed.TDTM_Global_API.TdtmToken('OpportunityAssignAD_TDTM', 'Opportunity', 'BeforeInsert;BeforeUpdate', 2.00)); 
        //create trigger handler for OpportunityTerritoryLogic__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('OpportunityTerritoryLogic_TDTM', 'Opportunity', 'BeforeInsert;BeforeUpdate', 1.00)); 
         //pass trigger handler config to set method
         hed.TDTM_Global_API.setTdtmConfig(tokens);

        //Test Contact record with "Other Address" 
        Contact c = new Contact(FirstName = 'Test',
                                LastName = 'StudentTest', 
                                OtherStreet = '2 Test Street', 
                                OtherCity = 'Buffalo', 
                                OtherState = 'NY', 
                                OtherPostalCode = '12345', 
                                OtherCountry = 'U.S.A.'); 
        //Inserting contact will create admin account 
        insert c; 

        //Query for admin account: 
        Account adminAcct = [SELECT ID FROM Account WHERE hed__Primary_Contact__c = :c.Id]; 
        //Query for existing territory assigned when billing country = Canada: 
        Territory2 canadaTerr = [SELECT ID, Program_Code__c FROM Territory2 WHERE DeveloperName = 'Canada_Exclude_ON']; 
        //Insert program app
        Account paTest = new Account(Name = 'FTMBA', Program_Code__c = canadaTerr.Program_Code__c, RecordTypeId = AccountService.AcademicProgramRTId ); 
        insert paTest; 

        //Insert related opp with unassigned territory: 
        Opportunity testOppWithPA = new Opportunity(Name = 'Test Oppty with PA', AccountId = adminAcct.id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Lead', 
                                                        Program_Account__c = paTest.Id, RecordTypeId = OpportunityService.DegreeProgramProspectRTId); 
        Opportunity testOppInquiry = new Opportunity(Name = 'Test Oppty with PA', AccountId = adminAcct.id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', 
                                                        Assign_Assistant_Director_Manual__c = true, Program_Account__c = paTest.Id, RecordTypeId = OpportunityService.DegreeProgramProspectRTId); 
        insert new List<Opportunity>{testOppWithPA, testOppInquiry}; 

        //Add new users and user assignments 
        addSetUpObjects();
    }
    /**
     * @description future method to add new users 
     * and user assignments in the setup method 
     */
    @future
    private static void addSetUpObjects(){
        //Query for Recruitment and Admissions Profile: 
        Profile profileRA = [SELECT ID FROM PROFILE WHERE NAME = 'System Administrator' LIMIT 1]; 
        //Create Test User Profiles: 
        List<user> usersToInsert = new List<User>(); 
        for(Integer i= 0; i < 4; i++){
            User testUser = new User(
                                    alias = 'test'+i, 
                                    email = 'test'+i+'@testuser.com', 
                                    emailencodingkey = 'UTF-8', 
                                    firstName = 'test',
                                    lastName = 'User'+i, 
                                    userName = 'test'+i+'@testuser.com.sa', 
                                    profileId = profileRA.Id, 
                                    timeZoneSidKey = 'America/Los_Angeles', 
                                    LocaleSidKey = 'en_US', 
                                    LanguageLocaleKey = 'en_US'); 
            usersToInsert.add(testUser); 
        }
        insert usersToInsert; 

        //Query for existing territory assigned when billing country = Canada: 
        Territory2 canadaTerr = [SELECT ID, Program_Code__c FROM Territory2 WHERE DeveloperName = 'Canada_Exclude_ON'];
        
        //Create User Assignments
        List<UserTerritory2Association> userAssignments = new List<UserTerritory2Association>(); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = canadaTerr.Id, UserId = usersToInsert[0].Id, RoleInTerritory2='Assistant Director')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = canadaTerr.Id, UserId = usersToInsert[1].Id, RoleInTerritory2='Assistant Director')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = canadaTerr.Id, UserId = usersToInsert[2].Id, RoleInTerritory2='Recruitment Officer')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = canadaTerr.Id, UserId = usersToInsert[3].Id, RoleInTerritory2='Recruitment Officer')); 
        insert userAssignments; 
    }

    /**
     * @description insert a contact record with "Other Address"
     * Assert that admin account was created 
     * Assert that "Other Address" is mapped to the admin account 
     * 
     */
    @isTest
    public static void testInsertAdminAccount(){ 
        Contact c = new Contact(FirstName = 'Test',
                                LastName = 'Student', 
                                OtherStreet = '1 Test Street', 
                                OtherCity = 'Chicago', 
                                OtherState = 'IL', 
                                OtherPostalCode = '18273', 
                                OtherCountry = 'U.S.A.'); 
        
        Test.startTest(); 
            insert c; 
        Test.stopTest(); 

        Account a = [SELECT ID, BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry, hed__Primary_contact__c FROM Account WHERE hed__Primary_Contact__c = :c.Id]; 

        System.Assert(a != null, 'Admin Account was not created. '); 
        System.AssertEquals(c.OtherStreet, a.BillingStreet); 
        System.AssertEquals(c.OtherCity, a.BillingCity); 
        System.AssertEquals(c.OtherState, a.BillingState); 
        System.AssertEquals(c.OtherPostalCode, a.BillingPostalCode); 
        System.AssertEquals(c.OtherCountry, a.BillingCountry); 
    }

    /**
     * @description update a contact record's "Other Address"
     * Assert that admin account's 'Billing Address' was updated 
     * Assert that account's Run_Territory_Assignment__c = true
     */
    @isTest
    public static void testUpdateOtherAddress_TerrAssignment(){
        Contact c = [SELECT ID, OtherStreet, OtherCity, OtherState, OtherPostalCode, OtherCountry, AccountId FROM Contact WHERE AccountId != null LIMIT 1]; 
        c.OtherStreet = '3 Test Street'; 
        c.OtherCity = 'Toronto'; 
        c.OtherState = 'AB'; 
        c.OtherPostalCode = '19238'; 
        c.OtherCountry = 'Canada';

        Test.startTest(); 
            update c; 
        Test.stopTest(); 

        Account a = [SELECT ID, BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountry, hed__Primary_contact__c, Run_Territory_Assignment__c FROM Account WHERE hed__Primary_Contact__c = :c.Id]; 

        System.AssertEquals(c.OtherStreet, a.BillingStreet); 
        System.AssertEquals(c.OtherCity, a.BillingCity); 
        System.AssertEquals(c.OtherState, a.BillingState); 
        System.AssertEquals(c.OtherPostalCode, a.BillingPostalCode); 
        System.AssertEquals(c.OtherCountry, a.BillingCountry); 
        System.AssertEquals(true, a.Run_Territory_Assignment__c); 
    }

    /**
     * @description update a contact record's "Other Address"
     * Assert that admin account's 'Billing Address' was updated 
     */
   /* @isTest  
    public static void testOppAssignments(){
        //Query for existing territory assigned when billing country = Canada: 
        Territory2 canadaTerr = [SELECT ID, Program_Code__c FROM Territory2 WHERE DeveloperName = 'Canada_Exclude_ON'];
        
        //Query for admin account
        Account a = [SELECT ID, BillingStreet, BillingCity, BillingState, BillingPostalCode, BillingCountryCode, BillingCountry, hed__Primary_contact__c, Run_Territory_Assignment__c FROM Account WHERE Run_Territory_Assignment__c = false LIMIT 1]; 

         //Create Territory Assignment on Admin Account: 
        // ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = a.Id, Territory2Id = canadaTerr.Id, AssociationCause = 'Territory2Manual');

        Test.startTest(); 
            //insert acctAssignment; 
        	a.BillingCountryCode = 'CA'; 
            a.Run_Territory_Assignment__c = true; 
            update a; 
        Test.stopTest(); 

         //Query for related prospect opportunities with no assigned territories: 
        for(Opportunity opp :[SELECT ID, AccountId, Territory2Id, Recruitment_Officer__c, Program_Account__c, Program_Account__r.Program_Code__c, isExcludedFromTerritory2Filter, Assign_Assistant_Director__c, Assistant_Director__c FROM Opportunity WHERE AccountId = :a.Id]){
            //Assert that territory was assigned: 
            System.Assert(opp.Territory2Id == canadaTerr.Id, 'Territory was not assigned properly: ' + opp.Territory2Id); 
            System.Assert(opp.Recruitment_Officer__c != null, 'Recruitment Officer was not assigned: ' + opp);
            if(opp.Assign_Assistant_Director__c){
                System.assert(opp.Assistant_Director__c != null, 'Assistant Director was not assigned: ' + opp); 
            } 
        }
    } */
}