@isTest
public with sharing class RemoveEPCourseControllerTest {
    @TestSetup
    static void setupTestData() {
        Calendar_View__c testCalendrViewObject = new Calendar_View__c();
        testCalendrViewObject.Link_to_Calendar_Item__c = 'https://uat-rotman.cs148.force.com/ExecutivePrograms/s/course-offering/{!itemId}';
        testCalendrViewObject.Name = 'Course Offering Calendar View';
        insert testCalendrViewObject;

        List<Contact> newCons = new List<Contact>{
            new Contact(
                FirstName           = 'Foriegn',
                hed__Citizenship__c = 'India',
                LastName            = 'Resident',
                Email				= '<EMAIL>'
            ),
            new Contact(
                FirstName           = 'Domestic',
                hed__Citizenship__c = 'Canada',
                LastName            = 'Resident',
                Email				= '<EMAIL>'
            )
        };

        insert newCons;

        //Create test Account:
        Account a = new Account(
            Name='TestAccount');
        insert a;
        
        //Create Term
        hed__Term__c trm = new hed__Term__c(
            hed__Account__c=a.Id,
            name='Spring 2020');
        insert trm;
        
        //Create course
        hed__Course__c course = new hed__Course__c
            (hed__Account__c=a.Id,
             name='MBA 2020');
        insert course;
        
        //Create Course Offerings
        hed__Course_Offering__c cOffering = new hed__Course_Offering__c(
            hed__Course__c = course.id,
            Name = 'Test Course Offering',
            hed__Term__c=trm.Id,
            hed__Faculty__c=newCons[0].id);
        insert cOffering;

        pymt__Shopping_Cart__c testShoppingCart = new pymt__Shopping_Cart__c();
        insert testShoppingCart;

        pymt__Shopping_Cart_Item__c testShoppingCartItem = new pymt__Shopping_Cart_Item__c();
        testShoppingCartItem.Name = 'Test Shopping Item';
        testShoppingCartItem.Course_Offering__c = cOffering.Id;
        testShoppingCartItem.pymt__Shopping_Cart__c = testShoppingCart.Id;
        insert testShoppingCartItem;
    }

    @isTest
    static void testDeleteCartItemsAndCart() {
        pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c];
        Test.startTest();
            String testUrl = RemoveEPCourseController.deleteCartItemsAndCart(testShoppingCart.Id);
        Test.stopTest();
        System.assertNotEquals(testUrl, null, 'URL should not be null');
    }
}