({
	doInit : function(cmp) {
        var recId = cmp.get("v.recordId");
        var objName = cmp.get("v.sObjectName");
        
        // get timeline items
        var actionGetItems = cmp.get("c.allTimelineItems");
        actionGetItems.setParams({
            objectName : objName,
            recordId : recId
        });
        
        actionGetItems.setCallback(this, function(response) {
            var state = response.getState();
            if (state === 'SUCCESS') {
                var result = response.getReturnValue();
                var futureItems = [];
                var pastItems = [];
                var futureItemsCollapsed = [];
                var pastItemsCollapsed = [];
                
                for (var i = 0; i < result.length; i++) {
                    var item = result[i];
                    if (item.isFuture) {
                    	futureItems.push(item);
                        
                        if (futureItemsCollapsed.length <= 2) {
                            futureItemsCollapsed.push(item);
                        } 
                    } else {
                    	pastItems.push(item);
                        if (pastItemsCollapsed.length <= 6) {
                            pastItemsCollapsed.push(item);
                        }
                    }
                }
                
                if (futureItemsCollapsed.length == futureItems.length) {
                    cmp.set("v.allowExpandFuture", false);
                } else {
                    cmp.set("v.allowExpandFuture", true);
                }
                if (pastItemsCollapsed.length == pastItems.length) {
                    cmp.set("v.allowExpandPast", false);
                } else {
                    cmp.set("v.allowExpandPast", true);
                }
                
                cmp.set("v.futureTimelineItems", futureItemsCollapsed);
                cmp.set("v.pastTimelineItems", pastItemsCollapsed);
                cmp.set("v.futureTimelineItems_all", futureItems);
                cmp.set("v.pastTimelineItems_all", pastItems);
                cmp.set("v.futureCollapsed", true);
                cmp.set("v.pastCollapsed", true);
            } else if (state === "ERROR") {
            	var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        console.log("Error message: " + errors[0].message);
                    }
                } else {
                	console.log("Unknown error");
                }
            }
        }); 
        
        //adds the server-side action to the queue        
        $A.enqueueAction(actionGetItems);
        
        
        // get timeline items
        var actionGetLabel = cmp.get("c.engagementLabel");
        actionGetLabel.setParams({
            objectName : objName
        });
        
        actionGetLabel.setCallback(this, function(response) {
            var state = response.getState();
            if (state === 'SUCCESS') {
                var result = response.getReturnValue();
                cmp.set("v.engagementLabel", result);
            } else if (state === "ERROR") {
            	var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        console.log("Error message: " + errors[0].message);
                    }
                } else {
                	console.log("Unknown error");
                }
            }
        }); 
        
        //adds the server-side action to the queue        
        $A.enqueueAction(actionGetLabel);
	}, 
    
    doToggleFuture : function(cmp) {
    	cmp.set("v.futureCollapsed", !cmp.get("v.futureCollapsed"));
	},
    
    doTogglePast : function(cmp) {
        cmp.set("v.pastCollapsed", !cmp.get("v.pastCollapsed"));
    }
})