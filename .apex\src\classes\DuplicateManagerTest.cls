@isTest
private class DuplicateManagerTest {

    @testSetup
    static void setup() {

        insert new Object_Merge_Handler__c( 
            Name            = 'Contact', 
            Active__c       = true, 
            RecordTypeId    = Schema.SObjectType.Object_Merge_Handler__c.getRecordTypeInfosByName().get('Parent Handler').getRecordTypeId()
        );

        Contact conOne = new Contact(
            FirstName   = 'Chr2345is',
            LastName    = 'Tes24t',
            MobilePhone = '1234567',
            hed__AlternateEmail__c = '<EMAIL>'
        );

        insert conOne;

        Contact dupeCon = new Contact(
            FirstName   = 'Ch2425ris',
            LastName    = 'Test42',
            MobilePhone = '1234567',
            hed__AlternateEmail__c = '<EMAIL>'
        );

        Database.DMLOptions dml = new Database.DMLOptions(); 
        dml.DuplicateRuleHeader.allowSave = true;
        Database.insert(dupeCon, dml);

        //Manually insert dupes
        DuplicateRecordSet drs   = new DuplicateRecordSet(
            DuplicateRuleId = (Id)[SELECT Id FROM DuplicateRule WHERE DeveloperName = 'Likely_Duplicate' LIMIT 1].Id
        );
        insert drs;
        insert new List<DuplicateRecordItem>{
            new DuplicateRecordItem(
                DuplicateRecordSetId = drs.Id,
                RecordId             = conOne.Id
            ),
            new DuplicateRecordItem(
                DuplicateRecordSetId = drs.Id,
                RecordId             = dupeCon.Id
            )
        };

    }

    @isTest
    static void validateGetDuplicates() {

        List<Contact> cons = [ SELECT Id FROM Contact ];

        Test.startTest();
            DuplicateManagerController.getDuplicates( 'Email, MobilePhone', null, cons[0].Id, 'Contact' );
        Test.stopTest();


    }


    @isTest
    static void validateGetDuplicatesFromChild() {

        List<Contact> cons = [ SELECT Id FROM Contact ];

        Opportunity o = new Opportunity(
            CloseDate   = Date.today(),
            Contact__c  = cons[0].Id,
            Name        = 'Some Name',
            StageName   = 'Inquiry'
        );

        insert o;

        Test.startTest();
            DuplicateManagerController.getDuplicates( 'Email, MobilePhone', 'Contact__c', o.Id, 'Opportunity' );
        Test.stopTest();


    }

    @isTest
    static void validateColumnGetter() {

        List<Contact> cons = [ SELECT Id FROM Contact ];

        Integer i=0;
        for ( DuplicateManagerController.FieldWrapper fw : DuplicateManagerController.getColumns( 'Email,FirstName,LastName', null, 'Contact' ) ) {
            if ( fw.fieldName == 'Email' ) {
                system.assertEquals( 'EMAIL', fw.fieldType );
                system.assertEquals( 'Email', fw.label );
            } else
                system.assertEquals( 'STRING', fw.fieldType );

            i++;
        }
        system.assertEquals( 3, i );
    }


    @isTest
    static void validateColumnGetterFromChild () {

        List<Contact> cons = [ SELECT Id FROM Contact ];

        Integer i=0;
        for ( DuplicateManagerController.FieldWrapper fw : DuplicateManagerController.getColumns( 'Email,FirstName,LastName', 'Contact__c', 'Opportunity' ) ) {
            if ( fw.fieldName == 'Email' ) {
                system.assertEquals( 'EMAIL', fw.fieldType );
                system.assertEquals( 'Email', fw.label );
            } else
                system.assertEquals( 'STRING', fw.fieldType );

            i++;
        }
        system.assertEquals( 3, i );
    }


    @isTest
    static void validateMerge () {

        List<Contact> cons = [ SELECT Id FROM Contact ];
        Object_Merge_Pair__c omp = DuplicateManagerController.mergeRecords( cons[0].Id, cons[1].Id ); 
        system.assertEquals( 'Merged', DuplicateManagerController.checkPair( omp.Id ) );
        
    }


    @isTest
    static void validateInvalidMerge () {

        List<Contact> cons = [ SELECT Id FROM Contact ];
        Object_Merge_Pair__c omp = DuplicateManagerController.mergeRecords( cons[0].Id, (Id)[ SELECT Id FROM Account LIMIT 1 ].Id ); 
        system.assertEquals( 'Error', DuplicateManagerController.checkPair( omp.Id ) );
        
    }

}