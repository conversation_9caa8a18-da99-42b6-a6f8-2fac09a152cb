/**
* @description    Test class for QRCodeController class
* <AUTHOR>
* @version        1.0 
* @created 2020-26-05
* @modified 2020-26-05
*/
@isTest
public class QRCodeControllerTest {
	@testSetup
    static void testSetup (){
        //Create test Account:
        Account a = (Account)TestFactory.createSObject(new Account(Name='TestAccount'));
        insert a;

        //Create test contact: 
        Contact c = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student'));
        insert c;

        //Create special Event Record
        evt__Special_Event__c sevnt = new evt__Special_Event__c(Name='Test Event', Published_Price__c =50);
        insert sevnt;
        //Create an Attendee record 
        evt__Attendee__c atndee = new evt__Attendee__c(evt__Event__c = sevnt.Id);
        insert atndee;
    }
    @isTest 
    static void markattendeeAttendedTheEvent() {
        evt__Attendee__c satndee = [SELECT Id from evt__Attendee__c limit 1];
    	Test.setCurrentPageReference(new PageReference('Page.QRCodeGenerator')); 
		System.currentPageReference().getParameters().put('id', satndee.Id);
        new QRCodeController();
        Test.StartTest();
        System.currentPageReference().getParameters().put('id', '12345');
        new QRCodeController();
        Test.StopTest();
    }
    @isTest 
    static void markatRegisteredertendeeAttendedTheEvent() {
        evt__Attendee__c satndee = [SELECT Id from evt__Attendee__c limit 1];
        satndee.evt__Invitation_Status__c = 'Registered';
        update satndee;
    	Test.setCurrentPageReference(new PageReference('Page.QRCodeGenerator')); 
		System.currentPageReference().getParameters().put('id', satndee.Id);
        QRCodeController qrcntrlr = new QRCodeController();
        qrcntrlr.markAttended();
        evt__Special_Event__c sevnt = [SELECT Id,evt__End__c,evt__Start__c from evt__Special_Event__c limit 1];
        sevnt.evt__End__c = System.Now();
        update sevnt;
        qrcntrlr = new QRCodeController();
        sevnt.evt__Start__c = System.Now().addHours(3);
        sevnt.evt__End__c = System.Now().addHours(3);
        update sevnt;
        qrcntrlr.markAttended();
        Test.StartTest();
        qrcntrlr = new QRCodeController();
        sevnt.evt__Start__c = System.Now().addHours(-3);
        sevnt.evt__End__c = System.Now().addHours(-2);
        update sevnt;
        qrcntrlr = new QRCodeController();
        qrcntrlr.isPermission = false;
        qrcntrlr = new QRCodeController();
        Test.StopTest();
    }
    @isTest 
    static void markatRegisteredertendeeAttendedTheEvent1() {
        evt__Attendee__c satndee = [SELECT Id from evt__Attendee__c limit 1];
        satndee.evt__Invitation_Status__c = 'Registered';
        update satndee;
    	Test.setCurrentPageReference(new PageReference('Page.QRCodeGenerator')); 
		System.currentPageReference().getParameters().put('id', satndee.Id);
        evt__Special_Event__c sevnt = [SELECT Id,evt__End__c,evt__Start__c from evt__Special_Event__c limit 1];
        sevnt.evt__Start__c = System.Now().addHours(1);
        sevnt.evt__End__c = System.Now().addHours(3);
        update sevnt;
        QRCodeController qrcntrlr = new QRCodeController();
        qrcntrlr.markAttended();
        //System.assertEquals(true,[SELECT Id,evt__Attended__c from evt__Attendee__c limit 1].evt__Attended__c);
        Test.StartTest();
        qrcntrlr = new QRCodeController();
        sevnt.evt__Start__c = System.Now().addHours(-3);
        sevnt.evt__End__c = System.Now().addHours(-2);
        update sevnt;
        qrcntrlr = new QRCodeController();
        Test.StopTest();
    }
}