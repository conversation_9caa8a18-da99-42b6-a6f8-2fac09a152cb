/**
 * @description Test Class for SessionConflictHandler_TDTM, & ConflictCheckService class
 * <AUTHOR>
 * @version 1.0
 * @created 08-AUG-2020
  */
@isTest
public class SessionConflictHandler_TDTM_Test{

	@testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for AttendeeConflictHandler_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('SessionConflictHandler_TDTM', 'evt__Session_Assignment__c', 'BeforeInsert;BeforeUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        RecordType stdEvntRT = [Select Id, Name from RecordType Where SObjectType = 'evt__Special_Event__c' and Name = 'Standard Event'];
        List<evt__Special_Event__c> eList = new List<evt__Special_Event__c>();
        eList.add( new evt__Special_Event__c (RecordTypeId = stdEvntRT.Id, Name = 'Test Event1', evt__Disable_Conflict_Checking__c = False, 
                                                Start_Date__c = Date.newInstance( 2025, 12, 12 ),
                                                End_Date__c = Date.newInstance( 2025, 12, 12 ),
                                                Start_Time__c = '1:00 PM',
                                                End_Time__c = '3:00 PM',
        									  evt__Start__c = Datetime.newInstanceGmt(2025,12,12,1,0,0), evt__End__c = DateTime.NewInstance(2025,12,12,3,0,0)));
        /*eList.add( new evt__Special_Event__c (RecordTypeId = stdEvntRT.Id, Name = 'Test Event2', evt__Disable_Conflict_Checking__c = False, 
        									  evt__Start__c = Datetime.newInstanceGmt(2025,12,13,4,0,0), evt__End__c = DateTime.NewInstance(2025,12,13,5,0,0)));
        eList.add( new evt__Special_Event__c (RecordTypeId = stdEvntRT.Id, Name = 'Test Event2', evt__Disable_Conflict_Checking__c = False, 
        									  evt__Start__c = Datetime.newInstanceGmt(2025,12,14,4,0,0), evt__End__c = DateTime.NewInstance(2025,12,14,5,0,0)));*/
        insert eList;
        
        List<Contact> cList = new List<Contact>();
        Contact c1 = (Contact)TestFactory.createSObject(new Contact());
        Contact c2 = (Contact)TestFactory.createSObject(new Contact());
        cList.add(c1); 
        cList.add(c2);
        Insert cList;
        
        List<evt__Attendee__c> atnList = new List<evt__Attendee__c>();
        atnList.add(new evt__Attendee__c(evt__Event__c = eList[0].Id, evt__Contact__c = cList[0].Id));
        //atnList.add(new evt__Attendee__c(evt__Event__c = eList[1].Id, evt__Contact__c = cList[0].Id));
        insert atnList;
        
        List<evt__Session__c> sList = new List<evt__Session__c>();
        sList.add(new evt__Session__c(evt__Event__c = eList[0].Id, Name = 'Test Session1', evt__Start__c = DateTime.newInstance(2025,12,12,1,0,0), evt__End__c = DateTime.newInstance(2025,12,12,2,0,0)));
        sList.add(new evt__Session__c(evt__Event__c = eList[0].Id, Name = 'Test Session2', evt__Start__c = DateTime.newInstance(2025,12,12,2,0,0), evt__End__c = DateTime.newInstance(2025,12,12,3,0,0)));
        //sList.add(new evt__Session__c(evt__Event__c = eList[2].Id, Name = 'Test Session2', evt__Start__c = DateTime.newInstance(2025,12,1,10,0,0), evt__End__c = DateTime.newInstance(2025,12,1,11,0,0)));
        insert sList;
        
        /*List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = s.Id, evt__Attendee__c = atnList[0].Id));
        insert saList;*/
    }
    

	@isTest
    static void sessionConflictInsertTest(){ 

        List<evt__Attendee__c> atnList = [Select Id, Name From evt__Attendee__c];
        List<evt__Session__c> sList = [Select Id, Name From evt__Session__c];
        
        List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id));
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id));
        //saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[1].Id, evt__Attendee__c = atnList[1].Id));
                
        Test.startTest();
        try{
            insert saList;

        } catch(Exception e){
            System.assert(e.getMessage().contains(SessionConflictHandler_TDTM.ERROR_MESSAGE));
        }
        Test.stopTest();
    }

    @isTest
    static void sessionConflictUpdateTest(){ 
        
        List<evt__Attendee__c> atnList = [Select Id, Name From evt__Attendee__c];
        List<evt__Session__c> sList = [Select Id, Name From evt__Session__c];
        
        List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id));
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[1].Id, evt__Attendee__c = atnList[0].Id));
        //saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[1].Id));
		insert saList;
        
        Test.startTest();
        try{
            saList[0].evt__Status__c = 'Interested';
            update saList[0];

        } catch(Exception e){
            system.debug(e.getMessage());
            System.assert(e.getMessage().contains(SessionConflictHandler_TDTM.ERROR_MESSAGE));
        }
        Test.stopTest();
    }
}