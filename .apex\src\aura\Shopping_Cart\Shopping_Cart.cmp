<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
                access="global" controller="ShoppingCartController" >
    <aura:handler name="init" value="{!this}" action="{!c.initData}"/>
    <aura:attribute Name="ItemMap" type="MAP"/>
    <aura:attribute Name="CartMap" type="MAP" default="{}"/>
    <aura:attribute Name="showCart" type="boolean" default="false"/>
    <aura:attribute Name="PaymentId" type="string"/>
    
    <div style="height:600px;width:700px;margin:10%">
        <table class="cartTable"  id="MainTable">
            <thead>
                <tr>
                    <th> Item Name </th>
                    <th> Description </th>
                    <th> Price </th>
                    <th> Add To Cart </th>
                </tr>
            </thead>
            
            <tbody> 
                <aura:iteration items="{!v.ItemMap}" var="cus" indexVar="key">
                    <tr> 
                        <td> {!cus.value.Name}</td>
                        <td> {!cus.value.evt__Short_Description__c}</td>
                        <td> 
                            ${!cus.value.evt__Event_Fees__r[0].evt__Amount__c}
                        </td>
                        <td>
                            <lightning:button class="slds-m-top_small" variant="brand" 
                                              type="submit" name="AddToCart" value="{!cus}"
                                              onclick="{!c.addToCart}" label="Add To Cart" />
                        </td>
                    </tr>  
                </aura:iteration>
                <!--      <tr id="c1">
                <td>
                    Short Course 1
                </td>
                <td>
                    Short Course 1 Description
                </td>
                <td>
                    $199.00
                </td>
                <td>
                    <lightning:button class="slds-m-top_small" variant="brand" 
                                      type="submit" name="AddToCart" 
                                      onclick="{!c.onSubmit}" label="Add To Cart" />
               
                    
                </td>
            </tr>
            <tr id="c2">
                <td>
                    Short Course 2
                </td>
                <td>
                    Short Course 2 Description
                </td>
                <td>
                    $99.00
                </td>
                <td>
                    <lightning:button class="slds-m-top_small" variant="brand" type="submit" name="AddToCart" onclick="{!c.onSubmit}" label="Add To Cart" />
                </td>
            </tr>
            <tr id="c3">
                <td>
                    Short Course 3
                </td>
                <td>
                    Short Course 3 Description
                </td>
                <td>
                    $299.00
                </td>
                <td>
                    <lightning:button class="slds-m-top_small" variant="brand" 
                                      type="submit" name="AddToCart" onclick="{!c.onSubmit}" 
                                      label="Add To Cart" />
                </td>
            </tr>
            <tr id="c4">
                <td>
                    Short Course 4
                </td>
                <td>
                    Short Course 4 Description
                </td>
                <td>
                    $159.00
                </td>
                <td>
                   <lightning:button class="slds-m-top_small" aura:id="Course4"
                                     variant="brand" type="submit" name="AddToCart" 
                                     onclick="{!c.addToCart}" label="Add To Cart" />
                </td>
            </tr> -->
                <aura:if isTrue="{!v.showCart}">
                    
                    <c:ShoppingCartComponent aura:id="ShopCart" showCart="true" CartMap="{!v.CartMap}" />
                 
                </aura:if>
            </tbody>
        </table>
    </div>
</aura:component>