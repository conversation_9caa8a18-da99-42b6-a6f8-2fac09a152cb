/** 
* @description    Shows the level of engagement on the Opportunities, principally based on which events they have attended.
* <AUTHOR> 
* @version        1.0 
*/
global class InteractionEnggmntScoreAuto_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Interaction records from trigger new 
     * @param oldList the list of Interaction records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Interactions 
     */
	global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
    	hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
		
        Map<Id, List<Staging_Table__c>> opportunityIdToInteractionMap = new Map<Id, List<Staging_Table__c>>();
		List<Opportunity> oppLstToUpdate = new List<Opportunity>();
        //AFTER INSERT CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
            for(Staging_Table__c stbl : (List<Staging_Table__c>) newList) {
                if ( stbl.Opportunity__c != null ) {
                    if(opportunityIdToInteractionMap.containsKey(stbl.Opportunity__c))
                        opportunityIdToInteractionMap.get(stbl.Opportunity__c).add(stbl);
                    else
                        opportunityIdToInteractionMap.put(stbl.Opportunity__c, new List<Staging_Table__c> { stbl });
                }

            }
            oppLstToUpdate = (List<Opportunity>) RollupService.calculateOpportunityEngagementScore(opportunityIdToInteractionMap);
        }
        
        //AFTER UPDATE CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            System.Debug('<<<<<CLASSEXECUTED>>>>>');
            for(Staging_Table__c stbl : (List<Staging_Table__c>) newList) {
                if ( stbl.Opportunity__c != null ) {
                    if(opportunityIdToInteractionMap.containsKey(stbl.Opportunity__c))
                        opportunityIdToInteractionMap.get(stbl.Opportunity__c).add(stbl);
                    else
                        opportunityIdToInteractionMap.put(stbl.Opportunity__c, new List<Staging_Table__c> { stbl });

                }
            }
            oppLstToUpdate = (List<Opportunity>) RollupService.calculateOpportunityEngagementScore(opportunityIdToInteractionMap);
        }
        dmlWrapper.objectsToUpdate.addAll(oppLstToUpdate);
        return dmlWrapper;
    }
}