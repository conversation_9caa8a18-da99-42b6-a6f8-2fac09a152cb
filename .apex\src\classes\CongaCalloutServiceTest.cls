/**
* @description    Test class for CongaCalloutService class
* <AUTHOR>
* @version        1.0 
* @created 2020-07-20
* @modified 2020-07-20
*/
@isTest
public class CongaCalloutServiceTest {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        List<Contact> contList =  (List<Contact>) TestFactory.createSObjectList(new Contact(), 25); 
        insert contList; 
    }

    /**
     * @description test Conga callout to Jitterbit
     */
    @isTest 
    static void validateCongaCallout () {
        //VidyardCreationMock returns the same response required to test CongaCalloutService: 
        Test.setMock(HttpCalloutMock.class, new VidyardCreationMock());

        
        List<CongaCalloutService.Request> requests = new List<CongaCalloutService.Request>(); 
        List<Contact> cons = [ SELECT Id, Name FROM Contact ];
        
        for(Contact c : cons){
            CongaCalloutService.Request request =  new CongaCalloutService.Request();  
            request.recordId = c.Id; 
            request.congaParams = '&Id=' + c.Id + '&OFN=' + c.Name; 
            requests.add(request); 
        }
        Test.startTest();
            CongaCalloutService.callJitterbit( requests );
        Test.stopTest();
    }
}