<!--

 Copyright (c) 2018, salesforce.com, inc.
 All rights reserved.

 Licensed under the BSD 3-Clause license.
 For full license text, see LICENSE.txt file in the repo root  or https://opensource.org/licenses/BSD-3-Clause

-->
<aura:component implements="lightning:availableForFlowActions,force:hasRecordId">
    
    <aura:attribute name="recordError" type="String"
                    description="An error message bound to force:recordData"/>
    
    <force:recordData aura:id="recordLoader"
                      layoutType="FULL"
                      recordId="{!v.recordId}"
                      targetError="{!v.recordError}"
                      />
    
</aura:component>