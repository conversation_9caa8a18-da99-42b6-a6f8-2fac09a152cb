.slds.THIS{
    padding : 10px;
    margin : 10px; 
    display: inline-block;
    border-radius: 15px;
    text-align: center;
    font-size : 2em;
} 

.THIS .flip-container {
	perspective: 1000px;
} 
/* hide back while swapping*/
.THIS .front, .THIS .back {
	backface-visibility: hidden; 
	position: absolute;
	top: 0;
	left: 0;
}

.THIS.flip-container, .THIS .front, .THIS .back {
	width: 100%;
	height: 100%;
}
.THIS .front {
	z-index: 2;  
}

/* Flip Speed */
.THIS .flipper {
	transition: 0.6s;
	transform-style: preserve-3d; 
	position: relative; 
} 

.THIS.flip-container.horizontal:hover .flipper, .THIS.flip-container.horizontal.hover .flipper {
		transform: rotateY(180deg);
}

.THIS.horizontal .front { 
	transform: rotateY(0deg);
}

/* back, initially hidden pane */
.THIS.horizontal .back {
	transform: rotateY(180deg);
}

.THIS.flip-container.vertical:hover .flipper, .THIS.flip-container.vertical.hover .flipper {
		transform: rotateX(180deg);
}

.THIS.vertical .front { 
	transform: rotateX(0deg);
}

/* back, initially hidden pane */
.THIS.vertical .back {
	transform: rotateX(180deg);
}