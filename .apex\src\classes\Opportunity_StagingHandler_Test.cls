/**
 * @description Test Class for Opportunity_StagingHandler_TDTM
 * <AUTHOR>
 * @version 1.0
 * @created 10-SEP-2020
  */
@isTest
public class Opportunity_StagingHandler_Test{

	@testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for Opportunity_StagingHandler_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('Opportunity_StagingHandler_TDTM', 'Opportunity', 'BeforeInsert;BeforeUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);

        Opportunity o = (Opportunity)TestFactory.createSObject(new Opportunity(Name = 'Test Opty 1', Staging_Source__c = 'Test Source', Staging_Stage__c = 'Test Stage', Staging_Close_Date__c = Date.newInstance(2020, 12, 1)));
		insert o;
        
    }
    
    @isTest
    static void opportunityInsertTest(){ 
        Opportunity o = (Opportunity)TestFactory.createSObject(new Opportunity(Name = 'Test Opty 2', Staging_Source__c = 'Test Source', Staging_Stage__c = 'Test Stage', Staging_Close_Date__c = Date.newInstance(2020, 12, 1)));
		insert o;
    }
    
    @isTest
    static void opportunityUpdateTest(){
        
        Opportunity o = [Select Name, Staging_Source__c, Staging_Close_Date__c, Staging_Stage__c, Allow_Stage_Regression__c from Opportunity Limit 1];
        o.Allow_Stage_Regression__c=true;
        o.Staging_Source__c = 'Test Source 2';
        o.Staging_Stage__c = 'Test Stage2';
        o.Staging_Close_Date__c = Date.newInstance(2020, 12, 2);
        update o;
        
        o.Allow_Stage_Regression__c = false;
        update o;
        
    }
}