public with sharing class MassUpdateContactEmailsToInvalid implements Database.Batchable<SObject>{

    Set<id> successRecord = new Set<id>();
    Set<id> failRecord = new Set<id>();

    public Database.QueryLocator start( Database.BatchableContext context ) {
        String query = 'SELECT Id, Email, hed__AlternateEmail__c, SFMC_Email__c, hed__UniversityEmail__c, hed__WorkEmail__c FROM Contact' ;
        return Database.getQueryLocator( query );
    }

    public void execute( Database.BatchableContext context, List<Contact> conList) {
        List <Contact> lstContacts = new list<Contact>();
        for ( Contact con :  conList) {
            con.Email = (con.Email != null) ? con.Email+'.invalid' : con.Email;
            con.hed__AlternateEmail__c = (con.hed__AlternateEmail__c != null) ? con.hed__AlternateEmail__c+'.invalid' : con.hed__AlternateEmail__c;
            con.SFMC_Email__c = (con.SFMC_Email__c != null) ? con.SFMC_Email__c+'.invalid' : con.SFMC_Email__c;
            con.hed__UniversityEmail__c = (con.hed__UniversityEmail__c != null) ? con.hed__UniversityEmail__c+'.invalid' : con.hed__UniversityEmail__c;
            con.hed__WorkEmail__c = (con.hed__WorkEmail__c != null) ? con.hed__WorkEmail__c+'.invalid' : con.hed__WorkEmail__c;
            lstContacts.add(con);
        }
        //update(lstContacts);
        Database.SaveResult[] srList = Database.update(lstContacts, false);

        for (Database.SaveResult sr : srList) {
            if (sr.isSuccess()) {
                // Operation was successful, so get the ID of the record that was processed
                successRecord.add(sr.id);
            } else {
                for(Database.Error err : sr.getErrors()) {
            }
                failRecord.add(sr.id);
            }
        }
    }

    public void finish(Database.BatchableContext info){ 
        // Get the ID of the AsyncApexJob representing this batch job
        // from Database.BatchableContext.
        // Query the AsyncApexJob object to retrieve the current job's information.
        AsyncApexJob a = [SELECT Id, Status, NumberOfErrors, JobItemsProcessed,
                          TotalJobItems, CreatedBy.Email FROM AsyncApexJob WHERE Id = :info.getJobId()];
  
        // Send an email to the Apex job's submitter notifying of job completion.
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();

        String[] toAddresses = new String[] {a.CreatedBy.Email};
        mail.setToAddresses(toAddresses);
        mail.setSubject('Account and contact update' + a.Status);
        mail.setPlainTextBody('The batch Apex job processed ' + a.TotalJobItems + ' batches with '+ a.NumberOfErrors + ' failures. SuccessRecordids: '+ successRecord + ' , FailRecordids: '+ failRecord);
        Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
    }   
}