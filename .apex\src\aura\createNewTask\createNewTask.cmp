<!--
 - Created by <PERSON><PERSON> on 2023-06-02.
 -->

<aura:component description="createNewTask"
                implements="flexipage:availableForAllPageTypes,force:hasRecordId,force:hasSObjectName"
                access="global" controller="caseController">
    <aura:attribute name="recordId" type="Id"/>
    <aura:attribute name="contactId" type="Id"/>
    <aura:attribute name="openTasks" type="List" />
    <aura:attribute name="columns" type="List" />

    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />

    <lightning:card title="Tasks" iconName="standard:task">
        <div class="autoHeight slds-card slds-card_boundary">
            <div class="slds-grid">
                <div class="slds-col slds-has-flexi-truncate firstHeaderRow"></div>
                <div class="slds-col slds-no-flex slds-grid slds-align-top slds-p-around_medium">
                    <lightning:button label="New Task" onclick="{!c.createTask}"/>
                </div>
            </div>
            <div class="autoHeight slds-card slds-card_boundary">
                <h2 style="margin-top: 10px; margin-bottom: 10px; padding-left: 10px;  font-weight: 500;">All related tasks</h2>
                <div class="slds-grid" style="max-width: 100%;">
                    <lightning:datatable data="{!v.openTasks}" columns="{!v.columns}"  keyField="Id" onrowaction="{! c.handleRowAction }" class="slds-scrollable_y"/>
                </div>
            </div>
        </div>
    </lightning:card>
</aura:component>
