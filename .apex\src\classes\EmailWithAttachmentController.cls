public without sharing class EmailWithAttachmentController {

	public static Boolean isValidURL(String urlString) {
		Pattern urlPattern = Pattern.compile('^(http|https)://(.*)$');
		Matcher urlMatcher = urlPattern.matcher(urlString);

		if (urlMatcher.matches()) {
			try {
				URL url = new URL(urlString);
				return true;
			} catch (Exception e) {
				return false;
			}
		} else {
			return false;
		}
	}

	@Future(callout=true)
	public static void sendEmailWithAttachment(Id shoppingCartId, Id contactId) {
		Boolean isWebUrl = false;
		Boolean hasPayment = false;
		String buyerName = '';
		String billingName = '';
		String eventImg = '';
		String paymentType = '';
		String last4Digits = '';
		String cardType = '';
		Map<String, Decimal> paymentList = new Map<String, Decimal>();
		Map<String, String> paymentListTxt = new Map<String, String>();
		Decimal subTotal = 0.00;
		Decimal taxTotal = 0.00;
		Decimal discountTotal = 0.00;
		Decimal cartTotal = 0.00;
		String subTotalTxt = '';
		String taxTotalTxt = '';
		String discountTotalTxt = '';
		String cartTotalTxt = '';
		String contactEmail = '';
		System.debug('==shoppingCartId: ' + shoppingCartId);
		System.debug('==contactId: ' + contactId);
		List<pymt__Shopping_Cart_Item__c> sci = new List<pymt__Shopping_Cart_Item__c>();
		List<ShoppingCartDetailsCtrl.attendeeWrapperClass> attendees = new List<ShoppingCartDetailsCtrl.attendeeWrapperClass>();
		String[] bccAttendees = new String[]{};

		String emailBody = '<div style="letter-spacing:596px;line-height:0;"></div><table style="min-width:100%; font-family: \'Nunito Sans\', arial, helvetica, sans-serif !important;" width="100%" bgcolor="#e0e0e0"> <tr><td><table style="margin-left:auto; margin-right:auto; border-collapse:collapse; border-spacing:0; border:0; padding:0; " cellpadding="0" cellspacing="0" border="0" class="container-table"><tr><td style="margin-left:auto;margin-right:auto;" align="center"><table style="min-width: 650px; border: none;" width="650"><tr><td align="center"><table width="600" cellpadding="0" cellspacing="0" border="0"> <tr> <td class="grid__col"><table align="center"><tr class="row_section"> <td></td> <td align="center"> <table style="border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%; color:#FFFFFF;"> <tr> <td style="line-height:12px;font-size:12px;" width="600"> <table style="line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; border:0; padding:0; ;width:100%;"> <tr> <td style="line-height:20px;font-size:16px;height:20px;text-align: center;"> <h3 style="color: #012b57;">Thank you for registering!</h3></td></tr></table> </td></tr></table></td><td></td></tr></table></td></tr><tr><td style="line-height:18px;"></td></tr><tr bgcolor="#FFFFFF"><td style="background-color:#FFFFFF;" class="grid__col"><table style="border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%;" align="left"><tr class="row_section"><td align="left" style="padding-left:20px;"><table style=" border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%; " class="" cellspacing="0" cellpadding="0" bgcolor="#FFFFFF" align="left"><tr><td style="padding: 0; text-align:left;" align="left" bgcolor="white" width="100%"><h2 style=" padding: 0; font-size: 16px; line-height: 24px; font-weight: 600; color: #012b57!important; margin-top: 20px; margin-bottom: 0;">';
		try {
			sci = [SELECT pymt__Contact__r.Name, pymt__Contact__r.Email, Special_Event__r.Name, Special_Event__r.Event_Location__c, Special_Event__r.Start_Local__c, Special_Event__r.End_Local__c, Special_Event__r.Start_Date__c, Special_Event__r.Start_Time__c, Special_Event__r.Confirmation_Email_Custom_Text__c, Special_Event__r.End_Date__c, Special_Event__r.End_Time__c, Special_Event__r.Thumbnail_Image__c, Special_Event__r.Event_Date_Time__c, Special_Event__r.evt__Web_Meeting_Join_URL__c, pymt__Payment__r.pymt__Payment_Type__c, pymt__Payment__r.pymt__Last_4_Digits__c, pymt__Payment__r.pymt__Card_Type__c, pymt__Payment__r.pymt__Billing_First_Name__c, pymt__Payment__r.pymt__Billing_Last_Name__c, CreatedDate, Event_Quantity__c, Event_Gross_Amount__c, Event_Discount_Amount__c, Event_Tax_Amount__c, Event_Total_Amount__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :shoppingCartId AND pymt__Payment_Completed__c = true];
		} catch (Exception e) {
			System.debug('==Exception: ' + e);
		}
		System.debug('==sci==: ' + sci);
		if (sci.size()>0) {
			subTotal = sci[0].Event_Gross_Amount__c;
			taxTotal = sci[0].Event_Tax_Amount__c;
			discountTotal = sci[0].Event_Discount_Amount__c;
			cartTotal = sci[0].Event_Total_Amount__c;
			System.debug('==cartTotal: ' + cartTotal);
			System.debug('==subTotal: ' + subTotal);
			System.debug('==taxTotal: ' + taxTotal);
			System.debug('==discountTotal: ' + discountTotal);
			if (subTotal > 0) {
				subTotalTxt = '$' + String.valueOf(subTotal.setScale(2));
			} else {
				subTotalTxt = '';
			}
			if (taxTotal > 0) {
				taxTotalTxt = '$' + String.valueOf(taxTotal.setScale(2));
			} else {
				taxTotalTxt = '';
			}
			if (discountTotal > 0) {
				discountTotalTxt = '$' + String.valueOf(discountTotal.setScale(2));
			} else {
				discountTotalTxt = '';
			}
			if (cartTotal > 0) {
				cartTotalTxt = '$' + String.valueOf(cartTotal.setScale(2));
			} else {
				cartTotalTxt = '';
			}
			if (sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c != null) {
				if ((Boolean)isValidURL(sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c)) {
					isWebUrl = true;
				}else{
					isWebUrl = false;
				}
			}
			billingName = sci[0].pymt__Payment__r.pymt__Billing_First_Name__c + ' ' + sci[0].pymt__Payment__r.pymt__Billing_Last_Name__c;
			String OrderTotal = '';
			String ticketTotal = '';
			String paymentDetails = '';
			paymentType = sci[0].pymt__Payment__c == null ? 'No Payment' : sci[0].pymt__Payment__r.pymt__Payment_Type__c;
			last4Digits = sci[0].pymt__Payment__c == null ? '' : sci[0].pymt__Payment__r.pymt__Last_4_Digits__c;
			cardType = sci[0].pymt__Payment__c == null ? '' : sci[0].pymt__Payment__r.pymt__Card_Type__c;
			String paymentDate = sci[0].CreatedDate.format('MM/dd/yyyy hh:mm a');
			if (paymentType == 'No Payment') {
				paymentDetails = 'Free Order';
				hasPayment = false;
			}else if (paymentType == 'Credit Card') {
				paymentDetails = 'Paid by ' + cardType + ' ending in ' + last4Digits;
				hasPayment = true;
			} else {
				paymentDetails = 'Paid by ' + paymentType;
				hasPayment = true;
			}
			List<Shopping_Cart_Item_Details__c> orders = [SELECT Item_Quantity__c, Item_Total_Amount__c, CreatedDate, Attendee__r.Check_In_QR_Code__c, Attendee__r.Check_In_QR_Code_URL__c, Attendee__r.evt__Reg_First_Name__c, Attendee__r.evt__Reg_Last_Name__c, Attendee__r.evt__Reg_Email__c, Attendee__r.evt__Event_Fee__r.Name FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :sci[0].Id AND Void_ticket__c = false];
			if (orders.size()>0){
				Integer i = 1;
				Decimal total = 0;
				for (Shopping_Cart_Item_Details__c sc : orders) {
					ShoppingCartDetailsCtrl.attendeeWrapperClass attendee = new ShoppingCartDetailsCtrl.attendeeWrapperClass();
					attendee.num = String.valueOf(i);
					attendee.attendeeName = sc.Attendee__r.evt__Reg_First_Name__c + ' ' + sc.Attendee__r.evt__Reg_Last_Name__c;
					attendee.ticketType = sc.Attendee__r.evt__Event_Fee__r.Name;
					attendee.attendeeEmail = sc.Attendee__r.evt__Reg_Email__c;
					total += sc.Item_Total_Amount__c;
					if (sc.Item_Total_Amount__c > 0){
						attendee.ticketPrice = '$'+String.valueOf(sc.Item_Total_Amount__c.setScale(2));
					} else {
						attendee.ticketPrice = 'Free';
					}
					attendees.add(attendee);
					String itemName = sc.Attendee__r.evt__Event_Fee__r.Name;
					Decimal price = sc.Item_Total_Amount__c;
					if (paymentList.containsKey(itemName)) {
						paymentList.put(itemName, paymentList.get(itemName) + price);
					} else {
						paymentList.put(itemName, price);
					}
					i++;
				}
				if (paymentList.size() > 0) {
					for (String key : paymentList.keySet()) {
						Decimal value = paymentList.get(key);
						paymentListTxt.put(key, '$' + String.valueOf(value.setScale(2)));
					}
				}
				if (total > 0){
					OrderTotal = '$'+String.valueOf(total.setScale(2));
				} else {
					OrderTotal = 'Free';
				}
				ticketTotal = String.valueOf(orders.size());
			}

			buyerName = sci[0].pymt__Contact__r.Name;
			eventImg = sci[0].Special_Event__r.Thumbnail_Image__c;
			if (eventImg == null || eventImg == '') {
				eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
			}else if (eventImg.contains('<img')){
				Pattern imgSrcPattern = Pattern.compile('<img[^>]*src="([^"]*)');
				Matcher matcher = imgSrcPattern.matcher(eventImg);
				while (matcher.find()) {
					eventImg = matcher.group(1);
				}
				eventImg = eventImg.replaceAll('amp;', '');
			}else{
				eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
			}
			if (eventImg.contains('uat')) {
				eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
			}else if (eventImg.contains('rotmancrm')) {
				eventImg = eventImg.replace('rotmancrm.file.force.com', 'community.rotman.utoronto.ca/events');
			}
			contactEmail = sci[0].pymt__Contact__r.Email;
			emailBody = emailBody + sci[0].Special_Event__r.Name;
			emailBody = emailBody +
					'</h2></td></tr><tr><td style="line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"> </td> </tr> <tr bgcolor="white"> <td style="padding-right: 15px; text-align:left;" align="left" width="100%"><img src="' +
					eventImg +
					'" border="0" width="99.3%" height="auto"/></td></tr><tr><td style="line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"></td></tr><tr><td class="" style="padding: 0; text-align:left;" align="left" bgcolor="white" width="100%"></td></tr><tr><td align="left" width="100%"> <table style="border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%; "> <tr class="row_section"> <td width="26" style="vertical-align: top;"><img src="https://cdn.evbstatic.com/s3-build/perm_001/17c084/django/images/emails_2018_rebrand/<EMAIL>" title="date" alt="" style="padding-top: 4px;" border="0" width="18" height="18" class=""/></td><td><table><tr style="" bgcolor="white"><td align="left" bgcolor="white" width="100%" height="22px"><span style=" font-weight: normal; margin: 4px 0; font-size: 14px; line-height: 24px; color:#495057;font-weight:normal;text-decoration:none!important;cursor:text;">';
			emailBody = emailBody + sci[0].Special_Event__r.Event_Date_Time__c;
			emailBody = emailBody +	'</span></td></tr></table></td></tr></table></td></tr>';
			if (sci[0].Special_Event__r.Event_Location__c != null && sci[0].Special_Event__r.Event_Location__c != '') {
				emailBody = emailBody + '<tr><td style="padding: 0; text-align:left;" align="left" bgcolor="white" width="100%"> <table style=" border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%; "><tr class="row_section" style=""> <td width="26" style="vertical-align: top;"><img src="https://cdn.evbstatic.com/s3-build/perm_001/02618c/django/images/emails_2018_rebrand/<EMAIL>" title="date" alt="" style="padding-top: 4px;" border="0" width="12" height="16" class=""/></td><td style=""><table><tr><td class="" style="padding: 0; font-size: 15px; line-height: 21px; text-align:left;" align="left" bgcolor="white" width="100%" height="22px"><span style="margin: 4px 0; font-size: 14px; line-height: 24px; color:#495057;font-weight:normal;">';
				emailBody = emailBody + sci[0].Special_Event__r.Event_Location__c +'</span></td></tr></table></td></tr></table> </td> </tr><tr><td width="600" height="12"></td></tr>';
			}
			if (sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c != null && sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c != '') {
				emailBody = emailBody +
						'<tr><td align="left" width="100%"><table style=" border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%; "> <tr class="row_section" style=""><td width="26" style="vertical-align: top;"><img src="https://cdn.evbstatic.com/s3-build/perm_001/17c084/django/images/emails_2018_rebrand/<EMAIL>" title="date" alt="" style="padding-top: 4px;" border="0" width="12" height="12" class=""/></td><td style=""><table><tr style="" bgcolor="white"><td style=" padding: 0; font-size: 15px; line-height: 21px; text-align:left;" align="left" bgcolor="white" width="100%" height="22px"><span style="margin: 4px 0; font-size: 14px; line-height: 24px; color:#495057;font-weight:normal;">';
				if (isWebUrl) {
					emailBody = emailBody + '<a href="' + sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c +
							'">Web Meeting Join URL</a>';
				} else {
					emailBody = emailBody + sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c;
				}
				emailBody = emailBody + '</span></td></tr></table></td></tr></table></td></tr>';
			}
			if (sci[0].Special_Event__r.Confirmation_Email_Custom_Text__c != null && sci[0].Special_Event__r.Confirmation_Email_Custom_Text__c != '') {
				emailBody = emailBody + '<tr><td style="line-height:18px;font-size:18px;border-top-left-radius:0;border-top-right-radius:0; background-color:#FFFFFF;" width="600" height="18"> <table style="border-collapse:collapse; border-spacing:0; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0"> <tr> <td style="font-size: 15px; line-height: 21px; padding: 5px 25px; text-align: justify; "> <p>';
				emailBody = emailBody+ sci[0].Special_Event__r.Confirmation_Email_Custom_Text__c;
				emailBody = emailBody + '</p></td></tr><tr><td></td></tr></table></td></tr>';
			}
			emailBody = emailBody + '</p></td></tr><tr><td></td></tr></table></td></tr><tr><td class="grid__col"><table style="border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%; " align="left"><tr class="row_section" style="" bgcolor="#FFFFFF"><td width="20" bgcolor="#FFFFFF" align="left"></td> <td class="" style="text-align:left;background-color:#FFFFFF;" align="left"><table style="border-collapse:collapse; border-spacing:0; border:0; padding:0; width:100%; " class="" cellspacing="0" cellpadding="0" bgcolor="#FFFFFF" align="left"> <tr> <td align="left" width="100%"> <table cellpadding="0" cellspacing="0" border="0" class="no_text_resize" width="100%" style="border-spacing: 0; border-collapse: collapse; "> <tr> <td colspan="3"><h2 style="padding: 0; margin: 12px 0 0 0; font-size: 17px; line-height: 24px; font-weight: 700; color: #012b57!important; margin-top: 10px;" class=""> Order Summary </h2></td></tr><tr><td style="line-height:6px;font-size:6px;background-color:#FFFFFF;" width="600" height="6"></td></tr><tr><td colspan="3"></td></tr><tr><td width="600" height="8"> </td> </tr> <tr> <td colspan="3"> <table cellpadding="0" cellspacing="0" border="0" width="100%"> <tbody>';
			if (hasPayment) {
				emailBody = emailBody + '<tr> <td width="45%" valign="top" style="padding-top: 5px; padding-bottom: 5px;"><span style="font-weight: bold; margin: 4px 0; font-size: 14px; line-height: 21px; color:#495057;" class="">HST Number</span></td><td width="55%" valign="top" align="right"><span style="padding-right:20px; margin: 4px 0; font-size: 12px; line-height: 21px;" class="">R108162330</span> </td> </tr>';
			}
			emailBody = emailBody + '<tr> <td width="45%" valign="top" style="padding-top: 5px; padding-bottom: 5px;"><span style="font-weight: bold; margin: 4px 0; font-size: 14px; line-height: 21px; color:#495057;" class=""> Purchase date </span></td><td width="55%" valign="top" align="right"><span style="padding-right:20px; margin: 4px 0; font-size: 12px; line-height: 21px;" class="">' + paymentDate +'</span></td></tr><tr> <td width="45%" valign="top" style="padding-top: 5px; padding-bottom: 5px;"><span style="font-weight: bold; margin: 4px 0; font-size: 14px; line-height: 21px; color:#495057;" class=""> Payment method </span></td><td width="55%" valign="top" align="right"><span style="padding-right:20px; margin: 4px 0; font-size: 12px; line-height: 21px;" class="">'+paymentType+'</span></td></tr><tr><td width="45%" valign="top" style="padding-top: 5px; padding-bottom: 5px;"><span style="font-weight: bold; margin: 4px 0; font-size: 14px; line-height: 21px; color:#495057;" class=""> Buyer Name </span> </td> <td width="55%" valign="top" align="right"><span style="padding-right:20px; margin: 4px 0; font-size: 12px; line-height: 21px;">'+
					buyerName +	'</span></td></tr>';
			if (hasPayment) {
				emailBody = emailBody +	'<tr> <td width="45%" valign="top" style="padding-top: 5px; padding-bottom: 5px;"><span style="font-weight: bold; margin: 4px 0; font-size: 14px; line-height: 21px; color:#495057;"> Billing Name </span> </td> <td width="55%" valign="top" align="right"><span style="padding-right:20px; margin: 4px 0; font-size: 12px; line-height: 21px;" class="">' + billingName +'</span></td></tr>';
			}
			emailBody = emailBody + '<tr><td width="45%" valign="top" style="padding-top: 5px; padding-bottom: 5px;"><span style="font-weight: bold; margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;" class="">Ticket total </span> </td> <td width="55%" valign="top" align="right"><span style=" padding-right:20px; margin: 4px 0; font-size: 15px; line-height: 21px;">' + ticketTotal + '</span></td></tr>';
			emailBody = emailBody + '<tr><td width="45%" valign="top" style="padding-top: 5px; padding-bottom: 5px;"><span style="font-weight: bold; margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;" class=""> Payment total </span> </td> <td width="55%" valign="top" align="right"><span style="padding-right:20px; margin: 4px 0; font-size: 12px; line-height: 21px; ">' + paymentDetails + '</span></td></tr><tr><td style="padding-top: 10px; padding-bottom: 10px;"> </td> </tr>';
			if (hasPayment) {
				emailBody = emailBody +'<tr><td width="100%" valign="top" colspan="2"> <div class="invoice-body" style="margin-right: 20px;"> <table class="table" style="border-collapse: collapse;width: 100%;"><thead><tr><th style="font-size: 16px;border: 1px solid #dcdcdc;background-color: #eeeeee;padding: 5px;">Item Description </th><th style="font-size: 16px;border: 1px solid #dcdcdc;background-color: #eeeeee;padding: 5px;">Amount </th></tr></thead><tbody>';
				for (String key : paymentListTxt.keySet()) {
					String value = paymentListTxt.get(key);
					System.debug('Key: ' + key + ', Value: ' + value);
					emailBody = emailBody + '<tr><td style="font-size: 14px;border: 1px solid #dcdcdc;text-align: left;background-color: #fff;padding:5px">';
					emailBody = emailBody + key;
					emailBody = emailBody + '</td><td style="font-size: 14px;border: 1px solid #dcdcdc;text-align: right;background-color: #fff;padding:5px">';
					emailBody = emailBody + value + '</td></tr>';
				}
				emailBody = emailBody + '</tbody></table> <div class="flex-table" style="display: flex;"><div class="flex-column" style="width: 100%;box-sizing: border-box;"></div><div class="flex-column" style="width: 100%;box-sizing: border-box;"><table class="table-subtotal" style="border-collapse: collapse;box-sizing: border-box;width: 100%;margin-top: 10px;"><tbody><tr> <td class="invoice-total-amount" style="font-weight: bold;color: #0F172A;text-align:right;font-size: 16px;"> Subtotal </td> <td class="invoice-total-amount" style="font-weight: bold;color: #0F172A;text-align:right;font-size: 16px;">';
				emailBody = emailBody + subTotalTxt;
				emailBody = emailBody + '</td></tr> <tr> <td class="invoice-total-amount" style="font-weight: bold;color: #0F172A;text-align:right;font-size: 16px;"> Tax </td> <td class="invoice-total-amount" style="font-weight: bold;color: #0F172A;text-align:right;font-size: 16px;"> ';
				emailBody = emailBody + taxTotalTxt;
				emailBody = emailBody + '</td> </tr> <tr><td class="invoice-total-amount" style="font-weight: bold;color: #0F172A;text-align:right;font-size: 16px;"> Discount </td><td class="invoice-total-amount" style="font-weight: bold;color: #0F172A;text-align:right;font-size: 16px;">';
				emailBody = emailBody + discountTotalTxt;
				emailBody = emailBody + '</td></tr></tbody></table></div></div><div class="invoice-total-amount" style="font-weight: bold;color: #0F172A;text-align: right;font-size: 16px;"><p>Total : ';
				emailBody = emailBody + cartTotalTxt;
				emailBody = emailBody + '</p></div></div></td></tr>';
			}

			emailBody = emailBody +'<tr><td style="line-height:6px;font-size:6px;background-color:#FFFFFF;" width="600" height="6"> <table style="line-height:6px;font-size:6px;height:6px; border-collapse:collapse; border-spacing:0; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="6"> <tr> <td style="line-height:12px;font-size:12px;height:12px;" height="12" bgcolor=""></td> </tr> </table> </td> </tr> <tr> <td colspan="3"></td> </tr><tr> <td style="line-height:6px;font-size:6px;background-color:#FFFFFF;" colspan="3"> <table style="line-height:6px;font-size:6px;height:6px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="6"> <tr> <td style="line-height:12px;font-size:12px;height:12px;" height="12" colspan="3"> <h2 style="padding: 0; margin: 12px 0 12px 0; font-size: 17px; line-height: 24px; font-weight: 600; color: #012b57!important; margin-top: 0;" class=""> Attendees </h2> </td> </tr>';
			for (ShoppingCartDetailsCtrl.attendeeWrapperClass att : attendees) {
				bccAttendees.add(att.attendeeEmail);
				emailBody = emailBody +
						'<tr> <td width="35%" valign="top"><span style="margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;;font-weight:normal;">' +
						att.attendeeName +
						'</span> </td> <td width="35%" valign="top"><span style="margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:bold;" class=""> ' +
						att.ticketType +
						'</span> </td> <td width="30%" align="right" valign="top" style="text-align: right; padding-right:20px;"> <span style="margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:normal;">' +
						att.ticketPrice + '</span> </td> </tr> </table> </td> </tr> <tr> <td colspan="3"></td> </tr> </table> </td> </tr> <tr> <td style="padding-top: 15px; padding-bottom: 15px;"></td> </tr> </table> </td> </tr> </table> </td> <td></td> </tr> </table> </td> </tr>';
			}
			emailBody = emailBody +
					'<tr> <td style="padding-top: 5px; padding-bottom:5px;"> </td> </tr> <tr> <td class="footer-content" style=" padding: 0;" align="center" width="100%"><span style="margin: 4px 0; font-size: 12px; color:#555555;"> <a style="text-decoration:none;color:#e20778;" href="https://www.rotman.utoronto.ca/">Rotman School of Management</a> | 105 St. George Street, | Toronto, Ontario M5S 3E6 </span> </td> </tr> <tr> <td class="footer-content" style="padding: 0; text-align:center;" align="center" bgcolor="#e0e0e0" width="100%"><span style="font-weight: normal; margin: 4px 0; font-size: 12px; color:#555555;" class="">Phone: (416)978-6122 / (416)978-6119 Fax: (416)978-1373 </span> </td> </tr> </table> </td> <td></td> </tr> </table> </td> </tr> </table> </td> </tr></table>';

			List<Messaging.EmailFileAttachment> myAttachments = new List<Messaging.EmailFileAttachment>();
			EmailTemplate et = [SELECT Id FROM EmailTemplate WHERE Name = 'Event Registration Confirmation'];
			//String id = ApexPages.currentPage().getParameters().get('shoppingCartId');

			// Generate the PDF
			PageReference pdfPage = Page.EventRegisterEmailAttachment;
			pdfPage.getParameters().put('shoppingCartId', shoppingCartId);
			Blob pdfBlob;
			if (Test.isRunningTest()) {
				pdfBlob = Blob.valueOf('Unit Test');
			}else {
				pdfBlob = pdfPage.getContentAsPDF();
			}

			// Create the email
			Messaging.EmailFileAttachment attachment = new Messaging.EmailFileAttachment();
			attachment.setContentType('application/pdf');
			attachment.setFileName('RotmanEventRegistration.pdf');
			attachment.setBody(pdfBlob);
			myAttachments.add(attachment);

			//Date startDate = sci[0].Special_Event__r.Start_Date__c;
			//String dateString1 = startDate.format();
			//Date endDate = sci[0].Special_Event__r.End_Date__c;
			//String dateString2 = endDate.format();

			//String[] timeComponents1 = sci[0].Special_Event__r.Start_Time__c.split(':');
			//Integer hours1 = Integer.valueOf(timeComponents1[0]);
			//Integer minutes1 = Integer.valueOf(timeComponents1[1].substring(0, 1));
			//Integer seconds1 = Integer.valueOf('00');
			//DateTime eventStartDate = DateTime.newInstance(startDate.year(), startDate.month(), startDate.day(), hours1, minutes1, seconds1);
			DateTime eventStartDate = sci[0].Special_Event__r.Start_Local__c;

			//startDateTimeString = dateString1 + ' ' + sci[0].Special_Event__r.Start_Time__c;
			//System.debug('startDateTimeString: ' + startDateTimeString);
			//Datetime eventStartDate = Datetime.parse(startDateTimeString);
			//Datetime eventStartDate = Datetime.parse();
			//String endDateTimeString = dateString2 + ' ' + sci[0].Special_Event__r.End_Time__c;
			//String[] timeComponents2 = sci[0].Special_Event__r.End_Time__c.split(':');
			//Integer hours2 = Integer.valueOf(timeComponents2[0]);
			//Integer minutes2 = Integer.valueOf(timeComponents2[1].substring(0, 1));
			//Integer seconds2 = Integer.valueOf('00');
			//DateTime eventEndDate = DateTime.newInstance(endDate.year(), endDate.month(), endDate.day(), hours2, minutes2, seconds2);
			DateTime eventEndDate =	sci[0].Special_Event__r.End_Local__c;

			//Datetime eventEndDate = Datetime.parse(endDateTimeString);
			String eventTitle = 'Rotman Event Registration';
			String eventSubject = sci[0].Special_Event__r.Name;
			String eventLocation = sci[0].Special_Event__r.Event_Location__c;

			Messaging.EmailFileAttachment eventInvite = new Messaging.EmailFileAttachment();
			eventInvite.filename = 'EventInvite.ics';
			eventInvite.ContentType = 'text/calendar; charset=utf-8; method=REQUEST';
			eventInvite.inline = true;
			if (!Test.isRunningTest()) {
				eventInvite.body = CalendarService.createInvite(eventStartDate, eventEndDate, eventTitle, eventSubject, eventLocation);
			}
			//eventInvite.body = CalendarService.createInvite(eventStartDate, eventEndDate, eventTitle, eventSubject, eventLocation);
			myAttachments.add(eventInvite);

			Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
			email.setTemplateId(et.Id);
			email.setSaveAsActivity(false);
			email.setSenderDisplayName('Rotman Events');
			//email.setTargetObjectId('a1JG1000000EvHlMAK');
			email.setWhatId(contactId);
			//email.setWhatId('0032B000003h5jZQAQ');
			//email.setWhatId('a1JG1000000EvHlMAK');
			email.setSubject('Please see the events for which you\'ve registered for listed below');
			email.setToAddresses(new String[] { contactEmail });
			//email.setToAddresses(new String[] { '<EMAIL>' });
			email.setBccAddresses(bccAttendees);
			email.setHtmlBody(emailBody);
			//email.setFileAttachments(new Messaging.EmailFileAttachment[] { attachment });
			email.setFileAttachments(myAttachments);

			// Send the email
			if (!Test.isRunningTest()) {
				Messaging.sendEmail(new Messaging.SingleEmailMessage[] { email });
			}
		}
	}

	@Future(callout=true)
	public static void sendCancelledEmailWithAttachment(Id shoppingCartId, Id contactId, String[] lstAttIds) {
		Boolean isWebUrl = false;
		System.debug('==shoppingCartId: ' + shoppingCartId);
		System.debug('==contactId: ' + contactId);
		System.debug('==lstAttIds: ' + lstAttIds);
		List<pymt__Shopping_Cart_Item__c> sci = new List<pymt__Shopping_Cart_Item__c>();
		List<ShoppingCartDetailsCtrl.attendeeWrapperClass> attendees = new List<ShoppingCartDetailsCtrl.attendeeWrapperClass>();
		String attParam = String.join(lstAttIds, ',');
		System.debug('==attParam: ' + attParam);
		String[] bccAttendees = new String[]{};

		String emailBody = '<div style="letter-spacing:596px;line-height:0;mso-hide:all"></div><table style="min-width:100%;" width="100%" bgcolor="#e0e0e0"> <tr> <td> <table style=" margin-left:auto; margin-right:auto; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; " cellpadding="0" cellspacing="0" border="0" class="container-table"> <tr> <td style="margin-left:auto;margin-right:auto;" align="center" bgcolor="#e0e0e0"> <table style="min-width: 650px; border: none;" width="650"> <tr> <td align="center" bgcolor="#e0e0e0"> <table style="width:600px;" width="600" cellpadding="0" cellspacing="0" border="0" bgcolor="#e0e0e0"> <tbody> <tr bgcolor="#e0e0e0"> <td style="background-color:#e0e0e0;" bgcolor="#e0e0e0" class="grid__col"> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; " align="center"> <tr class="row_section" style="" bgcolor="#e0e0e0"> <td width="30" bgcolor="#e0e0e0" align="center"></td> <td class="" style="text-align:center;background-color:#e0e0e0;" align="center"> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; color:#FFFFFF;" class="" cellspacing="0" cellpadding="0" bgcolor="#e0e0e0" align="center"> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;background-color:#e0e0e0;" width="600" height="12"> <table style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="12"> <tr> <td style="mso-line-height-rule:exactly;line-height:24px;font-size:24px;height:24px;" height="24" bgcolor=""> </td> </tr> </table> </td> </tr> <tr style="" bgcolor="#e0e0e0"> <td class="" style=" padding: 0; text-align:center;" align="center" bgcolor="#e0e0e0" width="100%" height=""></td> </tr> </table> </td> <td width="20" bgcolor="#e0e0e0"></td> </tr> </table> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;background-color:#FFFFFF; border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0; " width="600" height="18"> <table style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="18"> <tr> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px;" height="18" bgcolor=""></td> </tr> <tr bgcolor="#FFFFFF"> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px;" height="18"></td> </tr> </table> </td> </tr> <tr bgcolor="#FFFFFF"> <td style="background-color:#FFFFFF;" bgcolor="#FFFFFF" class="grid__col"> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; " align="left"> <tr class="row_section" style="" bgcolor="#FFFFFF"> <td width="20" bgcolor="#FFFFFF" align="left"></td> <td class="" style="text-align:left;background-color:#FFFFFF;" align="left"> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; " class="" cellspacing="0" cellpadding="0" bgcolor="#FFFFFF" align="left"> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""><h2 style=" padding: 0; margin: 12px 0 0 0; font-size: 23px; line-height: 32px; font-weight: normal; font-weight: 500; color: #012b57!important; margin-top: 0;" class="">';

		try {
			sci = [SELECT pymt__Contact__r.Name, pymt__Contact__r.Email, Special_Event__r.Name, Special_Event__r.Event_Location__c, Special_Event__r.Start_Local__c, Special_Event__r.End_Local__c, Special_Event__r.Start_Date__c, Special_Event__r.Start_Time__c, Special_Event__r.End_Date__c, Special_Event__r.End_Time__c, Special_Event__r.Thumbnail_Image__c, Special_Event__r.Event_Date_Time__c, Special_Event__r.evt__Web_Meeting_Join_URL__c, Special_Event__r.Confirmation_Email_Custom_Text__c, pymt__Payment__r.pymt__Payment_Type__c, pymt__Payment__r.pymt__Last_4_Digits__c, pymt__Payment__r.pymt__Card_Type__c, CreatedDate FROM pymt__Shopping_Cart_Item__c WHERE Id = :shoppingCartId];
		} catch (Exception e) {
			System.debug('==Exception: ' + e);
		}
		if (sci.size()>0) {
			if (sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c != null) {
				if ((Boolean)isValidURL(sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c)) {
					isWebUrl = true;
				}else{
					isWebUrl = false;
				}
			}
			String OrderTotal = '';
			String ticketTotal = '';
			String paymentDetails = '';
			String paymentType = sci[0].pymt__Payment__c == null ? 'No Payment' : sci[0].pymt__Payment__r.pymt__Payment_Type__c;
			String last4Digits = sci[0].pymt__Payment__c == null ? '' : sci[0].pymt__Payment__r.pymt__Last_4_Digits__c;
			String cardType = sci[0].pymt__Payment__c == null ? '' : sci[0].pymt__Payment__r.pymt__Card_Type__c;
			String paymentDate = sci[0].CreatedDate.format('MM/dd/yyyy hh:mm a');
			if (paymentType == 'No Payment') {
				paymentDetails = 'Free Order';
			}else if (paymentType == 'Credit Card') {
				paymentDetails = 'Paid by ' + paymentType + ' ending in ' + last4Digits;
			} else {
				paymentDetails = 'Paid by ' + paymentType;
			}
			List<Shopping_Cart_Item_Details__c> orders = [SELECT Item_Total_Amount__c, CreatedDate, Attendee__c, Attendee__r.Check_In_QR_Code__c, Attendee__r.Check_In_QR_Code_URL__c, Attendee__r.evt__Reg_First_Name__c, Attendee__r.evt__Reg_Last_Name__c, Attendee__r.evt__Reg_Email__c, Attendee__r.evt__Event_Fee__r.Name FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :sci[0].Id AND Void_ticket__c = true];
			System.debug('==orders==: ' + orders);
			if (orders.size()>0){
				Integer i = 1;
				Decimal total = 0;
				for (Shopping_Cart_Item_Details__c sc : orders) {
					ShoppingCartDetailsCtrl.attendeeWrapperClass attendee = new ShoppingCartDetailsCtrl.attendeeWrapperClass();
					attendee.Id = sc.Attendee__c;
					attendee.num = String.valueOf(i);
					attendee.attendeeName = sc.Attendee__r.evt__Reg_First_Name__c + ' ' + sc.Attendee__r.evt__Reg_Last_Name__c;
					attendee.ticketType = sc.Attendee__r.evt__Event_Fee__r.Name;
					attendee.attendeeEmail = sc.Attendee__r.evt__Reg_Email__c;
					total += sc.Item_Total_Amount__c;
					if (sc.Item_Total_Amount__c > 0){
						attendee.ticketPrice = '$'+String.valueOf(sc.Item_Total_Amount__c.setScale(2));
					} else {
						attendee.ticketPrice = 'Free';
					}
					attendees.add(attendee);
					i++;
				}
				if (total > 0){
					OrderTotal = '$'+String.valueOf(total.setScale(2));
				} else {
					OrderTotal = 'Free';
				}
				ticketTotal = String.valueOf(orders.size());
			}

			String buyerName = sci[0].pymt__Contact__r.Name;
			String eventImg = sci[0].Special_Event__r.Thumbnail_Image__c;
			if (eventImg == null || eventImg == '') {
				eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
			}else if (eventImg.contains('<img')){
				Pattern imgSrcPattern = Pattern.compile('<img[^>]*src="([^"]*)');
				Matcher matcher = imgSrcPattern.matcher(eventImg);
				while (matcher.find()) {
					eventImg = matcher.group(1);
				}
				eventImg = eventImg.replaceAll('amp;', '');
			}else{
				eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
			}
			if (eventImg.contains('uat')) {
				eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
			}else if (eventImg.contains('rotmancrm')) {
				eventImg = eventImg.replace('rotmancrm.file.force.com', 'community.rotman.utoronto.ca/events');
			}
			String contactEmail = sci[0].pymt__Contact__r.Email;
			emailBody = emailBody + sci[0].Special_Event__r.Name;
			emailBody = emailBody + '</h2></td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"> <table style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="12"> <tr> <td style="mso-line-height-rule:exactly;line-height:24px;font-size:24px;height:24px;" height="24" bgcolor=""></td> </tr> </table> </td> </tr> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""><img src="'+ eventImg+'" style="" border="0" width="100%" height="auto" class=""/></td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"> <table style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="12"> <tr> <td style="mso-line-height-rule:exactly;line-height:24px;font-size:24px;height:24px;" height="24" bgcolor=""></td> </tr> </table> </td> </tr> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""></td> </tr> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"> <table style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="12"> <tr> <td style="mso-line-height-rule:exactly;line-height:24px;font-size:24px;height:24px;" height="24" bgcolor=""></td> </tr> </table> </td> </tr> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; "> <tr class="row_section" style=""> <td width="26" style="vertical-align: top;"><img src="https://cdn.evbstatic.com/s3-build/perm_001/17c084/django/images/emails_2018_rebrand/<EMAIL>" title="date" alt="" style="padding-top: 4px;" border="0" width="18" height="18" class=""/></td> <td style=""> <table> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height="22px"><span style=" font-weight: normal; margin: 4px 0; font-size: 17px; line-height: 24px; font-family: " Helvetica Neue", Helvetica, Arial, sans-serif; color:#495057;font-weight:normal;text-decoration:none !important;cursor:text;" class="">';
			emailBody = emailBody + sci[0].Special_Event__r.Event_Date_Time__c;
			emailBody = emailBody +	'</span> </td> </tr> </table> </td> </tr> </table> </td> </tr> </td> </tr> ';
			if (sci[0].Special_Event__r.Event_Location__c != null && sci[0].Special_Event__r.Event_Location__c != '') {
				emailBody = emailBody + '<tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"> <table style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="12"> <tr> <td style="mso-line-height-rule:exactly;line-height:24px;font-size:24px;height:24px;" height="24" bgcolor=""></td> </tr> </table> </td> </tr> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; "> <tr class="row_section" style=""> <td width="26" style="vertical-align: top;"><img src="https://cdn.evbstatic.com/s3-build/perm_001/02618c/django/images/emails_2018_rebrand/<EMAIL>" title="date" alt="" style="padding-top: 4px;" border="0" width="12" height="16" class=""/></td> <td style=""> <table> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; font-size: 15px; line-height: 21px; text-align:left;" align="left" bgcolor="white" width="100%" height="22px"><span style=" font-weight: normal; margin: 4px 0; font-size: 17px; line-height: 24px; font-family: " Helvetica Neue", Helvetica, Arial, sans-serif; color:#495057;font-weight:normal;" class="">';
				emailBody = emailBody + sci[0].Special_Event__r.Event_Location__c;
			}else{
				emailBody = emailBody + '<tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"> <table style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="12"> <tr> <td style="mso-line-height-rule:exactly;line-height:24px;font-size:24px;height:24px;" height="24" bgcolor=""></td> </tr> </table> </td> </tr> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; "> <tr class="row_section" style=""> <td width="26" style="vertical-align: top;"></td> <td style=""> <table> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; font-size: 15px; line-height: 21px; text-align:left;" align="left" bgcolor="white" width="100%" height="22px"><span style=" font-weight: normal; margin: 4px 0; font-size: 17px; line-height: 24px; font-family: " Helvetica Neue", Helvetica, Arial, sans-serif; color:#495057;font-weight:normal;" class="">';
				emailBody = emailBody + '';
			}
			if (sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c != null && sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c != '') {
				emailBody = emailBody +
						'</span> </td> </tr> </table> </td> </tr> </table> </td> </tr><tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;background-color:#FFFFFF;" width="600" height="12"> <table style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="12"> <tr> <td style="mso-line-height-rule:exactly;line-height:24px;font-size:24px;height:24px;" height="24" bgcolor=""></td> </tr> </table> </td> </tr> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; "> <tr class="row_section" style=""> <td width="26" style="vertical-align: top;"><img src="https://cdn.evbstatic.com/s3-build/perm_001/17c084/django/images/emails_2018_rebrand/<EMAIL>" title="date" alt="" style="padding-top: 4px;" border="0" width="12" height="12" class=""/></td> <td style=""> <table> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; font-size: 15px; line-height: 21px; text-align:left;" align="left" bgcolor="white" width="100%" height="22px"><span style=" font-weight: normal; margin: 4px 0; font-size: 17px; line-height: 24px; font-family: " Helvetica Neue", Helvetica, Arial, sans-serif; color:#495057;font-weight:normal;" class="">';
				if (isWebUrl) {
					emailBody = emailBody + '<a href="' + sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c +
							'">Web Meeting Join URL</a>';
				} else {
					emailBody = emailBody + sci[0].Special_Event__r.evt__Web_Meeting_Join_URL__c;
				}
				emailBody = emailBody + '</span> </td> </tr> </table> </td> </tr> </table> </td> </tr>';
			}
			emailBody = emailBody +
					'<tr> <td style="mso-line-height-rule:exactly;line-height:8px;font-size:8px;background-color:#FFFFFF;" width="600" height="8"> <table style="mso-line-height-rule:exactly;line-height:8px;font-size:8px;height:8px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="8"> <tr> <td style="mso-line-height-rule:exactly;line-height:16px;font-size:16px;height:16px;" height="16" bgcolor=""></td> </tr> </table> </td> </tr> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:center;" align="center" bgcolor="white" width="100%" height=""> </td> </tr> </table> </td> <td width="20" bgcolor="#FFFFFF"></td> </tr> </table> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;background-color:#FFFFFF; border-top-left-radius:0;border-top-right-radius:0;" width="600" height="18"> <table style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="18"> <tr bgcolor="#FFFFFF"> <td style="font-size: 14px; padding: 5px 25px; text-align: justify; ">';
			if (sci[0].Special_Event__r.Confirmation_Email_Custom_Text__c != null && sci[0].Special_Event__r.Confirmation_Email_Custom_Text__c != '') {
				emailBody = emailBody + sci[0].Special_Event__r.Confirmation_Email_Custom_Text__c;
			}else{
				emailBody = emailBody + '';
			}
			emailBody = emailBody + '</td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px;" height="18" bgcolor=""></td> </tr> </table> </td> </tr> <tr bgcolor="#FFFFFF"> <td style="background-color:#FFFFFF;" bgcolor="#FFFFFF" class="grid__col"> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; " align="left"> <tr class="row_section" style="" bgcolor="#FFFFFF"> <td width="20" bgcolor="#FFFFFF" align="left"></td> <td class="" style="text-align:left;background-color:#FFFFFF;" align="left"> <table style=" border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; width:100%; " class="" cellspacing="0" cellpadding="0" bgcolor="#FFFFFF" align="left"> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height=""> <table cellpadding="0" cellspacing="0" border="0" class="no_text_resize" width="100%" style="border-spacing: 0; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> <tr> <td colspan="3"><h2 style=" padding: 0; margin: 12px 0 0 0; font-size: 23px; line-height: 32px; font-weight: normal; font-weight: 500; color: #012b57!important; margin-top: 0;" class=""> Order Summary </h2> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;background-color:#FFFFFF;" width="600" height="6"> <table style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;height:6px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="6"> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px;" height="12" bgcolor=""></td> </tr> </table> </td> </tr> <tr> <td colspan="3"></td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:8px;font-size:8px;background-color:#FFFFFF;" width="600" height="8"> <table style="mso-line-height-rule:exactly;line-height:8px;font-size:8px;height:8px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="8"> <tr> <td style="mso-line-height-rule:exactly;line-height:16px;font-size:16px;height:16px;" height="16" bgcolor=""></td> </tr> </table> </td> </tr> <tr> <td colspan="3"> <table cellpadding="0" cellspacing="0" border="0" width="100%"> <tbody> <tr> <td width="50%" valign="top"><span style=" font-weight: bold; margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;" class="">'+
					buyerName +
					'</span> </td> </tr> <tr> <td width="50%" valign="top"><span style=" font-weight: bold; margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;" class=""> Order total </span> </td> <td width="50%" valign="top" colspan="2"><span style=" font-weight: normal; margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:normal;" class="">' +
					OrderTotal +
					'</span> </td> </tr> <tr> <td width="50%" valign="top"><span style=" font-weight: bold; margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;" class=""> Payment details </span> </td> <td width="50%" valign="top" colspan="2"><span style=" font-weight: normal; margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:normal;" class="">' +
					paymentDetails +
					'</span> </td> </tr> <tr> <td width="50%" valign="top"><span style=" font-weight: bold; margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;" class=""> Purchase date </span> </td> <td width="50%" valign="top" colspan="2"><span style=" font-weight: normal; margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:normal;" class="">' +
					paymentDate +
					'</span> </td> </tr> <tr> <td width="50%" valign="top"><span style=" font-weight: bold; margin: 4px 0; font-size: 15px; line-height: 21px; color:#495057;" class="">Ticket total </span> </td> <td width="50%" valign="top" colspan="2"><span style=" font-weight: normal; margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:normal;" class="">' +
					ticketTotal +
					'</span> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;background-color:#FFFFFF;" width="600" height="6"> <table style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;height:6px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="6"> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px;" height="12" bgcolor=""></td> </tr> </table> </td> </tr> <tr> <td colspan="3"><h2 style=" padding: 0; margin: 12px 0 0 0; font-size: 20px; line-height: 32px; font-weight: normal; font-weight: 500; color: red!important; margin-top: 0;" class=""> Attendees Cancelled/Refunded </h2></td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;background-color:#FFFFFF;" width="600" height="6"> <table style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;height:6px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="6"> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px;" height="12" bgcolor=""></td> </tr> </table> </td> </tr>';
			for (ShoppingCartDetailsCtrl.attendeeWrapperClass att: attendees) {
				if (lstAttIds.contains(att.Id)) {
					bccAttendees.add(att.attendeeEmail);
					emailBody = emailBody + '<tr> <td width="25%" valign="top"><span style=" font-weight: normal; margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:normal;" class="">'+ att.attendeeName +'</span> </td> <td width="50%" valign="top"><span style=" font-weight: normal; margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:bold;" class=""> 1 x '+ att.ticketType +'</span> </td> <td width="20%" align="right" valign="top" style=" text-align: left;"> <span style=" font-weight: normal; margin: 4px 0; font-size: 15px; line-height: 21px; color:#6F7287;font-weight:normal;">'+ att.ticketPrice +'</span> </td> </tr>';
				}
			}
			emailBody = emailBody +'</tbody> </table> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:4px;font-size:4px;background-color:#FFFFFF;" width="600" height="4"> <table style="mso-line-height-rule:exactly;line-height:4px;font-size:4px;height:4px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="4"> <tr> <td style="mso-line-height-rule:exactly;line-height:8px;font-size:8px;height:8px;" height="8" bgcolor=""></td> </tr> </table> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;background-color:#FFFFFF;" width="600" height="6"> <table style="mso-line-height-rule:exactly;line-height:6px;font-size:6px;height:6px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="6"> <tr> <td style="mso-line-height-rule:exactly;line-height:12px;font-size:12px;height:12px;" height="12" bgcolor=""></td> </tr> </table> </td> </tr> </table> <tr style="" bgcolor="white"> <td class="" style=" padding: 0; text-align:left;" align="left" bgcolor="white" width="100%" height="26"><span style=" font-weight: normal; margin: 4px 0; font-size: 12px; line-height: 18px; color:#000000;font-weight:600;" class="no_text_resize"> </span> </td> </tr> </table> </td> <td width="20" bgcolor="#FFFFFF"></td> </tr> </table> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;background-color:#FFFFFF; border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:4px;border-bottom-left-radius:4px; " width="600" height="18"> <table style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="18"> <tr bgcolor="#FFFFFF"> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px;" height="18"></td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;height:18px;" height="18" bgcolor=""></td> </tr> </table> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:4px;font-size:4px;background-color:#e0e0e0;" width="600" height="4"> <table style="mso-line-height-rule:exactly;line-height:4px;font-size:4px;height:4px; border-collapse:collapse; border-spacing:0; mso-table-lspace:0pt; mso-table-rspace:0pt; border:0; padding:0; ;width:100%;" cellspacing="0" cellpadding="0" height="4"> <tr> <td style="mso-line-height-rule:exactly;line-height:8px;font-size:8px;height:8px;" height="8" bgcolor=""></td> </tr> </table> </td> </tr> <tr> <td style="mso-line-height-rule:exactly;line-height:18px;font-size:18px;background-color:#e0e0e0;" width="600" height="18"></td> </tr> <tr style="" bgcolor="#e0e0e0"> <td class="footer-content" style=" padding: 0; text-align:center;" align="center" bgcolor="#e0e0e0" width="100%" height="24"><span style=" font-weight: normal; margin: 4px 0; font-size: 12px; line-height: 18px; color:#555555;" class=""> <a style="text-decoration:none;color:#e20778;" href="https://www.rotman.utoronto.ca/">Rotman School of Management</a> | 105 St. George Street, | Toronto, Ontario M5S 3E6 </span> </td> </tr> </table> </td> <td width="20" bgcolor="#ffffff"></td> </tr> </table> </td> </tr> </tbody> </table> </td> </tr></table>';

			List<Messaging.EmailFileAttachment> myAttachments = new List<Messaging.EmailFileAttachment>();
			EmailTemplate et = [SELECT Id FROM EmailTemplate WHERE Name = 'Event Registration Confirmation'];
			//String id = ApexPages.currentPage().getParameters().get('shoppingCartId');

			// Generate the PDF
			PageReference pdfPage = Page.EventCancellationAttachment;
			pdfPage.getParameters().put('shoppingCartId', shoppingCartId);
			pdfPage.getParameters().put('attIds', attParam);
			Blob pdfBlob;
			if (Test.isRunningTest()) {
				pdfBlob = Blob.valueOf('UNIT TEST');
			}else{
				pdfBlob = pdfPage.getContentAsPDF();
			}

			// Create the email
			Messaging.EmailFileAttachment attachment = new Messaging.EmailFileAttachment();
			attachment.setContentType('application/pdf');
			attachment.setFileName('RotmanEventCancellation.pdf');
			attachment.setBody(pdfBlob);
			myAttachments.add(attachment);

			//Date startDate = sci[0].Special_Event__r.Start_Date__c;
			//String dateString1 = startDate.format();
			//Date endDate = sci[0].Special_Event__r.End_Date__c;
			//String dateString2 = endDate.format();

			//String[] timeComponents1 = sci[0].Special_Event__r.Start_Time__c.split(':');
			//Integer hours1 = Integer.valueOf(timeComponents1[0]);
			//Integer minutes1 = Integer.valueOf(timeComponents1[1].substring(0, 1));
			//Integer seconds1 = Integer.valueOf('00');
			//DateTime eventStartDate = DateTime.newInstance(startDate.year(), startDate.month(), startDate.day(), hours1, minutes1, seconds1);
			DateTime eventStartDate = sci[0].Special_Event__r.Start_Local__c;

			//startDateTimeString = dateString1 + ' ' + sci[0].Special_Event__r.Start_Time__c;
			//System.debug('startDateTimeString: ' + startDateTimeString);
			//Datetime eventStartDate = Datetime.parse(startDateTimeString);
			//Datetime eventStartDate = Datetime.parse();
			//String endDateTimeString = dateString2 + ' ' + sci[0].Special_Event__r.End_Time__c;
			//String[] timeComponents2 = sci[0].Special_Event__r.End_Time__c.split(':');
			//Integer hours2 = Integer.valueOf(timeComponents2[0]);
			//Integer minutes2 = Integer.valueOf(timeComponents2[1].substring(0, 1));
			//Integer seconds2 = Integer.valueOf('00');
			//DateTime eventEndDate = DateTime.newInstance(endDate.year(), endDate.month(), endDate.day(), hours2, minutes2, seconds2);
			DateTime eventEndDate =	sci[0].Special_Event__r.End_Local__c;

			//Datetime eventEndDate = Datetime.parse(endDateTimeString);
			String eventTitle = 'Rotman Event Registration';
			String eventSubject = sci[0].Special_Event__r.Name;
			String eventLocation = sci[0].Special_Event__r.Event_Location__c;

			Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
			//email.setTemplateId(et.Id);
			email.setSaveAsActivity(false);
			email.setSenderDisplayName('Rotman Events');
			email.setWhatId(contactId);
			email.setSubject('Rotman Event Cancellation Confirmation');
			email.setToAddresses(new String[] { contactEmail });
			email.setBccAddresses(bccAttendees);
			email.setHtmlBody(emailBody);
			email.setFileAttachments(myAttachments);

			// Send the email
			if (!Test.isRunningTest()) {
				Messaging.sendEmail(new Messaging.SingleEmailMessage[] { email });
			}
		}
	}
}