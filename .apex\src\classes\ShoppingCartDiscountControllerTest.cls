@isTest
public with sharing class ShoppingCartDiscountControllerTest {
    
    @TestSetup
    public static void testDataSetup() {
        Account acc = (Account)TestFactory.createSObject(new Account(Name = 'Dept Account'));
        insert acc;
        Contact con = (Contact)TestFactory.createSObject(new Contact(lastName = 'Last Name'));
        insert con;

        insert new User(
                alias = 'test2',
                communityNickname = 'test123',
                contactId = con.Id, 
                email = '<EMAIL>', 
                emailencodingkey = 'UTF-8', 
                firstName = 'testCommunity2',
                lastName = 'User', 
                userName = '<EMAIL>', 
                profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Applicant Community User' LIMIT 1].Id, 
                timeZoneSidKey = 'America/Los_Angeles', 
                LocaleSidKey = 'en_US', 
                LanguageLocaleKey = 'en_US'
            );
        String cookieName = String.valueOf(dateTime.now());
        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
        cart.pymt__Cart_UID__c = cookieName;
        insert cart;

        evt__Special_Event__c event = new evt__Special_Event__c();
        event.Name = 'Special event';
        
        evt__Special_Event__c event2 = new evt__Special_Event__c();
        event2.Name = 'Special event 2';
        
        List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
        listOfEventToInsert.add(event);
        listOfEventToInsert.add(event2);

        insert listOfEventToInsert;

        evt__Event_Fee__c fee = new evt__Event_Fee__c();
        fee.Name = 'special event fee';
        fee.evt__Event__c = event.Id;
        fee.evt__Amount__c = 5.00;
        fee.evt__Active__c = true;
        fee.evt__Category__c = 'Attendee';
        fee.evt__Order__c = 1;

        evt__Event_Fee__c fee2 = new evt__Event_Fee__c();
        fee2.Name = 'special event fee2';
        fee2.evt__Event__c = event2.Id;
        fee2.evt__Amount__c = 5.00;
        fee2.evt__Active__c = true;
        fee2.evt__Category__c = 'Attendee';
        fee2.evt__Order__c = 1;
        
        insert new List<evt__Event_Fee__c>{fee, fee2};

        Discount__c programBalance = new Discount__c(Name = 'Program Balance', Available_for__c = 'Individual', Type__c = 'Program Balance', Program_Balance_Amount__c = 200); 
        programBalance.Code__c = 'TESTEVENT123';
        programBalance.Max_Usage__c = 2;

        Discount__c programBalance2 = new Discount__c(Name = 'Program Balance 2', Available_for__c = 'Individual', Type__c = 'Program Balance', Program_Balance_Amount__c = 200); 
        programBalance2.Code__c = 'TESTEVENT';
        programBalance2.Max_Usage__c = 2;
        insert new List<Discount__c>{programBalance, programBalance2}; 

        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event2.Name;
        cartItem.Special_Event__c = event2.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = con.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        cartItem.pymt__Shopping_Cart__c = cart.Id;
        //cartItem.Discount__c = programBalance.Id;
        insert cartItem;

        evt__Session__c testSession1 = new evt__Session__c();
        testSession1.Name = 'Test Session1';
        testSession1.Start_Date__c = Date.newInstance(2021, 12, 9); 
        testSession1.Start_Time__c= Time.newInstance(9,30,0,0);
        testSession1.End_Date__c = Date.newInstance(2021, 12, 9);
        testSession1.End_Time__c= Time.newInstance(21,30,0,0);
        testSession1.evt__Event__c = event.Id;

        evt__Session__c testSession2 = new evt__Session__c();
        testSession2.Name = 'Test Session2';
        testSession2.Start_Date__c = Date.newInstance(2021, 12, 9); 
        testSession2.Start_Time__c= Time.newInstance(9,30,0,0);
        testSession2.End_Date__c = Date.newInstance(2021, 12, 9);
        testSession2.End_Time__c= Time.newInstance(21,30,0,0);
        testSession2.evt__Event__c = event2.Id;
        
        insert new List<evt__Session__c>{testSession1,testSession2};

        pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
        pymnt.Name = 'Event Checkout';
        pymnt.pymt__Transaction_Type__c = 'Payment';
        pymnt.pymt__Payment_Type__c	 = 'Credit Card';
        pymnt.pymt__Status__c = 'Online Checkout';
        pymnt.pymt__Contact__c = con.Id;
        pymnt.pymt__Amount__c = 100;
        pymnt.Gross_Amount__c = 100;
        pymnt.pymt__Tax__c = 10;
        pymnt.pymt__Discount__c = 20;
        pymnt.Type__c = 'Event Registration';
        pymnt.pymt__Payment_Processor__c = 'Global Pay';
        insert pymnt;

        evt__Attendee__c atndynew = new evt__Attendee__c();
        atndynew.evt__Reg_First_Name__c = con.FirstName;
        atndynew.evt__Reg_Last_Name__c = con.LastName;
        atndynew.evt__Reg_Phone__c = con.MobilePhone;
        atndynew.evt__Reg_Email__c = con.Email;
        atndynew.evt__Reg_Street__c = con.MailingStreet;
        atndynew.evt__Reg_City__c = con.MailingCity;
        atndynew.evt__Reg_State__c = con.MailingState;
        atndynew.evt__Reg_Postal_Code__c = con.MailingPostalCode;
        atndynew.evt__Reg_Country__c = con.MailingCountry;
        atndynew.evt__Contact__c = con.Id !=null ? con.Id : null;
        atndynew.Shopping_Cart_Item__c = cartItem.Id;
        atndynew.evt__Event__c = cartItem.Special_Event__c;
        atndynew.evt__Event_Fee__c = cartItem.Event_Fee__c;
        atndynew.evt__Payment__c = pymnt.Id;
        insert atndynew;
    }

    @isTest
    static void testGetDisocuntCode(){
        pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c];
        Test.startTest();
            String str = ShoppingCartDiscountController.getDisocuntCode('TESTEVENT123', cartItem.Id);
        Test.stopTest();

        System.assertNotEquals(str, null, 'String should not be null');
    }

    @isTest
    static void testGetDisocuntCodeWhenDiscountCodeIsNull(){
        pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c ];
        Discount__c discountToApply = [SELECT Id FROM Discount__c WHERE Name = 'Program Balance'];
        cartItem.Discount__c = discountToApply.Id;
        update cartItem;

        Test.startTest();
            String str = ShoppingCartDiscountController.getDisocuntCode('TESTEVENT', cartItem.Id);
        Test.stopTest();

        System.assertNotEquals(str, null, 'String should not be null');
    }

    @isTest
    static void testGetDisocuntCodeWhenMaxLimitIsNotApplied(){
        pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c ];
        Discount__c discountToApply = [SELECT Id, Max_Usage__c FROM Discount__c WHERE Name = 'Program Balance'];
        discountToApply.Max_Usage__c = null;
        update discountToApply;

        Test.startTest();
            String str = ShoppingCartDiscountController.getDisocuntCode('TESTEVENT123', cartItem.Id);
        Test.stopTest();

        System.assertNotEquals(str, null, 'String should not be null');
    }

    @isTest
    static void testGetDisocuntCodeWhenMaxLimitIsReached(){
        pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c ];
        Discount__c discountToApply = [SELECT Id, Max_Usage__c, Current_Usage__c FROM Discount__c WHERE Name = 'Program Balance'];
        discountToApply.Max_Usage__c = 1;
        discountToApply.Current_Usage__c = 1;
        update discountToApply;

        Test.startTest();
            String str = ShoppingCartDiscountController.getDisocuntCode('TESTEVENT123', cartItem.Id);
        Test.stopTest();

        System.assertNotEquals(str, null, 'String should not be null');
    }

    @IsTest
    static void testCheckEmailExisting() {
        Contact mycon = [SELECT Id, Email FROM Contact WHERE LastName = 'Last Name'];
        mycon.Email = '<EMAIL>';
        update mycon;

        Test.startTest();
        String testEmail = '<EMAIL>';
        Boolean ce = ShoppingCartDiscountController.isEmailExisted(testEmail);
        Test.stopTest();
        System.assert(true, ce);
    }

    @IsTest
    static void testGetCountries() {
        Test.startTest();
        Object obj = ShoppingCartDiscountController.getCountries();
        Test.stopTest();
        System.debug('==='+obj);
    }
}