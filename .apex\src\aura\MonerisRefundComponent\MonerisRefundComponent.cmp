<aura:component implements="flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" 
                access="global" controller="PaymentTerminalController">
    <aura:handler name="init" value="{!this}" action="{!c.initData}"/>
    <aura:attribute name="recordId" type="Id" />
    
    <aura:attribute name="paymentCardWrapper" type="object"/>
    
    <div aura:id="mainModal" class="" id="modal-content-id-1" >
        <aura:if isTrue="{!v.paymentCardWrapper.pymt.pymt__Status__c == 'Voided'}" >
            <center><p>Payment is Voided!</p></center>
        </aura:if>
        <aura:if isTrue="{!v.paymentCardWrapper.pymt.pymt__Status__c != 'Voided'}" >
            <lightning:recordViewForm recordId="{!v.recordId}" objectApiName="pymt__PaymentX__c">
                <lightning:notificationsLibrary aura:id="notifLib"/>
                
                <lightning:helptext content="" class="slds-hidden"></lightning:helptext>
                <br/>
                <label for="payment">Payment: {!v.paymentCardWrapper.pymt.Name}</label> <br/>
                <label for="date">Date: {!v.paymentCardWrapper.pymt.pymt__Date__c}</label> <br/>
                <label for="">Status: {!v.paymentCardWrapper.pymt.pymt__Status__c}</label><br/>
                <label for="Processor">Processor: {!v.paymentCardWrapper.pymt.pymt__Payment_Processor__c}</label><br/>
                <label for="totalAmount">Total Amount: {!v.paymentCardWrapper.pymt.pymt__Amount__c}</label> <br/>
                <label for="paymentType" >Payment Type: {!v.paymentCardWrapper.pymt.pymt__Payment_Type__c}</label>
                <lightning:input aura:id="RefundAmount" type="number" required="true" label="Refund Amount"
                                 Name="*Refund Amount" formatter="currency" step="0.01"
                                 placeholder="0.00"
                                 value="{!v.paymentCardWrapper.Amount}"
                                 />
                
               <!-- <lightning:input aura:id="PaymentType" Name="*PaymentType" required="true"
                                 label="Payment Type"
                                 value="{!v.paymentCardWrapper.pymt.pymt__Payment_Type__c}"
                                 /> -->
                <lightning:input aura:id="fee" type="number" required="false" label="Administration Fee"
                                 Name="Administration Fee" formatter="currency" step="0.01"
                                 placeholder="0.00"
                                 value="{!v.paymentCardWrapper.pymt.Administration_Fee__c}"
                                 /> 
                <lightning:input aura:id="RefundReason" Name="*RefundReason" required="true"
                                 label="Refund Reason"
                                 value="{!v.paymentCardWrapper.pymt.Refund_Reason__c}"
                                 />
            </lightning:recordViewForm>
            <lightning:button class="slds-m-top_small" variant="brand" type="submit" name="PurchaseCall" onclick="{!c.RefundSubmit}" label="Refund Payment" />
        </aura:if>
        
        
    </div>
    
</aura:component>