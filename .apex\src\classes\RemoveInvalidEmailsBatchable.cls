public with sharing class RemoveInvalidEmailsBatchable implements Database.Batchable<SObject>{

    public final String sObjectName;

    public RemoveInvalidEmailsBatchable(String sObjectName) {
        this.sObjectName = sObjectName;
    }

    public Database.QueryLocator start( Database.BatchableContext context ) {
        String query ;
        System.debug( 'sObjectName: ' + sObjectName );
        if(sObjectName == 'User')
            query = 'SELECT id, email, username, isActive, profile.name FROM User WHERE isActive = true AND profile.name IN (\'Applicant Community User\' , \'Student Community User\') AND email LIKE \'%.invalid\'' ;
        else if (sObjectName == 'Contact')
            query = 'SELECT id, email, hed__AlternateEmail__c, hed__UniversityEmail__c, hed__WorkEmail__c, SFMC_Email__c FROM Contact WHERE email LIKE \'%.invalid\' OR hed__AlternateEmail__c LIKE \'%.invalid\' OR hed__UniversityEmail__c  LIKE \'%.invalid\' OR hed__WorkEmail__c LIKE \'%.invalid\' OR SFMC_Email__c LIKE \'%.invalid\'';
        System.debug( 'query: ' + query );
        return Database.getQueryLocator( query );
    }

    public void execute( Database.BatchableContext context, List<sObject> scope ) {
        if(scope[0].getSobjectType() == Schema.User.SObjectType){
            // deacivate the user
            for ( User u : (List<User>)scope ) {
                u.IsActive = false;
            }
            update scope;

            // Remove the invalid from username and email
            for ( User u : (List<User>)scope ) {
                System.debug( 'user sandbox email: ' + u.email );
                u.email = u.email.removeEndIgnoreCase( '.invalid' );
                u.username = u.username.removeEndIgnoreCase( '.invalid' );
                System.debug( 'user reverted email: ' + u.email );
                System.debug( 'user reverted username: ' + u.username );
            }
            update scope;

            // reactivate the users
            for (User u : (List<User>)scope) {
                u.IsActive = true;
            }
            update scope;
        } else if (scope[0].getSobjectType() == Schema.Contact.SObjectType) {
            for ( Contact con : (List<Contact>)scope ) {
                con.email = con.email.removeEndIgnoreCase( '.invalid' );
                if (con.hed__AlternateEmail__c != null)
                    con.hed__AlternateEmail__c = con.hed__AlternateEmail__c.removeEndIgnoreCase( '.invalid' );
                if (con.hed__UniversityEmail__c != null)
                    con.hed__UniversityEmail__c = con.hed__UniversityEmail__c.removeEndIgnoreCase( '.invalid' );
                if (con.SFMC_Email__c != null)
                    con.SFMC_Email__c = con.SFMC_Email__c.removeEndIgnoreCase( '.invalid' );
            }
            update scope;
        }

    }

    public void finish( Database.BatchableContext context ) {

    }

    
}