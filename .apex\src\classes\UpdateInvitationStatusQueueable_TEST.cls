@isTest
private class UpdateInvitationStatusQueueable_TEST {
	@testSetup
	static void setup() {
		Account account = new Account(
				Name = 'Test Account'
		);
		insert account;

		Contact contact = new Contact(
				FirstName = 'Test',
				LastName = 'Contact',
				Email = '<EMAIL>',
				AccountId = account.Id
		);
		insert contact;

		evt__Special_Event__c specialEvent = new evt__Special_Event__c(
				Name = 'Test Event',
				evt__Agenda_HTML__c = 'Test Agenda',
				evt__Publish_To__c = 'Public Events',
				Start_Local__c = Datetime.now(),
				End_Local__c = Datetime.now().addDays(1)
		);
		insert specialEvent;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = specialEvent.Id;
		fee.evt__Amount__c = 10.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = contact.Id;
		item.Event_Fee__c = fee.Id;
		item.pymt__Unit_Price__c = 10.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';

		insert item;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = contact.Id;
		at.evt__Invitation_Status__c = 'Invited';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.Shopping_Cart_Item__c = item.Id;
		at.evt__Event__c = specialEvent.Id;
		at.evt__Reg_Email__c = '<EMAIL>';
		at.evt__Reg_First_Name__c = 'John';
		at.evt__Reg_Last_Name__c = 'Doe';

		insert at;
	}

	@isTest
	static void testUpdateInvitationStatus() {
		List<evt__Attendee__c> attendees = [SELECT Id, evt__Invitation_Status__c FROM evt__Attendee__c];
		List<Id> attendeeIds = new List<Id>();

		for (evt__Attendee__c attendee : attendees) {
			attendeeIds.add(attendee.Id);
		}

		UpdateInvitationStatusQueueable updateJob = new UpdateInvitationStatusQueueable(attendeeIds);

		Test.startTest();
		System.enqueueJob(updateJob);
		Test.stopTest();

	}
}