/**
 * @description Updates Track Fields 'Discount_Amount_Paid__c', 'Gross_Amount_Paid__c', 'Total_Amount_Paid__c' when payment on Shopping Cart Item is updated/inserted.
 * <AUTHOR>
 * @version 1.0
 * @created 2020-10-23
 */
global class UpdateTrackFields_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for the object 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 

        List<pymt__Shopping_Cart_Item__c> itemsToUpdate = new List<pymt__Shopping_Cart_Item__c>(); 

        /*AFTER INSERT CONTEXT: if payment reocrd is attached to the newly inserted SCI, Update Track Fields. */
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert){
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){

                if(sci.pymt__Payment__c != null){
                    sci.Discount_Amount_Paid__c = sci.Discount_Amount__c;
                    sci.Gross_Amount_Paid__c = sci.Gross_Amount__c;
                    sci.Total_Amount_Paid__c = sci.Total_Amount__c;
                    itemsToUpdate.add(sci); 
                }
            }

            //update SCI
            /*if(itemsToUpdate.size() >0)
            	dmlWrapper.objectsToUpdate.addAll(itemsToUpdate); */

        }/*AFTER UPDATE CONTEXT: if payment reocrd is updated on the SCI record, Update Track Fields. */
        else if(triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList); 

            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                if(sci.pymt__Payment__c == null){
                    sci.Discount_Amount_Paid__c = 0;
                    sci.Gross_Amount_Paid__c = 0;
                    sci.Total_Amount_Paid__c = 0;
                    itemsToUpdate.add(sci);
                }
                if(sci.pymt__Payment__c != null && oldMap.get(sci.Id).pymt__Payment__c != sci.pymt__Payment__c ){
                    sci.Discount_Amount_Paid__c = sci.Discount_Amount__c;
                    sci.Gross_Amount_Paid__c = sci.Gross_Amount__c;
                    sci.Total_Amount_Paid__c = sci.Total_Amount__c;
                    itemsToUpdate.add(sci);
                }
            }

            //update discounts: 
            /*if(itemsToUpdate.size() >0)
                dmlWrapper.objectsToUpdate.addAll(itemsToUpdate);*/

        }

        return null;//dmlWrapper; 
    }
}