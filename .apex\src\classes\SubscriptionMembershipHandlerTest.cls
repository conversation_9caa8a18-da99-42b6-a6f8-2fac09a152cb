@isTest(SeeAllData=false)
private class SubscriptionMembershipHandlerTest {
    
    public static testMethod void testSubscriptionMemberships() {
        test.startTest();
        
        Id conId = createContact();
        Id leadId = createLead();
        Id subscrId = createSubscription();
        
        Subscription_Membership__c sm1 = new Subscription_Membership__c(Subscription_Status__c = 'Subscribed', Contact__c = conId, Subscription__c = subscrId);
        insert sm1;
        
        sm1.Subscription_Status__c = 'Unsubscribed';
        update sm1;
        
        Subscription_Membership__c sm2 = new Subscription_Membership__c(Subscription_Status__c = 'Unsubscribed', Lead__c = leadId, Subscription__c = subscrId);
        insert sm2;
        
        sm2.Subscription_Status__c = 'Subscribed';
        update sm2;
    }
    
    public static Id createContact() {
        Contact con = new Contact(
            FirstName = 'Test',
            LastName = 'TestContact',
            Email = '<EMAIL>'
        );
        insert con;
        return con.Id;
    }
    
    public static Id createLead() {
        Lead ld = new Lead(
            FirstName = 'Test',
            LastName = 'TestLead',
            Email = '<EMAIL>',
            Company = 'Test Co'
        );
        insert ld;
        return ld.Id;
    }
    
    
    public static Id createSubscription() {
        Subscription__c subscr = new Subscription__c(Name = 'Test Subscription', Subscription_ID__c = 'testsubscr');
        insert subscr;
        
        return subscr.Id;
    }
    
}