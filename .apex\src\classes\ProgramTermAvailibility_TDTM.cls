/* 
*
*Ticket UTR-3299
Create a TDTM trigger on Program Term Availability (after update): When the active checkbox is unchecked (updated from true to false), 
query for all related applications (where status = "In Progress" and Record Type = "Full Time and Specialized" 
and null out the Program term availability lookup field on the application record). 
*
* <AUTHOR> 
* @since   2021-05-14 
*
* Love your neighbour as yourself
*/

global class ProgramTermAvailibility_TDTM extends hed.TDTM_Runnable {
    
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist,
                                             List<SObject> oldlist,
                                             hed.TDTM_Runnable.Action triggerAction,
                                             Schema.DescribeSObjectResult objResult) {

        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
                
       // ('Full-Time and Specialized') recrdtypeid
        Id fulltimeId = ApplicationService.FTandSpecializedRTId;
        List<hed__Application__c> appsToUpdate = new List<hed__Application__c>();
        List<Id> nonActivePTArecords = new List<Id>();
        List<Program_Term_Availability__c> newPTAList = (List<Program_Term_Availability__c>) newlist;       //trigger.old
        List<Program_Term_Availability__c> oldPTAList = (List<Program_Term_Availability__c>) oldlist;       //trigger.new
                                                         

        //fire after update
        if (triggerAction == hed.TDTM_Runnable.Action.AfterUpdate) {
            
         system.debug('MC IN HERE');
            
            //check if Active__c is changed AND if equal to false
            for(integer i = 0; i < newPTAList.size(); i++){
                if(newPTAList[i].Active__c == false && (newPTAList[i].Active__c != oldPTAList[i].Active__c)){
                    nonActivePTArecords.add(newPTAList[i].Id);
                }
            }

          
        //query all related applications to program term
        List<hed__Application__c> relatedApplications = [select id, hed__Application_Status__c, RecordTypeId, Program_Term_Availability__c, Program_Term_Availability__r.Id from hed__Application__c where Program_Term_Availability__r.Id IN:nonActivePTArecords and RecordTypeId =: fulltimeId and hed__Application_Status__c = 'In Progress' ];
            
            //set program term to null for related applications
            for(hed__Application__c app:relatedApplications){
                app.Program_Term_Availability__c = null;
                appsToUpdate.add(app);
            }
            
        }


      //update records
      dmlWrapper.objectsToUpdate.addAll((List<hed__Application__c>)appsToUpdate);
      return dmlWrapper;
    }
    
}