/**
* @description    Test class for TestDateStampDupRulesApex_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-11-05
* @modified 2020-11-05
*/
@isTest
public class PrgrmEnrlmntCreateFulfillmnt_TDTMTest {
	@testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('PrgrmEnrlmntCreateReqmntfulfillment_TDTM', 'Program_Enrollment__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        //Create test Account:
        Account a = (Account)TestFactory.createSObject(new Account(Name='TestAccount'));
        insert a;

        //Create test contact: 
        Contact c = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student'));
        insert c;
        //Create Term
        hed__Term__c trm = new hed__Term__c(hed__Account__c=a.Id,name='Spring 2020');
        insert trm;
        //Create course
        hed__Course__c course = new hed__Course__c(hed__Account__c=a.Id,name='MBA 2020');
        insert course;
        hed__Course__c course1 = new hed__Course__c(hed__Account__c=a.Id,name='MBA 2021');
        insert course1;
        //Create Course Offering
        hed__Course_Offering__c cOffering = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering',hed__Term__c=trm.Id);
        insert cOffering;
        //Create Course Offering
        hed__Course_Offering__c cOffering1 = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering1',hed__Term__c=trm.Id);
        insert cOffering1;
        //Create a Program Plan
        hed__Program_Plan__c ppln = new hed__Program_Plan__c(name='Test Program Plan',hed__Account__c=a.Id,hed__Start_Date__c=Date.Today(),hed__End_Date__c = Date.Today().addDays(30));
        insert ppln;
        hed__Program_Plan__c ppln1 = new hed__Program_Plan__c(name='Test Program Plan1',hed__Account__c=a.Id,hed__Start_Date__c=Date.Today(),hed__End_Date__c = Date.Today().addDays(30));
        insert ppln1;
        //Create Course Connection
        hed__Course_Enrollment__c cenrollment = new hed__Course_Enrollment__c(hed__Contact__c = c.Id,hed__Course_Offering__c=cOffering.Id,hed__Status__c='Current');
        insert cenrollment;
        //Create Plan Requirements
        hed__Plan_Requirement__c planRequirementParent = new hed__Plan_Requirement__c(name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id,hed__Category__c = 'Required');
        insert planRequirementParent;
        hed__Plan_Requirement__c planRequirementParentwithCourse = new hed__Plan_Requirement__c(hed__Course__c	 = course.id,hed__Category__c = 'Required',name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        insert planRequirementParentwithCourse;
        List<hed__Plan_Requirement__c> plnRqrmntLst = new List<hed__Plan_Requirement__c>();
        hed__Plan_Requirement__c planRequirement1 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement1);
        hed__Plan_Requirement__c planRequirement2 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement2);
        hed__Plan_Requirement__c planRequirement3 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement3);
        hed__Plan_Requirement__c planRequirement4 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement4);
        hed__Plan_Requirement__c planRequirement5 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement5);
        insert plnRqrmntLst;
        
        //Create Course Connection
        hed__Course_Enrollment__c cenrollment1 = new hed__Course_Enrollment__c(hed__Contact__c = c.Id,hed__Course_Offering__c=cOffering1.Id,hed__Status__c='Former');
        insert cenrollment1;
        //Create Plan Requirements
        hed__Plan_Requirement__c planRequirementParent1 = new hed__Plan_Requirement__c(name='Test Plan Requirement 1',hed__Program_Plan__c = ppln1.Id,hed__Category__c = 'Required');
        insert planRequirementParent1;
        hed__Plan_Requirement__c planRequirementParentwithCourse1 = new hed__Plan_Requirement__c(hed__Course__c	 = course1.id,hed__Category__c = 'Required',name='Test Plan Requirement 1',hed__Program_Plan__c = ppln1.Id);
        insert planRequirementParentwithCourse1;
        List<hed__Plan_Requirement__c> plnRqrmntLst1 = new List<hed__Plan_Requirement__c>();
        hed__Plan_Requirement__c planRequirement11 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent1.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln1.Id);
        plnRqrmntLst.add(planRequirement11);
        hed__Plan_Requirement__c planRequirement21 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent1.Id,name='Test Plan Requirement 2',hed__Program_Plan__c = ppln1.Id);
        plnRqrmntLst.add(planRequirement21);
        hed__Plan_Requirement__c planRequirement31 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent1.Id,name='Test Plan Requirement 3',hed__Program_Plan__c = ppln1.Id);
        plnRqrmntLst.add(planRequirement31);
        hed__Plan_Requirement__c planRequirement41 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent1.Id,name='Test Plan Requirement 4',hed__Program_Plan__c = ppln1.Id);
        plnRqrmntLst.add(planRequirement41);
        hed__Plan_Requirement__c planRequirement51 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent1.Id,name='Test Plan Requirement 5',hed__Program_Plan__c = ppln1.Id);
        plnRqrmntLst.add(planRequirement51);
        insert plnRqrmntLst1;
    }
    @isTest 
    static void PrgrmEnrlmntCreateFulfillmnt_TDTMTestMethodInsert() {
        Contact c = [Select id from Contact limit 1];
        hed__Program_Plan__c ppln = [Select Id from hed__Program_Plan__c limit 1];
        Account acc = [Select id from Account limit 1];
        Test.StartTest();
        hed__Program_Enrollment__c penrollment = new hed__Program_Enrollment__c(hed__Contact__c=c.Id,hed__Account__c = acc.Id,hed__Program_Plan__c = ppln.Id);
        insert penrollment;
        System.assertEquals(6,[Select Id from Plan_Requirement_Fulfillment__c].Size());
        Test.StopTest();
    }
    @isTest 
    static void PrgrmEnrlmntCreateFulfillmnt_TDTMTestMethodUpdate() {
        Contact c = [Select id from Contact limit 1];
        List<hed__Program_Plan__c> pplnLst = [Select Id from hed__Program_Plan__c];
        Account acc = [Select id from Account limit 1];
        Test.StartTest();
        hed__Program_Enrollment__c penrollment = new hed__Program_Enrollment__c(hed__Contact__c=c.Id,hed__Account__c = acc.Id,hed__Program_Plan__c = pplnLst[0].Id);
        insert penrollment;
        System.assertEquals(6,[Select Id from Plan_Requirement_Fulfillment__c].Size());
        penrollment.hed__Program_Plan__c= pplnLst[1].Id;
        update penrollment;
        System.assertEquals(1,[Select Id from Plan_Requirement_Fulfillment__c].Size());
        Test.StopTest();
    }
}