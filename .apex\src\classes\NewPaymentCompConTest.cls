@isTest 
public class NewPaymentCompConTest {
	 @testSetup
    static void setupMethod(){
        Gateway_Setting__c gs = new Gateway_Setting__c();
        gs.Endpoint__c = 'https://esqa.moneris.com/gateway2/servlet/MpgRequest';
        gs.API_token__c = 'yesguy';
        gs.Store_Id__c = 'store3';
        insert gs;
        Contact con = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', lastName = 'Contact'));
        insert con;
        
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
            pymt.Name = 'payment_'+string.valueOf(datetime.now());
            pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
            pymt.pymt__Transaction_Type__c = 'Payment';
            pymt.pymt__Status__c = 'Scheduled';
            pymt.pymt__Amount__c = 10.12;
        	pymt.pymt__Payment_Type__c ='Credit Card';
            insert pymt;
        	system.debug('payment--> '+pymt.pymt__Amount__c);
    } 
    
    static testMethod void checkCreditCardPayment() {
        
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
            pymt.Name = 'payment_'+string.valueOf(datetime.now());
            pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
            pymt.pymt__Transaction_Type__c = 'Payment';
            pymt.pymt__Status__c = 'Scheduled';
            pymt.pymt__Amount__c = 10.12;
        	pymt.pymt__Payment_Type__c ='Credit Card';
        test.startTest();
        test.setMock(HttpCalloutMock.class, new MonerisMockResponse());
        NewPaymentCompController.getPaymentDone(pymt,'099','2102','****************');
        test.stopTest();
    }
    static testMethod void checkCashMemoPayment() {
        
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
            pymt.Name = 'payment_'+string.valueOf(datetime.now());
            pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
            pymt.pymt__Transaction_Type__c = 'Payment';
            pymt.pymt__Status__c = 'Scheduled';
            pymt.pymt__Amount__c = 10.12;
        	pymt.pymt__Payment_Type__c ='Check or Money Order';
        test.startTest();
        //test.setMock(HttpCalloutMock.class, new MonerisMockResponse());
        NewPaymentCompController.getPaymentDone(pymt,null,null,null);
        test.stopTest();
    }
    static testMethod void checkContactDetails(){
        contact con = [SELECT id, Name FROM Contact WHERE FirstName = 'Test' LIMIT 1 ];
        CardWrapperClass cardDetails =  NewPaymentCompController.getContactDetails('["'+string.valueOf(con.Id)+'"]') ;
        system.debug('Name- '+cardDetails.pymt.pymt__Billing_First_Name__c);
        system.assertEquals('Test',cardDetails.pymt.pymt__Billing_First_Name__c );
       
    }
}