@isTest
public class ProgramTermAvailibility_TDTM_TEST {

    @testSetup
    static void testSetup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for ProgramTermAvailibility_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('ProgramTermAvailibility_TDTM', 'Program_Term_Availability__c', 'AfterUpdate', 1.00));
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        Id fulltimeId = ApplicationService.FTandSpecializedRTId;
        //Create test Account
        Account a = new Account(Name='TestAccount');
        insert a;
        //Create Term
        hed__Term__c trm = new hed__Term__c(hed__Account__c=a.Id,name='Spring 2020');
        insert trm;
        //Create test record
        Program_Term_Availability__c pta = new Program_Term_Availability__c(
        	Active__c = True,
            Program__c = a.Id,
            Term__c = trm.Id
        );   
        insert pta;
        
        hed__Application__c app = new hed__Application__c(
        	Program_Term_Availability__c = pta.Id,
            hed__Application_Status__c = 'In Progress',
            RecordTypeId = fulltimeId,
            hed__Applying_To__c = a.Id,
            hed__Term__c = trm.Id
        );
        insert app;
    }
    
    @isTest
    public static void testProgramTerm(){
        Program_Term_Availability__c pta = [SELECT Id, Active__c FROM Program_Term_Availability__c LIMIT 1];
        
        test.startTest();
        	pta.Active__c = False;
        	update pta;
        test.stopTest();
    }
}