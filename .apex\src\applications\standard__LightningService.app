<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Account_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Account</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Contact</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#8924BD</headerColor>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>Lets support agents work with multiple records across customer service channels on one screen</description>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Service Console</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Active Student Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Active Student Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <setupExperience>service</setupExperience>
    <tabs>standard-Case</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-Account</tabs>
    <tabs>Knowledge__kav</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>standard-AppLauncher</tabs>
    <tabs>standard-home</tabs>
    <tabs>standard-LightningQuickText</tabs>
    <tabs>Landing_Page</tabs>
    <tabs>Engagement_Timeline_Item__c</tabs>
    <tabs>sfal__SuccessPlanTemplate__c</tabs>
    <tabs>evt__Session_Assignment__c</tabs>
    <tabs>Discount__c</tabs>
    <tabs>Cohort__c</tabs>
    <tabs>Calendar_View__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>LightningService_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>Calendar_View__c</tab>
        </mappings>
        <mappings>
            <tab>Cohort__c</tab>
        </mappings>
        <mappings>
            <tab>Discount__c</tab>
        </mappings>
        <mappings>
            <tab>Engagement_Timeline_Item__c</tab>
        </mappings>
        <mappings>
            <tab>Knowledge__kav</tab>
        </mappings>
        <mappings>
            <tab>Landing_Page</tab>
        </mappings>
        <mappings>
            <tab>evt__Session_Assignment__c</tab>
        </mappings>
        <mappings>
            <tab>sfal__SuccessPlanTemplate__c</tab>
        </mappings>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <tab>standard-AppLauncher</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <tab>standard-Contact</tab>
        </mappings>
        <mappings>
            <tab>standard-Dashboard</tab>
        </mappings>
        <mappings>
            <tab>standard-LightningQuickText</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
        <mappings>
            <tab>standard-report</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
