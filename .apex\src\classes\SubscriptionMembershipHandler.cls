public class SubscriptionMembershipHandler {
    
        /*public static void updateContactsAndLeads(map<Id, Subscription_Membership__c> newMap, map<Id, Subscription_Membership__c> oldMap) {
        set<Id> uniqueContacts = new set<Id>();
        set<Id> uniqueLeads = new set<Id>();
        for (Subscription_Membership__c lm : newMap.values()) {
            if (oldMap == null || oldMap.get(lm.Id).Subscription_Status__c != lm.Subscription_Status__c) {
                if (lm.Contact__c != null) uniqueContacts.add(lm.Contact__c);
                else if (lm.Lead__c != null) uniqueLeads.add(lm.Lead__c);
            }
        }
        
        // recalculate the unsubscribes
        map<Id, string> contactUnsubs = new map<Id, string>();
        map<Id, string> leadUnsubs = new map<Id, string>();
        map<Id, string> contactSubs = new map<Id, string>();
        map<Id, string> leadSubs = new map<Id, string>();
        for (Subscription_Membership__c lm : [SELECT Id, Contact__c, Lead__c, Subscription__c, Subscription__r.Subscription_ID__c, Subscription_Status__c FROM Subscription_Membership__c WHERE (Contact__c != null AND Contact__c IN :uniqueContacts) OR (Lead__c != null AND Lead__c IN :uniqueLeads)]) {
            if (lm.Subscription_Status__c == 'Unsubscribed') {
                if (lm.Contact__c != null) {
                    if (!contactUnsubs.containsKey(lm.Contact__c)) contactUnsubs.put(lm.Contact__c, lm.Subscription__r.Subscription_ID__c);
                    else contactUnsubs.put(lm.Contact__c, contactUnsubs.get(lm.Contact__c) + ';' + lm.Subscription__r.Subscription_ID__c);
                } else if (lm.Lead__c != null) {
                    if (!leadUnsubs.containsKey(lm.Lead__c)) leadUnsubs.put(lm.Lead__c, lm.Subscription__r.Subscription_ID__c);
                    else leadUnsubs.put(lm.Lead__c, leadUnsubs.get(lm.Lead__c) + ';' + lm.Subscription__r.Subscription_ID__c);
                }
            }
        }
        
        // update contacts
        if (uniqueContacts.size() > 0) {
            list<Contact> consToUpdate = [SELECT Id, Unsubscribes__c FROM Contact WHERE Id IN :uniqueContacts];
            for (Contact c : consToUpdate) {
                if (contactUnsubs.containsKey(c.Id)) {
                    c.Unsubscribes__c = contactUnsubs.get(c.Id);
                } else {
                    c.Unsubscribes__c = '';
                }
            }
            update consToUpdate;
        }
        
        // update leads
        if (uniqueLeads.size() > 0) {
            list<Lead> leadsToUpdate = [SELECT Id, Unsubscribes__c FROM Lead WHERE Id IN :uniqueLeads];
            for (Lead l : leadsToUpdate) {
                if (leadUnsubs.containsKey(l.Id)) {
                    l.Unsubscribes__c = leadUnsubs.get(l.Id);
                } else {
                    l.Unsubscribes__c = '';
                }
            }
            update leadsToUpdate;
        }
    }*/

    public static void stampSubscriptionMatchingKey(List<Subscription_Membership__c> subscriptions){
        for(Subscription_Membership__c sm : subscriptions){
            sm.subscription_membership_matching_key__c = String.valueOf(sm.contact__c) + String.valueOf(sm.Subscription__c); 
        }
    }
}