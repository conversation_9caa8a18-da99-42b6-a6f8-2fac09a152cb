<!-- add implements="forceCommunity:availableForAllPageTypes" to surface the component in community builder -->
<aura:component controller="LightningForgotPasswordController" implements="forceCommunity:availableForAllPageTypes">
    <aura:attribute name="usernameLabel" type="String" required="false" default="Username"/>
    <aura:attribute name="submitButtonLabel" type="String" required="false" default="Reset Password"/>
    <aura:attribute name="showError" type="Boolean" required="true" description="" default="false" access="private"/>
    <aura:attribute name="errorMessage" type="String" required="false" description="" access="private"/>
    <aura:attribute name="checkEmailUrl" type="String" required="true"/>
    <aura:attribute name="expid" type="String" required="false" description="The branding experience ID" />    
    <aura:handler name="init" value="{!this}" action="{!c.initialize}"/>
    <aura:handler event="c:setExpId" action="{!c.setExpId}"/>    
    <aura:dependency resource="c:setExpId" type="EVENT"/>  
    
    <div>
            <aura:renderIf isTrue="{!v.showError}">
                <div id="error">
                    <ui:outputRichText value="{!v.errorMessage}"/>
                </div>
            </aura:renderIf>
            <span class="form__header">
                RESET PASSWORD
            </span>
            <p>To reset your password, we'll need your username. We'll send password reset instructions to the email address associated with your account.</p>
            <div id="sfdc_username_container" class="sfdc">
                <span id="sfdc_user" class="login-icon" data-icon="a"></span>
                <ui:inputText value="" aura:id="username" placeholder="{!v.usernameLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>
    
            <div class="sfdc">
                <ui:button aura:id="submitButton" label="{!v.submitButtonLabel}" press="{!c.handleForgotPassword}" class="sfdc_button"/>
            </div>
    
    </div>
</aura:component>