/**
 * Created by <PERSON><PERSON> on 2023-02-23.
 */

public class ProgramTermAvailibilityBatch implements Database.Batchable<SObject> {
    final Id fulltimeId = ApplicationService.FTandSpecializedRTId;
    Set<Id> IdPTAListBatch = new Set<Id>();
    List<hed__Application__c> appsToUpdate = new List<hed__Application__c>();

    public ProgramTermAvailibilityBatch(Set<Id> IdPTAList) {
        IdPTAListBatch = IdPTAList;
    }

    public Database.QueryLocator start(Database.BatchableContext bc) {
        String query = 'select id, hed__Application_Status__c, RecordTypeId, Program_Term_Availability__c, Program_Term_Availability__r.Id from hed__Application__c where Program_Term_Availability__r.Id IN :IdPTAListBatch and RecordTypeId =: fulltimeId and hed__Application_Status__c = \'In Progress\'';
        return Database.getQueryLocator(query);
    }

    public void execute(Database.BatchableContext bc, List<hed__Application__c> scope) {
        System.debug('Applications '+scope);

        for (hed__Application__c app : scope) {
            app.Program_Term_Availability__c = null;
            appsToUpdate.add(app);
        }
        update appsToUpdate;
    }

    public void finish(Database.BatchableContext bc) {
    }
}