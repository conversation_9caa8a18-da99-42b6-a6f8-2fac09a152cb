({
    getCartInfo : function(component,callTo) {
        return new Promise(function(resolve, reject) { 
            callTo.setCallback(this, 
                               function(response) {
                                   var state = response.getState();
                                   console.log(state);
                                   console.log(response);
                                   console.log('res=> '+response.getReturnValue());
                                   if (state === "SUCCESS") {
                                       resolve(response.getReturnValue());
                                       
                                       var UserDetail = component.get("c.getUserDetails");
                                       UserDetail.setCallback(this, function(response) {
                                           var state = response.getState();
                                           console.log(state);
                                           if (state === "SUCCESS") {
                                               console.log('UserName: '+response.getReturnValue());
                                               component.set("v.userName",response.getReturnValue()+'\'s');
                                               
                                           }
                                       });
                                       $A.enqueueAction(UserDetail);
                                   } else {
                                       reject(new Error(response.getError()));
                                   }
                               }); 
            $A.enqueueAction(callTo);
        });
    }
})