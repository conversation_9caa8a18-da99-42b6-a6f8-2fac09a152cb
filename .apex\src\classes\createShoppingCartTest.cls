@isTest
public class createShoppingCartTest {
    
    @testSetup
    static void setupMethod(){
        Account acc = (Account)TestFactory.createSObject(new Account(Name = 'Dept Account'));
        insert acc;
        id userId = UserInfo.getUserId();
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        Contact con = (Contact)TestFactory.createSObject(new Contact(lastName = u.Name));
        insert con;
        Contact con1 = new contact();
        con1.FirstName = 'Peter';
        con1.LastName = 'Dupre';
        insert con1;
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        pymt.Name = 'payment_'+string.valueOf(datetime.now());
        pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
        pymt.pymt__Transaction_Type__c = 'Payment';
        pymt.pymt__Status__c = 'Scheduled';
        pymt.pymt__Amount__c = 10.12;
        //pymt.pymt__Contact__c = con.Id;
        insert pymt;
        hed__Term__c term = new hed__Term__c();
        term.Name='Term';
        term.hed__Type__c = 'Quarter';
        term.hed__Account__c=acc.Id;
        term.hed__Start_Date__c= date.today().addDays(5);
        term.hed__End_Date__c = date.today().addDays(10);
        insert term;
        
        hed__Course__c course= new hed__Course__c();
        course.hed__Account__c = acc.Id;
        course.Name = 'Negotiation Foundations';
        course.hed__Course_ID__c = 'Course1';
        insert course;
        
        hed__Course_Offering__c courseOff = new hed__Course_Offering__c();
        courseOff.Name = 'Course Offering';
        courseOff.hed__Course__c = course.Id;
        courseOff.hed__Term__c = term.Id;
        courseOff.hed__Start_Date__c = date.today().addDays(5);
        courseOff.hed__End_Date__c = date.today().addDays(10);
        courseOff.Registration_Fee__c = 50;
        insert courseOff;
        
        RecordType eventType = [select id,Name from RecordType where SobjectType='evt__Special_Event__c' AND Name = 'Standard Event' LIMIT 1];
        evt__Special_Event__c event = new evt__Special_Event__c();
        event.RecordTypeId = eventType.Id;
        event.Published_Price__c = 8;
        event.Name = 'Special event';
        insert event;
        
        evt__Special_Event__c event2 = new evt__Special_Event__c();
        event2.RecordTypeId = eventType.Id;
        event2.Published_Price__c = 6;
        event2.Name = 'Special event 2';
        insert event2;
        
        evt__Event_Fee__c fee = new evt__Event_Fee__c();
        fee.Name = 'special event fee';
        fee.evt__Event__c = event.Id;
        fee.evt__Amount__c = 5.00;
        fee.evt__Active__c = true;
        fee.evt__Category__c = 'Attendee';
        fee.evt__Order__c = 1;
        insert fee;
        evt__Event_Fee__c fee2 = new evt__Event_Fee__c();
        fee2.Name = 'special event fee2';
        fee2.evt__Event__c = event2.Id;
        fee2.evt__Amount__c = 5.00;
        fee2.evt__Active__c = true;
        fee2.evt__Category__c = 'Attendee';
        fee2.evt__Order__c = 1;
        insert fee2;
    } 
    static testMethod void addToCart() {
        id userId = UserInfo.getUserId();
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        Contact con = [SELECT Id, Name From Contact WHERE Name=:u.Name];
        evt__Special_Event__c event = [select Id from evt__Special_Event__c where Name = 'Special event'];
        evt__Special_Event__c event2 = [select Id,Name from evt__Special_Event__c where Name = 'Special event 2'];
        hed__Course_Offering__c courseOff = [select Id from hed__Course_Offering__c where Name = 'Course Offering'];
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event2.Name;
        cartItem.Special_Event__c = event2.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = con.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        insert cartItem;
        test.startTest();
        createShoppingCartController.addToCart(event.Id);
        createShoppingCartController.addToCart(event2.Id);
        createShoppingCartController.addToCart(courseOff.Id);
        test.stopTest();
    }
    static testMethod void testPaymentId() {
        createShoppingCartController con = new createShoppingCartController();
        evt__Special_Event__c event = [select Id from evt__Special_Event__c where Name = 'Special event'];
        
        test.startTest();
        createShoppingCartController.addToCart(event.Id);
        createShoppingCartController.PaymentId(event.Id);// (event.Id);
        //
        //createShoppingCartController.addToCart(courseOff.Id);
        test.stopTest();
    }//removeFromCart
    static testMethod void testRemove() {
        evt__Special_Event__c event = [select ID,Name from evt__Special_Event__c where Name = 'Special event'];
        evt__Special_Event__c event2 = [select Id,Name from evt__Special_Event__c where Name = 'Special event 2'];
        hed__Course_Offering__c courseOff = [select Id from hed__Course_Offering__c where Name = 'Course Offering'];
        id userId = UserInfo.getUserId();
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        Contact con = [SELECT Id, Name From Contact WHERE Name=:u.Name];
        test.startTest();
        createShoppingCartController.addToCart(event.Id);
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event.Name;
        cartItem.Special_Event__c = event.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = con.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        insert cartItem;
        createShoppingCartController.addToCart(event.Id);
        createShoppingCartController.addToCart(event2.Id);
        createShoppingCartController.addToCart(courseOff.Id);
        createShoppingCartController.removeFromCart(event.Id);// (event.Id);
        
        //createShoppingCartController.addToCart(courseOff.Id);
        test.stopTest();
    }
    static testMethod void testContinue() {
        evt__Special_Event__c event = [select Id from evt__Special_Event__c where Name = 'Special event'];
        evt__Special_Event__c event2 = [select Id from evt__Special_Event__c where Name = 'Special event 2'];
        hed__Course_Offering__c courseOff = [select Id from hed__Course_Offering__c where Name = 'Course Offering'];
        
        test.startTest();
        createShoppingCartController.addToCart(event.Id);// (event.Id);
        createShoppingCartController.addToCart(event2.Id);
        list<createShoppingCartController.cartWrapper> wrap = createShoppingCartController.addToCart(courseOff.Id);
        list<createShoppingCartController.cartWrapper> wrap2 = createShoppingCartController.addToCart(courseOff.Id);
        system.debug( 'size--> '+wrap.size() );
        createShoppingCartController.continueCartSave(wrap);
        createShoppingCartController.noRecord();
        test.stopTest();
    }
    static testMethod void testLoggedInUser(){
        test.startTest();
        createShoppingCartController.getUserDetails();
        
        test.stopTest();
    }
}