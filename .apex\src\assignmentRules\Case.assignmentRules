<?xml version="1.0" encoding="UTF-8"?>
<AssignmentRules xmlns="http://soap.sforce.com/2006/04/metadata">
    <assignmentRule>
        <fullName>Rotman Case Assignments</fullName>
        <active>true</active>
        <ruleEntry>
            <assignedTo>EP_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>equals</operation>
                <value>Employment Verification</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>RO_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>equals</operation>
                <value>Problem</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>RO_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>contains</operation>
                <value>Add/Drop Request Form,Class Registration,Course Auditing Form - Rotman Students Only,COVID-19 Emergency Aid Form,Emergency Financial Assessment Needs Form,Extension to Complete Coursework</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>RO_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>contains</operation>
                <value>Extra Course Application,Fee Deferral Form,FT Extra Course Form,FT Overload Form,Independent Study / Experiential Opportunity Form,International Experience,Letter Request Form,ME Extra Course Form,ME Overload Form</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>RO_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>contains</operation>
                <value>Onboarding Application,Petition for Grading Reassessment,Request for a Leave of Absence,Request to Accelerate Program Completion,Request to Decelerate Program Completion,Rotman Interest Subsidy Program (RISP) Application Form</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>RO_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>contains</operation>
                <value>Student Conferences/Competitions Subsidy Form,Verification of Student Illness or Injury</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>PS_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>contains</operation>
                <value>International Elective Program</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>FTMBA_Queue_Case</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;FTMBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>MMA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;MMA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>MFRM_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;MFRM&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>MFIN_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;MFIN&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>MEMBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>(Program_of_Interest__r.Name = &apos;Morning/Evening MBA (AM)&apos; || Program_of_Interest__r.Name = &apos;Morning/Evening MBA (PM)&apos;) &amp;&amp;  (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>GEMBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;GEMBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>GEMBA_HLS_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;GEMBA-HLS&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>Skoll_MBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;Skoll/MBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>JD_MBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;JD/MBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>MD_MBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;MD/MBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>MGA_MBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;MGA/MBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>PharmD_MBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;PharmD/MBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>EMBA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;EMBA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>GDipPA_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <formula>Program_of_Interest__r.Name = &apos;GDipPA&apos; &amp;&amp; (ISPICKVAL(Type, &apos;Resume Pre-Assessment&apos;) || ISPICKVAL(Type, &apos;Introduce Yourself RFI&apos;) || ISPICKVAL(Type, &apos;Questions for Rotman Team&apos;))</formula>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>EP_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>equals</operation>
                <value>Executive Programs RFI</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <criteriaItems>
                <field>Case.sfal__IsAdviseeRecord__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>Contact.Section__c</field>
                <operation>equals</operation>
                <value>Section 1</value>
            </criteriaItems>
            <overrideExistingTeams>true</overrideExistingTeams>
            <team>Section 1</team>
        </ruleEntry>
        <ruleEntry>
            <criteriaItems>
                <field>Case.sfal__IsAdviseeRecord__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>Contact.Section__c</field>
                <operation>equals</operation>
                <value>Section 2</value>
            </criteriaItems>
            <overrideExistingTeams>true</overrideExistingTeams>
            <team>Section 2</team>
        </ruleEntry>
        <ruleEntry>
            <criteriaItems>
                <field>Case.sfal__IsAdviseeRecord__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>Contact.Section__c</field>
                <operation>equals</operation>
                <value>Section 3</value>
            </criteriaItems>
            <overrideExistingTeams>true</overrideExistingTeams>
            <team>Section 3</team>
        </ruleEntry>
        <ruleEntry>
            <criteriaItems>
                <field>Case.sfal__IsAdviseeRecord__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>Contact.Section__c</field>
                <operation>equals</operation>
                <value>Section 4</value>
            </criteriaItems>
            <overrideExistingTeams>true</overrideExistingTeams>
            <team>Section 4</team>
        </ruleEntry>
        <ruleEntry>
            <criteriaItems>
                <field>Case.sfal__IsAdviseeRecord__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <team>RO Case Team</team>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>Event_Refund_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>contains</operation>
                <value>Refund Request</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>ADV_Case_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Type</field>
                <operation>equals</operation>
                <value>Course Auditing Form for Rotman Alumni</value>
            </criteriaItems>
            <template>unfiled$public/SUPPORTNewassignmentnotificationSAMPLE</template>
        </ruleEntry>
    </assignmentRule>
</AssignmentRules>
