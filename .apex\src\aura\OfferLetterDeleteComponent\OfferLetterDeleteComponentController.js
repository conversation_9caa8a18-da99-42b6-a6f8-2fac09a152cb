/**
 * Created by <PERSON><PERSON> on 2022-05-05.
 */

({
    /*call apex controller method "fetchContentDocument" to get salesforce file records*/
    doInit : function(component) {
        let action = component.get("c.fetchContentDocument");
        action.setParams({"appId": component.get("v.recordId")});
        action.setCallback(this, function(response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                component.set('v.lstContentDoc', response.getReturnValue());
                let curLstFiles = component.get('v.lstContentDoc');
                if (curLstFiles.length > 0){
                    component.set('v.hasOfferLetter', true);
                }
            }
            else if (state === "INCOMPLETE") {
                console.error('No response from server or client is offline.');
            } else if (state === "ERROR") {
                const errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        console.log("Error message: " +
                            errors[0].message);
                    }
                } else {
                    console.log("Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
    handleConfirmDialog : function(component, event, helper) {
        let conf = confirm('Are you sure you want to delete this Offer Letter PDF file?');
        console.log(conf);
        if (conf === true){
            helper.delSelected(component, event);
        }
    },

});