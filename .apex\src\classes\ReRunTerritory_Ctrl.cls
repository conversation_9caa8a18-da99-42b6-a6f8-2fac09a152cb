/**
 * Created by <PERSON><PERSON> on 2022-06-10.
 */

global class ReRunTerritory_Ctrl {
    @AuraEnabled
    global static String reRunTerritoryAssignment(String accId){
        List<Account> recordsToUpdate = new List<Account>();
        for (Account acc: [SELECT Id, Run_Territory_Assignment__c FROM Account WHERE Id = :accId]) {
            acc.Run_Territory_Assignment__c = true;
            recordsToUpdate.add(acc);
        }
        if (recordsToUpdate.size()>0) {
            try{
                System.debug('Update +++ '+recordsToUpdate);
                update recordsToUpdate;
            } catch(System.DmlException ex){
                throw new AuraHandledException(ex.getMessage());
            }
        }
        return 'SUCCESS';
    }
}