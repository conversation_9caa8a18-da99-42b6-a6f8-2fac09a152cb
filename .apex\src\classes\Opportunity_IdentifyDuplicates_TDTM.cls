/** 

* @description    TDTM driven handler settigng Opportunity names on create and identifying duplicate contacs
* <AUTHOR> 
* @version        1.0 

*/ 
global class Opportunity_IdentifyDuplicates_TDTM extends hed.TDTM_Runnable {

    // the Trigger Handler’s Run method we must provide
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
 
        //Only check Opportunities which are being created for duplicates.
        if ( triggerAction == hed.TDTM_Runnable.Action.BeforeInsert ) {
            Map<String, Integer> dupeOpptysByKey    = new Map<String, Integer>();
            Map<String, Integer> dupeOpptysByGKey   = new Map<String, Integer>();
            List<Opportunity> clonedOpptys          = new List<Opportunity>();

            for ( Opportunity o : (List<Opportunity>)newList )
                clonedOpptys.add( o.clone(false, false, false, false) );

            Formula.recalculateFormulas( clonedOpptys );
                
            for ( Integer i=0; i < newList.size(); i++ ) {

                Opportunity o       = (Opportunity)newList[i];
                Opportunity oClone  = clonedOpptys[i];

                
                //Assign the Opportunity's name using the naming formula
                o.Name = oClone.Formula_Name__c != null ? oClone.Formula_Name__c : o.Name ;

                //Assign account ID as contact admin account
                if ( o.AccountId == null && oClone.Contact_Administrative_Account__c != null )
                    o.AccountId = oClone.Contact_Administrative_Account__c;

                //Check to see if the Opportunity has any matching Opportunities with a matching program
                if ( oClone.Matching_Key__c != null ) 
                    if ( dupeOpptysByKey.keySet().contains(oClone.Matching_Key__c) || dupeOpptysByGKey.keySet().contains(oClone.Matching_Key__c) ) {
                        o.addError('Record at row ' + dupeOpptysByKey.get(oClone.Matching_Key__c) + ' duplicates record ' + i + ' in list');
                    } else {
                        dupeOpptysByKey.put( oClone.Matching_Key__c, i );
                        if ( oClone.Matching_Key_Genericized__c != null )
                            dupeOpptysByGKey.put( oClone.Matching_Key_Genericized__c, i );
                    }

            }
                        
            for ( Opportunity o : [ SELECT Id, Matching_Key_Genericized__c, Matching_Key__c FROM Opportunity WHERE IsClosed = false AND (Matching_Key__c IN :dupeOpptysByKey.keySet() OR Matching_Key__c IN :dupeOpptysByGKey.keySet() OR Matching_Key_Genericized__c IN :dupeOpptysByKey.keySet()) ] ) {
                
                if ( dupeOpptysByKey.containsKey(o.Matching_Key__c) )
                    newList[dupeOpptysByKey.get(o.Matching_Key__c)].addError('Record on row ' + dupeOpptysByKey.get(o.Matching_Key__c) + ' duplicates id: ' + o.Id );
                else if ( dupeOpptysByGKey.containsKey(o.Matching_Key__c) )
                    newList[dupeOpptysByGKey.get(o.Matching_Key__c)].addError('Record on row ' + dupeOpptysByGKey.get(o.Matching_Key__c) + ' duplicates id: ' + o.Id );
                else if ( dupeOpptysByKey.containsKey(o.Matching_Key_Genericized__c) )
                    newList[dupeOpptysByKey.get(o.Matching_Key_Genericized__c)].addError('Record on row ' + dupeOpptysByKey.get(o.Matching_Key__c) + ' duplicates id: ' + o.Id );

            }

        
        //Need to rename any Opportunities which have had their program changed
        } else if ( triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate ) {

            List<Opportunity> newOpptys = (List<Opportunity>)newList;
            List<Opportunity> oldOpptys = (List<Opportunity>)oldList;

            for ( Integer i=0; i<newList.size(); i++ ) 
                if ( newOpptys[i].Program_Account__c != oldOpptys[i].Program_Account__c ) {
                    Opportunity oClone  = newOpptys[i].clone(false, false, false, false);
                    newOpptys[i].Name   = oClone.Formula_Name__c;
                }

        }

        return dmlWrapper;
    }
}