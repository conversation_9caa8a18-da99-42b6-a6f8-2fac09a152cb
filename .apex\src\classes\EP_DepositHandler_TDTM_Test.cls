/**
* @description    Test class for EP_DepositHandler_TDTM_Test class
* <AUTHOR>
* @version        1.0 
* @created 2020-30-10
* @modified 2020-30-10
*/
@isTest
public class EP_DepositHandler_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for EP_DepositHandler_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('EP_DepositHandler_TDTM', 'pymt__Shopping_Cart_Item__c', 'BeforeInsert;BeforeUpdate', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        insert c; 
        //Insert store configs
        Store_Configuration__c sc = new Store_Configuration__c (
            Name = 'EP Balance'
        );
        insert sc;
        Store_Configuration__c sc1 = new Store_Configuration__c (
            Name = 'EP Courses'
        );
        insert sc1;
        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c    = c.Id,
            Name                = 'TEST Payment',
            pymt__Status__c     = 'Online Checkout'
        );
        insert pymt;
        pymt__Shopping_Cart__c sCart = new pymt__Shopping_Cart__c(Store_Configuration__c = sc1.Id,pymt__Contact__c = c.Id);
        insert sCart;
        //List of test records to insert: 
        List<pymt__Shopping_Cart_Item__c> shpngCITMToInsert = new List<pymt__Shopping_Cart_Item__c>(); 
        shpngCITMToInsert.add(new pymt__Shopping_Cart_Item__c(pymt__Payment__c= pymt.ID,pymt__Payment_Completed__c=true, Type__c = 'EP Program Deposit', Name = 'Test1', pymt__Unit_Price__c = 12,pymt__Quantity__c=300)); 
        shpngCITMToInsert.add(new pymt__Shopping_Cart_Item__c(pymt__Payment__c= pymt.ID,pymt__Payment_Completed__c=true, Type__c = 'EP Program Deposit', Name = 'Test2', pymt__Unit_Price__c = 13, pymt__Quantity__c = 700)); 
        shpngCITMToInsert.add(new pymt__Shopping_Cart_Item__c(pymt__Contact__c = c.Id, pymt__Shopping_Cart__c = sCart.Id,pymt__Payment__c= pymt.ID,pymt__Payment_Completed__c=false, Type__c = 'EP Program Deposit', Name = 'Test3', pymt__Unit_Price__c = 15,pymt__Quantity__c=400));
        shpngCITMToInsert.add(new pymt__Shopping_Cart_Item__c(pymt__Contact__c = c.Id, pymt__Shopping_Cart__c = sCart.Id,pymt__Payment__c= pymt.ID,pymt__Payment_Completed__c=false, Type__c = 'EP Program Deposit', Name = 'Test4', pymt__Unit_Price__c = 15,pymt__Quantity__c=400)); 
        insert shpngCITMToInsert;
    }

    /**
     * @description Update Shopping Cart Items
     * 
     *  
     */
    @isTest 
    public static void updateSCI(){
        pymt__Shopping_Cart_Item__c scItemToUpdate = [SELECT Id,Type__c,pymt__Payment__c,pymt__Quantity__c,pymt__Unit_Price__c,pymt__Payment_Completed__c from pymt__Shopping_Cart_Item__c where pymt__Payment_Completed__c=false Limit 1];
        scItemToUpdate.pymt__Payment_Completed__c = true;
        update scItemToUpdate;
    }
}