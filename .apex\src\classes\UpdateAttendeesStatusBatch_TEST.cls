@IsTest
private class UpdateAttendeesStatusBatch_TEST {
	@testSetup
	static void setup() {
		Account account = new Account(
				Name = 'Test Account'
		);
		insert account;

		Contact contact = new Contact(
				FirstName = 'Test',
				LastName = 'Contact',
				Email = '<EMAIL>',
				AccountId = account.Id
		);
		insert contact;

		evt__Special_Event__c specialEvent = new evt__Special_Event__c(
				Name = 'Test Event',
				evt__Agenda_HTML__c = 'Test Agenda',
				evt__Publish_To__c = 'Public Events',
				Start_Local__c = Datetime.now(),
				End_Local__c = Datetime.now().addDays(1)
		);
		insert specialEvent;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = specialEvent.Id;
		fee.evt__Amount__c = 10.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
		pymnt.Name = 'Event Checkout';
		pymnt.pymt__Transaction_Type__c = 'Payment';
		pymnt.pymt__Payment_Type__c	 = 'Credit Card';
		pymnt.pymt__Contact__c = contact.Id;
		pymnt.pymt__Amount__c = 100;
		pymnt.Gross_Amount__c = 100;
		pymnt.pymt__Tax__c = 10;
		pymnt.pymt__Discount__c = 20;
		pymnt.Type__c = 'Event Registration';
		pymnt.pymt__Payment_Processor__c = 'Global Pay';
		pymnt.pymt__Transaction_Id__c = 'TRN_ReJjB6G4EQPgkBtL4ZTI9aGUMtAp8w_00000NnKzIAK';
		pymnt.Payment_Response__c = '2024-05-07 01:45:14 ->{"id":"TRN_2GHoQs5VSYbwTudmDamrgORvlUXWlx_00000QEgrIAG","time_created":"2024-05-07T01:45:14.607Z","type":"SALE","status":"CAPTURED","channel":"CNP","capture_mode":"AUTO","amount":"395","currency":"CAD","country":"CA","merchant_id":"MER_7e3e2c7df34f42819b3edee31022ee3f","merchant_name":"Sandbox_merchant_3","account_id":"TRA_c9967ad7d8ec4b46b6dd44a61cde9a91","account_name":"transaction_processing","reference":"a0yG1000000QEgrIAG","payment_method":{"result":"00","message":"(00)[ test system ] Authorised","entry_mode":"ECOM","card":{"brand":"MASTERCARD","masked_number_last4":"XXXXXXXXXXXX5454","authcode":"123456","brand_reference":"GVLKuLGH9yLNau2x","brand_time_created":"","cvv_result":"NOT_CHECKED","provider":{"result":"00","cvv_result":"U","avs_address_result":"U","avs_postal_code_result":"U"}}},"risk_assessment":[{"mode":"ACTIVE","result":"ACCEPTED","rules":[{"reference":"0c93a6c9-7649-4822-b5ea-1efa356337fd","description":"Cardholder Name Rule","mode":"ACTIVE","result":"ACCEPTED"},{"reference":"a539d51a-abc1-4fff-a38e-b34e00ad0cc3","description":"CardNumber block","mode":"ACTIVE","result":"ACCEPTED"},{"reference":"d023a19e-6985-4fda-bb9b-5d4e0dedbb1e","description":"Amount test","mode":"ACTIVE","result":"ACCEPTED"}]}],"batch_id":"BAT_1388783","action":{"id":"ACT_2GHoQs5VSYbwTudmDamrgORvlUXWlx","type":"AUTHORIZE","time_created":"2024-05-07T01:45:14.607Z","result_code":"SUCCESS","app_id":"NZOF9fCkU7eQzwKAogwHJTM1Z8rC3oC9","app_name":"sample_app_CERT"}}';
		insert pymnt;

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = contact.Id;
		item.Event_Fee__c = fee.Id;
		item.pymt__Unit_Price__c = 10.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';
		item.Special_Event__c = specialEvent.Id;
		item.pymt__Payment__c = pymnt.Id;

		insert item;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = contact.Id;
		at.evt__Invitation_Status__c = 'Invited';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.Shopping_Cart_Item__c = item.Id;
		at.evt__Event__c = specialEvent.Id;
		at.evt__Reg_Email__c = '<EMAIL>';
		at.evt__Reg_First_Name__c = 'John';
		at.evt__Reg_Last_Name__c = 'Doe';

		insert at;

	}

	@isTest
	private static void testExecuteWithUpdate() {
		Test.startTest();
		UpdateAttendeesStatusBatch batchJob = new UpdateAttendeesStatusBatch();
		Database.executeBatch(batchJob);
		Test.stopTest();
	}
}