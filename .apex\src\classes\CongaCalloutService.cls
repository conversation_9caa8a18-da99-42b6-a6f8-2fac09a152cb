/**

* @description    fires a REST callout to Jitterbit containing all Conga parameters
* <AUTHOR>
* @version        1.0

*/

public class CongaCalloutService {

    private static final Integer BATCH_SIZE = 20; //Maximum number of calls allowed in future method
    private static Map<Id, String> recordIdToCongaParamsMap = new Map<Id, String>();

    /**
     * @description Request class holds record Id and conga params
     */
    public class Request{
        @InvocableVariable(label='Conga Parameters' description='Additional Conga Parameters (in string notation)' required=true)
        public String congaParams;

        @InvocableVariable(label='Additional Conga Parameters' description='Additional Conga Parameters (in string notation)' required=false)
        public String additionalParams;

        @InvocableVariable(label='Record Id' description='Record Id' required=true)
        public Id recordId;
    }

    /**
     * @description makes callout to Jitterbit to generate conga documents
     * @param List<Request> list of requests
     */
    @InvocableMethod(label='Conga Callout to Jitterbit' description='Fires REST callout to Jitterbit to generate Conga document')
    public static void callJitterbit(List<Request> requests){
        List<Id> loopRequests = new List<Id>(); //App IDs to be sent to individual async context. Vidyard API does not support batching, necessary to 'batch' callouts to prevent timeouts
        Map<Id, String> idToCongaParamsMap = new Map<Id, String>();

        for ( Integer i=0; i < requests.size(); i++ ) {
            String parameters = requests[i].additionalParams != null ? requests[i].congaParams + requests[i].additionalParams : requests[i].congaParams;
            System.debug(parameters);
            idToCongaParamsMap.put(requests[i].recordId, parameters);

            //If the batch size is equal to the number of Ids in the list, call future method to make callouts
            if ( idToCongaParamsMap.size() == BATCH_SIZE || (i == requests.size()-1 && idToCongaParamsMap.size() > 0) ) {
                performCall(idToCongaParamsMap);
                idToCongaParamsMap = new Map<Id, String>();
            }
        }

    }


    /**
     * @description constructs HTTP request and makes callout to Jitterbit
     * @param recordIdToCongaParamsMap maps record time to conga parameters
     */
    @future(callout=true)
    public static void performCall(Map<Id, String> recordIdToCongaParamsMap) {
        System.debug(recordIdToCongaParamsMap);
        for ( Id objId : recordIdToCongaParamsMap.keySet() ) {

            Http http = new Http();
            HttpRequest request = new HttpRequest();
            request.setEndpoint('callout:Conga_Jitterbit_Integration');
            // request.setEndpoint('https://uot440031.jitterbit.net/Development/congaRequest');
            request.setMethod('POST');
            request.setHeader('Content-Type', 'application/json');

            //Set the body of the request
            String body = '{"fields":{' +
                    '"Id":"' + objId + '",' +
                    '"CongaParams":"'  + recordIdToCongaParamsMap.get(objId) + '"' +
                    '},' +
                    '"notification_url":"http://test.com",' +
                    '"meta":{' +
                    '"Id": "' + objId + '"' +
                    '}}';

            request.setBody(body);
            request.setTimeout(120000);

            HttpResponse response = http.send(request);

        }

    }
}