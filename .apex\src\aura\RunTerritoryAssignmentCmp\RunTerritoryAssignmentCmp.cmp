<aura:component implements="lightning:actionOverride,force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global" controller="ReRunTerritory_Ctrl">

    <!--aura attributes-->
    <aura:attribute name="recordId" type="string"/>
    <div class="slds-m-around_xx-large" style="margin:10px!important;">
        <div class="slds-modal__content">
            <div style="font-weight: bold;">
                <p>This button will go through all applications related to re-running Territory Assignment Rules in the Backend if you find that ADs are not assigned correctly to this account.</p>
                <br/>
                <p>Rerun your report a few minutes later after this button clicked, to ensure ADs are assigned correctly.</p>
            </div>
            <div class="slds-clearfix" style="padding: 25px;">
                <div class="slds-float_right">
                    <lightning:button class="runTerritoryAssignmentBtn" variant="brand"
                                      label="Run Territory Assignment" title="Run Territory Assignment"
                                      onclick="{! c.runTerritoryAssignment }"/>
                </div>
            </div>
        </div>
    </div>

</aura:component>