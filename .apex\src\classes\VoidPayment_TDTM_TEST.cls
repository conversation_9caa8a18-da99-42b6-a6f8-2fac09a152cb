@isTest
public class VoidPayment_TDTM_TEST {
	@testSetup
    static void testSetup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for VoidPayment_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('VoidPayment_TDTM', 'pymt__Shopping_Cart_Item__c', 'BeforeInsert;BeforeUpdate', 1.00));
        //create trigger handler for PaymentService class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('PaymentService_TDTM', 'pymt__PaymentX__c', 'BeforeInsert;', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        insert c; 
        //Insert store configs
        Store_Configuration__c sc = new Store_Configuration__c (
            Name = 'EP Balance'
        );
        insert sc;
        Store_Configuration__c sc1 = new Store_Configuration__c (
            Name = 'EP Courses'
        );
        insert sc1;
        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c    = c.Id,
            Name                = 'TEST Payment',
            pymt__Status__c     = 'Online Checkout'
        );
        insert pymt;
        pymt__Shopping_Cart__c sCart = new pymt__Shopping_Cart__c(Store_Configuration__c = sc1.Id,pymt__Contact__c = c.Id);
        insert sCart;
        //List of test records to insert: 
        List<pymt__Shopping_Cart_Item__c> sciToInsert = new List<pymt__Shopping_Cart_Item__c>(); 
        sciToInsert.add(new pymt__Shopping_Cart_Item__c(pymt__Payment__c= pymt.ID,pymt__Payment_Completed__c=False, Type__c = 'EP Program Balance', Name = 'Test1', pymt__Unit_Price__c = 12,pymt__Quantity__c=1));
        insert sciToInsert;
    }
    
    @isTest
    public static void testVoidPayment(){
        Contact c = [SELECT ID FROM Contact LIMIT 1];
        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c    = c.Id,
            Name                = 'TEST Payment',
            pymt__Status__c     = 'Online Checkout'
        );
        insert pymt;
        pymt__Shopping_Cart_Item__c newSCI = new pymt__Shopping_Cart_Item__c(
            pymt__Payment__c= pymt.ID,
            pymt__Payment_Completed__c=False, 
            Type__c = 'EP Program Balance', 
            Name = 'Test1', 
            pymt__Unit_Price__c = 15,
            pymt__Quantity__c=1,
            Void_Payment__c = True
        );
        
        pymt__Shopping_Cart_Item__c updatedSCI = [SELECT Id, Void_Payment__c FROM pymt__Shopping_Cart_Item__c LIMIT 1];
        updatedSCI.Void_Payment__c = True;
        
        test.startTest();
        	insert newSCI;
        	update updatedSCI;
        test.stopTest();
        
        List<pymt__Shopping_Cart_Item__c> sciList = [SELECT Id, Void_Payment__c, pymt__Payment__c FROM pymt__Shopping_Cart_Item__c WHERE Void_Payment__c = True AND pymt__Payment__c = null];
        System.assertEquals(2,sciList.size());
    }
}