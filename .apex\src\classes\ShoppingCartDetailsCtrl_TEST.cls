@IsTest
private class ShoppingCartDetailsCtrl_TEST {
	@TestSetup
	private static void testDataSetup() {
		Id fulltimeId = ApplicationService.FTandSpecializedRTId;
		Account testAccount = new Account(Name = 'Test Account');
		insert testAccount;

		Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>',
				AccountId = testAccount.Id);
		insert testContact;

		String cookieName = String.valueOf(dateTime.now());

		pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
		cart.pymt__Cart_UID__c = cookieName;
		insert cart;

		hed__Term__c trm = new hed__Term__c(hed__Account__c = testAccount.Id, name = 'Spring 2020');
		insert trm;

		Cohort__c cohort = new Cohort__c(
				Name = 'FTMBA - 2025',
				Start_Term__c = trm.Id,
				Program__c = testAccount.Id,
				Orientation_Date__c = Date.valueOf('2024-04-09 09:30:40'),
				Key__c = 'FTMBA - 2025'
		);
		insert cohort;

		Program_Term_Availability__c pta = new Program_Term_Availability__c(
				Active__c = true,
				Program__c = testAccount.Id,
				Cohort__c = cohort.Id,
				Program_Start_Date__c = Date.valueOf('2022-12-09 10:15:30'),
				Program_End_Date__c = Date.valueOf('2024-04-09 09:30:40'),
				Term__c = trm.Id
		);
		insert pta;

		hed__Application__c app = new hed__Application__c(
				Program_Term_Availability__c = pta.Id,
				hed__Application_Status__c = 'In Progress',
				RecordTypeId = fulltimeId,
				hed__Applying_To__c = testAccount.Id,
				hed__Term__c = trm.Id
		);
		insert app;

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c =
				'$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.Venue_Type__c = 'In-Person';
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.Tags__c = 'Strategic Communications';
		event.Thumbnail_Image__c =
				'<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';
		event.evt__Web_Meeting_Join_URL__c = 'http://test.zoom.us';
		event.Support_Email__c = '<EMAIL>';

		List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
		listOfEventToInsert.add(event);

		insert listOfEventToInsert;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = listOfEventToInsert[0].Id;
		fee.evt__Amount__c = 0.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		/*pymt__PaymentX__c payment = new pymt__PaymentX__c();
		payment.Name = 'Event Payment';
		payment.pymt__contact__c = testContact.Id;
		payment.Type__c = 'Event Registration';
		payment.pymt__Status__c = 'Online Checkout';
		payment.pymt__Amount__c = 10.0;
		payment.pymt__Status__c = 'Completed';
		payment.pymt__Payment_Type__c = 'Credit Card';
		payment.pymt__Payment_Processor__c = 'Global Pay';
		payment.pymt__Billing_First_Name__c = 'John';
		payment.pymt__Billing_Last_Name__c = 'Doe';
		payment.pymt__Billing_Street__c = '123 Main St';
		payment.pymt__Billing_City__c = 'Toronto';
		payment.pymt__Billing_State__c = 'ON';
		payment.pymt__Billing_Postal_Code__c = 'M5V 1A1';
		payment.pymt__Billing_Country__c = 'CA';
		payment.pymt__Card_Type__c = 'Visa';
		payment.pymt__Last_4_Digits__c = '1234';
		insert payment;*/

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = testContact.Id;
		//item.pymt__Payment__c = payment.Id;
		item.Event_Fee__c = fee.Id;
		item.pymt__Unit_Price__c = 0.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';

		insert item;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = testContact.Id;
		at.evt__Invitation_Status__c = 'Registered';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.Shopping_Cart_Item__c = item.Id;
		at.evt__Event__c = event.Id;
		at.evt__Reg_Email__c = '<EMAIL>';
		at.Industry__c = 'Technology';

		insert at;

		Shopping_Cart_Item_Details__c testCartItemDetail1 = new Shopping_Cart_Item_Details__c();
		testCartItemDetail1.SC_event__c = listOfEventToInsert[0].Id;
		testCartItemDetail1.Event_Fee__c = fee.Id;
		testCartItemDetail1.Contact__c = testContact.Id;
		testCartItemDetail1.Shopping_Cart_Item__c = item.Id;
		testCartItemDetail1.Attendee__c = at.Id;
		testCartItemDetail1.Item_Unit_Price__c = 5.0;
		testCartItemDetail1.Item_Quantity__c = 1.0;
		testCartItemDetail1.Item_Discount_Amount__c = 0.0;
		testCartItemDetail1.Item_Gross_Amount__c = 5.0;
		testCartItemDetail1.Item_Tax_Amount__c = 0.0;
		testCartItemDetail1.Item_Total_Amount__c = 5.0;

		insert testCartItemDetail1;

		Shopping_Cart_Item_Details__c testCartItemDetail2 = new Shopping_Cart_Item_Details__c();
		testCartItemDetail2.SC_event__c = listOfEventToInsert[0].Id;
		testCartItemDetail2.Event_Fee__c = fee.Id;
		testCartItemDetail2.Contact__c = testContact.Id;
		testCartItemDetail2.Shopping_Cart_Item__c = item.Id;
		testCartItemDetail2.Attendee__c = at.Id;
		testCartItemDetail2.Item_Unit_Price__c = 5.0;
		testCartItemDetail2.Item_Quantity__c = 1.0;
		testCartItemDetail2.Item_Discount_Amount__c = 0.0;
		testCartItemDetail2.Item_Gross_Amount__c = 5.0;
		testCartItemDetail2.Item_Tax_Amount__c = 0.0;
		testCartItemDetail2.Item_Total_Amount__c = 5.0;

		insert testCartItemDetail2;
	}

	@isTest
	static void testGetOrderInfo1() {
		pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];

		Test.startTest();
		ShoppingCartDetailsCtrl ordersList = new ShoppingCartDetailsCtrl();
		List<Shopping_Cart_Item_Details__c> orders = ordersList.getOrderInfo(cartItem.Id, false);
		Test.stopTest();
	}

	@isTest
	static void testGetOrderInfo2() {
		pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];

		Test.startTest();
		ShoppingCartDetailsCtrl ordersList = new ShoppingCartDetailsCtrl();
		List<Shopping_Cart_Item_Details__c> orders = ordersList.getOrderInfo(cartItem.Id, true);
		Test.stopTest();
	}

	@isTest
	static void testGetEventInfo() {
		pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		//pymt__PaymentX__c payment = [SELECT Id FROM pymt__PaymentX__c LIMIT 1];
		//cartItem.pymt__Payment__c = payment.Id;
		//update cartItem;

		List<Shopping_Cart_Item_Details__c> testCartItemDetail = [SELECT Id, Name, SC_event__c, Contact__c, SC_event__r.Thumbnail_Image__c, SC_event__r.evt__Web_Meeting_Join_URL__c, SC_event__r.Name, SC_event__r.Event_Date_Time__c, Contact__r.Name, SC_event__r.Support_Email__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Payment_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Card_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Last_4_Digits__c, CreatedDate, Item_Total_Amount__c FROM Shopping_Cart_Item_Details__c];

		Test.startTest();
		System.debug(testCartItemDetail);
		ShoppingCartDetailsCtrl ordersList = new ShoppingCartDetailsCtrl();
		Map<String, String> eventInfo = ordersList.getEventInfo(testCartItemDetail);
		Test.stopTest();

	}

	@isTest
	static void testGetAttendees() {
		pymt__Shopping_Cart_Item__c cartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		List<Shopping_Cart_Item_Details__c> testCartItemDetail = [SELECT Id, Name, Attendee__r.Check_In_QR_Code_URL__c, Attendee__r.evt__Reg_First_Name__c, Attendee__r.evt__Reg_Last_Name__c, Attendee__r.evt__Reg_Email__c, Attendee__r.evt__Event_Fee__r.Name, Attendee__r.evt__Invitation_Status__c, Item_Total_Amount__c FROM Shopping_Cart_Item_Details__c];

		Test.startTest();
		ShoppingCartDetailsCtrl ordersList = new ShoppingCartDetailsCtrl();
		List<ShoppingCartDetailsCtrl.attendeeWrapperClass> attendees =
				ordersList.getAttendees(testCartItemDetail, new List<String>());
		Test.stopTest();
	}
}