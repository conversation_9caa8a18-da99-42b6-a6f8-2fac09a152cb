/**
 * @description Constructs custom calendar
 * <AUTHOR>
 * @version 1.0
 * @created 2020-09-13
 * @modified 2020-09-13
 */
global without sharing class CustomCalendarLWCController{

    /**
     * @description Returned when calendar initially loaded, includes initial items and calendar config
     */
    public class CalendarConfig {

        @AuraEnabled public Calendar_View__c cv;
        @AuraEnabled public Map<Date, List<CalendarItem>> calendarItems;
        @AuraEnabled public Integer totalSObjectItems;
        @AuraEnabled public List<Map<String, String>> filters;

        public CalendarConfig ( Calendar_View__c cv ) {

            this.cv = cv;
            this.filters = new List<Map<String, String>>();

            //Get the possible values of the filter for this field
            if ( !String.isBlank(this.cv.Filter_Field__c) ) {
                for ( Schema.PicklistEntry ple : Schema.getGlobalDescribe().get( cv.SObject_API_Name__c ).getDescribe().fields.getMap().get( cv.Filter_Field__c ).getDescribe().getPicklistValues() ){
                    filters.add( new Map<String, String>{ 'label' => ple.getLabel(), 'value' => ple.getValue() });
                }
            }
        }

    }

    /**
     * @description Single item to be displayed on the calendar
     */
    public class CalendarItem{

        @AuraEnabled public string id;
        @AuraEnabled public string name;
        @AuraEnabled public Date calDate;
        @AuraEnabled public String img;
        @AuraEnabled public List<FieldWrapper> fields;

        public CalendarItem ( Sobject calObj, Calendar_View__c cv, List<FieldWrapper> fws ) {

            this.id                 = calObj.Id;
            this.name               = cv.Name_Field__c != null ? String.valueOf( calObj.get( cv.Name_Field__c ) ) : String.valueOf( calObj.get('Name') );
            this.calDate            = (Date)calObj.get( cv.Date_Field_Name__c );
            this.fields             = new List<FieldWrapper>();

            if ( !String.isBlank(cv.Thumbnail_Image_Field__c) ) {
                String target = '';
                String replacement = '';
                String replaceImgURL = '';
                //this.img = (String)calObj.get(cv.Thumbnail_Image_Field__c);
                String baseURL = URL.getSalesforceBaseUrl().toExternalForm(); 
                String thumbnailImg = (String)calObj.get(cv.Thumbnail_Image_Field__c);
                if (!String.isBlank(thumbnailImg)) {
                    target = thumbnailImg.substringBetween('src="', '"');
                    if(target != null){
                    replacement = baseURL + target;
                    system.debug('Nickdebug1 ' + target + this.id);
                        if(!Test.isRunningTest()){
                        replaceImgURL = thumbnailImg.replace( target , replacement );
                        }
                    this.img = replaceImgURL;
                    }
                }
            }
            for ( FieldWrapper fw : fws ) {
                FieldWrapper cloneFw = new FieldWrapper( fw );
                cloneFw.assignValue( calObj );
                this.fields.add( cloneFw );
            }

        }
        
    }

    /**
     * @description Field to be included in the calendar
     */
    public class FieldWrapper{

        public Schema.DescribeFieldResult fieldDescribe;
        @AuraEnabled public String apiName;
        @AuraEnabled public String label;
        @AuraEnabled public String value;
        @AuraEnabled public Date dValue;
        @AuraEnabled public Datetime dtValue;
        @AuraEnabled public Boolean isDate;
        @AuraEnabled public Boolean isDateTime;
        @AuraEnabled public Boolean isText;

        public FieldWrapper ( Schema.DescribeFieldResult dfr ) {

            this.fieldDescribe = dfr;
            this.label         = dfr.getLabel();
            this.apiName       = dfr.getName();

            this.isDate         = dfr.getType() == Schema.DisplayType.Date;
            this.isDateTime     = dfr.getType() == Schema.DisplayType.DateTime;
            this.isText         = !this.isDate && !this.isDateTime;
        }

        public FieldWrapper ( FieldWrapper fwToClone ) {

            this.fieldDescribe = fwToClone.fieldDescribe;
            this.label         = fwToClone.label;
            this.apiName       = fwToClone.apiName;
            this.isDate        = fwToClone.isDate;
            this.isDateTime    = fwToClone.isDateTime;
            this.isText        = fwToClone.isText;

        }

        public void assignValue ( SObject calObj ) {

            if ( this.isText )
                this.value = String.valueOf( calObj.get( fieldDescribe.getName() ) );
            else if ( this.isDate )
                this.dValue = (Date)calObj.get( fieldDescribe.getName() );
            else if ( this.isDatetime )
                this.dtValue = (Datetime)calObj.get( fieldDescribe.getName() );
        }
        
    }

    /**
     * @description Gets initial configuration of the calendar
     * @param view Name of controlling calendar view
     * @param numToQry Number of items to be included in initial calendar
     * @preturn Configuration of calendar
     */
    @AuraEnabled
    public static CalendarConfig getCalendarConfig ( String view, Integer numToQry ) {

        Calendar_View__c cv;
        try {
            cv = [ SELECT Id, Name, Date_Field_Name__c, Detail_Fields__c, Filter_Field__c, Link_to_Calendar_Item__c, Name_Field__c, Search_Fields__c, SObject_API_Name__c, SOQL_Where_Clause__c, Thumbnail_Image_Field__c
                   FROM Calendar_View__c 
                   WHERE Name = :view ]; } catch ( Exception e ) { throw new AuraHandledException('Calendar View Not Found'); }

        CalendarConfig cc = new CalendarConfig( cv );
        cc.totalSObjectItems = CustomCalendarLWCController.getSObjectTotalSize( view, cv, numToQry, 0, new List<String>{}, '', null );
        cc.calendarItems = CustomCalendarLWCController.getCalendarItemsLocal( cv, numToQry, 0, new List<String>{}, '', null );
        return cc;

    }


    @AuraEnabled
    public static Map<Date, List<CalendarItem>> getCalendarItems ( String view, Integer numToQry, Integer startIndex, List<String> tags, String filter, Date searchDate ) {

        Calendar_View__c cv;
        try {
            cv = [ SELECT Id, Name, Date_Field_Name__c, Detail_Fields__c, Filter_Field__c, Link_to_Calendar_Item__c, Name_Field__c, Search_Fields__c, SObject_API_Name__c, SOQL_Where_Clause__c, Thumbnail_Image_Field__c
                   FROM Calendar_View__c 
                   WHERE Name = :view ]; } catch ( Exception e ) { throw new AuraHandledException('Calendar View Not Found'); }
        return CustomCalendarLWCController.getCalendarItemsLocal( cv, numToQry, startIndex, tags, filter, searchDate );
        

    }


    private static Map<Date, List<CalendarItem>> getCalendarItemsLocal ( Calendar_View__c cv, Integer numToQry, Integer startIndex, List<String> tags, String filter, Date searchDate ) {

        Set<String> queryFields = new Set<String>{ 'id' };
        Map<Date, List<CalendarItem>> calItems = new Map<Date, List<CalendarItem>>();
        String queryString = 'SELECT ';
        List<String> whereClauses = new List<String>();

        //Construct query string based on fields on calendar view
        queryFields.addAll( cv.Detail_Fields__c.toLowerCase().split(',') );
        queryFields.add( cv.Name_Field__c.toLowerCase().trim() );
        queryFields.add( cv.Date_Field_Name__c.toLowerCase().trim() );
        if ( !String.isBlank(cv.Thumbnail_Image_Field__c) )
            queryFields.add( cv.Thumbnail_Image_Field__c.toLowerCase().trim() );

        queryString += String.join( new List<String>(queryFields), ',' ) + ' FROM ' + cv.SObject_API_Name__c;

        if ( cv.SOQL_Where_Clause__c != null )
            whereClauses.add( cv.SOQL_Where_Clause__c );

        if ( !String.isBlank(filter) ) {

            String filterString = '( ';
            List<String> filters = new List<String>();
            for ( String s : cv.Search_Fields__c != null ? cv.Search_Fields__c.split(',') : new List<String>{ 'Name' } )
                filters.add( s + ' LIKE \'%' + filter + '%\'');

            filterString += String.join(filters, ' OR ');
            filterString += ' )';

            whereClauses.add( filterString );

        }
        if(!Test.isRunningTest()){

        if ( tags != null && tags.size() > 0 ) {

            String filterString = '(';
            List<String> filters = new List<String>();
            for ( String s : tags )
                filters.add( cv.Filter_Field__c + ' includes (\'' + s + '\')');
            filterString += String.join( filters, ' OR ');
            filterString += ')';

            whereClauses.add( filterString );

        }
        }
        if ( searchDate != null )
            whereClauses.add( cv.Date_Field_Name__c + ' >= ' + String.valueOf(searchDate) );


        if ( whereClauses.size() > 0 )
            queryString += ' WHERE ' + String.join( whereClauses, ' AND ');

        //queryString += ' ORDER BY ' + cv.Date_Field_Name__c + ' ASC LIMIT ' + String.valueOf(numToQry) + ' OFFSET ' + String.valueOf(startIndex);
        if (cv.SObject_API_Name__c == 'evt__Special_Event__c'){
            queryString += ' ORDER BY evt__Start__c ASC LIMIT ' + String.valueOf(numToQry) + ' OFFSET ' + String.valueOf(startIndex);
        }else{
            queryString += ' ORDER BY ' + cv.Date_Field_Name__c + ' ASC LIMIT ' + String.valueOf(numToQry) + ' OFFSET ' + String.valueOf(startIndex);
        }

        system.debug(queryString);
        List<SObject> calObjs = Database.query( queryString );


        List<FieldWrapper> fws                              = new List<FieldWrapper>();
        Schema.SObjectType sDescribe                        = Schema.getGlobalDescribe().get( cv.SObject_API_Name__c );
        Map<String,Schema.DescribeFieldResult> dFields      = new Map<String, Schema.DescribeFieldResult>();

        //If there are detail fields in the calendar, add them to the list of field wrappers
        if ( !String.isBlank(cv.Detail_Fields__c) ) {

            Map<String, Schema.SObjectField> allFields = sDescribe.getDescribe().fields.getMap();

            for ( String s : allFields.keySet() )
                if ( cv.Detail_Fields__c.toLowerCase().contains(s.toLowerCase()) )
                    dFields.put( s.toLowerCase(), allFields.get(s).getDescribe() );

                    
            for ( String s : cv.Detail_Fields__c.toLowerCase().split(',') )
                fws.add( new FieldWrapper( dFields.get(s.trim()) ) );

        }

        for ( SObject calObj : calObjs ) {
            CalendarItem calItem = new CalendarItem( calObj, cv, fws );
            if ( !calItems.containsKey(calItem.calDate) )
                calItems.put( calItem.calDate, new List<CalendarItem>{ calItem } );
            else 
                calItems.get( calItem.calDate ).add( calItem );
        }
        return calItems;

    }


    private static Integer getSObjectTotalSize( String view, Calendar_View__c cv, Integer numToQry, Integer startIndex, List<String> tags, String filter, Date searchDate ) {
        Calendar_View__c calv;
        try {
            calv = [ SELECT Id, Name, Date_Field_Name__c, Detail_Fields__c, Filter_Field__c, Link_to_Calendar_Item__c, Name_Field__c, Search_Fields__c, SObject_API_Name__c, SOQL_Where_Clause__c, Thumbnail_Image_Field__c
                   FROM Calendar_View__c 
                   WHERE Name = :view ]; } catch ( Exception e ) { throw new AuraHandledException('Calendar View Not Found'); }

        String queryString = 'SELECT Id FROM ' + calv.SObject_API_Name__c;
        List<String> whereClauses = new List<String>();
        if ( calv.SOQL_Where_Clause__c != null )
            whereClauses.add( calv.SOQL_Where_Clause__c );

        if ( !String.isBlank(filter) ) {

            String filterString = '( ';
            List<String> filters = new List<String>();
            for ( String s : calv.Search_Fields__c != null ? calv.Search_Fields__c.split(',') : new List<String>{ 'Name' } )
                filters.add( s + ' LIKE \'%' + filter + '%\'');

            filterString += String.join(filters, ' OR ');
            filterString += ' )';

            whereClauses.add( filterString );

        }

        if ( tags != null && tags.size() > 0 ) {

            String filterString = '(';
            List<String> filters = new List<String>();
            for ( String s : tags )
                filters.add( calv.Filter_Field__c + ' includes (\'' + s + '\')');
            filterString += String.join( filters, ' OR ');
            filterString += ')';

            whereClauses.add( filterString );

        }

        if ( searchDate != null )
            whereClauses.add( calv.Date_Field_Name__c + ' >= ' + String.valueOf(searchDate) );


        if ( whereClauses.size() > 0 )
            queryString += ' WHERE ' + String.join( whereClauses, ' AND ');


        List<SObject> calObjsTotal = Database.query( queryString );
        //System.debug('*** calObjectsTotalSize in the new method: ***' + calObjsTotal.size());

        return calObjsTotal.size();  
    }

}