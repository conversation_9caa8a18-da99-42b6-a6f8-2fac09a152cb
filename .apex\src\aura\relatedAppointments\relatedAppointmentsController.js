({
    doInit : function(component, event, helper) {
        var caseRecordId = component.get("v.recordId");
        var actions = [
            { label: 'View', name: 'view' }
        ];

        var action = component.get("c.getAppointmentsByCase");
        action.setParams({
            caseId: caseRecordId
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                let records = response.getReturnValue().map((row) => ({
                    Id : row.Id,
                    Name : row.Name,
                    sfal__Topic__c : row.sfal__Topic__c,
                    linkName : window.location.hostname + '/lightning/r/sfal__Appointment__c/'+row.Id+'/view',
                    sfal__StartDateTime__c : $A.localizationService.formatDateTime(row.sfal__StartDateTime__c),
                    sfal__EndDateTime__c : $A.localizationService.formatDateTime(row.sfal__EndDateTime__c),
                    linkOwner: window.location.hostname + '/lightning/r/User/'+row.Owner.Id+'/view',
                    OwnerName : row.Owner.Name
                }));
                console.log(records);
                component.set("v.relatedAppointments", records);
                component.set("v.columns", [
                    { label: 'Appointment Subject', fieldName: 'linkName', type: 'url', typeAttributes: { label: { fieldName: 'Name' }, value: { fieldName: 'linkName' }, target: '_blank' }},
                    { label: 'Topic', fieldName: 'sfal__Topic__c', type: 'text' },
                    { label: 'Start', fieldName: 'sfal__StartDateTime__c', type: 'datetime' },
                    { label: 'End', fieldName: 'sfal__EndDateTime__c', type: 'datetime' },
                    { label: 'Assigned Staff Member', fieldName: 'linkOwner', type: 'url', typeAttributes: { label: { fieldName: 'OwnerName' }, value: { fieldName: 'linkOwner' }, target: '_blank' }},
                    { type: 'action', typeAttributes: { rowActions: actions } }
                ]);
            }
        });
        $A.enqueueAction(action);
    },

    handleRowAction: function(component, event, helper) {
        var action = event.getParam('action');
        var row = event.getParam('row');
        switch (action.name) {
            case 'view':
                var navEvt = $A.get("e.force:navigateToSObject");
                navEvt.setParams({
                    "recordId": row.Id,
                    "slideDevName": "detail"
                });
                navEvt.fire();
                break;
        }
    },
})