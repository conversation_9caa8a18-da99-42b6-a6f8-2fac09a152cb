global without sharing class CustomEventsLWCController {
     /**
     * @description Returned when calendar initially loaded, includes initial items and calendar config
     */
    public class CalendarConfig {

        @AuraEnabled public Calendar_View__c cv;
        @AuraEnabled public evt__Special_Event__c specialEventsDetail;
        @AuraEnabled public List<Map<String, String>> filters;

        public CalendarConfig ( Calendar_View__c cv ) {

            this.cv = cv;
        }
    }

    @AuraEnabled
    public static CalendarConfig getCalendarConfig (String eRegisterExternalId , String view) {
        Calendar_View__c cv;
        try {
                cv = [ SELECT Id , Name , Link_to_Calendar_Item__c , SObject_API_Name__c
                FROM Calendar_View__c 
                WHERE Name = :view ];       
        } catch (Exception e) {
                throw new AuraHandledException('Events Button View Not Found'); 
        }
        CalendarConfig cc = new CalendarConfig( cv );
        cc.specialEventsDetail = CustomEventsLWCController.getSpecialEventRecordDetails(cv , eRegisterExternalId);
        return cc;
    }

    private static evt__Special_Event__c getSpecialEventRecordDetails(Calendar_View__c cv , String eRegisterExternalId) {
        evt__Special_Event__c eventsList;
        try {
                eventsList = [SELECT Id , Name , E_Register_External_ID__c 
                              FROM evt__Special_Event__c 
                              WHERE E_Register_External_ID__c = :eRegisterExternalId];

        } catch (Exception e) {
                throw new AuraHandledException(e.getMessage());
        }
            return eventsList;
    }

}