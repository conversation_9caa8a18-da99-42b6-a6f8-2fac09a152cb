/**
* @description Test Class for CCON_UpdateCounter_TDTM class
* <AUTHOR>
* @version 1.0
* @created 17-MAY-2021
*/
@isTest
public class CCON_UpdateCounter_TEST {
    @testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for CCON_UpdateCounter_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('CCON_UpdateCounter_TDTM', 'Course_Enrollment__c', 'AfterInsert;AfterUpdate;AfterDelete;', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        RecordType EPRT = [Select Id, Name from RecordType where SObjectType = 'hed__Course__c' AND Name = 'EP Course'];
        Account a = (Account)TestFactory.createSObject(new Account(RecordTypeId = AccountService.AcademicProgramRTId));
        insert a;
        
        List<hed__Course__c> corList = new List<hed__Course__c>();
        corList.add( new hed__Course__c (RecordTypeId = EPRT.Id, Name = 'Leadership Course', Suite__c = 'Leadership Suite', hed__Account__c = a.Id));
        corList.add( new hed__Course__c (RecordTypeId = EPRT.Id, Name = 'Focused Course', Suite__c = 'Focused Suite', hed__Account__c = a.Id));
        insert corList;
        
        Contact c1 = new Contact(lastName = 'TestCon', Email = '<EMAIL>');
        insert c1;
    
        hed__Term__c t = new hed__Term__c(Name = 'Test Term1', hed__Account__c = a.Id);
        insert t;
                    
        List<hed__Course_Offering__c> coList = new List<hed__Course_Offering__c>();
        coList.add(new hed__Course_Offering__c(Name = 'Leadership CO', hed__Course__c = corList[0].Id, hed__Term__c = t.Id, hed__Start_Date__c = Date.newInstance(2025,1,1), hed__End_Date__c = Date.newInstance(2025,1,15)));
        coList.add(new hed__Course_Offering__c(Name = 'Focused CO', hed__Course__c = corList[1].Id, hed__Term__c = t.Id, hed__Start_Date__c = Date.newInstance(2025,2,1), hed__End_Date__c = Date.newInstance(2025,2,15)));
        insert coList;
    }
    @isTest
    static void testCase_Leadership(){ 
        hed__Course_Offering__c co = [SELECT Id FROM hed__Course_Offering__c WHERE Name = 'Leadership CO' LIMIT 1];
        Contact con = [Select Id, Name from Contact WHERE lastName = 'TestCon'];
        
        hed__Course_Enrollment__c ce = new hed__Course_Enrollment__c(hed__Contact__c = con.Id, hed__Course_Offering__c = co.Id, hed__Status__c = 'Current');
        insert ce;
        
        ce.hed__Status__c = 'Complete';
        update ce;
        List<Certificate_Achievement__c> caList = [SELECT Id, Course_Counter_Optional__c, Course_Counter_Required__c FROM Certificate_Achievement__c WHERE Contact__c = :con.Id];
        System.assert(caList.size() > 0);
        if(caList.size() > 0)
            System.assertEquals(1, caList[0].Course_Counter_Required__c);
        delete ce;
        
        ce = new hed__Course_Enrollment__c(hed__Contact__c = con.Id, hed__Course_Offering__c = co.Id, hed__Status__c = 'Complete');
        insert ce;
    }
    @isTest
    static void testCase_Focused(){ 
        hed__Course_Offering__c co = [SELECT Id FROM hed__Course_Offering__c WHERE Name = 'Focused CO' LIMIT 1];
        Contact con = [Select Id, Name from Contact WHERE lastName = 'TestCon'];
        
        hed__Course_Enrollment__c ce = new hed__Course_Enrollment__c(hed__Contact__c = con.Id, hed__Course_Offering__c = co.Id, hed__Status__c = 'Current');
        insert ce;
        
        ce.hed__Status__c = 'Complete';
        update ce;
        List<Certificate_Achievement__c> caList = [SELECT Id, Course_Counter_Optional__c, Course_Counter_Required__c FROM Certificate_Achievement__c WHERE Contact__c = :con.Id];
        System.assert(caList.size() > 0);
        if(caList.size() > 0)
            System.assertEquals(1, caList[0].Course_Counter_Optional__c);
        delete ce;
        
        ce = new hed__Course_Enrollment__c(hed__Contact__c = con.Id, hed__Course_Offering__c = co.Id, hed__Status__c = 'Complete');
        insert ce;
    }
}