@isTest
global class VidyardCreationMock implements HttpCalloutMock {
    // Implement this interface method
    global HTTPResponse respond(HTTPRequest req) {
        
        //Validate that the information is correct
        // String body                     = req.getBody();
        // Map<String, String> jsonBody    = (Map<String, String>)JSON.deserialize( body, Map<String, String>.class );
        // Map<String, String> fieldsBody  = (Map<String, String>)JSON.deserialize( jsonBody.get('fields'), Map<String, String>.class );
        // Map<String, String> metaBody    = (Map<String, String>)JSON.deserialize( jsonBody.get('meta'), Map<String, String>.class );

        // String appId = metaBody.get('player_UUID');

        // Create a fake response
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        res.setBody('{"example":"test"}');
        res.setStatusCode(200);
        return res;
    }

}