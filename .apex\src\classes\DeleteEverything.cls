public class DeleteEverything implements Database.Batchable<sObject>{
 
   public final List<String> objs;
   public final Boolean allOrNone;
 
   public DeleteEverything(List<String> o, Boolean a ){
       
      this.objs 		= o;
      this.allOrNone 	= a;
   }
 
   public Database.QueryLocator start(Database.BatchableContext BC){
       /*if(objs[0] == 'User'){
           //return Database.getQueryLocator('SELECT Id, Name, Email, isPortalEnabled, isActive, contactId, userName, federationIdentifier FROM ' + objs[0] 
                                         //  + ' WHERE federationidentifier != null');
             return Database.getQueryLocator('SELECT Id, Name, Email, createdby.name FROM ' + objs[0] 
                                           + ' WHERE NOT(email LIKE \'%invalid%\') ORDER BY email Nulls LAST');
       } */
      return Database.getQueryLocator('SELECT ID FROM ' + objs[0] );
   }
 
   public void execute(Database.BatchableContext BC, List<sObject> scope){
       Database.delete(scope, allOrNone);
     /* if(scope[0].getSobjectType() == Schema.User.SObjectType){
           for(User u: (List<User>)scope){
               	//u.email += '.example.invalid'; 
    			//u.username += '.example.invalid';
    			//u.isPortalEnabled = false;
    			//u.FederationIdentifier = null; 
               if(u.email != null && u.createdby.Name == 'Integration User'){
                   u.email += '.invalid'; 
               }
           }
           Database.update(scope, allOrNone);
       } 
       else{ */
       	/*AssignmentRule AR = new AssignmentRule();
		AR = [select id from AssignmentRule where SobjectType = 'Case' and Active = true limit 1];

		//Creating the DMLOptions for "Assign using active assignment rules" checkbox
		Database.DMLOptions dmlOpts = new Database.DMLOptions();
		dmlOpts.assignmentRuleHeader.assignmentRuleId= AR.id;
        Database.update(scope, dmlOpts);
       } */
   }
 
   public void finish(Database.BatchableContext BC){
      if ( objs.size() > 1 ) {
           this.objs.remove(0);
           DeleteEverything de = new DeleteEverything( this.objs, allOrNone );
           String cronID = System.scheduleBatch(de, 'Delete all ' + this.objs[0], 0);

       } 
           
   }
}