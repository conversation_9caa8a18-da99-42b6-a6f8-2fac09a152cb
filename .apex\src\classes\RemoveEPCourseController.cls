public without sharing class RemoveEPCourseController {
    @AuraEnabled
    public static String deleteCartItemsAndCart(Id cartId){
        String redirectURL;
        if(cartId != null) {
            pymt__Shopping_Cart__c shoppingCartToDelete = new pymt__Shopping_Cart__c();
            shoppingCartToDelete.Id = cartId;
            pymt__Shopping_Cart_Item__c shoppingCartItem = [SELECT Id, Course_Offering__c FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cartId];
            delete shoppingCartToDelete;
            Calendar_View__c clvObj = [SELECT Id, Link_to_Calendar_Item__c FROM Calendar_View__c WHERE Name = 'Course Offering Calendar View'];
            if(clvObj != null && !String.isBlank(clvObj.Link_to_Calendar_Item__c)) {
                redirectURL = clvObj.Link_to_Calendar_Item__c.replace('{!itemId}', (String)shoppingCartItem.Course_Offering__c);
            }
        }
        return redirectURL;
    }
}