Global without sharing class createShoppingCartController_clone {
    @AuraEnabled
    public static string response{get;set;}
    public static Id recId{get;set;}
    public static Id cartId{get;set;}
    public static Contact currentContact =new Contact();
    public static string Cookie = '';
    //@AuraEnabled
    //public static list<cartWrapper> cart= new list<cartWrapper>{};
    public createShoppingCartController_clone(){
        Cookie setCookie;
       setCookie = apexpages.currentPage().getCookies().get('cartID');
           if(setCookie == NULL || string.isEmpty(setCookie.getValue())){
          Cookie =  createCookie();
        }
        else{
            Cookie = setCookie.getValue();
        }
        
        system.debug('cookie: '+Cookie);
        recId= ApexPages.currentpage().getparameters().get('id');
        if(ApexPages.currentpage().getparameters().get('cartId') != null){
            cartId = ApexPages.currentpage().getparameters().get('cartId');
        }
        else{
            currentContact = contactDetails();
            if(currentContact != null){
                LIST<pymt__Shopping_Cart__c> sc = [Select id, name,pymt__contact__c,Shopping_Cart_Payment__c from pymt__Shopping_Cart__c where pymt__Cart_UID__c=: Cookie LIMIT 1];    
                if(!sc.isEmpty()){
                    cartId = sc[0].Id;
                }
                
            }
            else{
                pymt__PaymentX__c pymt = new pymt__PaymentX__c();
                pymt.pymt__Contact__c = currentContact.Id;
                pymt.Name = 'Payment_'+currentContact.Name;
                pymt.pymt__Status__c = 'Scheduled';
                pymt.pymt__Billing_Country__c = 'CA';
                pymt.pymt__Billing_State__c = 'ON';
                pymt.pymt__Tax_Method__c = 'State/Province Lookup';
                //pymt.pymt__Tax__c = (totalCartAmount*0.2);
                //pymt.pymt__Amount__c = totalCartAmount;//course[0].pymt__Unit_Price__c;//Double.valueOF('90');
                
            insert pymt;
                pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
                if(currentContact != null){
                    cart.pymt__Contact__c = currentContact.id;
                }
                cart.Shopping_Cart_Payment__c = pymt.Id;
                insert cart; 
                cartId = cart.Id;
            }
        }
        
        shoppingCartId(cartId);
        
    }
    
    @AuraEnabled
    public static string createCookie(){
        string newCookie ; 
        string cookieName = string.valueOf(dateTime.now());
        Cookie cookie = new Cookie('cartID', cookieName, null, -1, false);
        ApexPages.currentPage().setCookies(new Cookie[]{cookie});
        newCookie = apexpages.currentPage().getCookies().get('cartID').getValue();
        system.debug('cookie Name: '+newCookie );
        return newCookie;
    }
    @AuraEnabled
    public static contact contactDetails(){
        currentContact = new contact();
        id userId = UserInfo.getUserId();
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        if(u.ContactId != null){
            currentContact  = [SELECT Id, Name,FirstName From Contact WHERE Id=: u.ContactId LIMIT 1];
        }
        else if(u != null){
            List<Contact> cons = [SELECT Id, Name,FirstName From Contact WHERE Name=: u.Name LIMIT 1];
            if(!cons.isEmpty()){
                currentContact = cons[0]; 
            }
            else{//Guest_User_Contact_Name
                currentContact = [SELECT Id, Name,FirstName From Contact WHERE Name=:System.Label.Guest_User_Contact_Name LIMIT 1];
            }
        }
        else{
            currentContact = [SELECT Id, Name,FirstName From Contact WHERE Name=:System.Label.Guest_User_Contact_Name LIMIT 1];
        }
        
        return currentContact;
    }
    public static pagereference shoppingCartId(id sCartId){
        String uiThemeDisplayed = UserInfo.getUiThemeDisplayed();
        PageReference pageRef = ApexPages.currentPage();
        system.debug('ui: '+uiThemeDisplayed);
        pageRef.getParameters().put('cartId', sCartId);
        if(uiThemeDisplayed=='Theme4d'){  // Lightning Theme Value          
            aura.redirect(pageRef);
        }
        
        if(uiThemeDisplayed=='Theme2' || uiThemeDisplayed=='Theme3'){  // Classic Theme Value
            pageRef.setRedirect(true);
        }
        return pageRef;
    }
    
    public class cartWrapper{
        @AuraEnabled public string description{get;set;}
        @AuraEnabled public double price{get;set;}
        @AuraEnabled  public string item{get;set;}
        @AuraEnabled public integer qty{get;set;}
        @AuraEnabled  public double total{get;set;}
        @AuraEnabled  public string cid{get;set;}
        @AuraEnabled  public string User{get;set;}
        @AuraEnabled  public string shoppingcartId{get;set;}
        @AuraEnabled public double shoppingCartTax{get;set;}
        //public evt__Special_Event__c event{get;set;}
    }
    @AuraEnabled
    public static string getUserDetails(){
        id userId = UserInfo.getUserId();
        
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        if(u != null && u.ContactId != null){
            currentContact  = [SELECT Id, Name,FirstName From Contact WHERE Id=: u.ContactId LIMIT 1];
            return currentContact.FirstName;
        }else{
            currentContact = [SELECT Id, Name,FirstName From Contact WHERE Name=:System.Label.Guest_User_Contact_Name LIMIT 1];
            return u.FirstName;
        }
        /*else{
currentContact = [SELECT Id, Name From Contact WHERE Name=:System.Label.Guest_User_Contact_Name LIMIT 1];
}*/
        //return 'Guest';
    }
    @AuraEnabled
    public static list<cartWrapper> addToCart(Id EventCourseId,Id sCartId,string cookie){
        currentContact = contactDetails();
        system.debug('currentContact= '+currentContact);
        system.debug('CartId = '+sCartId);
        system.debug('EventCourseId = '+EventCourseId);
        cartId = sCartId;
        Cookie = cookie;
        system.debug('Cookie: '+ cookie);
        
        id pid ;//= PaymentId(EventCourseId,Cookie);
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        if(CartId == null){
            
            pymt.pymt__Contact__c = currentContact.Id;
            pymt.Name = 'Payment_'+currentContact.Name;
            pymt.pymt__Status__c = 'Scheduled';
            pymt.pymt__Billing_Country__c = 'CA';
            pymt.pymt__Billing_State__c = 'ON';
            pymt.pymt__Tax_Method__c = 'State/Province Lookup';
            //pymt.pymt__Tax__c = (totalCartAmount*0.2);
            //pymt.pymt__Amount__c = totalCartAmount;//course[0].pymt__Unit_Price__c;//Double.valueOF('90');
            try{
            
        
            insert pymt;
            pid = pymt.Id;
            pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
            if(currentContact != NULL){
                cart.pymt__Contact__c = currentContact.id;
            }
            cart.Shopping_Cart_Payment__c = pymt.Id;
            cart.pymt__Cart_UID__c = cookie;
            pymt.pymt__Trigger_Tax_Calc__c = True;
            insert cart;
            //pymt.pymt__Trigger_Tax_Calc__c = True;
            //update pymt;
            CartId = cart.Id;
            }
        catch(exception e){
            response = 'Error: '+e.getMessage();
        }
        }
        else{
            LIST<pymt__Shopping_Cart__c> shoppingCart = [Select Id, name, Shopping_Cart_Payment__c,
                                                         pymt__Cart_UID__c
                                                         FROM pymt__Shopping_Cart__c 
                                                         where pymt__Cart_UID__c=: cookie OR Id=:CartId
                                                         LIMIT 1]; 
            system.debug('shoppingCart= '+shoppingCart);
            if(!shoppingCart.isEmpty() && shoppingCart[0].Shopping_Cart_Payment__c != Null){
                shoppingCart[0].pymt__Cart_UID__c = cookie;
                update shoppingCart[0];
                pid = shoppingCart[0].Shopping_Cart_Payment__c;
                pymt = [Select Id,pymt__Trigger_Tax_Calc__c From pymt__PaymentX__c Where Id=: shoppingCart[0].Shopping_Cart_Payment__c ];
            }
            else if(!shoppingCart.isEmpty() && shoppingCart[0].Shopping_Cart_Payment__c == NULL){
                pymt.pymt__Contact__c = currentContact.Id;
                pymt.Name = 'Payment_'+currentContact.Name;
                pymt.pymt__Status__c = 'Scheduled';
                pymt.pymt__Billing_Country__c = 'CA';
                pymt.pymt__Billing_State__c = 'ON';
                pymt.pymt__Tax_Method__c = 'State/Province Lookup';
                try{
                    insert pymt;
                    pid = pymt.Id;
                    shoppingCart[0].Shopping_Cart_Payment__c = pymt.Id;
                    shoppingCart[0].pymt__Cart_UID__c = cookie;
                    update shoppingCart[0];
                }
                catch(exception e){
                    response = 'Error: '+e.getMessage();
                }
            }
        }
        if(recId != null && EventCourseId == Null){
            system.debug('id= '+recId);
            EventCourseId = recId;
        } 
        if(EventCourseId != null){
            system.debug('objectname='+EventCourseId.getSObjectType().getDescribe().getName());
            if(EventCourseId.getSObjectType().getDescribe().getName() == 'hed__Course_Offering__c'){
                LIST<hed__Course_Offering__c> course = [SELECT 
                                                        Name,Registration_Fee__c,hed__Course__r.hed__Description__c,
                                                        (SELECT Id, Name,pymt__Quantity__c From Shopping_Cart_Items__r where pymt__Shopping_Cart__c=: CartId)
                                                        FROM hed__Course_Offering__c
                                                        WHERE Id=:EventCourseId
                                                       ];
                
                system.debug('course='+course);
                if(!course.isEmpty() && course[0].Shopping_Cart_Items__r.size() > 0){
                    LIST<pymt__Shopping_Cart_Item__c> cartItem = [SELECT 
                                                                  Id, Name,pymt__Quantity__c,pymt__Payment__c
                                                                  FROM pymt__Shopping_Cart_Item__c
                                                                  WHERE pymt__Shopping_Cart__c=: CartId AND Course_Offering__c =: EventCourseId
                                                                  LIMIT 1];
                    if(!cartItem.isEmpty()){
                        cartItem[0].pymt__Quantity__c +=1;
                        if(cartItem[0].pymt__Payment__c == NULL){
                            cartItem[0].pymt__Payment__c = pid;
                        }
                        
                        try{
                            update cartItem;
                            response = 'Shopping Cart Updated';
                         } 
                        catch(exception e){
                            response = 'Error: '+e.getMessage();
                        }
                    }
                    
                }
                else{
                    system.debug('In else');
                    pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
                    cartItem.Name = course[0].Name;
                    cartItem.Course_Offering__c = EventCourseId;
                    cartItem.pymt__Quantity__c =1;
                    cartItem.pymt__Contact__c = currentContact.id;
                    cartItem.pymt__Description__c = course[0].hed__Course__r.hed__Description__c;
                    cartItem.pymt__Shopping_Cart__c= CartId;
                    if(cartItem.pymt__Unit_Price__c != null){
                        cartItem.pymt__Unit_Price__c += course[0].Registration_Fee__c;
                    }
                    else{
                        cartItem.pymt__Unit_Price__c = course[0].Registration_Fee__c;
                    }
                    if(pid != NULL){
                        cartItem.pymt__Payment__c = pid;
                    }
                    cartItem.pymt__Taxable__c =True; //It sets Taxable true for Shopping cart Item.
                    try{
                        insert cartItem;
                        response = 'Item Added to Shopping Cart ';
                    }
                    catch(exception e){
                        response = 'Error: '+e.getMessage();
                    }                
                }
                
            }
            else IF(EventCourseId.getSObjectType().getDescribe().getName() == 'evt__Special_Event__c'){
                system.debug('');
                List<evt__Special_Event__c> course = [SELECT 
                                                      Name,evt__Short_Description__c,
                                                      (SELECT Id, Name,pymt__Quantity__c,pymt__Payment__c From Shopping_Cart_Items__r where pymt__Shopping_Cart__c=: CartId),
                                                      (SELECT  evt__Amount__c From  evt__Event_Fees__r)
                                                      FROM evt__Special_Event__c
                                                      WHERE Id=:EventCourseId
                                                     ]; 
                
                if(!course.isEmpty() && course[0].Shopping_Cart_Items__r.size() > 0){
                    system.debug('In Event CartItem');
                    LIST<pymt__Shopping_Cart_Item__c> cartItem = [SELECT 
                                                                  Id, Name,pymt__Quantity__c,pymt__Unit_Price__c,pymt__Payment__c
                                                                  FROM pymt__Shopping_Cart_Item__c
                                                                  WHERE pymt__Shopping_Cart__c=: CartId  AND Special_Event__c =: EventCourseId
                                                                  LIMIT 1    ];
                    // wrap.description = course.name;
                    if(!cartItem.isEmpty()){
                        
                        cartItem[0].pymt__Quantity__c +=1;
                        system.debug('fee= '+course[0].evt__Event_Fees__r );
                        system.debug('amount= '+course[0].evt__Event_Fees__r[0].evt__Amount__c);
                        if(course[0].evt__Event_Fees__r.size() > 0 ){
                            if(cartItem[0].pymt__Unit_Price__c != null){
                                
                            }
                            else{
                                cartItem[0].pymt__Unit_Price__c = course[0].evt__Event_Fees__r[0].evt__Amount__c;	
                            }
                            
                        }
                        if(pid != NULL && cartItem[0].pymt__Payment__c == NUll){
                            cartItem[0].pymt__Payment__c = pid;
                        }
                        try{
                            update cartItem;
                            response = 'Shopping Cart Updated';
                        } 
                        catch(exception e){
                            response = 'Error: '+e.getMessage();
                        }
                    }
                    
                }
                else{
                    system.debug('In else');
                    pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
                    cartItem.Name = course[0].Name;
                    cartItem.Special_Event__c = EventCourseId;
                    cartItem.pymt__Quantity__c =1;
                    cartItem.pymt__Contact__c = currentContact.id;
                    cartItem.pymt__Description__c = course[0].evt__Short_Description__c;
                    cartItem.pymt__Shopping_Cart__c= CartId;
                    system.debug('c: '+ course[0].evt__Event_Fees__r);
                    system.debug('c: '+ course[0].evt__Event_Fees__r[0].evt__Amount__c);
                    cartItem.pymt__Unit_Price__c = course[0].evt__Event_Fees__r[0].evt__Amount__c;
                    cartItem.pymt__Taxable__c =True; //It sets Taxable true for Shopping cart Item.
                    if(pid != NULL){
                        cartItem.pymt__Payment__c = pid;
                    }
                    try{
                        insert cartItem;
                        response = 'Item Added to Shopping Cart ';
                    }
                    catch(exception e){
                        response = 'Error: '+e.getMessage();
                    }                
                }
                
            }
        }
        pymt__PaymentX__c UpdatePymt = [Select Id,pymt__Trigger_Tax_Calc__c From pymt__PaymentX__c Where Id=: pid ];
        UpdatePymt.pymt__Trigger_Tax_Calc__c = True;
        update UpdatePymt;
        system.debug('payment: '+UpdatePymt);
        list<cartWrapper> cart = new list<cartWrapper>{};
            
            //cart.add(wrap);
            //system.debug('cart='+cart);
            List<pymt__Shopping_Cart_Item__c> shopCartList = [SELECT 
                                                              Name, id,Special_Event__c,Special_Event__r.Name,pymt__Quantity__c,
                                                              Course_Offering__c,pymt__Description__c,
                                                              Course_Offering__r.Name,pymt__Payment__r.pymt__Tax__c,
                                                              Course_Offering__r.Registration_Fee__c
                                                              FROM pymt__Shopping_Cart_Item__c
                                                              WHERE pymt__Shopping_Cart__c=: CartId
                                                             ];
        List<id> eventIds = new List<id>{};
            MAP<Id,double> amountMap = new MAP<Id,double>{};
                for(pymt__Shopping_Cart_Item__c sitem: shopCartList){
                    if(sitem.Special_Event__c != null){
                        eventIds.add(sitem.Special_Event__c);
                    }
                }
        if(!eventIds.isEmpty()){
            List<evt__Event_Fee__c> fee = [SELECT evt__Amount__c,evt__Event__c FROM evt__Event_Fee__c 
                                           WHERE evt__Event__c IN: eventIds];
            for(evt__Event_Fee__c fees: fee){
                amountMap.put(fees.evt__Event__c,fees.evt__Amount__c);
            }
        }
        for(pymt__Shopping_Cart_Item__c item: shopCartList){
            cartWrapper Cartitem = new cartWrapper();
            Cartitem.user = currentContact.firstName;
            Cartitem.shoppingcartId = CartId;
            Cartitem.item = item.Name;
            Cartitem.qty = integer.valueOf(item.pymt__Quantity__c);
            Cartitem.description = item.pymt__Description__c;
            Cartitem.shoppingCartTax = item.pymt__Payment__r.pymt__Tax__c;
            if(item.Special_Event__c != null){
                //item.Special_Event__r.Name;
                Cartitem.cid = string.valueOf(item.Special_Event__c);
                if(amountMap.containsKey(item.Special_Event__c)){
                    Cartitem.price = amountMap.get(item.Special_Event__c);
                }
                else{
                    Cartitem.price = 0.00;
                }
                system.debug('price='+  Cartitem.price);
                system.debug('amount= '+amountMap.get(item.Special_Event__c));
            }
            else if(item.Course_Offering__c != null){
                //Cartitem.description = '';//item.Course_Offering__r.Name;
                Cartitem.cid = string.valueOf(item.Course_Offering__c);
                Cartitem.price = item.Course_Offering__r.Registration_Fee__c;
            }
            Cartitem.total =   Cartitem.price * Cartitem.qty;       
            //Cartitem.qty
            
            cart.add(Cartitem);
        }
        return cart;
        //return response;
    }
    @AuraEnabled
    public static string PaymentId(Id event,string cookie){
        //Id event= ApexPages.currentpage().getparameters().get('id');
        system.debug('Cookie= '+ cookie);
        system.debug('currentContact='+currentContact);
        //User u = [select id, contactId from User where id = : userId];
        //Contact con = [SELECT Id, Name From Contact WHERE Name='Guest Event User'];
        LIST<pymt__Shopping_Cart_Item__c> course = [SELECT 
                                                    Name, id,pymt__Unit_Price__c, pymt__Payment__c
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE pymt__Shopping_Cart__r.pymt__Cart_UID__c=: cookie 
                                                    /*(Special_Event__c=: event OR Course_Offering__c=:event ) AND*/ 
                                                    
                                                   ];
        system.debug('course= '+ course);
        if(!course.isEmpty() && course[0].pymt__Payment__c != null){
            return string.valueOF(course[0].pymt__Payment__c);
        }
        //string query = 'SELECT Id FROM pymt__PaymentX__c WHERE WHERE pymt__Contact__c =: currentContact.Id AND pymt__Status__c = \'Scheduled\'';
        //if()
        LIST<pymt__PaymentX__c> payment = [SELECT Id 
                                           FROM pymt__PaymentX__c 
                                           WHERE pymt__Contact__c =: currentContact.Id AND pymt__Status__c = 'Scheduled'
                                           LIMIT 1
                                          ];
        system.debug('payment= '+ payment);
        If(payment.isEmpty() ){
            double totalCartAmount= 0.00;
            //for(pymt__Shopping_Cart_Item__c ci:course){
            //     totalCartAmount += ci.pymt__Unit_Price__c ;
            // }
            pymt__PaymentX__c pymt = new pymt__PaymentX__c();
            pymt.pymt__Contact__c = currentContact.Id;
            pymt.Name = 'Payment_'+event;
            pymt.pymt__Status__c = 'Scheduled';
            //pymt.pymt__Tax__c = (totalCartAmount*0.2);
            //pymt.pymt__Amount__c = totalCartAmount;//course[0].pymt__Unit_Price__c;//Double.valueOF('90');
            
            insert pymt;
            if(!course.isEmpty()){
                for(pymt__Shopping_Cart_Item__c ci:course){
                    ci.pymt__Payment__c = pymt.id;
                }
                
                update course;
            }
            
            system.debug('pymt= '+ pymt);
            return string.valueOF(pymt.Id);
        }
        else if(!payment.isEmpty()){
            system.debug('pymtId= '+ payment[0].Id);
            return string.valueOF( payment[0].Id);
        }
        //Id contactid = '0034c000001xfyeAAA';
        //evt__Special_Event__c spEvent = []//evt__Event__c
        return null;
    }
    
    
    @AuraEnabled
    public static list<cartWrapper> removeFromCart(Id EventCourseId, Id scartId){
        list<cartWrapper> cart = new list<cartWrapper>{};
            //cartId = sCartId;
        // Contact con = [SELECT Id, Name From Contact WHERE Name='Peter Dupre'];
        system.debug('EventCourseId-> '+EventCourseId);
        LIST<pymt__Shopping_Cart_Item__c> course = [SELECT 
                                                    Name, id,pymt__Payment__c, pymt__Payment__r.pymt__Tax__c
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE (Special_Event__c=: EventCourseId OR Course_Offering__c=:EventCourseId )
                                                    AND pymt__Shopping_Cart__c =:sCartId
                                                   ];
        id pid = course[0].pymt__Payment__c;
        if(!course.isEmpty()){
            try {
                delete course;
                //return 'Deleted';
            }
            catch(exception e){
                //return 'Error';
            }
        }
        //Update Payment Record
        pymt__PaymentX__c UpdatePymt = [Select Id,pymt__Trigger_Tax_Calc__c From pymt__PaymentX__c Where Id=: pid ];
        UpdatePymt.pymt__Trigger_Tax_Calc__c = True;
        update UpdatePymt;
        List<pymt__Shopping_Cart_Item__c> shopCartList = [SELECT 
                                                          Name, id,Special_Event__c,Special_Event__r.Name,pymt__Quantity__c,
                                                          Course_Offering__c,
                                                          Course_Offering__r.Name,
                                                          Course_Offering__r.Registration_Fee__c,
                                                          pymt__Payment__c, pymt__Payment__r.pymt__Tax__c
                                                          FROM pymt__Shopping_Cart_Item__c
                                                          WHERE pymt__Shopping_Cart__c =:sCartId
                                                         ];
        List<id> eventIds = new List<id>{};
            MAP<Id,double> amountMap = new MAP<Id,double>{};
                for(pymt__Shopping_Cart_Item__c sitem: shopCartList){
                    if(sitem.Special_Event__c != null){
                        eventIds.add(sitem.Special_Event__c);
                    }
                }
        system.debug('eventIds-> '+eventIds);
        if(!eventIds.isEmpty()){
            List<evt__Event_Fee__c> fee = [SELECT evt__Amount__c,evt__Event__c FROM evt__Event_Fee__c 
                                           WHERE evt__Event__c IN: eventIds];
            for(evt__Event_Fee__c fees: fee){
                amountMap.put(fees.evt__Event__c,fees.evt__Amount__c);
            }
        }
        
        for(pymt__Shopping_Cart_Item__c item: shopCartList){
            cartWrapper Cartitem = new cartWrapper();
            Cartitem.item = item.Name;
            Cartitem.shoppingcartId = sCartId;
            Cartitem.qty = integer.valueOf(item.pymt__Quantity__c);
            system.debug('special= '+item.Special_Event__c);
            if(item.Special_Event__c != null){
                Cartitem.description = item.Special_Event__r.Name;
                Cartitem.cid = string.valueOf(item.Special_Event__c);
                if(amountMap.containsKey(item.Special_Event__c)){
                    Cartitem.price = amountMap.get(item.Special_Event__c);
                }
                else{
                    Cartitem.price = 0.00;
                }
                
                system.debug('price='+  Cartitem.price);
                system.debug('amount= '+amountMap.get(item.Special_Event__c));
            }
            else if(item.Course_Offering__c != null){
                Cartitem.description = item.Course_Offering__r.Name;
                Cartitem.cid = string.valueOf(item.Course_Offering__c);
                Cartitem.price = item.Course_Offering__r.Registration_Fee__c;
            }
            Cartitem.shoppingCartTax = item.pymt__Payment__r.pymt__Tax__c;
            Cartitem.total =   Cartitem.price * Cartitem.qty;       
            //Cartitem.qty
            cart.add(Cartitem);
        }
        system.debug('cart= '+cart);
        return cart;
    }
    
    @AuraEnabled
    public static list<cartWrapper> continueCartSave(list<cartWrapper> cartItems,Id scartId, string cookie){
        id pid;
        if(scartId == null){
            
            list<pymt__Shopping_Cart__c> cartCookie = [SELECT id, Name,Shopping_Cart_Payment__c From pymt__Shopping_Cart__c WHERE pymt__Cart_UID__c=: cookie];
            if(!cartCookie.isEmpty()){
                scartId =  cartCookie[0].Id;
                pid = cartCookie[0].Shopping_Cart_Payment__c;
            }
        }
        else{ //To set pid for fetching Payment record//
            list<pymt__Shopping_Cart__c> cartCookie = [SELECT id, Name,Shopping_Cart_Payment__c From pymt__Shopping_Cart__c WHERE id=: scartId AND pymt__Cart_UID__c=: cookie];
            if(!cartCookie.isEmpty() && cartCookie[0].Shopping_Cart_Payment__c != null){
                pid = cartCookie[0].Shopping_Cart_Payment__c;
            }
            
        }
        Map<id,integer> itemQtyMap = new Map<id,integer>{};
            system.debug('scartId='+scartId);
        //cartId = sCartId;
        for(cartWrapper item: cartItems ){
            system.debug('id-'+item.cid);
            system.debug('qty-'+item.qty);
            itemQtyMap.put(item.cid,item.qty);
        }
        //Update Payment Record
        
        
        system.debug('itemQtyMap='+itemQtyMap);
        if(!itemQtyMap.isEmpty()){
            LIST<pymt__Shopping_Cart_Item__c> course = [SELECT 
                                                        Name, id,pymt__Quantity__c,Special_Event__c,Course_Offering__c,
                                                        pymt__Payment__c, pymt__Payment__r.pymt__Tax__c
                                                        FROM pymt__Shopping_Cart_Item__c
                                                        WHERE (Special_Event__c IN: itemQtyMap.keySet() OR Course_Offering__c IN:itemQtyMap.keySet() )
                                                        AND pymt__Shopping_Cart__c =:scartId
                                                       ];
            system.debug('coursePayment= '+ course[0].pymt__Payment__c);
            system.debug('coursePaymentTax= '+ course[0].pymt__Payment__r.pymt__Tax__c);
            if(!course.isEmpty()){
                for(pymt__Shopping_Cart_Item__c cartItem: course ){
                    system.debug('qty-'+cartItem.pymt__Quantity__c);
                    if(cartItem.Special_Event__c != Null && itemQtyMap.containsKey(cartItem.Special_Event__c)){
                        cartItem.pymt__Quantity__c = itemQtyMap.get(cartItem.Special_Event__c);
                        system.debug(itemQtyMap.get(cartItem.Special_Event__c)+' = qty= '+cartItem.pymt__Quantity__c);
                    }
                    if(cartItem.Course_Offering__c != Null && itemQtyMap.containsKey(cartItem.Course_Offering__c)){
                        cartItem.pymt__Quantity__c = itemQtyMap.get(cartItem.Course_Offering__c);
                        system.debug(itemQtyMap.get(cartItem.Course_Offering__c)+' = qty= '+cartItem.pymt__Quantity__c);
                    }
                    //cartItem.pymt__Quantity__c = itemQtyMap.get('');
                }
                system.debug('course='+course);
                update course;
            }
        }
        system.debug('update Pid: '+pid);
        List<pymt__PaymentX__c> UpdatePymt = [Select Id,pymt__Trigger_Tax_Calc__c,pymt__Tax__c From pymt__PaymentX__c Where Id=: pid ];
        if(!UpdatePymt.isEmpty()){
            system.debug('In update: '+UpdatePymt[0].pymt__Tax__c);
            UpdatePymt[0].pymt__Trigger_Tax_Calc__c = True;
        	update UpdatePymt;
        }
         UpdatePymt = [Select Id,pymt__Trigger_Tax_Calc__c,pymt__Tax__c From pymt__PaymentX__c Where Id=: pid ];
        for(cartWrapper item: cartItems){
            item.shoppingCartTax = UpdatePymt[0].pymt__Tax__c;
        }
        
        return cartItems;
        
    }
    
    @AuraEnabled
    public static list<cartWrapper> noRecord(Id scartId){
        
        if(scartId == null){
            //createShoppingCartController_clone.init();
            //cartId = ApexPages.currentpage().getparameters().get('cartId');
        }
        else{
            cartId = sCartId;
        }
        list<cartWrapper> cart = new list<cartWrapper>{};
            
            //cart.add(wrap);
            //system.debug('cart='+cart);
            List<pymt__Shopping_Cart_Item__c> shopCartList = [SELECT 
                                                              Name, id,Special_Event__c,Special_Event__r.Name,pymt__Quantity__c,
                                                              Course_Offering__c,pymt__Description__c,
                                                              Course_Offering__r.Name,pymt__Payment__c, 
                                                              pymt__Payment__r.pymt__Tax__c,
                                                              Course_Offering__r.Registration_Fee__c
                                                              FROM pymt__Shopping_Cart_Item__c
                                                              WHERE pymt__Shopping_Cart__c =:cartId
                                                             ];
        List<id> eventIds = new List<id>{};
            MAP<Id,double> amountMap = new MAP<Id,double>{};
                for(pymt__Shopping_Cart_Item__c sitem: shopCartList){
                    if(sitem.Special_Event__c != null){
                        eventIds.add(sitem.Special_Event__c);
                    }
                }
        if(!eventIds.isEmpty()){
            List<evt__Event_Fee__c> fee = [SELECT evt__Amount__c,evt__Event__c FROM evt__Event_Fee__c 
                                           WHERE evt__Event__c IN: eventIds];
            for(evt__Event_Fee__c fees: fee){
                amountMap.put(fees.evt__Event__c,fees.evt__Amount__c);
            }
        }
        for(pymt__Shopping_Cart_Item__c item: shopCartList){
            cartWrapper Cartitem = new cartWrapper();
            if(currentContact!= null){
                cartitem.user = currentContact.firstName;
            }
            Cartitem.item = item.Name;
            Cartitem.shoppingcartId = CartId;
            Cartitem.qty = integer.valueOf(item.pymt__Quantity__c);
            Cartitem.description = item.pymt__Description__c;
            if(item.Special_Event__c != null){
                //item.Special_Event__r.Name;
                Cartitem.cid = string.valueOf(item.Special_Event__c);
                if(amountMap.containsKey(item.Special_Event__c)){
                    Cartitem.price = amountMap.get(item.Special_Event__c);
                }
                else{
                    Cartitem.price = 0.00;
                }
                system.debug('price='+  Cartitem.price);
                system.debug('amount= '+amountMap.get(item.Special_Event__c));
            }
            else if(item.Course_Offering__c != null){
                //Cartitem.description = '';//item.Course_Offering__r.Name;
                Cartitem.cid = string.valueOf(item.Course_Offering__c);
                Cartitem.price = item.Course_Offering__r.Registration_Fee__c;
            }
            Cartitem.shoppingCartTax = item.pymt__Payment__r.pymt__Tax__c;
            Cartitem.total =   Cartitem.price * Cartitem.qty;       
            //Cartitem.qty
            
            cart.add(Cartitem);
        }
        return cart;
    }
    
    @AuraEnabled
    public static string noCart(string cookie){
        Id cartId;
        list<pymt__Shopping_Cart__c> cartCookie = [SELECT id, Name From pymt__Shopping_Cart__c WHERE pymt__Cart_UID__c=: cookie];
        if(!cartCookie.isEmpty()){
            cartId =  cartCookie[0].Id;
        }
        else{
            pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
            cart.pymt__Cart_UID__c = cookie;
            insert cart; 
            cartId = cart.Id;
        }
        
        system.debug('cartId: '+cartId);
        
        //list<cartWrapper> carts = addToCart(EventCourseId,cart.Id);
        return string.valueOf(cartId);
    }
}