/**
 * @description Application sync to held or confirm their seat.
 * <AUTHOR>
 * @version 1.0
 * @created 2020-05-22
 * @modified 2020-12-14
 */
global class BuildEpCourseApplicationsSync_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context ( AfterInsert,AfterUpdate etc..)
     * @param objResult the describe for Object
     */
    global static Boolean alreadyRan = false;
    public static final Set<String> appStatuses = new Set<String>{'In Progress','Submitted','Accepted Offer', 'Refused', 'Offer', 'Cancelled', 'Declined Offer', 'Accepted Offer', 'Deferred Offer'}; 
    public static final Map<String, String> appStatusToCEStatus = new Map<String, String>{'In Progress' => 'Prospect', 
                                                                                        'Submitted' => 'Applicant', 
                                                                                        'Refused' => 'Cancelled',
                                                                                        'Offer' => 'Offered', 
                                                                                        'Cancelled' => 'Cancelled', 
                                                                                        'Declined Offer' => 'Declined Offer', 
                                                                                        'Accepted Offer' => 'Current', 
                                                                                        'Deferred Offer' => 'Deferred'
                                                                                        }; 

    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();
        List<hed__Application__c> appLst = new List<hed__Application__c>();
        Set<Id> coIds = new Set<Id>(); 
        List<hed__Course_Enrollment__c> courseConnectionsToUpsert = new List<hed__Course_Enrollment__c>(); 
        


        //AFTER INSERT CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert && !BuildEpCourseApplicationsSync_TDTM.alreadyRan){
            BuildEpCourseApplicationsSync_TDTM.alreadyRan = true; 
            Map<Id, hed__Application__c> appMap = new Map<Id, hed__Application__c>(); 
            //Filter out applications in set of valid app statuses (appStatuses): 
            for(hed__Application__c app : (List<hed__Application__c>) newList){
                if(app.hed__Application_Status__c != null && appStatuses.contains(app.hed__Application_Status__c) && app.Course_Offering__c != null && app.RecordtypeId == ApplicationService.ExecutiveProgramsRTId){
                    appMap.put(app.Id, app); 
                    coIds.add(app.Course_Offering__c); 
                }
            }
            
            if(appMap.size() > 0){
                //Map Course Offering Id to Course offering: 
                Map<Id, hed__Course_Offering__c> cIdToChildrenCoMap = new Map<Id, hed__Course_Offering__c>([SELECT Id,Parent_Course__c,
                                                                                                            (Select Id from Child_Course_Offerings__r) 
                                                                                                            FROM hed__Course_Offering__c 
                                                                                                            WHERE Id IN :coIds]);
                //Create course connection records for each application
                for(hed__Application__c app : appMap.values()){
                    //Create course connection for each related child course offering: 
                    if(cIdToChildrenCoMap.get(app.Course_Offering__c).Child_Course_Offerings__r.size() > 0){
                        //if course connection is a parent course offering, set disable conflict checking to true
                        courseConnectionsToUpsert.add(createCourseConnection(app, app.Course_Offering__c, appStatusToCEStatus.get(app.hed__Application_Status__c), true));
                        for(hed__Course_Offering__c cco : cIdToChildrenCoMap.get(app.Course_Offering__c).Child_Course_Offerings__r){
                            courseConnectionsToUpsert.add(createCourseConnection(app, cco.Id, appStatusToCEStatus.get(app.hed__Application_Status__c), false)); 
                        }
                    }else{
                        //if course connection is not a parent course offering, set disable conflict check to false:
                        courseConnectionsToUpsert.add(createCourseConnection(app, app.Course_Offering__c, appStatusToCEStatus.get(app.hed__Application_Status__c), false));
                    }
                }
            }
            if(courseConnectionsToUpsert.size() > 0){
                Schema.SObjectField upsertField = hed__Course_Enrollment__c.Fields.Upsert_Key__c;
                Database.UpsertResult [] cr = Database.upsert(courseConnectionsToUpsert, upsertField);
            }
        
        //AFTER UPDATE CONTEXT:    
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate && !BuildEpCourseApplicationsSync_TDTM.alreadyRan){
            BuildEpCourseApplicationsSync_TDTM.alreadyRan = true; 
            Map<Id, hed__Application__c> oldMap = new Map<Id, hed__Application__c>((List<hed__Application__c>) oldList); 
            for(hed__Application__c app : (List<hed__Application__c>) newList){
                //Trigger on application status update: 
                if(app.hed__Application_Status__c != null && app.hed__Application_Status__c != oldMap.get(app.Id).hed__Application_Status__c && appStatuses.contains(app.hed__Application_Status__c) 
                    && app.Course_Offering__c != null && app.RecordtypeId == ApplicationService.ExecutiveProgramsRTId){
                    appLst.add(app);
                    coIds.add(app.Course_Offering__c);      
                }
            }

            if(appLst.size() > 0){
                //Map Course Offering Id to Course offering: 
                Map<Id, hed__Course_Offering__c> cIdToChildrenCoMap = new Map<Id, hed__Course_Offering__c>([SELECT Id,Parent_Course__c,
                                                                                                            (Select Id from Child_Course_Offerings__r) 
                                                                                                            FROM hed__Course_Offering__c 
                                                                                                            WHERE Id IN :coIds]);

                //Create course connection record for each application
                for(hed__Application__c app : appLst){    
                    //Create course connection for each related child course offering: 
                    if(cIdToChildrenCoMap.get(app.Course_Offering__c).Child_Course_Offerings__r.size() > 0){
                        //if course connection is a parent course offering, set disable conflict checking to true
                        courseConnectionsToUpsert.add(createCourseConnection(app, app.Course_Offering__c, appStatusToCEStatus.get(app.hed__Application_Status__c), true));
                        for(hed__Course_Offering__c cco : cIdToChildrenCoMap.get(app.Course_Offering__c).Child_Course_Offerings__r){
                            courseConnectionsToUpsert.add(createCourseConnection(app, cco.Id, appStatusToCEStatus.get(app.hed__Application_Status__c), false)); 
                        }
                    }else{
                        //if course connection is not a parent course offering, set disable conflict check to false:
                        courseConnectionsToUpsert.add(createCourseConnection(app, app.Course_Offering__c, appStatusToCEStatus.get(app.hed__Application_Status__c), false));
                    }
                }

                if(courseConnectionsToUpsert.size() > 0){
                    Schema.SObjectField upsertField = hed__Course_Enrollment__c.Fields.Upsert_Key__c;
                    Database.UpsertResult [] cr = Database.upsert(courseConnectionsToUpsert, upsertField);
                }
            }

        //BEFORE DELETE CONTEXT: 
        }else if(triggerAction == hed.TDTM_Runnable.Action.BeforeDelete && !BuildEpCourseApplicationsSync_TDTM.alreadyRan){
            BuildEpCourseApplicationsSync_TDTM.alreadyRan = true; 
            Map<Id, hed__Application__c> oldMap = new Map<Id, hed__Application__c>((List<hed__Application__c>)oldList);
            //Delete all related course connection records
            List<hed__Course_Enrollment__c> cenrollmentTodelete =  [SELECT Id FROM hed__Course_Enrollment__c WHERE Application__c IN :oldMap.keySet()];
            if(cenrollmentTodelete.size() > 0) dmlWrapper.objectsToDelete.addAll(cenrollmentTodelete);
        }
        return dmlWrapper; 
    }


    private hed__Course_Enrollment__c createCourseConnection(hed__Application__c app, Id coId, String ceStatus, Boolean disableConflictCheck){
        hed__Course_Enrollment__c ce =  new hed__Course_Enrollment__c(Application__c = app.Id, 
                                                hed__Course_Offering__c = coId, 
                                                hed__Contact__c = app.hed__Applicant__c, 
                                                hed__Status__c = ceStatus, 
                                                Upsert_Key__c = String.valueOf(app.hed__Applicant__c) + String.valueOf(coId)); 
        if(disableConflictCheck) ce.Disable_Conflict_Checking__c = true; 
        return ce; 
    }
}