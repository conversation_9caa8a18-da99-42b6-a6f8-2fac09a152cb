<aura:component implements="flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" 
                access="global" controller="createShoppingCartController_clone"> <!-- force:appHostable,force:hasRecordId,-->
    <aura:handler name="init" value="{!this}" action="{!c.initFun}"/>
    
    <aura:attribute Name="recordId" type="Id" />
    <aura:attribute Name="cartId" type="Id" default=""/>
    <aura:attribute Name="SuccessMsg" type="String" />
    <aura:attribute Name="CartMap" type="LIST" default="[]"/>
    <aura:attribute Name="showCartInfo" type="boolean" default="false"/> 
    <aura:attribute Name="showCartUpdate" type="boolean" default="false"/>
    <aura:attribute Name="userName" type="string" default="" />
    <aura:attribute Name="Total" type="Double"  />
    <aura:attribute Name="SubTotal" type="Double" default="0.00" />
    <aura:attribute Name="salesTax" type="Double" default="0.00"  /> <!--Tax_Label -->
    <aura:attribute Name="cartEmpty" type="boolean" default="false"/> 
    <center>
        <div layout="block" style="font-size: x-large;">
            {!v.userName} Shopping Cart
        </div>
        <aura:if isTrue="{!and(empty(v.CartMap),v.showCartInfo)}">
            <div style="font-size: x-large;">Your Shopping Cart Seems To Be Empty!!!</div>
        </aura:if>
        <aura:if isTrue="{!(v.showCartUpdate)}" >
        	<div style="font-size: x-large; colour:green;">Your Shopping Cart is Updated!</div>
        </aura:if>
    </center>
    <aura:if isTrue="{!not(empty(v.CartMap))}"> 
        <div id="CartId" layout="block"  >
            <div style="float: right; border: 1px solid;padding: 2%;">
                <div style="font-size: large;border-bottom:1px solid">
                    Order Summary	
                </div> <br/>
                <div>
                    <table>
                        <thead>
                            <tr>
                                <th>  </th>
                                <th>  </th>
                            </tr>
                        </thead>
                        <tbody>
                           <tr>
                                <td>Subtotal</td>
                                <td>$<lightning:formattedNumber value="{!v.SubTotal}" minimumFractionDigits="2" maximumFractionDigits="2"/></td>
                            </tr>
                            <tr>
                                <td>{!$Label.c.Tax_Label}</td>
                                <td>$<lightning:formattedNumber value="{!v.salesTax}" minimumFractionDigits="2" maximumFractionDigits="2"/></td>
                            </tr> 
                            <tr>
                                <td><b>Total</b></td>
                                <td><b>$<lightning:formattedNumber value="{!v.Total}" minimumFractionDigits="2" maximumFractionDigits="2"/></b></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <lightning:button class="slds-m-top_small" variant="brand" 
                                  type="submit" name="" value="" onclick="{!c.checkout}"
                                  label="Proceed To Checkout" />
                </div>
                
            </div>
        </div> 
        <div style="padding-left:3%;">
            <br/><br/>
            <table class="cartTable2" id="CartTable2" style="width:65%;">
                <thead>
                    <tr 
                        style="border-top: 1px solid grey;border-bottom: 1px solid grey;height: 30px;
                               font-size: medium;">
                        <th><center>Item</center>   </th>
                        <th> <center>Price</center></th>
                        <th><center> Qty</center></th>
                        <th><center> Total</center></th>
                    </tr>
                </thead>
                <tbody>
                    <aura:iteration items="{!v.CartMap}" var="cus" indexVar="key">
                        
                        <tr style="height:100px; border:none; ">
                            <td style="padding-left:3%;padding-right: 3%;overflow: visible;">
                                <Span style="font-size: large">{!cus.item}
                                </Span> <br/> 
                                <Span style="font-size: small">{!cus.description}</Span>  
                                <span style="font-size: smaller"><br/>
                                <lightning:button class="" variant="base" aura:id="{!cus.cid}"
                                                      type="submit" name="{!cus.cid}" onclick="{!c.removeItem}" 
                                                      label="Remove" >
                                </lightning:button> 
                                </span>
                            </td>
                            <td style="width:12%;">
                                <center>
                                    $<lightning:formattedNumber value="{!cus.price}" minimumFractionDigits="2" maximumFractionDigits="2"/>
                               
                                </center>
                            </td>
                            <td style="width:12%; ">
                                
                                <lightning:input type="number" variant="label-hidden" name="qty" min="0" onchange="{!c.changeQty}" value="{!cus.qty}" />
                            </td>
                            <td style="width:20%; ">
                                <center>
                             	 <div > 
                                     $<lightning:formattedNumber value="{!cus.price * cus.qty}" minimumFractionDigits="2" maximumFractionDigits="2"/>
                                  
                                </div>
                                    </center>
                            </td>
                            
                        </tr>
                        
                        
                    </aura:iteration>
                </tbody>
            </table>
            <div style="padding-left:28%;">
                <lightning:button class="slds-m-top_small" variant="brand" 
                                  type="submit" name="AddToCart" onclick="{!c.continueShopping}" 
                                  label="Continue Shopping" />
                
                <lightning:button class="slds-m-top_small" variant="brand" 
                                  type="submit" name="Update" value="Update" onclick="{!c.UpdateCart}"
                                  label="Update Cart" />
            </div>
        </div>
    </aura:if> 
</aura:component>