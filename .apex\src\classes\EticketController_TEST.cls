@isTest
private class EticketController_TEST {
	@TestSetup
	public static void testDataSetup() {
		Account testAccount = new Account(Name = 'Test Account');
		insert testAccount;

		Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>', AccountId = testAccount.Id);
		insert testContact;

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c = '$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.Venue_Type__c = 'In-Person';
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.Tags__c = 'Strategic Communications';

		List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
		listOfEventToInsert.add(event);

		insert listOfEventToInsert;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = listOfEventToInsert[0].Id;
		fee.evt__Amount__c = 0.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		String cookieName = String.valueOf(dateTime.now());
		pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
		cart.pymt__Cart_UID__c = cookieName;
		insert cart;

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = testContact.Id;
		item.Event_Fee__c = fee.Id;
		item.pymt__Unit_Price__c = 0.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';

		insert item;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = testContact.Id;
		at.evt__Invitation_Status__c = 'Registered';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.Shopping_Cart_Item__c = item.Id;
		at.evt__Event__c = listOfEventToInsert[0].Id;
		at.evt__Reg_Email__c = '<EMAIL>';

		insert at;
	}

	@isTest
	static void testEticketController() {
		evt__Attendee__c attendee1 = [SELECT Id, evt__Contact__r.FirstName, evt__Contact__r.LastName, evt__Event_Fee__r.Name, evt__Event_Fee__r.evt__Event__r.Name FROM evt__Attendee__c WHERE evt__Contact__r.FirstName = 'John' LIMIT 1];

		test.startTest();
		String recordIds = '';
		recordIds += attendee1.Id;
		Test.setCurrentPageReference(new PageReference('/apex/printTickets'));
		ApexPages.currentPage().getParameters().put('Ids', recordIds);

		ApexPages.StandardController standardController = new ApexPages.StandardController(new evt__Attendee__c());
		EticketController controller = new EticketController(standardController);

		Test.stopTest();
	}
}