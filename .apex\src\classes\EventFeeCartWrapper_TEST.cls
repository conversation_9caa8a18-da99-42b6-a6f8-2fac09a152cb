@isTest
private class EventFeeCartWrapper_TEST {
	@isTest
	static void testEventFeeCartWrapper() {
		EventFeeCartWrapper wrapper = new EventFeeCartWrapper();
		wrapper.eventFeeId = 'a1VJQ000000IlV7';
		wrapper.Name = 'Test Event Fee';
		wrapper.mailingenabled = true;
		wrapper.price = 100.00;
		wrapper.quantity = 2;
		wrapper.tax = 10.00;
		wrapper.taxable = true;
		wrapper.eventId = 'a1VJQ000000IlV8';
		wrapper.currentUsage = 0;
		wrapper.maxUsage = 100;
		wrapper.LimitPerPurchase = 5;
	}
}