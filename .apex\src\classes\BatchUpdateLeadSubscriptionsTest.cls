@isTest(seealldata = false)
private class BatchUpdateLeadSubscriptionsTest {
    
    @isTest
    static void test_execute_unsubscribe() {
        Test.StartTest();
        makeLeads();
        
        BatchUpdateLeadMCSubsSchedule sh1 = new BatchUpdateLeadMCSubsSchedule();
        String sch = '0 0 23 * * ?'; 
        system.schedule('Test MC Lead Subscription Update', sch, sh1);
        
        Test.StopTest();
    }
    
    static void makeLeads() {
        list<Lead> lds = new list<Lead>();
        for (integer i = 0; i<5; i++) {
            Lead c = new Lead();
            c.LastName = 'Test' + string.valueOf(i);
            c.Do_Not_Contact__c = TRUE;
            c.Needs_MC_Subscription_Update__c = TRUE;
            c.Company = 'test';
            lds.add(c);
        }
        for (integer i = 0; i<5; i++) {
            Lead c = new Lead();
            c.LastName = 'Test' + string.valueOf(i);
            c.Do_Not_Contact__c = FALSE;
            c.Needs_MC_Subscription_Update__c = TRUE;
            c.Company = 'test';
            lds.add(c);
        }
        insert lds;
    }
}