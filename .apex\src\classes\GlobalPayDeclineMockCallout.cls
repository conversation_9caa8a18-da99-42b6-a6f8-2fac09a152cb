@IsTest
global class GlobalPayDeclineMockCallout implements HttpCalloutMock{
	global HttpResponse respond(HTTPRequest req) {
		HttpResponse res = new HttpResponse();
		res.setStatus('OK');
		res.setStatusCode(200);
		res.setBody('{"id":"TRN_cU2QgjjtbanVUrakacL1RR8jfjNG6g","time_created":"2024-04-23T07:31:16.308Z","type":"SALE","status":"DECLINED","amount":"1550","currency":"CAD","country":"CA","reference":"a0y4g000000AnyyAAC","payment_method":{"result":"00","message":"APPROVAL","card":{"authcode":"C23922","brand_reference":""}},"batch_id":"BAT_631639-422","action":{"id":"ACT_cU2QgjjtbanVUrakacL1RR8jfjNG6g","type":"SALE","time_created":"2021-04-23T07:31:16.308Z","result_code":"SUCCESS","app_id":"NZOF9fCkU7eQzwKAogwHJTM1Z8rC3oC9","app_name":"sample_app_CERT"}}');
		return res;
	}
}