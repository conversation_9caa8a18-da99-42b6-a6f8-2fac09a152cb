global class ContactCreationBatch implements Database.Batchable<sObject>, Database.Stateful {
	private List<evt__Attendee__c> attendees = new List<evt__Attendee__c>();
	private List<evt__Attendee__c> lstAttendeesInsert;
	private Set<Id> eventIds = new Set<Id>();
	private Set<String> emailAddresses = new Set<String>();
	private Set<Id> consIds = new Set<Id>();
	private Map<String, evt__Attendee__c> attendeeMap = new Map<String, evt__Attendee__c>();
	private Map<String, String> contactMap = new Map<String, String>();
	private Map<String, String> conTosub = new Map<String, String>();
	private String emailKey;

	global ContactCreationBatch(List<evt__Attendee__c> attendees, Map<String, String> mapSubscriptions) {
		this.conTosub = mapSubscriptions;
		this.lstAttendeesInsert = attendees;
		this.attendees = attendees;
		if (attendees.size() > 0) {
			for (evt__Attendee__c attendee : attendees) {
				this.eventIds.add(attendee.evt__Event__c);
				this.emailAddresses.add(attendee.evt__Reg_Email__c);
				this.attendeeMap.put(attendee.evt__Reg_Email__c, attendee);
			}
		}
		//System.debug('07-0-1 Attendees: ' + this.attendees.size());
		//System.debug('07-0-2 Attendees: ' + this.attendees);
		//System.debug('07-0-3 Event Ids: ' + this.eventIds.size());
		//System.debug('07-0-4 Event Ids: ' + this.eventIds);
		//System.debug('07-0-5 Email Addresses: ' + this.emailAddresses.size());
		//System.debug('07-0-6 Email Addresses: ' + this.emailAddresses);
		//System.debug('07-0-7 Attendees Map: ' + this.attendeeMap.size());
		//System.debug('07-0-8 Attendees Map: ' + this.attendeeMap);
	}

    global Database.QueryLocator start(Database.BatchableContext BC) {
		return Database.getQueryLocator([SELECT Id, Name FROM evt__Attendee__c Limit 1]);
    }

    global void execute(Database.BatchableContext BC, List<evt__Attendee__c> scope) {

		this.consIds = new Set<Id>();
		List<Subscription__c> lstSub = [SELECT Id, Name FROM Subscription__c];
		List<Contact> contactsToInsert = new List<Contact>();
		List<Subscription_Membership__c> lstSubscription = new List<Subscription_Membership__c>();

		List<Contact> lstContacts = [SELECT Id, Name, hed__AlternateEmail__c FROM Contact WHERE hed__AlternateEmail__c IN :this.emailAddresses];
		System.debug('07-0-9-1 Contacts: ' + lstContacts.size());
		if (lstContacts.size() > 0) {
			for (Contact con : lstContacts) {
				this.consIds.add(con.Id);
				this.emailAddresses.remove(con.hed__AlternateEmail__c);
			}
		}
		System.debug('07-0-9-2 emailAddresses: ' + this.emailAddresses);
		Boolean isSandbox = [SELECT IsSandbox FROM Organization LIMIT 1].IsSandbox;
		Map<String, String> mapSubs = new Map<String, String>();
		if (lstSub.size() > 0) {
			for (Subscription__c sub : lstSub) {
				mapSubs.put(sub.Name, sub.Id);
			}
		}
		for (String myEmail : this.emailAddresses) {
			Contact contact = new Contact();
			contact.FirstName = this.attendeeMap.get(myEmail).evt__Reg_First_Name__c;
			contact.Preferred_First_Name__c = this.attendeeMap.get(myEmail).evt__Reg_First_Name__c;
			contact.hed__Gender__c = this.attendeeMap.get(myEmail).Gender__c;
		 	contact.Former_Last_Name__c = this.attendeeMap.get(myEmail).evt__Reg_Last_Name__c;
		  	contact.LastName = this.attendeeMap.get(myEmail).evt__Reg_Last_Name__c;
		  	contact.hed__Preferred_Email__c = 'Alternate Email';
		  	contact.hed__AlternateEmail__c = this.attendeeMap.get(myEmail).evt__Reg_Email__c;
		  	contact.MailingStreet = this.attendeeMap.get(myEmail).evt__Reg_Street__c;
		  	contact.MailingCity = this.attendeeMap.get(myEmail).evt__Reg_City__c;
		  	contact.MailingState = this.attendeeMap.get(myEmail).evt__Reg_State__c;
		  	contact.MailingCountry = ImportAttendeesController.capitalizeFirstLetter(this.attendeeMap.get(myEmail).evt__Reg_Country__c);
		  	contact.MailingPostalCode = this.attendeeMap.get(myEmail).evt__Reg_Postal_Code__c;
			String newCountry = this.attendeeMap.get(myEmail).evt__Reg_Country__c;
			if (isSandbox) {
				contact.MailingCountry = ImportAttendeesController.capitalizeFirstLetter(this.attendeeMap.get(myEmail).evt__Reg_Country__c);
			} else {
				//System.debug('This is a Production environment.');
				if (newCountry == 'United States of America' || newCountry == 'USA' || newCountry == 'U.S.A'|| newCountry == 'U.S.A.' || newCountry == 'U.S.' || newCountry == 'US' || newCountry == 'us' || newCountry == 'United States' || newCountry == 'UNITED STATES' || newCountry == 'UNITED STATES OF AMERICA' || newCountry == 'UNITED STATES OF AMERICA (USA)') {
					contact.MailingCountry = 'U.S.A.';
				}
			}
			contactsToInsert.add(contact);
		}
		System.debug('07-0-11 Contacts to create: ' + contactsToInsert.size());
		System.debug('07-0-12 Contacts to create: ' + contactsToInsert);
		if (contactsToInsert.size() > 0) {
			Database.UpsertResult[] insertResults = Database.upsert(contactsToInsert, false);
			for (Database.UpsertResult sr : insertResults) {
				if (sr.isSuccess()) {
					System.debug('07-0-9 Contacts created successfully');
					this.consIds.add(sr.getId());
				}else{
					System.debug('07-0-10 Contacts creation error: ' + sr.getErrors()[0].getMessage());
				}
			}
		}

		System.debug('07-0-13 Contacts Ids: ' + this.consIds.size());
		System.debug('07-0-14 Contacts Ids: ' + this.consIds);
		if (this.consIds.size() > 0) {
			List<Contact> lstCons = [SELECT Id, hed__AlternateEmail__c FROM Contact WHERE Id IN :this.consIds];
			//System.debug('07-0-15 Contacts: ' + lstCons.size());
			//System.debug('07-0-16 Contacts: ' + lstCons);
			for (Contact con : lstCons) {
				String myEmailKey = con.hed__AlternateEmail__c;
				myEmailKey = myEmailKey.trim();
				myEmailKey = myEmailKey.toLowerCase();
				this.contactMap.put(myEmailKey, con.Id);
				if (!this.conTosub.containsKey(myEmailKey)) {
					this.conTosub.remove(myEmailKey);
				}
			}
			System.debug('07-0-17 Contacts Map: ' + this.contactMap.size());
			System.debug('07-0-18 Contacts Map: ' + this.contactMap);

			Set<String> programNames1 = new Set<String>();
			Set<String> termNames1 = new Set<String>();
			for (evt__Attendee__c att : this.lstAttendeesInsert) {
				if (att?.Academic_Program__c != null) {
					programNames1.add(att?.Academic_Program__c);
				}
				if (att?.Academic_Term__c != null) {
					termNames1.add(att.Academic_Term__c);
				}
			}
			System.debug('07-0-19 programNames===> ' + programNames1);
			System.debug('07-0-20 termNames===> ' + termNames1);
			Map<String, String> programMap = new Map<String, String>();
			List<Program_Term_Availability__c> programList = [SELECT Program__c, Program__r.Name FROM Program_Term_Availability__c WHERE Program__r.Name IN :programNames1];
			for (Program_Term_Availability__c pta : programList) {
				String pname = pta.Program__r.Name;
				pname = pname.trim();
				pname = pname.toUpperCase();
				programMap.put(pname, pta.Program__c);
			}

			Map<String, String> termMap = new Map<String, String>();
			List<Program_Term_Availability__c> termList = [SELECT Term__c, Term__r.Name FROM Program_Term_Availability__c WHERE Term__r.Name IN :termNames1];
			for (Program_Term_Availability__c pta : termList) {
				termMap.put(pta.Term__r.Name, pta.Term__c);
			}

			System.debug('07-0-21 programMap===> ' + programMap);
			System.debug('07-0-22 termMap===> ' + termMap);
			if (this.lstAttendeesInsert.size() > 0) {
				//System.debug('07-0-23 lstAttendeesInsert===> ' + this.lstAttendeesInsert);
				List<evt__Attendee__c> lstAttsUpdate = new List<evt__Attendee__c>();

				for (evt__Attendee__c att : this.lstAttendeesInsert) {
					String programName = att?.Academic_Program__c;
					if (programName != null && programName != ''){
						programName = programName.trim();
						programName = programName.toUpperCase();
					}
					if (att?.Academic_Program__c != null && programMap.containsKey(programName)) {
						att.Academic_Program__c = programMap.get(programName);
					}else{
						att.Academic_Program__c = null;
					}
					if (att?.Academic_Term__c != null && termMap.containsKey(att.Academic_Term__c)) {
						att.Academic_Term__c = termMap.get(att.Academic_Term__c);
					}else{
						att.Academic_Term__c = null;
					}
					this.emailKey = att.evt__Reg_Email__c;
					this.emailKey = this.emailKey.trim();
					this.emailKey = this.emailKey.toLowerCase();
					System.debug('07-0-25 myEmail===> ' + this.emailKey);
					System.debug('07-0-26 myContactId===> ' + this.contactMap.get(this.emailKey));
					if (this.contactMap.containsKey(this.emailKey)) {
						att.evt__Contact__c = this.contactMap.get(this.emailKey);
					}else{
						att.evt__Contact__c = null;
					}
					lstAttsUpdate.add(att);
				}

				System.debug('07-0-24 lstAttsUpdate===> ' + lstAttsUpdate);
				System.debug('07-0-25 lstAttsUpdate.size===> ' + lstAttsUpdate.size());
				Database.SaveResult[] insertResults = Database.insert(lstAttsUpdate, false);
				for (Database.SaveResult sr : insertResults) {
					if (!sr.isSuccess()) {
						System.debug('07-0-26-01 res===> ' + sr.getErrors()[0].getMessage());
						break;
					}
				}

				for (String subKey : this.conTosub.keySet()) {
					String subValue = this.conTosub.get(subKey);
					List<String> splitValues = subValue.split(';');
					System.debug('03-0-2-1== '+subKey);
					System.debug('03-0-2-2== '+splitValues);
					if (splitValues.size() > 1) {
						for (String val : splitValues) {
							//List<Subscription_Membership__c> sm1 = [SELECT Id FROM Subscription_Membership__c WHERE Contact_Email__c = :subKey AND Subscription__r.Name = :val];
							//System.debug('03-0-2-31== '+sm1);
							//if (sm1.size() == 0) {
								if (mapSubs.containsKey(val)) {
									Subscription_Membership__c smNew = new Subscription_Membership__c();
									smNew.Subscription__c = mapSubs.get(val);
									String currentKey = subKey.trim();
									currentKey = currentKey.toLowerCase();
									String myCon = this.contactMap.get(currentKey);
									if (myCon != null) {
										System.debug('03-0-2-37== '+myCon);
										smNew.Contact__c = myCon;
									}
									smNew.Subscribed_Date__c = Date.today();
									smNew.Subscription_Status__c = 'Subscribed';
									lstSubscription.add(smNew);
								}
							//}
						}
					} else if (splitValues.size() == 1) {
						//List<Subscription_Membership__c> sm2 = [SELECT Id FROM Subscription_Membership__c WHERE Contact_Email__c = :subKey AND Subscription__r.Name = :subValue];
						//System.debug('03-0-2-32== '+sm2);
						System.debug('03-0-2-33== '+splitValues);
						System.debug('03-0-2-35== subKey'+subKey);
						String currentKey1 = subKey.trim();
						currentKey1 = currentKey1.toLowerCase();
						//if (sm2.size() == 0) {
							if (mapSubs.containsKey(subValue)) {
								Subscription_Membership__c smNew = new Subscription_Membership__c();
								smNew.Subscription__c = mapSubs.get(subValue);
								smNew.Contact__c = this.contactMap.get(currentKey1);
								smNew.Subscribed_Date__c = Date.today();
								smNew.Subscription_Status__c = 'Subscribed';
								lstSubscription.add(smNew);
							}
						//}
					}
				}

				System.debug('03-0-3== '+lstSubscription);
				if (lstSubscription.size() > 0) {
					Database.SaveResult[] insertSubResults = Database.insert(lstSubscription, false);
					for (Database.SaveResult sr : insertSubResults) {
						if (sr.isSuccess()) {
							System.debug('03-0-4 Subscription member created successfully');
						} else {
							System.debug('03-0-5 Subscription member creation error: ' + sr.getErrors()[0].getMessage());
						}
					}
				}
			}
		}
    }

    global void finish(Database.BatchableContext BC) {
		System.debug('07-0-15 Contacts creation Batch job completed');
    }
}