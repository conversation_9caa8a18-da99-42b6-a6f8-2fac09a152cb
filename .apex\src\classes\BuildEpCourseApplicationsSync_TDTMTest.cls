/**
* @description    Test class for BuildEpCourseApplicationsSync_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-26-05
* @modified 2020-26-05
*/
@isTest
public class BuildEpCourseApplicationsSync_TDTMTest {
    @testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('BuildEpCourseApplicationsSync_TDTM', 'Application__c', 'AfterInsert;AfterUpdate;BeforeDelete', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        //Create test Account:
        Account a = new Account(Name='TestAccount');
        insert a;
        //Create test contact: 
        Contact c = (Contact) TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student',email='<EMAIL>')); 
        insert c;
        Contact c1 = (Contact) TestFactory.createSObject(new Contact(FirstName = 'Test1', LastName = 'Student1',email='<EMAIL>')); 
        insert c1;
        //Create Term
        hed__Term__c trm = new hed__Term__c(hed__Account__c=a.Id,name='Spring 2020');
        insert trm;
        //Create course
        hed__Course__c course = new hed__Course__c(hed__Account__c=a.Id,name='MBA 2020');
        insert course;
        //List to Insert Parent and Child offerings 
        List<hed__Course_Offering__c> parentOfferings = new List<hed__Course_Offering__c>();
        List<hed__Course_Offering__c> childOfferings = new List<hed__Course_Offering__c>();
        //Create Course Offerings
        hed__Course_Offering__c cOfferingParent = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering parent Only',hed__Term__c=trm.Id);
        parentOfferings.add(cOfferingParent);
        hed__Course_Offering__c cOfferingParentwithChild = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering parent with Childs',hed__Term__c=trm.Id);
        parentOfferings.add(cOfferingParentwithChild);
        insert parentOfferings;
        hed__Course_Offering__c cOfferingChild1 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child1',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild1);
        hed__Course_Offering__c cOfferingChild2 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child2',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild2);
        hed__Course_Offering__c cOfferingChild3 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child3',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild3);
        hed__Course_Offering__c cOfferingChild4 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child4',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild4);
        insert childOfferings;
    }
    @isTest 
    static void BuildEpCourseApplicationsSync_TDTMTestMethod() {
        Contact con = [Select id from Contact where lastname='Student' limit 1];
        Contact con1 = [Select id from Contact where lastname='Student1' limit 1];
        hed__Course_Offering__c coffrng = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent Only' limit 1];
        hed__Course_Offering__c coffrngWithChilds = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent with Childs' limit 1];
        List<hed__Application__c> applst = new List<hed__Application__c>();
        List<hed__Application__c> applstToUpdate = new List<hed__Application__c>();
        hed__Application__c app = new hed__Application__c(hed__Application_Status__c = 'Offer',Course_Offering__c = coffrng.id,hed__Applicant__c = con.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(app);
        hed__Application__c appWithChildOffrngs = new hed__Application__c(hed__Application_Status__c = 'Accepted Offer',Course_Offering__c = coffrngWithChilds.id,hed__Applicant__c = con1.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(appWithChildOffrngs);
        insert applst;
        System.assertEquals(6,[Select Id from hed__Course_Enrollment__c].size());
        for(hed__Application__c applctn : applst){
            applctn.hed__Application_Status__c = 'Refused';
            applctn.RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId();
            applstToUpdate.add(applctn);
        }
        update applstToUpdate;
    }
    @isTest 
    static void BuildEpCourseApplicationsSync_TDTMTestMethod1() {
        Contact con = [Select id from Contact limit 1];
        hed__Course_Offering__c coffrng = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent Only' limit 1];
        hed__Course_Offering__c coffrngWithChilds = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent with Childs' limit 1];
        List<hed__Application__c> applst = new List<hed__Application__c>();
        List<hed__Application__c> applstToUpdate = new List<hed__Application__c>();
        hed__Application__c app = new hed__Application__c(hed__Application_Status__c = 'Accepted Offer',Course_Offering__c = coffrng.id,hed__Applicant__c = con.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(app);
        hed__Application__c appWithChildOffrngs = new hed__Application__c(hed__Application_Status__c = 'Offer',Course_Offering__c = coffrngWithChilds.id,hed__Applicant__c = con.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(appWithChildOffrngs);
        insert applst;
        System.assertEquals(6,[Select Id from hed__Course_Enrollment__c].size());
        for(hed__Application__c applctn : applst){
            applctn.hed__Application_Status__c = 'Accepted Offer';
            applctn.RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId();
            applstToUpdate.add(applctn);
        }
        update applstToUpdate;
    }
    @isTest 
    static void BuildEpCourseApplicationsSync_TDTMTestMethod2() {
        Contact con = [Select id from Contact limit 1];
        hed__Course_Offering__c coffrng = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent Only' limit 1];
        hed__Course_Offering__c coffrngWithChilds = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent with Childs' limit 1];
        List<hed__Application__c> applst = new List<hed__Application__c>();
        List<hed__Application__c> applstToUpdate = new List<hed__Application__c>();
        hed__Application__c app = new hed__Application__c(hed__Application_Status__c = 'Accepted Offer',Course_Offering__c = coffrng.id,hed__Applicant__c = con.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(app);
        hed__Application__c appWithChildOffrngs = new hed__Application__c(hed__Application_Status__c = 'Offer',Course_Offering__c = coffrngWithChilds.id,hed__Applicant__c = con.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(appWithChildOffrngs);
        insert applst;
        System.assertEquals(6,[Select Id from hed__Course_Enrollment__c].size());
        for(hed__Application__c applctn : applst){
            applctn.hed__Application_Status__c = 'Offer';
            applctn.RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId();
            applstToUpdate.add(applctn);
        }
        update applstToUpdate;
        delete applstToUpdate;
        System.assertEquals(6,[Select Id from hed__Course_Enrollment__c].size());
    }
    @isTest 
    static void BuildEpCourseApplicationsSync_TDTMTestMethod3() {
        Contact con = [Select id from Contact limit 1];
        hed__Course_Offering__c coffrng = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent Only' limit 1];
        hed__Course_Offering__c coffrngWithChilds = [Select id from hed__Course_Offering__c where Name ='Test Course Offering parent with Childs' limit 1];
        List<hed__Application__c> applst = new List<hed__Application__c>();
        List<hed__Application__c> applstToUpdate = new List<hed__Application__c>();
        hed__Application__c app = new hed__Application__c(hed__Application_Status__c = 'Accepted Offer',Course_Offering__c = coffrng.id,hed__Applicant__c = con.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(app);
        hed__Application__c appWithChildOffrngs = new hed__Application__c(hed__Application_Status__c = 'Offer',Course_Offering__c = coffrngWithChilds.id,hed__Applicant__c = con.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(appWithChildOffrngs);
        insert applst;
        System.assertEquals(6,[Select Id from hed__Course_Enrollment__c].size());
        for(hed__Application__c applctn : applst){
            applctn.hed__Application_Status__c = 'Offer';
            applctn.RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId();
            applstToUpdate.add(applctn);
        }
        BuildEpCourseApplicationsSync_TDTM.alreadyRan = false;
        update applstToUpdate;
        BuildEpCourseApplicationsSync_TDTM.alreadyRan = false;
        delete applstToUpdate;
        System.assertEquals(0,[Select Id from hed__Course_Enrollment__c].size());
    }
}