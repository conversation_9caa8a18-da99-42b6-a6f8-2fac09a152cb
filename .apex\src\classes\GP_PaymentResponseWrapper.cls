// Generated by JSON2Apex http://json2apex.herokuapp.com/
//
// The supplied json has fields with names that are not valid in apex
// and so can only be parsed with explicitly generated code, this option
// was auto selected for you.

public class GP_PaymentResponseWrapper {

	public class Payment_method {
		public String result {get;set;} 
		public String message {get;set;} 
		public String entry_mode {get;set;} 
        
		public Card card {get;set;} 

		public Payment_method(JSONParser parser) {
			while (parser.nextToken() != System.JSONToken.END_OBJECT) {
				if (parser.getCurrentToken() == System.JSONToken.FIELD_NAME) {
					String text = parser.getText();
					if (parser.nextToken() != System.JSONToken.VALUE_NULL) {
						if (text == 'result') {
							result = parser.getText();
						} else if (text == 'message') {
							message = parser.getText();
						} else if (text == 'entry_mode') {
							entry_mode = parser.getText();
						} else if (text == 'card') {
							card = new Card(parser);
						} else {
							System.debug(LoggingLevel.WARN, 'Payment_method consuming unrecognized property: '+text);
							consumeObject(parser);
						}
					}
				}
			}
		}
	}
	
	public class Action {
		public String id {get;set;} 
		public String type_Z {get;set;} // in json: type
		public String time_created {get;set;} 
		public String result_code {get;set;} 
		public String app_id {get;set;} 
		public String app_name {get;set;} 

		public Action(JSONParser parser) {
			while (parser.nextToken() != System.JSONToken.END_OBJECT) {
				if (parser.getCurrentToken() == System.JSONToken.FIELD_NAME) {
					String text = parser.getText();
					if (parser.nextToken() != System.JSONToken.VALUE_NULL) {
						if (text == 'id') {
							id = parser.getText();
						} else if (text == 'type') {
							type_Z = parser.getText();
						} else if (text == 'time_created') {
							time_created = parser.getText();
						} else if (text == 'result_code') {
							result_code = parser.getText();
						} else if (text == 'app_id') {
							app_id = parser.getText();
						} else if (text == 'app_name') {
							app_name = parser.getText();
						} else {
							System.debug(LoggingLevel.WARN, 'Action consuming unrecognized property: '+text);
							consumeObject(parser);
						}
					}
				}
			}
		}
	}
	
	public String id {get;set;} 
	public String time_created {get;set;} 
	public String type_Z {get;set;} // in json: type
	public String status {get;set;} 
	public String channel {get;set;} 
	public String capture_mode {get;set;} 
	public String amount {get;set;} 
	public String currencyVal {get;set;} 
	public String country {get;set;} 
	public String merchant_id {get;set;} 
	public String merchant_name {get;set;} 
	public String account_id {get;set;} 
	public String account_name {get;set;} 
	public String reference {get;set;} 
	public Payment_method payment_method {get;set;} 
	public String batch_id {get;set;} 
    public Action action {get;set;} 
    
    public String error_code {get;set;}  
    public String detailed_error_code {get;set;} 
    public String detailed_error_description {get;set;} 
    
	public GP_PaymentResponseWrapper(JSONParser parser) {
        System.debug('parser1 '+parser);
		while (parser.nextToken() != System.JSONToken.END_OBJECT) {
			if (parser.getCurrentToken() == System.JSONToken.FIELD_NAME) {
				String text = parser.getText();
                System.debug('text '+text);
				if (parser.nextToken() != System.JSONToken.VALUE_NULL) {
					if (text == 'id') {
						id = parser.getText();
					} else if (text == 'time_created') {
						time_created = parser.getText();
					} else if (text == 'type') {
						type_Z = parser.getText();
                        System.debug('type_Z '+type_Z);
					} else if (text == 'status') {
						status = parser.getText();
					} else if (text == 'channel') {
						channel = parser.getText();
					} else if (text == 'capture_mode') {
						capture_mode = parser.getText();
					} else if (text == 'amount') {
						amount = parser.getText();
					} else if (text == 'currency') {
                        System.debug('currencyVal ');
						currencyVal = parser.getText();
                        System.debug('currencyVal '+currencyVal);
					} else if (text == 'country') {
						country = parser.getText();
					} else if (text == 'merchant_id') {
						merchant_id = parser.getText();
					} else if (text == 'merchant_name') {
						merchant_name = parser.getText();
					} else if (text == 'account_id') {
						account_id = parser.getText();
					} else if (text == 'account_name') {
						account_name = parser.getText();
					} else if (text == 'reference') {
						reference = parser.getText();
					} else if (text == 'payment_method') {
						payment_method = new Payment_method(parser);
					} else if (text == 'batch_id') {
						batch_id = parser.getText();
					} else if (text == 'error_code') {
						error_code = parser.getText();
					} else if (text == 'detailed_error_code') {
						detailed_error_code = parser.getText();
					} else if (text == 'detailed_error_description') {
						detailed_error_description = parser.getText();
					} else if (text == 'action') {
						action = new Action(parser);
					} else {
						System.debug(LoggingLevel.WARN, 'JSON2Apex consuming unrecognized property: '+text);
						consumeObject(parser);
					}
				}
			}
		}
	}
	
	public class Card {
		public String brand {get;set;} 
		public String masked_number_last4 {get;set;} 
		public String authcode {get;set;} 
		public String brand_reference {get;set;} 
		public String brand_time_created {get;set;} 
		public String cvv_result {get;set;} 

		public Card(JSONParser parser) {
			while (parser.nextToken() != System.JSONToken.END_OBJECT) {
				if (parser.getCurrentToken() == System.JSONToken.FIELD_NAME) {
					String text = parser.getText();
					if (parser.nextToken() != System.JSONToken.VALUE_NULL) {
						if (text == 'brand') {
							brand = parser.getText();
						} else if (text == 'masked_number_last4') {
							masked_number_last4 = parser.getText();
						} else if (text == 'authcode') {
							authcode = parser.getText();
						} else if (text == 'brand_reference') {
							brand_reference = parser.getText();
						} else if (text == 'brand_time_created') {
							brand_time_created = parser.getText();
						} else if (text == 'cvv_result') {
							cvv_result = parser.getText();
						} else {
							System.debug(LoggingLevel.WARN, 'Card consuming unrecognized property: '+text);
							consumeObject(parser);
						}
					}
				}
			}
		}
	}
	
	
	public static GP_PaymentResponseWrapper parse(String json) {
        system.debug('PayDetailsWrapperPayDetailsWrapper');
		System.JSONParser parser = System.JSON.createParser(json);
		return new GP_PaymentResponseWrapper(parser);
	}
	
	public static void consumeObject(System.JSONParser parser) {
		Integer depth = 0;
		do {
			System.JSONToken curr = parser.getCurrentToken();
			if (curr == System.JSONToken.START_OBJECT || 
				curr == System.JSONToken.START_ARRAY) {
				depth++;
			} else if (curr == System.JSONToken.END_OBJECT ||
				curr == System.JSONToken.END_ARRAY) {
				depth--;
			}
		} while (depth > 0 && parser.nextToken() != null);
	}
}