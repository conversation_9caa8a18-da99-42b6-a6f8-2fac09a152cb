global class SubscriptionCenterHashBatch implements Database.Batchable<sObject> {
  
    // Query all contacts to run batch on
    global Database.QueryLocator start(Database.BatchableContext bc) {
        
        // Query with all needed fields
        String query = 'SELECT Id, HASH_Id__c FROM Contact';
        
        return Database.getQueryLocator(query); // Return query locator
    }
    
    // Run hash utility for each batch
    global void execute(Database.BatchableContext bc, List<Contact> records) {
        update SubscriptionCenterHashUtility.calculateHash(records);
    }
    
    // Do nothing on finish
    global void finish(Database.BatchableContext bc) {}
}