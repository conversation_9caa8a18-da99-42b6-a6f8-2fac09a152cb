/**
 * @description Determines the 
 * <AUTHOR>
 * @version 1.0
 * @created 2020-09-09
 * @modified 2020-09-09
 */
global class ShoppingCartFeeService_TDTM extends hed.TDTM_Runnable{
    final List<String> sciTypesToInclude = new List<String>{'Event Registration'}; 
    public static final List<String> amountFields = new List<String>{ 'Gross_Amount__c', 'Discount_Amount__c', 'Tax_Amount__c', 'Total_Amount__c' };

    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper                         = new hed.TDTM_Runnable.DMLWrapper(); 
        Map<Id, List<pymt__Shopping_Cart_Item__c>> scisToUpdateByEvent  = new Map<Id, List<pymt__Shopping_Cart_Item__c>>();

        //Check new shopping cart items which are not paud and have a special event but not a fee
        if ( triggerAction ==  hed.TDTM_Runnable.Action.BeforeInsert ) {

            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList)
                if( !sci.pymt__Payment_Completed__c && sci.Event_Fee__c == null && sci.Special_Event__c != null && sciTypesToInclude.contains(sci.Type__c))
                    if ( scisToUpdateByEvent.containsKey(sci.Special_Event__c) ) {
                        scisToUpdateByEvent.get( sci.Special_Event__c ).add( sci ); 
                    } else {
                        scisToUpdateByEvent.put( sci.Special_Event__c, new List<pymt__Shopping_Cart_Item__c>{ sci } ); 
                    }


        //Check all shopping cart items updated to include an event
        } else if ( triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate ) {
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList);

            for ( pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList ) 
                if ( !sci.pymt__Payment_Completed__c && sci.Event_Fee__c == null && oldMap.get(sci.Id).Special_Event__c == null && sci.Special_Event__c != null && sciTypesToInclude.contains(sci.Type__c)) 
                    if ( scisToUpdateByEvent.containsKey(sci.Special_Event__c) ) {
                        scisToUpdateByEvent.get( sci.Special_Event__c ).add( sci ); 
                    } else {
                        scisToUpdateByEvent.put( sci.Special_Event__c, new List<pymt__Shopping_Cart_Item__c>{ sci } ); 
                    }
        } 


        if ( scisToUpdateByEvent.keySet().size() > 0 ) {

            List<Contact> uContact      = [ SELECT Id FROM Contact WHERE Community_User__c = :UserInfo.getUserId() ];
            Set<Id> conIds              = new Set<Id>();
            Map<Id, Contact> cons       = new Map<Id, Contact>();

            //Get the contacts and types of the shopping cart items to apply the filters
            for ( List<pymt__Shopping_Cart_Item__c> scis : scisToUpdateByEvent.values() )
                for ( pymt__Shopping_Cart_Item__c sci : scis ) {
                    if ( sci.pymt__Contact__c == null && uContact.size() > 0 )  { sci.pymt__Contact__c = uContact[0].Id; }
                    if ( sci.pymt__Contact__c != null )                         { conIds.add( sci.pymt__Contact__c ); }
                }

            if ( conIds.size() > 0 )
                cons = new Map<Id, Contact>( [ SELECT Id, Type__c FROM Contact WHERE ID IN :conIds ] );

            for ( evt__Special_Event__c e : [ SELECT Id, (SELECT Id, Type__c FROM evt__Event_Fees__r WHERE Available_for_Checkout__c = true AND evt__Limit_Per_Purchase__c = null AND evt__Active__c= true ORDER BY evt__Amount__C ASC ) FROM evt__Special_Event__c WHERE Id IN :scisToUpdateByEvent.keySet() ] )
                for ( pymt__Shopping_Cart_Item__c sci : scisToUpdateByEvent.get(e.Id) ) {
                    if ( sci.pymt__Contact__c != null && e.evt__Event_Fees__r != null )
                        sci.Event_Fee__c = getFee( cons.get(sci.pymt__Contact__c), e.evt__Event_Fees__r );
                    else if ( e.evt__Event_Fees__r != null ) 
                        sci.Event_Fee__c = getFee( null, e.evt__Event_Fees__r );
                    }

        }

        return dmlWrapper; 
    }


    private Id getFee ( Contact con, List<evt__Event_Fee__c> fees ) {
        
        //Get the list of all contact types to filter on, if no contact, assume empty list for filtration purposes
        List<String> types = new List<String>();
        if ( con != null && !String.isBlank(con.Type__c) )
            types = con.Type__c.split(';');

        //Loop through ordered event fees to determine appropriate default fee for each
        for ( evt__Event_Fee__c fee : fees )
            if  ( con != null && fee.Type__c != null && types.contains( fee.Type__c ) )
                return fee.Id;
            else if ( fee.Type__c == 'Standard' || fee.Type__c == null  )
                return fee.Id;

        return null;

    }

}