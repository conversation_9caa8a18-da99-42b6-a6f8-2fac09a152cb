@isTest private class UpsertContactTest {

    private static testmethod void testUpsertContact() {
        
        Contact contact = new Contact(firstName = 'Joe',
                                     LastName = 'Test',
                                     MailingStreet = '123 Oak',
                                     MailingCity = 'Encinitas',
                                     MailingState = 'CA',
                                     MailingPostalCode = '92024',
                                     MailingCountry = 'US',
                                     Email = '<EMAIL>');
        UpsertContact.upsertContact(new List<Contact>{contact});
        
    }
}