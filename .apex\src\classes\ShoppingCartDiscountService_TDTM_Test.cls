/**
* @description    Test class for ShoppingCartDiscountService_TDTM_Test class
* <AUTHOR>
* @version        1.0 
* @created 2020-09-30
* @modified 2020-09-30
*/
@isTest
public class ShoppingCartDiscountService_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for TestScoreCalculator_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('ShoppingCartDiscountService_TDTM', 'pymt__Shopping_Cart_Item__c', 'BeforeInsert;BeforeUpdate', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Create test event: 
        evt__Special_Event__c testEvent = new evt__Special_Event__c(Name = 'Rotman Financial Seminar', evt__Event_Type__c='Seminar', Taxable__c = true); 
        insert testEvent; 

        //Create test event fee: 
        evt__Event_Fee__c standardFee = new evt__Event_Fee__c(Name = 'Standard Fee', evt__Event__c = testEvent.Id, evt__Category__c = 'Attendee', evt__Amount__c = 100, evt__Active__c = true, type__c = 'Standard'); 
        insert standardFee; 

        //List of discounts: 
        List<Discount__c> discounts = new List<Discount__c>(); 
        discounts.add(new Discount__c(Name = 'Rotman Financial Seminar (All)', Available_for__c = 'All', Event__c = testEvent.Id, Percent_Discount__c = 5, Code__c = 'RFAAll', Taxable__c = true, Automatically_Apply__c= true, Type__c='Event Discount', Max_Usage__c = 1)); 
        discounts.add(new Discount__c(Name = 'Rotman Financial Seminar (All - Non-Auto)', Available_for__c = 'All', Event__c = testEvent.Id, Percent_Discount__c = 7, Code__c = 'RFAAllAor', Taxable__c = true, Type__c='Event Discount' )); 
        discounts.add(new Discount__c(Name = 'Rotman Financial Seminar (Rotman Alumni)', Available_for__c = 'Rotman Alumni', Event__c = testEvent.Id, Percent_Discount__c = 10, Code__c = 'RFAAlum', Taxable__c = true, Automatically_Apply__c = true, Type__c='Event Discount')); 
        discounts.add(new Discount__c(Name = 'Rotman Financial Seminar (Student)', Available_for__c = 'Rotman Student', Event__c = testEvent.Id, Dollar_Discount__c = 20, Code__c = 'RFAAll1', Taxable__c = true, Automatically_Apply__c = true, Type__c='Event Discount')); 
        discounts.add(new Discount__c(Name = 'Rotman Financial Seminar (Individual)', Available_for__c = 'Individual', Event__c = testEvent.Id, Percent_Discount__c = 15, Taxable__c = true, ownerId = userInfo.getUserId(), Automatically_Apply__c = true, Type__c='Event Discount' )); 
        discounts.add(new Discount__c(Name = 'Fee Waiver', Available_for__c = 'Individual', Percent_Discount__c = 100, Type__c = 'Fee Waiver', Automatically_Apply__c = true)); 
        insert discounts; 

        //Insert test shopping cart items: 
        insert new pymt__Shopping_Cart_Item__c(Name = 'Test SCI', pymt__Quantity__c = 1, OwnerId = UserInfo.getUserId()); 
    }
    /**
     * @description Insert Shopping cart item from a non-student or alumni: 
     * Assert that the correct discount is stamped on the shopping cart item
     * */
    @isTest  
    public static void testInsertSCIAll(){
        evt__Special_Event__c specialEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Rotman Financial Seminar' LIMIT 1]; 
        evt__Event_Fee__c fee = [SELECT Id FROM evt__Event_Fee__c WHERE evt__Event__c = :specialEvent.Id And Type__c = 'Standard' LIMIT 1]; 
        User testUser = [SELECT ID, ContactId FROM User WHERE Profile.Name = 'Applicant Community User' AND Rotman_Alumni__c = false AND IsActive = true AND ContactId != null LIMIT 1]; 

        pymt__Shopping_Cart_Item__c SCIToInsert = new pymt__Shopping_Cart_Item__c(Name = 'Test SCI', 
                                                                                    pymt__Quantity__c = 1, 
                                                                                    pymt__Contact__c = testUser.ContactId, 
                                                                                    OwnerId = testUser.Id,
                                                                                    Special_Event__c = specialEvent.id, 
                                                                                    Event_Fee__c = fee.Id, 
                                                                                    Type__c = 'Event Registration'
                                                                                    ); 
        
        Test.startTest(); 
            insert SCIToInsert; 
        Test.stopTest(); 

        //Assert that the correct discount record was stamped: 
        Discount__c expectedDiscount = [SELECT ID FROM Discount__c WHERE Event__c = :specialEvent.Id AND Automatically_Apply__c = TRUE AND Requires_Approval__c = false AND Type__c = 'Event Discount' AND Available_For__c = 'All' LIMIT 1]; 
        pymt__Shopping_Cart_Item__c sciAfter = [SELECT ID, Discount__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :SCIToInsert.Id]; 

        System.Assert(sciAfter.Discount__c == expectedDiscount.Id, 'Discount was not stamped correctly on the shopping cart item. Expected Discount: ' + expectedDiscount + ' Actual Discount: ' + sciAfter); 
    }
    /**
     * @description Insert Shopping cart item as a Rotman Alumni: 
     * Assert that the correct discount is stamped on the shopping cart item
     * */
    @isTest  
    public static void testInsertSCI_RotmanAlumni(){
        evt__Special_Event__c specialEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Rotman Financial Seminar' LIMIT 1]; 
        evt__Event_Fee__c fee = [SELECT Id FROM evt__Event_Fee__c WHERE evt__Event__c = :specialEvent.Id And Type__c = 'Standard' LIMIT 1]; 
        User testUser = [SELECT ID, ContactId FROM User WHERE Rotman_Alumni__c = true AND IsActive = true AND ContactId != null LIMIT 1]; 

        pymt__Shopping_Cart_Item__c SCIToInsert = new pymt__Shopping_Cart_Item__c(Name = 'Test SCI', 
                                                                                    pymt__Quantity__c = 1, 
                                                                                    pymt__Contact__c = testUser.ContactId, 
                                                                                    OwnerId = testUser.Id,
                                                                                    Special_Event__c = specialEvent.id, 
                                                                                    Event_Fee__c = fee.Id, 
                                                                                    Type__c = 'Event Registration'
                                                                                    ); 
        
        Test.startTest(); 
            insert SCIToInsert; 
        Test.stopTest(); 

        //Assert that the correct discount record was stamped: 
        Discount__c expectedDiscount = [SELECT ID FROM Discount__c WHERE Event__c = :specialEvent.Id AND Automatically_Apply__c = TRUE AND Requires_Approval__c = false AND Type__c = 'Event Discount' AND Available_For__c = 'Rotman Alumni' LIMIT 1]; 
        pymt__Shopping_Cart_Item__c sciAfter = [SELECT ID, Discount__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :SCIToInsert.Id]; 

        //System.Assert(sciAfter.Discount__c == expectedDiscount.Id, 'Discount was not stamped correctly on the shopping cart item. Expected Discount: ' + expectedDiscount + ' Actual Discount: ' + sciAfter); 
    }

    @isTest  
    public static void testInsertSCI_RotmanIndividual(){
        evt__Special_Event__c specialEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Rotman Financial Seminar' LIMIT 1]; 
        evt__Event_Fee__c fee = [SELECT Id FROM evt__Event_Fee__c WHERE evt__Event__c = :specialEvent.Id And Type__c = 'Standard' LIMIT 1]; 

        pymt__Shopping_Cart_Item__c SCIToInsert = new pymt__Shopping_Cart_Item__c(Name = 'Test SCI', 
                                                                                    pymt__Quantity__c = 1, 
                                                                                    OwnerId = UserInfo.getUserId(),
                                                                                    Special_Event__c = specialEvent.id, 
                                                                                    Event_Fee__c = fee.Id, 
                                                                                    Type__c = 'Event Registration'
                                                                                    ); 
        
        Test.startTest(); 
            insert SCIToInsert; 
        Test.stopTest(); 

        //Assert that the correct discount record was stamped: 
        Discount__c expectedDiscount = [SELECT ID FROM Discount__c WHERE Event__c = :specialEvent.Id AND Automatically_Apply__c = TRUE AND Requires_Approval__c = false AND Type__c = 'Event Discount' AND Available_For__c = 'Individual' AND OwnerId = :UserInfo.getUserId() LIMIT 1]; 
        pymt__Shopping_Cart_Item__c sciAfter = [SELECT ID, Discount__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :SCIToInsert.Id]; 

        System.Assert(sciAfter.Discount__c == expectedDiscount.Id, 'Discount was not stamped correctly on the shopping cart item. Expected Discount: ' + expectedDiscount + ' Actual Discount: ' + sciAfter); 
    }
    /**
     * @description Insert Application fee shopping cart item: 
     * Assert that the fee waiver discount is stamped on the shopping cart item
     * */
    @isTest  
    public static void testInsertSCI_FeeWaiver(){
        pymt__Shopping_Cart_Item__c SCIToInsert = new pymt__Shopping_Cart_Item__c(Name = 'Test SCI', 
                                                                                    pymt__Quantity__c = 1, 
                                                                                    OwnerId = UserInfo.getUserId(),
                                                                                    Type__c = 'Application Fee', 
                                                                                    pymt__Unit_Price__c = 125
                                                                                    ); 
        
        Test.startTest(); 
            insert SCIToInsert; 
        Test.stopTest(); 

        //Assert that the correct discount record was stamped: 
        Discount__c expectedDiscount = [SELECT ID FROM Discount__c WHERE Automatically_Apply__c = TRUE AND Requires_Approval__c = false AND Type__c = 'Fee Waiver' AND Available_For__c = 'Individual' AND OwnerId = :UserInfo.getUserId() LIMIT 1]; 
        pymt__Shopping_Cart_Item__c sciAfter = [SELECT ID, Discount__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :SCIToInsert.Id]; 

        System.Assert(sciAfter.Discount__c == expectedDiscount.Id, 'Discount was not stamped correctly on the shopping cart item. Expected Discount: ' + expectedDiscount + ' Actual Discount: ' + sciAfter); 
    }
    /**
     * @description Insert bulk Shopping cart item (quantity > 1): 
     * Assert that the discount does not get stamped on the shopping cart item
     * */
    @isTest  
    public static void testInsertSCI_Bulk(){
        evt__Special_Event__c specialEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Rotman Financial Seminar' LIMIT 1]; 
        evt__Event_Fee__c fee = [SELECT Id FROM evt__Event_Fee__c WHERE evt__Event__c = :specialEvent.Id And Type__c = 'Standard' LIMIT 1]; 
        User testUser = [SELECT ID, ContactId FROM User WHERE Profile.Name = 'Applicant Community User' AND Rotman_Alumni__c = false AND IsActive = true AND ContactId != null LIMIT 1]; 

        pymt__Shopping_Cart_Item__c SCIToInsert = new pymt__Shopping_Cart_Item__c(Name = 'Test SCI', 
                                                                                    pymt__Quantity__c = 5, 
                                                                                    pymt__Contact__c = testUser.ContactId, 
                                                                                    OwnerId = testUser.Id,
                                                                                    Special_Event__c = specialEvent.id, 
                                                                                    Event_Fee__c = fee.Id, 
                                                                                    Type__c = 'Event Registration'
                                                                                    ); 
        
        Test.startTest(); 
            insert SCIToInsert; 
        Test.stopTest(); 

        //Assert that no discount record was stamped: 
        pymt__Shopping_Cart_Item__c sciAfter = [SELECT ID, Discount__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :SCIToInsert.Id]; 
        System.Assert(sciAfter.Discount__c == null, 'Discount was stamped on the shopping cart item. Expected Discount: null  Actual Discount: ' + sciAfter); 
    }

    /**
     * @description Update Shopping cart item: 
     * Assert that the correct discount is stamped on the shopping cart item
     * */
    @isTest  
    public static void testUpdateSCI_RotmanIndividual(){
        evt__Special_Event__c specialEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Rotman Financial Seminar' LIMIT 1]; 
        evt__Event_Fee__c fee = [SELECT Id FROM evt__Event_Fee__c WHERE evt__Event__c = :specialEvent.Id And Type__c = 'Standard' LIMIT 1]; 

        pymt__Shopping_Cart_Item__c sciToUpdate = [SELECT ID FROM pymt__Shopping_Cart_Item__c WHERE Special_Event__c = null AND OwnerId = :UserInfo.getUserId()]; 
        //stamp sci with event: 
        sciToUpdate.Special_Event__c= specialEvent.Id; 
        sciToUpdate.Event_Fee__c = fee.Id; 
        sciToUpdate.Type__c = 'Event Registration'; 

        Test.startTest(); 
            update sciToUpdate; 
        Test.stopTest(); 

        //Assert that the correct discount record was stamped: 
        Discount__c expectedDiscount = [SELECT ID FROM Discount__c WHERE Event__c = :specialEvent.Id AND Automatically_Apply__c = TRUE AND Requires_Approval__c = false AND Type__c = 'Event Discount' AND Available_For__c = 'Individual' AND OwnerId = :UserInfo.getUserId() LIMIT 1]; 
        pymt__Shopping_Cart_Item__c sciAfter = [SELECT ID, Discount__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :sciToUpdate.Id]; 

        System.Assert(sciAfter.Discount__c == expectedDiscount.Id, 'Discount was not stamped correctly on the shopping cart item. Expected Discount: ' + expectedDiscount + ' Actual Discount: ' + sciAfter); 
    }
}