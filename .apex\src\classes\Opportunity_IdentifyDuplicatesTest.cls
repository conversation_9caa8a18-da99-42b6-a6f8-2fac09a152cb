@isTest
private class Opportunity_IdentifyDuplicatesTest {
    
    @testSetup
    static void testSetup () {

        insert new hed__Trigger_Handler__c(
            hed__Active__c              = true,
            hed__Class__c               = 'Opportunity_IdentifyDuplicates_TDTM', 
            hed__Load_Order__c          = 1, 
            hed__Object__c              = 'Opportunity',
            hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate'
        );

        insert new List<Account>{
            new Account(
                Name = 'Program 1'
            ),
            new Account(
                Name = 'Program 2'
            )
        };

        insert new List<Contact>{
            new Contact(
                FirstName = 'Test Fn 1',
                LastName = ' Test FL 1'
            ),
            new Contact(
               FirstName = 'Test Fn 2',
               LastName =  'Test Ln 2'
            )
        };        

    }

    //Validates that two completely unique Opportunities can be inserted independently of oneanother
    @isTest 
    static void validateDupeOpptysInList () {

        List<Account> accts = [ SELECT Id, Name FROM Account WHERE Name LIKE '%Program%' ];
        List<Contact> cons  = [ SELECT Id, AccountId, Name FROM Contact ];
        Id oRecTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Degree_Program_Prospect').getRecordTypeId();
        
        Test.startTest();
        try{
            insert new List<Opportunity>{
                new Opportunity(
                    Name = 'Some Oppty',            
                    CloseDate           = Date.today(),
                    Program_Account__c  = accts[0].Id,
                    Contact__c          = cons[0].Id,
                    AccountID           = cons[0].AccountId,
                    StageName           = 'Inquiry',
                    RecordTypeId        = oRecTypeId        
                ),
                new Opportunity(
                    Name                = 'Some Oppty',            
                    CloseDate           = Date.today(),
                    Program_Account__c  = accts[0].Id,
                    Contact__c          = cons[0].Id,
                    AccountID           = cons[0].AccountId,
                    StageName           = 'Inquiry',
                    RecordTypeId        = oRecTypeId        
                )
            };

            system.assert( false, 'Duplicate Opportunity was inserted successfully');

        } catch ( Exception e ) {
            System.assert( e.getMessage().contains('duplicate'), 'Identical Opportunity not recognized as duplicate, found ' + e.getMessage());
        }
        Test.stopTest();

    }

    //Validates that duplicates in same list are caught
    @isTest 
    static void validateUniqueOpptys () {

        List<Account> accts = [ SELECT Id, Name FROM Account WHERE Name LIKE '%Program%' ];
        List<Contact> cons  = [ SELECT Id, AccountId, Name FROM Contact ];
        Id oRecTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Degree_Program_Prospect').getRecordTypeId();
        
        Test.startTest();
        insert new Opportunity(
            Name = 'Some Oppty',            
            CloseDate           = Date.today(),
            Program_Account__c  = accts[0].Id,
            Contact__c          = cons[0].Id,
            AccountID           = cons[0].AccountId,
            StageName           = 'Inquiry',
            RecordTypeId        = oRecTypeId        
        );
        insert new Opportunity(
            Name                = 'Some Oppty',            
            CloseDate           = Date.today(),
            Program_Account__c  = accts[1].Id,
            Contact__c          = cons[1].Id,
            AccountID           = cons[1].AccountId,
            StageName           = 'Inquiry',
            RecordTypeId       = oRecTypeId        
        );
        Test.stopTest();

    }

    //Validates that two completely idnetical Opportunities cannot be inserted
    @isTest 
    static void validateIdenticalOpptys () {

        List<Account> accts = [ SELECT Id, Name FROM Account WHERE Name LIKE '%Program%' ];
        List<Contact> cons  = [ SELECT Id, AccountId, Name FROM Contact ];
        Id oRecTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Degree_Program_Prospect').getRecordTypeId();

        Test.startTest();
            Opportunity originalOppty = new Opportunity(
                CloseDate           = Date.today(),
                Program_Account__c  = accts[0].Id,
                Contact__c          = cons[0].Id,
                AccountID           = cons[0].AccountId,
                StageName           = 'Inquiry',
                RecordTypeId       = oRecTypeId        
            );
            insert originalOppty;

            try {
                insert new Opportunity(
                    Name = 'Some Oppty',                
                    CloseDate           = Date.today(),
                    Program_Account__c  = accts[0].Id,
                    Contact__c          = cons[0].Id,
                    AccountID           = cons[0].AccountId,
                    StageName           = 'Inquiry',
                    RecordTypeId       = oRecTypeId            
                );
                system.assert( false, 'Duplicate Opportunity was inserted successfully');

            } catch ( Exception e ) {
                System.assert( e.getMessage().contains('duplicate') && e.getMessage().contains('id: ' + originalOppty.Id), 'Identical Opportunity not recognized as duplicate, found ' + e.getMessage());
            }
        Test.stopTest();

    }



    //Validates that two completely idnetical Opportunities cannot be inserted
    @isTest 
    static void validateNoProgramOppty () {

        List<Account> accts = [ SELECT Id, Name FROM Account WHERE Name LIKE '%Program%' ];
        List<Contact> cons  = [ SELECT Id, AccountId, FirstName, LastName, Name FROM Contact ];
        Id oRecTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Degree_Program_Prospect').getRecordTypeId();

        Test.startTest();
            Opportunity oppty = new Opportunity(
                Name                = 'Some Oppty',                
                CloseDate           = Date.today(),
                Contact__c          = cons[0].Id,
                AccountID           = cons[0].AccountId,
                StageName           = 'Inquiry',
                RecordTypeId       = oRecTypeId        
            );
            insert oppty;

            Opportunity insertedOppty = [ SELECT Id, Name FROM Opportunity WHERE Id = :oppty.Id ];
            system.assertEquals(cons[0].Name + '-Unspecified', insertedOppty.Name);

            oppty.Program_Account__c = accts[0].Id;
            update oppty;

            Opportunity updatedOppty = [ SELECT Id, Name FROM Opportunity WHERE Id = :oppty.Id ];
            system.assertEquals(cons[0].Name + '-' + accts[0].Name + '-', updatedOppty.Name);

        Test.stopTest();
    }


    //Validates that two completely idnetical Opportunities cannot be inserted
    @isTest 
    static void validateProgramOppty () {

        List<Account> accts = [ SELECT Id, Name FROM Account WHERE Name LIKE '%Program%' ];
        List<Contact> cons  = [ SELECT Id, AccountId, Name FROM Contact ];
        Id oRecTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Degree_Program_Prospect').getRecordTypeId();

        Test.startTest();
        Opportunity originalOppty = new Opportunity(
            Name                = 'Some Oppty',                
            CloseDate           = Date.today(),
            Contact__c          = cons[0].Id,
            AccountID           = cons[0].AccountId,
            Program_Account__c  = accts[0].Id,
            StageName           = 'Inquiry',
            RecordTypeId       = oRecTypeId        
        );
        insert originalOppty;

            try {
                insert new Opportunity(
                    Name                = 'Some Oppty',                
                    CloseDate           = Date.today(),
                    Contact__c          = cons[0].Id,
                    AccountID           = cons[0].AccountId,
                    StageName           = 'Inquiry',
                    RecordTypeId       = oRecTypeId            
                );
                system.assert( false, 'Duplicate Opportunity was inserted successfully');

            } catch ( Exception e ) {
                System.assert( e.getMessage().contains('duplicate') && e.getMessage().contains('id: ' + originalOppty.Id), 'Opportunity with program not recognized as duplicate, found ' + e.getMessage());
            }
        Test.stopTest();

    }


     //Validates Opportunities can be renames
     @isTest 
     static void validateRenameOppty () {

        List<Account> accts = [ SELECT Id, Name FROM Account WHERE Name LIKE '%Program%' ];
        List<Contact> cons  = [ SELECT Id, AccountId, Name FROM Contact ];
        Id oRecTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Degree_Program_Prospect').getRecordTypeId();

        Test.startTest();
            Opportunity originalOppty = new Opportunity(
                Name                = 'Some Oppty',                
                CloseDate           = Date.today(),
                Contact__c          = cons[0].Id,
                AccountID           = cons[0].AccountId,
                StageName           = 'Inquiry',
                RecordTypeId       = oRecTypeId        
            );
            insert originalOppty;

            try {
                insert new Opportunity(
                    Name                = 'Some Oppty',                
                    CloseDate           = Date.today(),
                    Program_Account__c  = accts[0].Id,
                    Contact__c          = cons[0].Id,
                    AccountID           = cons[0].AccountId,
                    StageName           = 'Inquiry',
                    RecordTypeId       = oRecTypeId            
                );
                system.assert( false, 'Duplicate Opportunity was inserted successfully');

            } catch ( Exception e ) {
                System.assert( e.getMessage().contains('duplicate') && e.getMessage().contains('id: ' + originalOppty.Id), 'Opportunity without program not recognized as duplicate, found ' + e.getMessage());
            }
        Test.stopTest();
    }

}