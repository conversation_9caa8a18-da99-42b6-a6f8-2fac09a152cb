/**
 * @description Calculates remaining program balance on discount and updates current usage
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-04
 * @modified 2020-10-19
 */
global class DiscountCalculator_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for the object 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        Set<Id> discountIds = new Set<Id>(); 
        List<pymt__Shopping_Cart_Item__c> itemsToCalculate = new List<pymt__Shopping_Cart_Item__c>(); 

        /*AFTER INSERT CONTEXT: if program balance discount is linked, calculate the program_balance_amount__c of the discount record */
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
            //filter out items with discounts & negative total: 
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){

                if(sci.Discount__c != null && sci.pymt__Total__c != null && sci.pymt__Total__c < 0 && sci.pymt__Payment_Completed__c){
                    itemsToCalculate.add(sci); 
                    discountIds.add(sci.Discount__c); 
                }
            }
            //Query for program balance discounts: 
            Map<Id, Discount__c> discountMap = new Map<Id, Discount__c>([SELECT Id, Program_Balance_Amount__c, Current_Usage__c,
                                                                        (SELECT Id FROM Shopping_Cart_Items__r WHERE pymt__Payment__c != null AND pymt__Payment_Completed__c = true ) 
                                                                        FROM Discount__c WHERE Id IN :discountIds 
                                                                        AND Type__c = 'Program Balance'
                                                                        AND Active__c = true ]);

            if(discountMap.size() > 0){
                //update program_balance_amount__c value: 
                for(pymt__Shopping_Cart_Item__c item :itemsToCalculate){
                    if(discountMap.containsKey(item.discount__c)){
                        updateProgramBalanceAmount(discountMap.get(item.discount__c), item.pymt__Total__c); 
                    }
                }
                //update discounts: 
                dmlWrapper.objectsToUpdate.addAll(discountMap.values()); 

            }/*AFTER UPDATE CONTEXT: if program balance discount is linked, calculate the program_balance_amount__c of the discount record */
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList);
            List<pymt__Shopping_Cart_Item__c> updatedItems = new List<pymt__Shopping_Cart_Item__c>();

            //filter out items with discounts & negative total: 
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                // if((sci.Discount__c != null && oldMap.get(sci.Id).Discount__c == null && sci.pymt__Payment_Completed__c) 
                //     || (sci.pymt__Payment_Completed__c && !oldMap.get(sci.Id).pymt__Payment_Completed__c && sci.Discount__c != null)  
                //     && sci.pymt__Total__c < 0 ){
                //     discountIds.add(sci.Discount__c); 
                //     itemsToCalculate.add(sci); 
                // }
                if((sci.Discount__c != null && oldMap.get(sci.Id).Discount__c == null && sci.pymt__Payment_Completed__c) 
                    || (sci.pymt__Payment_Completed__c && !oldMap.get(sci.Id).pymt__Payment_Completed__c && sci.Discount__c != null)  
                    && sci.Program_Balance_Amount__c < 0 ){
                    discountIds.add(sci.Discount__c); 
                    itemsToCalculate.add(sci); 
                }
/*                if ((sci.pymt__Payment_Completed__c == true && oldMap.get(sci.Id).pymt__Payment_Completed__c == false) && sci.pymt__Contact__c != null) {
                    ID jobID = System.enqueueJob(new SendConfirmationEmail(sci.Id, sci.pymt__Contact__c));
                    System.debug('==jobID=='+jobID);
                }*/
                if ((sci.pymt__Payment_Completed__c == true && oldMap.get(sci.Id).pymt__Payment_Completed__c == false) && sci.pymt__Contact__c != null) {
                    String currentPayment = sci.pymt__Payment__c;
                    String currentPaymentMethod = sci.Payment_Method__c;
                    System.debug('==currentPayment=='+currentPayment);
                    System.debug('==currentPaymentMethod=='+currentPaymentMethod);
                    if (sci.pymt__Payment__c != null) {
                        if (sci.Payment_Method__c == 'Credit Card') {
                            pymt__PaymentX__c payment = [SELECT Id, pymt__Status__c, pymt__Transaction_Id__c, Payment_Type__c FROM pymt__PaymentX__c WHERE Id = :sci.pymt__Payment__c];
                            if (payment != null) {
                                String pymtType = payment.pymt__Status__c;
                                System.debug('==pymtType==' + pymtType);
                                String pymtTransactionId = payment.pymt__Transaction_Id__c;
                                System.debug('==pymtTransactionId==' + pymtTransactionId);
                                String type = payment.Payment_Type__c;
                                Boolean status1 = sci.pymt__Payment_Completed__c;
                                Boolean status2 = oldMap.get(sci.Id).pymt__Payment_Completed__c;
                                String itemName = sci.Name;
                                String contactName = sci.pymt__Contact__r.Name;
                                String eventId = sci.Special_Event__c;
                                if (payment.pymt__Status__c == 'Completed' && payment.pymt__Transaction_Id__c != null) {
                                    if (!Test.isRunningTest()) {
                                        ID jobID = System.enqueueJob(new SendConfirmationEmail(sci.Id, sci.pymt__Contact__c));
                                        System.debug('==jobID==' + jobID);
                                    }
                                }
                            }
                        }else{
                            if (!Test.isRunningTest()) {
                                ID jobID = System.enqueueJob(new SendConfirmationEmail(sci.Id, sci.pymt__Contact__c));
                                System.debug('==jobID==' + jobID);
                            }
                        }
                    }else{
                        String msci2 = sci.Id;
                        String myContactId2 = sci.pymt__Contact__c;
                        String myPayment = sci.pymt__Payment__c;
                        String myPaymentMethod = sci.Payment_Method__c;
                        String msci3 = oldMap.get(sci.Id).Id;
                        String myContactId3 = oldMap.get(sci.Id).pymt__Contact__c;
                        String myPayment3 = oldMap.get(sci.Id).pymt__Payment__c;
                        String myPaymentMethod3 = oldMap.get(sci.Id).Payment_Method__c;
                        Boolean status3 = sci.pymt__Payment_Completed__c;
                        Boolean status4 = oldMap.get(sci.Id).pymt__Payment_Completed__c;
                        if (!Test.isRunningTest()) {
                            ID jobID = System.enqueueJob(new SendConfirmationEmail(sci.Id, sci.pymt__Contact__c));
                            System.debug('==jobID==' + jobID);
                        }
                    }
                }
            }
             //Query for program balance discount: 
            Map<Id, Discount__c> discountMap = new Map<Id, Discount__c>([SELECT Id, Program_Balance_Amount__c, Current_Usage__c,
                                                                        (SELECT Id FROM Shopping_Cart_Items__r WHERE pymt__Payment__c != null AND pymt__Payment_Completed__c = true ) 
                                                                        FROM Discount__c WHERE Id IN :discountIds 
                                                                        AND Type__c = 'Program Balance'
                                                                        AND Active__c = true]); 

            if(discountMap.size() > 0){
                //update program balance_amount__c  value: 
/*                for(pymt__Shopping_Cart_Item__c item :itemsToCalculate){
                    if(discountMap.containsKey(item.discount__c)){
                        // updateProgramBalanceAmount(discountMap.get(item.discount__c), item.pymt__Total__c); 
                        updateProgramBalanceAmount(discountMap.get(item.discount__c), item.Program_Balance_Amount__c); 
                    }
                }*/
                //update discounts: 
                //dmlWrapper.objectsToUpdate.addAll(discountMap.values());
                System.debug('==discountMap=='+discountMap);

            }
            if(updatedItems.size() > 0){
                dmlWrapper.objectsToUpdate.addAll(updatedItems);
            }

        }

        return dmlWrapper; 
    }

    /* @description update program_balance_amount__c and current usage on discount__c record
     * @param discount discount record
     * @param decimal total amount from shopping cart item 
     */
    private void updateProgramBalanceAmount(Discount__c discount, Decimal totalAmount){
        if(discount.program_balance_amount__c != null){
            discount.program_balance_amount__c += totalAmount; 
            discount.current_usage__c = discount.Shopping_Cart_Items__r.size(); 
        }
    }
}