@isTest
public with sharing class RegisterforEventControllerTest {
    @TestSetup
    static void makeData(){
        Account acc = (Account)TestFactory.createSObject(new Account(Name = 'Dept Account'));
        insert acc;
        Contact con = (Contact)TestFactory.createSObject(new Contact(lastName = 'Last Name', firstName = 'First Name',hed__WorkEmail__c	= '<EMAIL>'));
        insert con;

        insert new User(
                alias = 'test2',
                communityNickname = 'test123',
                contactId = con.Id, 
                email = '<EMAIL>', 
                emailencodingkey = 'UTF-8', 
                firstName = 'testCommunity2',
                lastName = 'User', 
                userName = '<EMAIL>', 
                profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Student Community User' LIMIT 1].Id, 
                timeZoneSidKey = 'America/Los_Angeles', 
                LocaleSidKey = 'en_US', 
                LanguageLocaleKey = 'en_US'
            );
        String cookieName = String.valueOf(dateTime.now());
        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
        cart.pymt__Cart_UID__c = cookieName;
        insert cart;

        evt__Special_Event__c event = new evt__Special_Event__c();
        event.Name = 'Special event';
        
        evt__Special_Event__c event2 = new evt__Special_Event__c();
        event2.Name = 'Special event 2';
        
        List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
        listOfEventToInsert.add(event);
        listOfEventToInsert.add(event2);

        insert listOfEventToInsert;

        evt__Event_Fee__c fee = new evt__Event_Fee__c();
        fee.Name = 'special event fee';
        fee.evt__Event__c = event.Id;
        fee.evt__Amount__c = 5.00;
        fee.evt__Active__c = true;
        fee.evt__Category__c = 'Attendee';
        fee.evt__Order__c = 1;

        evt__Event_Fee__c fee2 = new evt__Event_Fee__c();
        fee2.Name = 'special event fee2';
        fee2.evt__Event__c = event2.Id;
        fee2.evt__Amount__c = 5.00;
        fee2.evt__Active__c = true;
        fee2.evt__Category__c = 'Attendee';
        fee2.evt__Order__c = 1;
        
        insert new List<evt__Event_Fee__c>{fee, fee2};
            
        Taxing_Authority__c txauth = new Taxing_Authority__c(Name = 'Test tax', Tax_Rate__c	= 15.000000, Tax_Label__c	='HST',State_Province__c='NL',Do_Not_Auto_Apply__c=false,Country__c='CA');
        insert txauth;

        Discount__c programBalance = new Discount__c(Name = 'Program Balance', Available_for__c = 'All', Type__c = 'Event Discount',Percent_Discount__c=10);
        insert programBalance; 
        
        Invoice__c inv = new Invoice__c();
        inv.Invoice_Status__c = 'Open-Personal';
        inv.Type__c = 'Events';
        inv.contact__c = con.Id;
        inv.Tax_Amount__c = 15;
        inv.Gross_Amount__c = 100;
        inv.Total_Amount_SCI__c = 115;
        insert inv;
        
        pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
        pymnt.Name = 'Event Checkout';
        pymnt.pymt__Transaction_Type__c = 'Payment';
        pymnt.pymt__Payment_Type__c	 = 'Credit Card';
        pymnt.pymt__Status__c = 'Online Checkout';
        pymnt.pymt__Contact__c = con.Id;
        pymnt.pymt__Amount__c = 90;
        pymnt.Gross_Amount__c = 100;
        pymnt.pymt__Tax__c = 10;
        pymnt.pymt__Discount__c = 20;
        pymnt.Type__c = 'Event Registration';
        pymnt.pymt__Payment_Processor__c = 'Global Pay';
        pymnt.invoice__c = inv.Id;
        insert pymnt;

        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event.Name;
        cartItem.Special_Event__c = event.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = con.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        cartItem.pymt__Shopping_Cart__c = cart.Id;
        cartItem.Discount__c = programBalance.Id;
        cartItem.Invoice__c = inv.Id;
        cartItem.pymt__Payment__c = pymnt.Id;

        pymt__Shopping_Cart_Item__c cartItem2 = new pymt__Shopping_Cart_Item__c();
        cartItem2.Name = event2.Name;
        cartItem2.Special_Event__c = event2.Id;
        cartItem2.pymt__Quantity__c =1;
        cartItem2.pymt__Contact__c = con.id;
        cartItem2.pymt__Unit_Price__c = 10.00;
        cartItem2.pymt__Shopping_Cart__c = cart.Id;
        cartItem2.Discount__c = programBalance.Id;
         insert new List<pymt__Shopping_Cart_Item__c>{cartItem, cartItem2};
             
        evt__Attendee__c gstatndy = new evt__Attendee__c();
        gstatndy.evt__Reg_Email__c = '<EMAIL>';
        gstatndy.evt__Reg_Postal_Code__c = '850103';
        gstatndy.evt__Reg_Country__c = 'USA';
        gstatndy.Relationship__c = 'Brother';
        gstatndy.evt__Reg_Title__c = 'Developer';
        gstatndy.evt__Reg_Company__c = 'Huron';
        gstatndy.evt__Reg_State__c = 'CA';
        gstatndy.evt__Reg_Last_Name__c = 'User1';
        gstatndy.evt__Reg_Street__c = '23rd cross';
        gstatndy.evt__Reg_First_Name__c = 'Test';
        gstatndy.Shopping_Cart_Item__c = cartItem.Id;
        gstatndy.evt__Category__c         = 'Attendee';
        gstatndy.evt__Event__c = cartItem.Special_Event__c;
        gstatndy.evt__Event_Fee__c = cartItem.Event_Fee__c;
        insert gstatndy;
        
        Subscription__c sc = new Subscription__c();
        sc.Name = 'Upcoming events';
        insert sc;
        
        evt__Session__c testSession1 = new evt__Session__c();
        testSession1.Name = 'Test Session1';
        testSession1.Start_Date__c = Date.newInstance(2021, 12, 9); 
        testSession1.Start_Time__c= Time.newInstance(9,30,0,0);
        testSession1.End_Date__c = Date.newInstance(2021, 12, 9);
        testSession1.End_Time__c= Time.newInstance(21,30,0,0);
        testSession1.evt__Event__c = event.Id;

        evt__Session__c testSession2 = new evt__Session__c();
        testSession2.Name = 'Test Session2';
        testSession2.Start_Date__c = Date.newInstance(2021, 12, 9); 
        testSession2.Start_Time__c= Time.newInstance(9,30,0,0);
        testSession2.End_Date__c = Date.newInstance(2021, 12, 9);
        testSession2.End_Time__c= Time.newInstance(21,30,0,0);
        testSession2.evt__Event__c = event2.Id;
        
        insert new List<evt__Session__c>{testSession1,testSession2};
            
        

        evt__Attendee__c atndynew = new evt__Attendee__c();
        atndynew.evt__Reg_First_Name__c = con.FirstName;
        atndynew.evt__Reg_Last_Name__c = con.LastName;
        atndynew.evt__Reg_Phone__c = con.MobilePhone;
        atndynew.evt__Reg_Email__c = con.Email;
        atndynew.evt__Reg_Street__c = con.MailingStreet;
        atndynew.evt__Reg_City__c = con.MailingCity;
        atndynew.evt__Reg_State__c = con.MailingState;
        atndynew.evt__Reg_Postal_Code__c = con.MailingPostalCode;
        atndynew.evt__Reg_Country__c = con.MailingCountry;
        atndynew.evt__Contact__c = con.Id !=null ? con.Id : null;
        atndynew.Shopping_Cart_Item__c = cartItem.Id;
        atndynew.evt__Event__c = cartItem.Special_Event__c;
        atndynew.evt__Category__c         = 'Attendee';
        atndynew.evt__Event_Fee__c = cartItem.Event_Fee__c;
        atndynew.evt__Payment__c = pymnt.Id;
        insert atndynew;
    }

    @isTest
    static void testCreateAttendeeRecord() {
        pymt__Shopping_Cart__c cart = [SELECT Id FROM pymt__Shopping_Cart__c];
        pymt__Shopping_Cart_Item__c cartItem =  [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c, 
        Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c,Discount_Amount__c,
        Special_Event__r.Supplemental_Event_Items__c,Invoice__c,pymt__Payment__c, Special_Event__r.evt__Show_Available_Seats__c, Special_Event__r.evt__Max_Additional_Attendees__c
        FROM pymt__Shopping_Cart_Item__c WHERE Name = 'Special event'];
        
        Contact con = [SELECT Id, FirstName, LastName, MobilePhone, Email, MailingStreet, MailingCity,
        MailingState, MailingPostalCode, MailingCountry, Dietary_Restrictions__c, Accessibility_Requirements__c, hed__WorkEmail__c FROM Contact];
        con.hed__WorkEmail__c = '<EMAIL>';

        evt__Special_Event__c evt = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
        evt__Attendee__c attendee = [SELECT Id, evt__Is_Primary__c FROM evt__Attendee__c WHERE evt__Contact__c =: con.Id];
        attendee.evt__Is_Primary__c = true;
        update attendee;
        
        List<evt__Attendee__c> guestlist = [select evt__Reg_First_Name__c, evt__Reg_Email__c, evt__Reg_Postal_Code__c, evt__Reg_Country__c,
                                 Relationship__c, evt__Reg_Title__c, evt__Reg_Company__c, evt__Reg_State__c, evt__Reg_Last_Name__c, 
                                 evt__Reg_Street__c, evt__Reg_City__c from evt__Attendee__c where evt__Reg_Email__c = '<EMAIL>'];
        
        pymt__PaymentX__c pymnt = [select id,pymt__Amount__c,Gross_Amount__c, pymt__Tax__c,pymt__Discount__c from pymt__PaymentX__c where name = 'Event Checkout'];
        pymnt.pymt__Discount__c = pymnt.pymt__Discount__c;
        
        Invoice__c inv = [select id,Tax_Amount__c,Gross_Amount__c, Total_Amount_SCI__c from Invoice__c where Invoice_Status__c = 'Open-Personal'];
        inv.Total_Amount_SCI__c = inv.Tax_Amount__c+inv.Gross_Amount__c;

        EventCartWrapper wrapper = new EventCartWrapper();
        wrapper.shoppingcartItem = cartItem;
        wrapper.listOfGuests = guestlist;
        evt__Session__c eventSession = new evt__Session__c();
        eventSession.Name = 'Session 1';
        eventSession.evt__Session_Fee__c = 100;
        eventSession.evt__Short_Description__c = 'Test Session';
        eventSession.evt__Event__c = evt.Id;
        wrapper.showAddGuests = true;

        Contact registerData = new Contact();
        registerData.MobilePhone = '************';
        registerData.MailingStreet =  '1 Yonge Street';
        registerData.MailingCity =  'Toronto';
        registerData.MailingState =  'Ontario';
        registerData.MailingCountry =  'Canada';
        registerData.MailingPostalCode =  'M1M1M1';

        Test.startTest();
            try {
                pymt__PaymentX__c payment = RegisterforEventController.createAttendeeRecord(cart.Id, con,
                        new List<evt__Session__c>{eventSession}, 'Developer', 'Huron', new List<EventCartWrapper>{wrapper}, registerData);
            } catch (exception e){
                System.debug(e.getMessage());
            }
        Test.stopTest();
        //System.assertNotEquals(payment, null);
    }

    @isTest 
    static void testCreateAttendeeRecordWhenAttendeeIsPrimary() {
        pymt__Shopping_Cart__c cart = [SELECT Id FROM pymt__Shopping_Cart__c];
        pymt__Shopping_Cart_Item__c cartItem =  [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c, 
        Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c,Discount_Amount__c,
        Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c, Special_Event__r.evt__Max_Additional_Attendees__c
        FROM pymt__Shopping_Cart_Item__c WHERE Name = 'Special event'];
        
        Contact con = [SELECT Id, FirstName, LastName, MobilePhone, Email, MailingStreet, MailingCity,
        MailingState, MailingPostalCode, MailingCountry, Dietary_Restrictions__c, Accessibility_Requirements__c, hed__WorkEmail__c FROM Contact];
        con.hed__WorkEmail__c = '<EMAIL>';

        evt__Special_Event__c evt = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
        evt__Attendee__c attendee = [SELECT Id, evt__Is_Primary__c FROM evt__Attendee__c WHERE evt__Contact__c =: con.Id];
        attendee.evt__Is_Primary__c = true;
        update attendee;
        
        List<evt__Attendee__c> guestlist = [select evt__Reg_First_Name__c, evt__Reg_Email__c, evt__Reg_Postal_Code__c, evt__Reg_Country__c,
                                 Relationship__c, evt__Reg_Title__c, evt__Reg_Company__c, evt__Reg_State__c, evt__Reg_Last_Name__c, 
                                 evt__Reg_Street__c, evt__Reg_City__c from evt__Attendee__c where evt__Reg_Email__c = '<EMAIL>'];

        EventCartWrapper wrapper = new EventCartWrapper();
        wrapper.shoppingcartItem = cartItem;
        wrapper.listOfGuests = guestlist;
        evt__Session__c eventSession = new evt__Session__c();
        eventSession.Name = 'Session 1';
        eventSession.evt__Session_Fee__c = 100;
        eventSession.evt__Short_Description__c = 'Test Session';
        eventSession.evt__Event__c = evt.Id;
        wrapper.showAddGuests = true;

        Contact registerData = new Contact();
        registerData.MobilePhone = '************';
        registerData.MailingStreet =  '1 Yonge Street';
        registerData.MailingCity =  'Toronto';
        registerData.MailingState =  'Ontario';
        registerData.MailingCountry =  'Canada';
        registerData.MailingPostalCode =  'M1M1M1';

        Test.startTest();
            try {
                pymt__PaymentX__c payment = RegisterforEventController.createAttendeeRecord(cart.Id, con,
                        new List<evt__Session__c>{eventSession}, 'Developer', 'Huron', new List<EventCartWrapper>{wrapper}, registerData);
            } catch (exception e){
                System.debug(e.getMessage());
            }
        Test.stopTest();
        //System.assertNotEquals(payment, null);
    }

    @isTest
    static void testCreateAttendeeRecordWhenNoAttendeeIsPresent() {
        pymt__Shopping_Cart__c cart = [SELECT Id FROM pymt__Shopping_Cart__c];
        List<pymt__Shopping_Cart_Item__c> listOfCartItem =  [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c,
        Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c,Discount_Amount__c,
        Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c, Special_Event__r.evt__Max_Additional_Attendees__c
        FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cart.Id];

        Contact con = [SELECT Id, FirstName, LastName, MobilePhone, Email, MailingStreet, MailingCity,
        MailingState, MailingPostalCode, MailingCountry, Dietary_Restrictions__c, Accessibility_Requirements__c, hed__WorkEmail__c FROM Contact];
        con.hed__WorkEmail__c = '<EMAIL>';

        evt__Special_Event__c evt = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
        evt__Attendee__c attendee = [SELECT Id, evt__Is_Primary__c, evt__Contact__c FROM evt__Attendee__c WHERE evt__Contact__c =: con.Id];
        attendee.evt__Contact__c = null;
        update attendee;

        List<evt__Attendee__c> guestlist = [select evt__Reg_First_Name__c, evt__Reg_Email__c, evt__Reg_Postal_Code__c, evt__Reg_Country__c,
                                 Relationship__c, evt__Reg_Title__c, evt__Reg_Company__c, evt__Reg_State__c, evt__Reg_Last_Name__c,
                                 evt__Reg_Street__c, evt__Reg_City__c from evt__Attendee__c where evt__Reg_Email__c = '<EMAIL>'];

        EventCartWrapper wrapper = new EventCartWrapper();
        wrapper.shoppingcartItem = listOfCartItem[0];
        wrapper.listOfGuests = guestlist;
        evt__Session__c eventSession = new evt__Session__c();
        eventSession.Name = 'Session 1';
        eventSession.evt__Session_Fee__c = 100;
        eventSession.evt__Short_Description__c = 'Test Session';
        eventSession.evt__Event__c = evt.Id;
        wrapper.showAddGuests = true;
        wrapper.showDonationCheckBox = false;
        wrapper.donationAmount = 0;

        EventCartWrapper wrapper2 = new EventCartWrapper();
        wrapper2.shoppingcartItem = listOfCartItem[1];
        wrapper2.showDonationCheckBox = false;
        wrapper2.donationAmount = 0;

        Contact registerData = new Contact();
        registerData.MobilePhone = '************';
        registerData.MailingStreet =  '1 Yonge Street';
        registerData.MailingCity =  'Toronto';
        registerData.MailingState =  'Ontario';
        registerData.MailingCountry =  'Canada';
        registerData.MailingPostalCode =  'M1M1M1';

        Test.startTest();
            try {
                pymt__PaymentX__c payment = RegisterforEventController.createAttendeeRecord(cart.Id, con,
                        new List<evt__Session__c>{eventSession}, 'Developer', 'EvanZhang', new List<EventCartWrapper>{wrapper, wrapper2}, registerData);

            } catch (exception e){
                System.debug(e.getMessage());
            }
        Test.stopTest();
        //System.assertNotEquals(payment, null);
    }

    @isTest
    static void testCreateAttendeeRecordWhenNewAttendee() {
        pymt__Shopping_Cart__c cart = [SELECT Id FROM pymt__Shopping_Cart__c];
        List<pymt__Shopping_Cart_Item__c> listOfCartItem =  [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c,
                Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c,Discount_Amount__c,
                Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c, Special_Event__r.evt__Max_Additional_Attendees__c
        FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cart.Id];

        Contact con = [SELECT Id, FirstName, LastName, MobilePhone, Email, MailingStreet, MailingCity,
                MailingState, MailingPostalCode, MailingCountry, Dietary_Restrictions__c, Accessibility_Requirements__c, hed__WorkEmail__c FROM Contact];

        Contact newcon = new Contact();
        newcon.FirstName = 'testFirstName';
        newcon.LastName = 'testLastName';
        newcon.Email = '<EMAIL>';
        newcon.MobilePhone = '4162871900';
        newcon.MailingStreet = 'test street';
        newcon.MailingCity = 'Toronto';
        newcon.MailingState = 'ON';
        newcon.MailingCountry = 'Canada';
        newcon.MailingPostalCode = 'M4E 1P7';

        evt__Special_Event__c evt = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
        evt__Attendee__c attendee = [SELECT Id, evt__Is_Primary__c, evt__Contact__c FROM evt__Attendee__c WHERE evt__Contact__c =: con.Id];
        attendee.evt__Contact__c = null;
        update attendee;

        List<evt__Attendee__c> guestlist = [select evt__Reg_First_Name__c, evt__Reg_Email__c, evt__Reg_Postal_Code__c, evt__Reg_Country__c,
                Relationship__c, evt__Reg_Title__c, evt__Reg_Company__c, evt__Reg_State__c, evt__Reg_Last_Name__c,
                evt__Reg_Street__c, evt__Reg_City__c from evt__Attendee__c where evt__Reg_Email__c = '<EMAIL>'];

        EventCartWrapper wrapper = new EventCartWrapper();
        wrapper.shoppingcartItem = listOfCartItem[0];
        wrapper.listOfGuests = guestlist;
        evt__Session__c eventSession = new evt__Session__c();
        eventSession.Name = 'Session 1';
        eventSession.evt__Session_Fee__c = 100;
        eventSession.evt__Short_Description__c = 'Test Session';
        eventSession.evt__Event__c = evt.Id;
        wrapper.showAddGuests = true;
        wrapper.showDonationCheckBox = false;
        wrapper.donationAmount = 0;

        EventCartWrapper wrapper2 = new EventCartWrapper();
        wrapper2.shoppingcartItem = listOfCartItem[1];
        wrapper2.showDonationCheckBox = false;
        wrapper2.donationAmount = 0;

        Contact registerData = new Contact();
        registerData.MobilePhone = '************';
        registerData.MailingStreet =  '1 Yonge Street';
        registerData.MailingCity =  'Toronto';
        registerData.MailingState =  'ON';
        registerData.MailingCountry =  'Canada';
        registerData.MailingPostalCode = 'M4E 1P7';

        Test.startTest();
            try {
                pymt__PaymentX__c payment = RegisterforEventController.createAttendeeRecord(cart.Id, newcon,
                        new List<evt__Session__c>{eventSession}, 'Developer', 'EvanZhang', new List<EventCartWrapper>{wrapper, wrapper2}, registerData);

            } catch (exception e){
                System.debug(e.getMessage());
            }
        Test.stopTest();
        //System.assertNotEquals(payment, null);
    }

    @isTest
    static void testCreateAttendeeRecordWhenNewAttendeeError() {
        pymt__Shopping_Cart__c cart = [SELECT Id FROM pymt__Shopping_Cart__c];
        List<pymt__Shopping_Cart_Item__c> listOfCartItem =  [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c,
                Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c,Discount_Amount__c,
                Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c, Special_Event__r.evt__Max_Additional_Attendees__c, pymt__Shopping_Cart__c
        FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cart.Id];

        Contact con = [SELECT Id, FirstName, LastName, MobilePhone, Email, MailingStreet, MailingCity,
                MailingState, MailingPostalCode, MailingCountry, Dietary_Restrictions__c, Accessibility_Requirements__c, hed__WorkEmail__c FROM Contact];

        Contact newcon = new Contact();
        newcon.FirstName = 'testFirstName';
        newcon.LastName = 'testLastName';
        newcon.Email = '<EMAIL>';
        newcon.MobilePhone = '4162871900';
        newcon.MailingStreet = 'test street';
        newcon.MailingCity = 'Toronto';
        newcon.MailingState = 'ON';
        newcon.MailingCountry = 'Canada';
        newcon.MailingPostalCode = 'M4E 1P7';

        evt__Special_Event__c evt = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
        evt__Attendee__c attendee = [SELECT Id, evt__Is_Primary__c, evt__Contact__c FROM evt__Attendee__c WHERE evt__Contact__c =: con.Id];
        attendee.evt__Contact__c = null;
        update attendee;

        List<evt__Attendee__c> guestlist = [select evt__Reg_First_Name__c, evt__Reg_Email__c, evt__Reg_Postal_Code__c, evt__Reg_Country__c,
                Relationship__c, evt__Reg_Title__c, evt__Reg_Company__c, evt__Reg_State__c, evt__Reg_Last_Name__c,
                evt__Reg_Street__c, evt__Reg_City__c from evt__Attendee__c where evt__Reg_Email__c = '<EMAIL>'];

        EventCartWrapper wrapper = new EventCartWrapper();
        wrapper.shoppingcartItem = listOfCartItem[0];
        wrapper.listOfGuests = guestlist;
        evt__Session__c eventSession = new evt__Session__c();
        eventSession.Name = 'Session 1';
        eventSession.evt__Session_Fee__c = 100;
        eventSession.evt__Short_Description__c = 'Test Session';
        eventSession.evt__Event__c = evt.Id;
        wrapper.showAddGuests = true;
        wrapper.showDonationCheckBox = true;
        wrapper.donationAmount = 1;

        EventCartWrapper wrapper2 = new EventCartWrapper();
        wrapper2.shoppingcartItem = listOfCartItem[1];
        wrapper2.showDonationCheckBox = false;
        wrapper2.donationAmount = 0;

        Contact registerData = new Contact();
        registerData.MobilePhone = '************';
        registerData.MailingStreet =  '1 Yonge Street';
        registerData.MailingCity =  'Toronto';
        registerData.MailingState =  'ON';
        registerData.MailingCountry =  'Canada';
        registerData.MailingPostalCode = 'M4E 1P7';

        Test.startTest();
            try {
                pymt__PaymentX__c payment = RegisterforEventController.createAttendeeRecord(cart.Id, newcon,
                        new List<evt__Session__c>{eventSession}, 'Developer', 'EvanZhang', new List<EventCartWrapper>{wrapper, wrapper2}, registerData);

            } catch (exception e){
                System.debug(e.getMessage());
            }
        Test.stopTest();
        //System.assertNotEquals(payment, null);
    }
}