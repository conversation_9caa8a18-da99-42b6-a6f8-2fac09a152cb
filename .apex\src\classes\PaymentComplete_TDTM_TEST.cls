@isTest
public class PaymentComplete_TDTM_TEST {

    @testSetup
    static void testSetup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for PaymentComplete_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('PaymentComplete_TDTM', 'pymt__PaymentX__c', 'AfterInsert;AfterUpdate', 1.00));
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        //Create test contact: 
        Contact c = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student')); 
        insert c;
        //Create invoice
        Invoice__c newInv = new Invoice__c(
            Invoice_Status__c = 'Open-Personal',
            Contact__c = c.Id
        );
        insert newInv;
        //Create SCIs 
        List<pymt__Shopping_Cart_Item__c> sciToInsert = new List<pymt__Shopping_Cart_Item__c>(); 
        sciToInsert.add(
            new pymt__Shopping_Cart_Item__c(
                pymt__Payment_Completed__c=False, 
                Type__c = 'EP Program Balance', 
                Name = 'Test1', 
                pymt__Unit_Price__c = 12,
                pymt__Quantity__c=1,
                Invoice__c = newInv.Id
            ));
        insert sciToInsert;
        //Insert test payment record
        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c = c.Id,
            Name = 'TEST Update Payment',
            pymt__Status__c = 'Online Checkout',
            Invoice__c = newInv.Id
        );
        insert pymt;
    }
    
    @isTest
    public static void testPayment(){
        //Query payment to update
        pymt__PaymentX__c pymtToUpdate = [SELECT Id FROM pymt__PaymentX__c LIMIT 1];
        //Create new payment
        Contact c = [SELECT Id FROM Contact LIMIT 1];
        //Create invoice
        Invoice__c newInv = new Invoice__c(
            Invoice_Status__c = 'Open-Personal',
            Contact__c = c.Id
        );
        insert newInv;
        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c = c.Id,
            Name = 'TEST Create Payment',
            pymt__Status__c = 'Online Checkout',
            Invoice__c = newInv.Id
        );
        test.startTest();
        	insert pymt;
        	update pymtToUpdate;
        test.stopTest();
    }
}