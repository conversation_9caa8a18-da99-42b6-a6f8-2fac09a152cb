/** 
* @description    At the time of creation of course connection, Connect requirement fulfillment for every consequential requirement.
* <AUTHOR> 
* @version        1.0 
*/
global class CourseCnctnCreateReqmntfulfillment_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Interaction records from trigger new 
     * @param oldList the list of Interaction records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc...)
     * @param objResult the describe for Interactions 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
    	hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        Map<Id,hed__Course_Enrollment__c> oldMap = new Map<Id,hed__Course_Enrollment__c>();
        List<Plan_Requirement_Fulfillment__c> reqmntFullflmntLstToInsert = new List<Plan_Requirement_Fulfillment__c>();
        List<hed__Course_Enrollment__c> courseEnrollmentLst = (List<hed__Course_Enrollment__c>) newList;
        
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
        	reqmntFullflmntLstToInsert = PlanRqmntToCourseConnect.courseConnectionToConnectPlanRequirement(courseEnrollmentLst);
        }
        //AFTER UPDATE CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            courseEnrollmentLst = new List<hed__Course_Enrollment__c>();
            for(hed__Course_Enrollment__c courselmnt : (List<hed__Course_Enrollment__c>) oldlist){
                oldMap.put(courselmnt.Id,courselmnt);
            }
            for(hed__Course_Enrollment__c courselmnt : (List<hed__Course_Enrollment__c>) newList){
                if(courselmnt.hed__Status__c != oldMap.get(courselmnt.Id).hed__Status__c){
                    courseEnrollmentLst.add(courselmnt);
                }
            }
            
            reqmntFullflmntLstToInsert = PlanRqmntToCourseConnect.courseConnectionToConnectPlanRequirement(courseEnrollmentLst);
        }
        
        System.Debug('<<<<<reqmntFullflmntLstToInsert>>>>>'+reqmntFullflmntLstToInsert);
        
        dmlWrapper.objectsToUpdate.addAll(reqmntFullflmntLstToInsert);
        return dmlWrapper;
    }
}