public without sharing  class RemoveEventController {
    @AuraEnabled
    public static Boolean hasPermissionSet(String permissionSetName) {
        Id userId = UserInfo.getUserId();
        List<PermissionSetAssignment> assignments = [
                SELECT Id
                FROM PermissionSetAssignment
                WHERE AssigneeId = :userId
                AND PermissionSet.Name = :permissionSetName
                LIMIT 1
        ];
        return !assignments.isEmpty();
    }

    @AuraEnabled
    public static String deleteCartItemsAndCart(Id cartItemId,Id cartId){
        String redirectURL;
        if(cartItemId != null) {
            //System.debug('CartId'+cartId);
            //System.debug('CartItemId'+cartItemId);
            pymt__Shopping_Cart__c shoppingCartToDelete = new pymt__Shopping_Cart__c();
            shoppingCartToDelete.Id = cartId;
            List<pymt__Shopping_Cart_Item__c> lisshoppingCartItem = [SELECT Id, Special_Event__c FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cartId];
            //System.debug('no.of items' +lisshoppingCartItem.size());
            pymt__Shopping_Cart_Item__c shoppingCartItem = [SELECT Id, Special_Event__c FROM pymt__Shopping_Cart_Item__c WHERE Id =: cartItemId];
            if(lisshoppingCartItem.size() == 1){
                delete shoppingCartItem;
                delete shoppingCartToDelete;
                //System.debug('ShoppingCart');
            }else{
                delete shoppingCartItem;
                //System.debug('CartItem');
            }

            Calendar_View__c clvObj = [SELECT Id, Link_to_Calendar_Item__c FROM Calendar_View__c WHERE Name = 'Events Home Page Calendar View'];

            if(clvObj != null && !String.isBlank(clvObj.Link_to_Calendar_Item__c)) {
                redirectURL = clvObj.Link_to_Calendar_Item__c.replace('{!itemId}', (String)shoppingCartItem.Special_Event__c);

            }
        }
        return redirectURL;
    }

    //========
    // The following method is used for new event registration apex classes
    //========

    @AuraEnabled
    public static String deleteCartItemsAndCart1(Id cartId, String eventId){
        String sfdcBaseURL = URL.getSalesforceBaseUrl().toExternalForm();
        //System.debug('Base URL: ' + sfdcBaseURL);
        String redirectURL = sfdcBaseURL + '/events/s';
        if(cartId != null) {
            //System.debug('CartId'+cartId);
            pymt__Shopping_Cart__c shoppingCartToDelete = [SELECT Id FROM pymt__Shopping_Cart__c WHERE Id =: cartId];

            List<pymt__Shopping_Cart_Item__c> lisshoppingCartItem = [SELECT Id, Special_Event__c, Special_Event__r.Event_Registration_URL__c FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cartId AND Special_Event__c =: eventId AND pymt__Payment_Completed__c= false];
            //System.debug('no.of items' +lisshoppingCartItem.size());
            if (lisshoppingCartItem.size() > 0) {
                redirectURL = lisshoppingCartItem[0].Special_Event__r.Event_Registration_URL__c;
                try {
                    delete  lisshoppingCartItem;
                    List<pymt__Shopping_Cart_Item__c> lstsci = [SELECT Id, Special_Event__c FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cartId];
                    if(lstsci.size() == 0){
                        delete shoppingCartToDelete;
                    }
                } catch(Exception e) {
                    System.debug('Error: ' + e.getMessage());
                }
            }
        }
        return redirectURL;
    }


    //==============
    // The following method is used for new event registration apex classes
    //==============
    @AuraEnabled
    public static Map<String, String> getCart(Id storeId, String cookieId) {

        Store_Configuration__c sc = [
                SELECT Id, Name, Link_to_Cart__c
                FROM Store_Configuration__c
                WHERE Id = :storeId
        ];
        Map<String, String> cartInfo = new Map<String, String>{
        'cartId' => '',
        'count' => '0',
        'link' => sc.Link_to_Cart__c,
        'events' => '',
        'type' => 'guest'
        };

        String cartKey = '';

        //If the user is logged in, check if they currently have a cart
        //First, check if the user is logged-in, if they are, use their shopping cart, otherwise,
        List<User> lstUser = [Select u.ContactId from User u where u.Id = :UserInfo.getUserId()];
        if (lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId)) {
            cartKey = lstUser[0].ContactId + '.' + sc.Id;
            cartInfo.put('type', 'auth');
        } else if (!String.isBlank(cookieId)) {
            cartKey = cookieId + '.' + sc.Id;
        }

        if (!String.isBlank(cartKey)) {
            List<AggregateResult> ars = [
                    SELECT pymt__Shopping_Cart__c cartId, Count(Id)
                    FROM pymt__Shopping_Cart_Item__c
                    WHERE pymt__Shopping_Cart__r.Cart_Key__c = :cartKey AND pymt__Payment_Completed__c = false
                    AND Item_Available__c = true AND pymt__Payment__r.pymt__Status__c != 'Invoice'
                    GROUP BY pymt__Shopping_Cart__c
            ];

            //query list of event Id and name from shopping cart item
            List<AggregateResult> eventList = [
                    SELECT Special_Event__c eventId, Special_Event__r.Name eventName, Count(Id)
                    FROM pymt__Shopping_Cart_Item__c
                    WHERE pymt__Shopping_Cart__r.Cart_Key__c = :cartKey AND pymt__Payment_Completed__c = false
                    AND Item_Available__c = true AND pymt__Payment__r.pymt__Status__c != 'Invoice'
                    GROUP BY Special_Event__c, Special_Event__r.Name
            ];

            if (ars.size() > 0) {
                cartInfo.put('cartId', String.valueOF(ars[0].get('cartId')));
                cartInfo.put('count', String.valueOF(ars[0].get('expr0')));
            }
            if (eventList.size() > 0) {
                List<cartEvents> lstCartEvents = new List<cartEvents>();
                for (AggregateResult ar1 : eventList) {
                    cartEvents ce = new cartEvents();
                    ce.eventId = String.valueOF(ar1.get('eventId'));
                    ce.eventName = String.valueOF(ar1.get('eventName'));
                    lstCartEvents.add(ce);
                }
                cartInfo.put('events', JSON.serialize(lstCartEvents));
            }
        }

        //System.debug('cartInfo===: ' + cartInfo);
        return cartInfo;

    }

    @AuraEnabled
    public static Map<String, String> getCartItemConfig(Id itemId, Id objConfigId, String cookieId) {

        System.debug('==itemId: ' + itemId);
        System.debug('==objConfigId: ' + objConfigId);
        System.debug('==cookieId: ' + cookieId);

        //Get the cart configuration
        Store_SObject_Configuration__c soc = [
                SELECT Id, Amount_Field_API_Name__c, Enable_One_Click_Checkout__c, Enforce_Cart_Item_Uniqueness__c, Field_Indicating_Availability_for_Cart__c, Include_Quantity_Field__c, Shopping_Cart_Lookup_to_Object__c, SObject_API_Name__c, Store_Configuration__c, Store_Configuration__r.Name, (SELECT Id, Default_Value__c, Target_Object_Field_API_Name__c, Cart_Field_API_Name__c
                FROM Store_SObject_Mappings__r)
                FROM Store_SObject_Configuration__c
                WHERE Id = :objConfigId
        ];
        System.debug('==soc: ' + soc);

        Map<String, String> cartInfo = new Map<String, String>{
        'addDisabled' => 'false',
        'oneClick' => String.valueOf(soc.Enable_One_Click_Checkout__c),
        'quantField' => String.valueOf(soc.Include_Quantity_Field__c),
        'unique' => String.valueOf(soc.Enforce_Cart_Item_Uniqueness__c)
        };

        //Check if this item already exists in the cart, and if it does, prevent the user from adding another one
        if (soc.Enforce_Cart_Item_Uniqueness__c && !String.isBlank(soc.Shopping_Cart_Lookup_to_Object__c)) {

            String cartKey;
            //First, check if the user is logged-in, if they are, use their shopping cart, otherwise,
            List<User> lstUser = [Select u.ContactId from User u where u.Id = :UserInfo.getUserId()];
            if (lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId)) {
                cartKey = lstUser[0].ContactId + '.' + soc.Store_Configuration__c;
            } else if (!String.isBlank(cookieId)) {
                cartKey = cookieId + '.' + soc.Store_Configuration__c;
            }

            System.debug('==cartInfo1: ' + cartInfo);
            if (!String.isBlank(cartKey)) {
                cartInfo.put('addDisabled', String.valueOf(database.query(
                                'SELECT Id FROM pymt__Shopping_Cart_Item__c WHERE ' +
                                soc.Shopping_Cart_Lookup_to_Object__c +
                                ' = :itemId AND pymt__Shopping_Cart__r.Cart_Key__c = :cartKey AND pymt__Payment_Completed__c <> true ').size() >
                0));
            }
            System.debug('==cartInfo2: ' + cartInfo);

            if (!String.isBlank(soc.Field_Indicating_Availability_for_Cart__c) &&
                            cartInfo.get('addDisabled') == 'false') {
                cartInfo.put('addDisabled', String.valueOf(database.query(
                                'SELECT Id FROM ' + soc.SObject_API_Name__c + ' WHERE ' +
                                soc.Field_Indicating_Availability_for_Cart__c + ' = true AND Id =:itemId ').size() ==
                        0));
            }
            System.debug('==cartInfo3: ' + cartInfo);
        }

        //check if to disable the add to cart button if there is no event fee associated with the event
        if (!String.isBlank(soc.Field_Indicating_Availability_for_Cart__c) && cartInfo.get('addDisabled') == 'false') {
            List<evt__Event_Fee__c> lstFees = [SELECT Id FROM evt__Event_Fee__c WHERE evt__Event__c = :itemId];
            if (lstFees.size() == 0) {
                cartInfo.put('addDisabled', 'true');
            }
        }
        System.debug('===cartInfo4===: ' + cartInfo);

        return cartInfo;
    }

    /*
	 * @description Retrieves the opt out URL field from the special event object
	 *
	 * @param recordId - special event record Id
	 *
	 * @return opt out url link
	 */
    @AuraEnabled
    public static Map<String, String> getEventOptOutURL(Id recordId) {
        String optOutURL = '';
        String siteURL = URL.getCurrentRequestUrl().toExternalForm();
        //System.debug('*** Site URL: *** ' + siteURL);
        string[] urlParse = siteURL.split('/');
        Map<String, String> eventsInfo = new Map<String, String>{
        'eventsWithOptOutURL' => 'false',
        'eventsOptOutURLLink' => '',
        'eventFeeDefault' => ''
        };

        List<evt__Special_Event__c> specialEventList = [SELECT Id, OptOutURL__c, (SELECT Id FROM evt__Event_Fees__r WHERE evt__Event__c = :recordId LIMIT 1) FROM evt__Special_Event__c WHERE Id = :recordId];
        if (specialEventList.size() > 0) {
            if (specialEventList[0].OptOutURL__c != null) {
                eventsInfo.put('eventsWithOptOutURL', 'true');
                eventsInfo.put('eventsOptOutURLLink', String.valueOf(specialEventList[0].OptOutURL__c));
            }
            List<evt__Event_Fee__c> eventFeeList = [SELECT Id FROM evt__Event_Fee__c WHERE evt__Event__c = :recordId AND evt__Active__c=true AND Available_for_Checkout__c=true Order By CreatedDate ASC LIMIT 1];
            if (eventFeeList.size() > 0) {
                eventsInfo.put('eventFeeDefault', String.valueOf(eventFeeList[0].Id));
            }
        }

        return eventsInfo;
    }

    @AuraEnabled
    public static List<evt__Event_Fee__c> getEventFeeList(Id eventId) {
        List<evt__Event_Fee__c> listOfEventFee= new List<evt__Event_Fee__c>();
        try {
            //If the user is logged in, get contact Id
            //First, check if the user is logged-in, if they are, display all event fees
            List<User> lstUser = [Select u.ContactId from User u where u.Id = :UserInfo.getUserId()];
            if (lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId)) {
                Contact loggedInContact = [SELECT ID, Type__c FROM Contact WHERE id = :lstUser[0].ContactId];
                if (loggedInContact.Type__c == null) {
                    listOfEventFee = [
                            SELECT Id, Name, evt__Amount__c, Type__c
                            FROM evt__Event_Fee__c
                            WHERE evt__Event__c = :eventId
                            AND Type__c = 'Standard'
                    ];
                } else if (loggedInContact.Type__c.contains('Faculty') && loggedInContact.Type__c.contains('Student')) {
                    listOfEventFee = [
                            SELECT Id, Name, evt__Amount__c, Type__c
                            FROM evt__Event_Fee__c
                            WHERE evt__Event__c = :eventId
                            AND (Type__c = 'Standard'
                            OR Type__c = 'Faculty'
                            OR Type__c = 'Student')
                    ];
                } else if ((Boolean)loggedInContact.Type__c.contains('Faculty')) {
                    listOfEventFee = [
                            SELECT Id, Name, evt__Amount__c, Type__c
                            FROM evt__Event_Fee__c
                            WHERE evt__Event__c = :eventId
                            AND (Type__c = 'Standard'
                            OR Type__c = 'Faculty')
                    ];
                } else if ((Boolean)loggedInContact.Type__c.contains('Student')) {
                    listOfEventFee = [
                            SELECT Id, Name, evt__Amount__c, Type__c
                            FROM evt__Event_Fee__c
                            WHERE evt__Event__c = :eventId
                            AND (Type__c = 'Standard'
                            OR Type__c = 'Student')
                    ];
                } else if ((Boolean)loggedInContact.Type__c.contains('Alumni')) {
                    listOfEventFee = [
                            SELECT Id, Name, evt__Amount__c, Type__c
                            FROM evt__Event_Fee__c
                            WHERE evt__Event__c = :eventId
                            AND (Type__c = 'Standard'
                            OR Type__c = 'Alumni')
                    ];
                } else if (loggedInContact.Type__c != null && !loggedInContact.Type__c.contains('Faculty') &&
                        !loggedInContact.Type__c.contains('Student')) {
                    listOfEventFee = [
                            SELECT Id, Name, evt__Amount__c, Type__c
                            FROM evt__Event_Fee__c
                            WHERE evt__Event__c = :eventId
                            AND Type__c = 'Standard'
                    ];
                }
            } else {
                listOfEventFee = [
                        SELECT Id, Name, evt__Amount__c, Type__c
                        FROM evt__Event_Fee__c
                        WHERE evt__Event__c = :eventId
                        AND Type__c = 'Standard'
                ];
            }
            //System.debug('imhere 4 ');
            //System.debug('listOfEventFee ' + listOfEventFee);
        } catch (Exception e) {
            System.debug('Error in getEventFeeList: ' + e.getMessage());
        }
        return listOfEventFee;
    }

    /*
	 * @description Adds an itemn into the specified shopping cart
	 *
	 * @param scId - Id indicating the shopping cart to add the item to
	 * @param objToAdd - Id of the object to add to the shopping cart
	 *
	 * @return Status of add to cart action
	 */
    @AuraEnabled
    public static Map<String, String> addToCart(Id itemId,
                                                Id objConfigId,
                                                String cookieId,
                                                evt__Event_Fee__c eventFeeRecord) {

        //Get the cart configuration
        Store_SObject_Configuration__c soc = [
                SELECT Id, Name, Amount_Field_API_Name__c, Enable_One_Click_Checkout__c, Enforce_Cart_Item_Uniqueness__c, Include_Quantity_Field__c, Shopping_Cart_Lookup_to_Object__c, SObject_API_Name__c, Store_Configuration__c, Store_Configuration__r.Name, Store_Configuration__r.Link_to_Cart__c, (SELECT Id, Default_Value__c, Target_Object_Field_API_Name__c, Cart_Field_API_Name__c
                FROM Store_SObject_Mappings__r)
                FROM Store_SObject_Configuration__c
                WHERE Id = :objConfigId
        ];

        //System.debug('*** soc *** ' + soc);

        Map<String, String> cartInfo = new Map<String, String>{
        'link' => soc.Store_Configuration__r.Link_to_Cart__c,
        'oneClick' => String.valueOf(soc.Enable_One_Click_Checkout__c),
        'quantField' => String.valueOf(soc.Include_Quantity_Field__c),
        'unique' => String.valueOf(soc.Enforce_Cart_Item_Uniqueness__c)
        };

        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c(Store_Configuration__c = soc.Store_Configuration__c);
        String cartItemName = '';

        //Construct query and get item in cart
        Set<String> fieldsToQuery = new Set<String>{
                'name', 'id'
                };
        if ((Boolean)!String.isBlank(soc.Amount_Field_API_Name__c))
            fieldsToQuery.add(soc.Amount_Field_API_Name__c.toLowerCase());

        for (Store_SObject_Mapping__c ssm : soc.Store_SObject_Mappings__r)
            if ((Boolean)!String.isBlank(ssm.Target_Object_Field_API_Name__c))
                fieldsToQuery.add(ssm.Target_Object_Field_API_Name__c.toLowerCase());

        //System.debug('*** fieldsToQuery *** ' + fieldsToQuery);
        //System.debug('*** soc.SObject_API_Name__c *** ' + soc.SObject_API_Name__c);
        //System.debug('*** itemId *** ' + itemId);

        List<SObject> sObjs = database.query('SELECT ' + String.join(new List<String>(fieldsToQuery), ',') + ' FROM ' +
                soc.SObject_API_Name__c + ' WHERE ID = \'' + itemId + '\'');

        //System.debug('*** sObjs *** ' + sObjs);

        //First, check if the user is logged-in, if they are, use their shopping cart, otherwise,
        User u = [SELECT Id FROM User WHERE Name = 'Events Site Guest User' Limit 1];
        //System.debug('*** u *** ' + u);
        List<User> lstUser = [SELECT Id, ContactId, Contact.Name, Username FROM User WHERE Id = :UserInfo.getUserId()];
        Id invoiceId = null;
        //System.debug('*** lstUser *** ' + lstUser);
        if (lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId) && lstUser[0].Id != u.Id) {
            cart.Cart_Key__c = lstUser[0].ContactId + '.' + soc.Store_Configuration__c;
            cart.pymt__Contact__c = lstUser[0].ContactId;
            cart.Guest_Cart__c = false;
            cart.Store_Configuration__c = soc.Store_Configuration__c;
            cartItemName = lstUser[0].ContactId != null ? lstUser[0].Contact.Name : lstUser[0].Username;
            cartInfo.put('type', 'auth');
            //System.debug('***********In here************');
            //UTR-3856: Create Invoice at Oneform submission instead
            //invoiceId = getInvoiceId(lstUser[0].ContactId, itemId);

        } else if (!String.isBlank(cookieId)) {
            cart.Cart_Key__c = cookieId + '.' + soc.Store_Configuration__c;
            cart.Guest_Cart__c = true;
            cart.Store_Configuration__c = soc.Store_Configuration__c;
            cartItemName = 'Anonymous';
            cartInfo.put('type', 'guest');
        } else {
            cookieId = EncodingUtil.ConvertToHex(Crypto.GenerateAESKey(128)).Substring(0, 18);
            cart.Cart_Key__c = cookieId + '.' + soc.Store_Configuration__c;
            cart.Guest_Cart__c = true;
            cart.Store_Configuration__c = soc.Store_Configuration__c;
            cartItemName = 'Anonymous';
            cartInfo.put('type', 'guest');
            cartInfo.put('newCookie', cookieId);

        }
        //System.debug('***********Cart Key************' + cart.Cart_Key__c);
        //System.debug('***********Guest Cart************' + cart.Guest_Cart__c);
        //System.debug('***********Store Configuration************' + cart.Store_Configuration__c);
        //System.debug('***********Cart Item Name************' + cartItemName);
        //System.debug('Cart Info: ' + cartInfo);
        //System.debug('Cart: ' + cart);


        database.upsert(cart, pymt__Shopping_Cart__c.FIELDS.Cart_Key__c);
        //System.debug('*** Cart Id: *** ' + cart.Id);
        cartInfo.put('cartId', cart.Id);


        //Construct item to be added to cart and insert it
        pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c(
                Name = (cartItemName + ' - ' + sobjs[0].get('Name')).left(80),
                pymt__Shopping_Cart__c = cart.Id,
                pymt__Quantity__c = 1
        );

        if (invoiceId != null) {
            item.put('Invoice__c', invoiceId);
        }

        if (eventFeeRecord != null) {
            item.put('pymt__Unit_Price__c', eventFeeRecord.evt__Amount__c);
            item.Event_Fee__c = eventFeeRecord.Id;
        } else if (eventFeeRecord == null) {
            if (!String.isBlank(soc.Amount_Field_API_Name__c) && sobjs[0].get(soc.Amount_Field_API_Name__c) != null) {
                item.put('pymt__Unit_Price__c', sobjs[0].get(soc.Amount_Field_API_Name__c));
            } else {
                item.put('pymt__Unit_Price__c', 0);
            }
        }

        for (Store_SObject_Mapping__c ssm : soc.Store_SObject_Mappings__r)
            if (!String.isBlank(ssm.Target_Object_Field_API_Name__c) &&
                            sobjs[0].get(ssm.Target_Object_Field_API_Name__c) != null &&
                            sobjs[0].get(ssm.Target_Object_Field_API_Name__c) != '')
                item.put(ssm.Cart_Field_API_Name__c, sobjs[0].get(ssm.Target_Object_Field_API_Name__c));
            else if (!String.isBlank(ssm.Default_Value__c))
                item.put(ssm.Cart_Field_API_Name__c, ssm.Default_Value__c);

        if (!String.isBlank(soc.Shopping_Cart_Lookup_to_Object__c))
            item.put(soc.Shopping_Cart_Lookup_to_Object__c, sobjs[0].Id);

        //System.debug('*** item *** ' + item);
        insert item;

        //System.debug('*** cartInfo *** ' + cartInfo);
        return cartInfo;

    }

    @AuraEnabled (Cacheable=true)
    public static List<evt__Attendee__c> searchAttendees(String searchKey) {
        String keyword = '%' + searchKey + '%';
        return [
                SELECT evt__First_Name__c, evt__Last_Name__c, evt__Email__c
                FROM evt__Attendee__c
                WHERE evt__First_Name__c LIKE :keyword
                OR evt__Last_Name__c LIKE :keyword
                OR evt__Email__c LIKE :keyword
                LIMIT 10
        ];
    }

    @AuraEnabled
    public static pymt__PaymentX__c createAttendeeRecords(Id shoppingCartId, String cardName, String cardNo, String cvv, String expMonth, String expYear, String accountName, String loggedInContactId, String contactFName, String contactLName, String contactEmail, List<evt__Attendee__c> listOfAttendees, pymt__PaymentX__c pymnt, Decimal subTotal, Decimal discount, String discountId, Integer currentUsage, Decimal tax, Decimal total, Boolean isPayment, String eventId, List<EventFeeCartWrapper> listOfEventFees, Integer quantity, String[] listOfSubscription, List<UploadAttendeeFileWrapper> listOfFiles, List<SessionAssignmentWrapper> listOfSessions) {
        pymt__PaymentX__c newpymnt = new pymt__PaymentX__c();
        Map<String, String> responseMap = new Map<String, String>();
        //String res= '';
        //System.debug('@0001 shoppingCartId===> ' + shoppingCartId);
        //System.debug('@0002 loggedInContactId===> ' + loggedInContactId);
        System.debug('@0003 contactFName===> ' + contactFName);
        System.debug('@0004 contactLName===> ' + contactLName);
        System.debug('@0005 contactEmail===> ' + contactEmail);
        System.debug('@0006 CartEventFee===> ' + listOfEventFees);
        System.debug('@0007 listOfAttendees===> ' + listOfAttendees);
        System.debug('@0008 Payment===> ' + pymnt);
        //System.debug('@0009 listOfSessions===> ' + listOfSessions);
        //System.debug('@0010 listFiles===> ' + listOfFiles);
        System.debug('@0011 Discount===> ' + discount);
        System.debug('@0012 DiscountId===> ' + discountId);
        System.debug('@0013 DiscountUsge===> ' + currentUsage);
        System.debug('@0014 tax===> ' + tax);
        System.debug('@0015 total===> ' + total);
        System.debug('@0016 subtotal===> ' + subTotal);
        System.debug('@0017 isPayment===> ' + isPayment);
        System.debug('@0018 eventId===> ' + eventId);
        System.debug('@0019 quantity===> ' + quantity);
        //System.debug('@0020 listOfSubscription===> ' + listOfSubscription);
        //System.debug('@0021 listOfFiles===> ' + listOfFiles);

        String returnResponse = null;
        Integer attendeeRegisteredCount = 0;
        String currentContactId = '';
        String currentShoppingCartItemId = '';
        Set<Id> eventIdlist = new Set<Id>();
        Set<Id> scIdlist = new Set<Id>();
        List<Shopping_Cart_Item_Details__c> lstSCID = new List<Shopping_Cart_Item_Details__c>();
        List<evt__Session_Assignment__c> sessnAsmntList = new List<evt__Session_Assignment__c>();
        Map<String, Decimal> mapPrice = new Map<String, Decimal>();
        String discountType = '';
        Decimal discountDollar = 0;
        Decimal discountPercent = 0;

        try {
            List<pymt__Shopping_Cart_Item__c> sciList = [SELECT Id, Name, pymt__Contact__c, Gross_Amount__c, Total_Amount__c, Tax_Amount__c, Discount__c, Discount_Amount__c, Event_Fee__c, Special_Event__c, pymt__Quantity__c FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c = :shoppingCartId AND Special_Event__c = :eventId Order By CreatedDate Desc Limit 1];
            //System.debug('@0022sciList *** ' + sciList);
            if (sciList.size() > 0) {
                for (Integer i = 0; i < sciList.size(); i++) {
                    sciList[i].pymt__Contact__c = loggedInContactId;
                    sciList[i].Event_Quantity__c = quantity;
                    sciList[i].Event_Gross_Amount__c = subTotal;
                    if (discount > 0) {
                        sciList[i].Event_Discount_Amount__c = discount;
                        sciList[i].Discount__c = discountId;
                    }else{
                        sciList[i].Event_Discount_Amount__c = 0;
                        sciList[i].Discount__c = null;
                    }
                    sciList[i].Event_Tax_Amount__c = tax;
                    sciList[i].Event_Total_Amount__c = total;
                    eventIdlist.add(sciList[i].Special_Event__c);
                    scIdlist.add(sciList[i].Id);
                    currentShoppingCartItemId = sciList[i].Id;
                    if (discountId != null && discountId != '') {
                        Discount__c discountObj = [SELECT Id, Name, Code__c, Discount_Amount_Unit__c, Dollar_Discount__c, Percent_Discount__c FROM Discount__c WHERE Id = :discountId LIMIT 1];
                        if (discountObj.Discount_Amount_Unit__c == 'Dollar') {
                            discountType = 'Dollar';
                            discountDollar = discountObj.Dollar_Discount__c;
                        } else if (discountObj.Discount_Amount_Unit__c == 'Percent') {
                            discountType = 'Percent';
                            discountPercent = discountObj.Percent_Discount__c;
                        }
                    }
                    for (EventFeeCartWrapper wrapperObj : listOfEventFees) {
                        if (wrapperObj.quantity > 0) {
                            for (Integer j=1; j<=wrapperObj.quantity; j++ ) {
                                Shopping_Cart_Item_Details__c scid = new Shopping_Cart_Item_Details__c();
                                scid.Shopping_Cart_Item__c = sciList[i].Id;
                                scid.Event_Fee__c = wrapperObj.eventFeeId;
                                scid.SC_event__c = wrapperObj.eventId;
                                scid.Item_Quantity__c = 1;
                                scid.Item_Unit_Price__c = wrapperObj.price;
                                scid.Item_Gross_Amount__c = wrapperObj.price;
                                if (discountId != null && discountId != '') {
                                    if (discountType == 'Dollar') {
                                        scid.Item_Discount_Amount__c = discountDollar;
                                    } else if (discountType == 'Percent') {
                                        scid.Item_Discount_Amount__c = (wrapperObj.price * discountPercent) / 100;
                                    }
                                }else{
                                    scid.Item_Discount_Amount__c = 0;
                                }
                                scid.Item_Total_Amount__c = wrapperObj.price - scid.Item_Discount_Amount__c;
                                scid.Item_Tax_Amount__c = scid.Item_Total_Amount__c * wrapperObj.tax;
                                lstSCID.add(scid);
                                mapPrice.put(wrapperObj.eventFeeId, wrapperObj.price);
                            }
                            /*
                            Shopping_Cart_Item_Details__c scid = new Shopping_Cart_Item_Details__c();
                            scid.Shopping_Cart_Item__c = sciList[i].Id;
                            scid.Event_Fee__c = wrapperObj.eventFeeId;
                            scid.SC_event__c = wrapperObj.eventId;
                            scid.Item_Quantity__c = wrapperObj.quantity;
                            scid.Item_Unit_Price__c = wrapperObj.price;
                            scid.Item_Gross_Amount__c = wrapperObj.quantity * wrapperObj.price;
                            scid.Item_Tax_Amount__c = wrapperObj.price * wrapperObj.quantity * wrapperObj.tax;
                            scid.Item_Total_Amount__c = (wrapperObj.quantity * wrapperObj.price) + (wrapperObj.price * wrapperObj.quantity * wrapperObj.tax);
                            lstSCID.add(scid);
                            */
                        }
                    }
                }
                update sciList;
            }
            //System.debug('@0023lstSCID===> ' + lstSCID);
            //System.debug('@0024mapPrice===> ' + mapPrice);

            List<Subscription__c> lstSc= [SELECT  Id FROM Subscription__c WHERE Name IN :listOfSubscription];

            if (String.isNotBlank(loggedInContactId)) {
                currentContactId = loggedInContactId;
                Contact loggedContact = [SELECT Id, Name, (SELECT Id, evt__Payment__c FROM evt__Attendees__r WHERE evt__Event__c IN :eventIdlist AND evt__Is_Primary__c = TRUE AND evt__Invitation_Status__c NOT IN ('Declined', 'Wait Listed', 'Cancelled', 'Invited', 'Payment Pending')), (SELECT Id, Name, Subscription__c FROM Subscription_Memberships__r WHERE Subscription__r.Name IN :listOfSubscription) FROM Contact WHERE Id = :loggedInContactId];
                if (loggedContact.Subscription_Memberships__r.size() == 0) {
                    for (Subscription__c sc : lstSc) {
                        Subscription_Membership__c smc = new Subscription_Membership__c();
                        smc.Contact__c = loggedInContactId;
                        smc.Subscription__c = sc.Id;
                        smc.Subscribed_Date__c = System.now();
                        smc.Subscription_Status__c = 'Subscribed';
                        insert smc;
                    }
                }
            } else {
                //create new contact
                Contact newContact = new Contact();
                newContact.FirstName = contactFName;
                newContact.Preferred_First_Name__c = contactFName;
                newContact.Former_Last_Name__c = contactLName;
                newContact.LastName = contactLName;
                newContact.Email = contactEmail;
                newContact.hed__Preferred_Email__c = 'Alternate Email';
                newContact.hed__AlternateEmail__c = contactEmail;
                newContact.MailingStreet = pymnt.pymt__Billing_Street__c;
                newContact.MailingCity = pymnt.pymt__Billing_City__c;
                newContact.MailingState = pymnt.pymt__Billing_State__c;
                newContact.MailingCountry = pymnt.pymt__Billing_Country__c;
                newContact.MailingPostalCode = pymnt.pymt__Billing_Postal_Code__c;
                newContact.Type__c = 'Event Attendee';
                try {
                    Database.SaveResult sr = Database.insert(newContact, false);
                    if (sr.success) {
                        currentContactId = newContact.Id;
                    } else {
                        for (Database.Error err : sr.getErrors()) {
                            System.debug('Error: ' + err.getMessage());
                        }
                    }
                    List<Subscription__c> lstSuc= [SELECT  Id FROM Subscription__c WHERE Name IN :listOfSubscription];
                    Contact conwithSbm = [SELECT Id, Name, (SELECT Id, Name, Subscription__c FROM Subscription_Memberships__r WHERE Subscription__r.Name IN :listOfSubscription) FROM Contact WHERE Id = :currentContactId];
                    //System.debug('conwithSbm===> ' + conwithSbm);
                    //System.debug('lstSuc===> ' + lstSuc);
                    if (conwithSbm != null) {
                        currentContactId = conwithSbm.Id;
                        if (conwithSbm.Subscription_Memberships__r.size() == 0) {
                            for (Subscription__c sc : lstSuc) {
                                Subscription_Membership__c smc = new Subscription_Membership__c();
                                smc.Contact__c = loggedInContactId;
                                smc.Subscription__c = sc.Id;
                                smc.Subscribed_Date__c = System.now();
                                smc.Subscription_Status__c = 'Subscribed';
                                insert smc;
                            }
                        }
                    }
                } catch (Exception e) {
                    System.debug('Error: ' + e.getMessage());
                    //res= 'Error1: ' + e.getMessage();
                    //responseMap.put('Error', 'Duplicate Contact Email found. Please use another email address.');
                }
            }

            // create new attendee with evt__Attendee__c params from front
            List<evt__Attendee__c> newAttendees = new List<evt__Attendee__c>();
            for (Integer j = 0; j < sciList.size(); j++) {
                for (Integer i = 0; i < listOfAttendees.size(); i++) {
                    //System.debug('@@listOfAttendees== ' + listOfAttendees[i]);
                    evt__Attendee__c newAttendee = new evt__Attendee__c();
                    newAttendee.evt__Reg_First_Name__c = (String) listOfAttendees[i].get('evt__Reg_First_Name__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_First_Name__c');
                    newAttendee.evt__Reg_Last_Name__c = (String) listOfAttendees[i].get('evt__Reg_Last_Name__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_Last_Name__c');
                    newAttendee.evt__Reg_Email__c = (String) listOfAttendees[i].get('evt__Reg_Email__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_Email__c');
                    newAttendee.evt__Event__c = eventId;
                    newAttendee.evt__Event_Fee__c = (Id) listOfAttendees[i].get('evt__Event_Fee__c') == null ? '' : (Id) listOfAttendees[i].get('evt__Event_Fee__c');
                    //System.debug('@@mapPrice== ' + mapPrice.get(newAttendee.evt__Event_Fee__c));
                    if (mapPrice.get(newAttendee.evt__Event_Fee__c) > 0) {
                        newAttendee.evt__Invitation_Status__c = 'Invited';
                    }else{
                        newAttendee.evt__Invitation_Status__c = 'Registered';
                    }
                    /*if (isPayment) {
                        newAttendee.evt__Invitation_Status__c = 'Invited';
                    }else{
                        newAttendee.evt__Invitation_Status__c = 'Registered';
                    }*/
                    //newAttendee.evt__Contact__c = currentContactId;
                    newAttendee.evt__Reg_Street__c = (String) listOfAttendees[i].get('evt__Reg_Street__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_Street__c');
                    newAttendee.evt__Reg_City__c = (String) listOfAttendees[i].get('evt__Reg_City__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_City__c');
                    newAttendee.evt__Reg_State__c = (String) listOfAttendees[i].get('evt__Reg_State__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_State__c');
                    newAttendee.evt__Reg_Country__c = (String) listOfAttendees[i].get('evt__Reg_Country__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_Country__c');
                    newAttendee.evt__Reg_Postal_Code__c = (String) listOfAttendees[i].get('evt__Reg_Postal_Code__c') == null ? '' : (String) listOfAttendees[i].get('evt__Reg_Postal_Code__c');
                    newAttendee.evt__Registration_Type__c = (String) listOfAttendees[i].get('evt__Registration_Type__c') == null ? '' : (String) listOfAttendees[i].get('evt__Registration_Type__c');
                    newAttendee.evt__Category__c = 'Attendee';
                    newAttendee.Shopping_Cart_Item__c = currentShoppingCartItemId;
                    newAttendee.Customize_FieldName_1__c = (String) listOfAttendees[i].get('Customize_FieldName_1__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldName_1__c');
                    newAttendee.Customize_FieldName_2__c = (String) listOfAttendees[i].get('Customize_FieldName_2__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldName_2__c');
                    newAttendee.Customize_FieldName_3__c = (String) listOfAttendees[i].get('Customize_FieldName_3__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldName_3__c');
                    newAttendee.Customize_FieldName_4__c = (String) listOfAttendees[i].get('Customize_FieldName_4__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldName_4__c');
                    newAttendee.Customize_FieldName_5__c = (String) listOfAttendees[i].get('Customize_FieldName_5__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldName_5__c');
                    newAttendee.Customize_FieldValue_1__c = (String) listOfAttendees[i].get('Customize_FieldValue_1__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldValue_1__c');
                    newAttendee.Customize_FieldValue_2__c = (String) listOfAttendees[i].get('Customize_FieldValue_2__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldValue_2__c');
                    newAttendee.Customize_FieldValue_3__c = (String) listOfAttendees[i].get('Customize_FieldValue_3__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldValue_3__c');
                    newAttendee.Customize_FieldValue_4__c = (String) listOfAttendees[i].get('Customize_FieldValue_4__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldValue_4__c');
                    newAttendee.Customize_FieldValue_5__c = (String) listOfAttendees[i].get('Customize_FieldValue_5__c') == null ? '' : (String) listOfAttendees[i].get('Customize_FieldValue_5__c');
                    if (listOfAttendees[i].get('Industry__c') != null && listOfAttendees[i].get('Industry__c') != ''){
                        newAttendee.Industry__c = (String) listOfAttendees[i].get('Industry__c');
                    }
                    if (listOfAttendees[i].get('Where_did_you_hear_about_this_event__c') != null && listOfAttendees[i].get('Where_did_you_hear_about_this_event__c') != ''){
                        newAttendee.Where_did_you_hear_about_this_event__c = (String) listOfAttendees[i].get('Where_did_you_hear_about_this_event__c');
                    }
                    if (listOfAttendees[i].get('Academic_Program__c') != null && listOfAttendees[i].get('Academic_Program__c') != ''){
                        newAttendee.Academic_Program__c = (String) listOfAttendees[i].get('Academic_Program__c');
                    }
                    if (listOfAttendees[i].get('Academic_Term__c') != null && listOfAttendees[i].get('Academic_Term__c') != ''){
                        newAttendee.Academic_Term__c = (String) listOfAttendees[i].get('Academic_Term__c');
                    }
                    if (listOfAttendees[i].get('Accessibility_Requirements__c') != null && listOfAttendees[i].get('Accessibility_Requirements__c') != ''){
                        newAttendee.Accessibility_Requirements__c = (String) listOfAttendees[i].get('Accessibility_Requirements__c') ;
                    }
                    if (listOfAttendees[i].get('Accessibility_Requirements_Other__c') != null && listOfAttendees[i].get('Accessibility_Requirements_Other__c') != ''){
                        newAttendee.Accessibility_Requirements_Other__c = (String) listOfAttendees[i].get('Accessibility_Requirements_Other__c');
                    }
                    if (listOfAttendees[i].get('Dietary_Restrictions__c') != null && listOfAttendees[i].get('Dietary_Restrictions__c') != ''){
                        newAttendee.Dietary_Restrictions__c = (String) listOfAttendees[i].get('Dietary_Restrictions__c');
                    }
                    if (listOfAttendees[i].get('Dietary_Restictions_Other__c') != null && listOfAttendees[i].get('Dietary_Restictions_Other__c') != ''){
                        newAttendee.Dietary_Restictions_Other__c = (String) listOfAttendees[i].get('Dietary_Restictions_Other__c');
                    }

                    newAttendees.add(newAttendee);
                }
            }
            List<evt__Attendee__c> updatedAttendeesList = new List<evt__Attendee__c>();
            if (newAttendees.size() > 0) {
                for (evt__Attendee__c eta :newAttendees){
                    //System.debug('eta: ' + eta);
                    //System.debug('eta.evt__Email__c: ' + eta.evt__Reg_Email__c);
                    //System.debug('eta.evt__Reg_First_Name__c: ' + eta.evt__Reg_First_Name__c);
                    //System.debug('eta.evt__Reg_Last_Name__c: ' + eta.evt__Reg_Last_Name__c);
                    List<Contact> c = [SELECT Id, Email, FirstName, LastName FROM Contact WHERE Email = :eta.evt__Reg_Email__c OR hed__AlternateEmail__c = :eta.evt__Reg_Email__c OR hed__WorkEmail__c = :eta.evt__Reg_Email__c OR hed__UniversityEmail__c = :eta.evt__Reg_Email__c  ORDER BY CreatedDate DESC LIMIT 1];
                    if (c.size() > 0) {
                        eta.evt__Contact__c = c[0].Id;
                    } else {
                        Contact newContact = new Contact();
                        newContact.FirstName = eta.evt__Reg_First_Name__c;
                        newContact.LastName = eta.evt__Reg_Last_Name__c;
                        newContact.Email = eta.evt__Reg_Email__c;
                        newContact.hed__Preferred_Email__c = 'Alternate Email';
                        newContact.hed__AlternateEmail__c = eta.evt__Reg_Email__c;
                        newContact.Type__c = 'Event Attendee';
                        insert newContact;
                        eta.evt__Contact__c = newContact.Id;
                    }
                    updatedAttendeesList.add(eta);
                }
            }

            if (updatedAttendeesList.size() > 0) {
                Database.DMLOptions dmlOptions = new Database.DMLOptions();
                dmlOptions.duplicateRuleHeader.allowSave = true;
                for (evt__Attendee__c newAttendee : updatedAttendeesList) {
                    newAttendee.setOptions(dmlOptions);
                }
                //System.debug('@@@updateAttendeesList: ' + updatedAttendeesList);
                Database.SaveResult[] sr = Database.insert(updatedAttendeesList, false);
                //System.debug('@@@sr: ' + sr);
                Set<Id> attendeeIds = new Set<Id>();
                for (Database.SaveResult s : sr) {
                    //System.debug('@@s: ' + s);
                    if (s.isSuccess()) {
                        //System.debug('Attendee created successfully');
                        attendeeIds.add(s.getId());
                    } else {
                        for (Database.Error err : s.getErrors()) {
                            System.debug('Error: ' + err.getMessage());
                        }
                        //res = 'Error2: Attendee not created '+s.getErrors();
                    }
                }
                /*
                if (!attendeeIds.isEmpty()) {
                    System.debug('attendeeIds: ' + attendeeIds);
                    List<evt__Attendee__c> newAttendeesList = [
                            SELECT Id, evt__Email__c, evt__Reg_First_Name__c, evt__Reg_Last_Name__c
                            FROM evt__Attendee__c
                            WHERE Id IN :attendeeIds
                    ];

                    List<evt__Attendee__c> updatedAttendeesList = new List<evt__Attendee__c>();
                    for (evt__Attendee__c eta :newAttendeesList){
                        System.debug('eta: ' + eta);
                        System.debug('eta.evt__Email__c: ' + eta.evt__Email__c);
                        System.debug('eta.evt__Reg_First_Name__c: ' + eta.evt__Reg_First_Name__c);
                        System.debug('eta.evt__Reg_Last_Name__c: ' + eta.evt__Reg_Last_Name__c);
                        List<Contact> c = [SELECT Id, Email, FirstName, LastName FROM Contact WHERE Email = :eta.evt__Email__c];
                        if (c.size() > 0) {
                            eta.evt__Contact__c = c[0].Id;
                        } else {
                            Contact newContact = new Contact();
                            newContact.FirstName = eta.evt__Reg_First_Name__c;
                            newContact.LastName = eta.evt__Reg_Last_Name__c;
                            newContact.Email = eta.evt__Email__c;
                            newContact.hed__Preferred_Email__c = 'Alternate Email';
                            newContact.hed__AlternateEmail__c = eta.evt__Email__c;
                            insert newContact;
                            eta.evt__Contact__c = newContact.Id;
                        }
                        updatedAttendeesList.add(eta);
                    }
                    //update newAttendeesList;
                    List<Database.SaveResult> updateResults = Database.update(newAttendeesList, false);
                    for(Database.SaveResult res : updateResults) {
                        if (res.isSuccess()) {
                            System.debug('Successfully updated attendees. Attendee ID: ' + res.getId());
                        }
                        else {
                            for(Database.Error err : res.getErrors()) {
                                System.debug('The following error has occurred in update process: ');
                                System.debug(err.getStatusCode() + ': ' + err.getMessage());
                            }
                        }
                    }
                }
                */
                System.debug('@@attendeeIds:== ' + attendeeIds);
                if (attendeeIds.size()>0) {
                    List<evt__Attendee__c> newAttendeeList = [
                            SELECT Id, evt__Event__c, evt__Event_Fee__c
                            FROM evt__Attendee__c
                            WHERE Id IN :attendeeIds
                    ];
                    System.debug('@@newAttendeeList:== ' + newAttendeeList);
                    for (Shopping_Cart_Item_Details__c sc :lstSCID){
                        Integer j = 0;
                        for (evt__Attendee__c et :newAttendeeList) {
                            if (sc.Event_Fee__c == et.evt__Event_Fee__c && sc.SC_event__c == et.evt__Event__c && sc.Attendee__c == null && sc.Contact__c == null) {
                                sc.Attendee__c = et.Id;
                                sc.Contact__c = currentContactId;
                                newAttendeeList.remove(j);
                                break;
                            }
                            j++;
                        }
                        System.debug('@@newAttendeeList:==0002 ' + newAttendeeList);
                    }
                }
                System.debug('@@@lstSCIDUpdate==0001: ' + lstSCID);
                if (lstSCID.size() > 0) {
                    insert lstSCID;
                }
                if (listOfFiles.size() > 0) {
                    List<UploadAttendeeFileWrapper> lstFiles = new List<UploadAttendeeFileWrapper>();
                    for (UploadAttendeeFileWrapper f : listOfFiles) {
                        //System.debug('f: ' + f.attendeeEmail + ' ' + f.eventFeeId);
                        if (f.attendeeEmail != null && f.eventFeeId != null) {
                            List<String> attEmails = new List<String>();
                            attEmails.add(f.attendeeEmail);
                            evt__Attendee__c evt = [SELECT Id FROM evt__Attendee__c WHERE evt__Reg_Email__c IN :attEmails AND evt__Event_Fee__c = :f.eventFeeId LIMIT 1];
                            if (evt != null) {
                                //System.debug('fileName: ' + f.fileName);
                                UploadAttendeeFileWrapper newf = new UploadAttendeeFileWrapper();
                                newf.attendeeId = evt.Id;
                                newf.file = f.file;
                                newf.fileName = f.fileName;
                                lstFiles.add(newf);
                            }
                        }else{
                            System.debug('Error: Attendee not found');
                            System.debug('f.attendeeEmail: ' + f.attendeeEmail);
                            System.debug('f.eventFeeId: ' + f.eventFeeId);
                            System.debug('f.fileName: ' + f.fileName);
                        }
                    }

                    if (lstFiles.size() > 0) {
                        for (UploadAttendeeFileWrapper uf : lstFiles) {
                            ContentVersion cv = new ContentVersion();
                            cv.Title = uf.fileName;
                            cv.PathOnClient = '/'+uf.fileName;
                            cv.VersionData = EncodingUtil.base64Decode(uf.file);
                            cv.FirstPublishLocationId = uf.attendeeId;
                            insert cv;
                        }
                    }
                }
                if (listOfSessions.size() > 0){
                    List<evt__Session_Assignment__c> lstSessions = new List<evt__Session_Assignment__c>();
                    for (SessionAssignmentWrapper sw : listOfSessions) {
                        //System.debug('sw: ' + sw);
                        evt__Attendee__c att = [SELECT Id FROM evt__Attendee__c WHERE evt__Event__c = :sw.eventId AND evt__Event_Fee__c = :sw.eventTypeId AND evt__Reg_Email__c = :sw.attendeeEmail ORDER BY CreatedDate Desc LIMIT 1];
                        if (att.Id != null) {
                            evt__Session_Assignment__c sa = new evt__Session_Assignment__c();
                            sa.evt__Attendee__c = att.Id;
                            sa.evt__Event_Session__c = sw.sessionId;
                            sa.evt__Status__c = 'Registered';
                            lstSessions.add(sa);
                        }
                    }
                    if (lstSessions.size() > 0) {
                        insert lstSessions;
                    }
                }
            }
            if (isPayment) {
                //create payment record, to update the payment record, we need to get the details from the shopping cart item.
                try {
                    //Create invoice record, associate contact with invoice.
                    Invoice__c inv = new Invoice__c();
                    inv.Invoice_Status__c = 'Open-Personal';
                    inv.Type__c = 'Events';
                    inv.Tax_Amount__c = tax;
                    inv.Gross_Amount__c = subTotal;
                    inv.Total_Amount_SCI__c = total;
                    inv.Contact__c = currentContactId;
                    insert inv;

                    //create payment record, to update the payment record, we need to get the details from the shopping cart item.
                    //to calculate the total of amount, tax and payments fields from sci to add it in payment record
                    newpymnt.Name = 'EventRegistration-OnlinePayment-'+DateTime.now().format('yyyy-MM-dd HH:mm:ss');
                    newpymnt.pymt__Transaction_Type__c = 'Payment';
                    newpymnt.pymt__Payment_Type__c = 'Credit Card';
                    newpymnt.pymt__Status__c = 'Online Checkout';
                    newpymnt.pymt__Contact__c = currentContactId != null ? currentContactId : null;
                    newpymnt.evt__Event__c = eventId;
                    newpymnt.pymt__Amount__c = total;
                    newpymnt.Gross_Amount__c = subTotal;
                    newpymnt.pymt__Tax__c = tax;
                    newpymnt.pymt__Discount__c = discount;
                    newpymnt.Type__c = 'Event Registration';
                    newpymnt.pymt__Payment_Processor__c = 'Global Pay';
                    newpymnt.Invoice__c = inv.Id;
                    newpymnt.pymt__Billing_First_Name__c = pymnt.pymt__Billing_First_Name__c;
                    newpymnt.pymt__Billing_Last_Name__c = pymnt.pymt__Billing_Last_Name__c;
                    newpymnt.pymt__Billing_Street__c = pymnt.pymt__Billing_Street__c;
                    newpymnt.pymt__Billing_City__c = pymnt.pymt__Billing_City__c;
                    newpymnt.pymt__Billing_State__c = pymnt.pymt__Billing_State__c;
                    newpymnt.pymt__Billing_Country__c = pymnt.pymt__Billing_Country__c;
                    newpymnt.pymt__Billing_Postal_Code__c = pymnt.pymt__Billing_Postal_Code__c;

                    insert newpymnt;

                    //update the shopping cart item with the payment record
                    for (Integer i = 0; i < sciList.size(); i++) {
                        sciList[i].pymt__Payment__c = newpymnt.Id;
                        sciList[i].Invoice__c = newpymnt.Invoice__c;
                        sciList[i].pymt__Payment_Completed__c = false;
                        update sciList[i];
                    }
                    //System.debug('res: ' + newpymnt);
                    //newpymnt = [SELECT Id, Name, pymt__Transaction_Type__c, pymt__Payment_Type__c, pymt__Status__c, pymt__Contact__c, pymt__Amount__c, pymt__Tax__c, pymt__Discount__c, Type__c, pymt__Payment_Processor__c, Invoice__c, pymt__Billing_First_Name__c, pymt__Billing_Last_Name__c, pymt__Billing_Street__c, pymt__Billing_City__c, pymt__Billing_State__c, pymt__Billing_Country__c, pymt__Billing_Postal_Code__c FROM pymt__PaymentX__c WHERE evt__Event__c = :eventId AND pymt__Contact__c = :currentContactId AND pymt__Status__c = 'Online Checkout' LIMIT 1];
                } catch (Exception ex) {
                    System.debug('Error: ' + ex.getMessage());
                    //res= 'Error3: ' + ex.getMessage();
                }
            } else {
                //update the shopping cart item with pymt__Payment_Completed__c = true
                for (Integer i = 0; i < sciList.size(); i++) {
                    sciList[i].pymt__Payment_Completed__c = true;
                    update sciList[i];
                }
            }
            if (discountId != null) {
                //update the current Usage field of discount record
                Discount__c myDiscount = [SELECT Id, Current_Usage__c FROM Discount__c WHERE Id = :discountId];
                myDiscount.Current_Usage__c = currentUsage;
                update myDiscount;
            }
        } catch (Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getStackTraceString());
            System.debug(e.getMessage() + ' -- ' + e.getStackTraceString());
            //res= 'Error4: ' + e.getMessage()+ ' -- '+e.getStackTraceString();
            //throw new AuraHandledException(e.getMessage());
        }

        return newpymnt;
        //return res;
    }

    /*
 * Creates a new contact record or retrieves an existing contact based on the provided parameters.
 *
 * @param loggedContactId The ID of the logged contact. If empty, a new contact will be created.
 * @param contactFName The first name of the contact.
 * @param contactLName The last name of the contact.
 * @param contactEmail The email of the contact.
 *
 * @return The ID of the created or retrieved contact.
 */
    @AuraEnabled
    public static String createNewContact(String loggedContactId,
                                          String contactFName,
                                          String contactLName,
                                          String contactEmail) {
        String result = '';
        Boolean isType = false;
        String currentType = '';
        //System.debug('0001-loggedContactId: ' + loggedContactId);
        //System.debug('0001Check Contact Id: ' + String.isEmpty(loggedContactId));
        //System.debug('0001contactFName: ' + contactFName);
        //System.debug('0001contactLName: ' + contactLName);
        //System.debug('0001contactEmail: ' + contactEmail);

        if (String.isEmpty(loggedContactId)) {
            List<Contact> lstCon = [SELECT Id, Type__c FROM Contact WHERE Email = :contactEmail OR hed__AlternateEmail__c = :contactEmail OR hed__WorkEmail__c = :contactEmail OR hed__UniversityEmail__c = :contactEmail ORDER BY CreatedDate DESC LIMIT 1];
            //System.debug('0001Contact List === ' + lstCon);
            if (lstCon.size() > 0) {
                result = lstCon[0].Id;
                isType = false;
                currentType = lstCon[0].Type__c;
                //System.debug('currentType: ' + currentType);
                if (String.isNotEmpty(currentType)) {
                    if (!currentType.contains('Event Attendee')) {
                        lstCon[0].Type__c = currentType + '; Event Attendee';
                        isType = true;
                    }
                }else{
                    lstCon[0].Type__c = 'Event Attendee';
                    isType = true;
                }
                if(isType){
                    update lstCon;
                }
            } else {
                Contact newContact = new Contact();
                newContact.FirstName = contactFName;
                newContact.LastName = contactLName;
                newContact.Email = contactEmail;
                newContact.Type__c = 'Event Attendee';
                try {
                    insert newContact;
                    result = newContact.Id;
                } catch (Exception e) {
                    result = e.getMessage();
                }
            }
        } else {
            List<Contact> listCon = [SELECT Id, Type__c FROM Contact WHERE Id = :loggedContactId LIMIT 1];
            if (listCon.size() > 0) {
                isType = false;
                currentType = listCon[0].Type__c;
                //System.debug('currentType: ' + currentType);
                if (String.isNotEmpty(currentType)) {
                    if (!currentType.contains('Event Attendee')) {
                        listCon[0].Type__c = currentType + '; Event Attendee';
                        isType = true;
                    }
                } else {
                    listCon[0].Type__c = 'Event Attendee';
                    isType = true;
                }
                if (isType) {
                    update listCon;
                }
            }
            result = loggedContactId;
        }
        //System.debug('0001Contact result: ' + result);

        return result;
    }

    @AuraEnabled
    public static List<pymt__Shopping_Cart_Item__c> getCartItems(Id cartId, String eventId) {
        List<pymt__Shopping_Cart_Item__c> shoppingItems = new List<pymt__Shopping_Cart_Item__c>();
        try {
            shoppingItems = [SELECT Id, pymt__Quantity__c, Event_Fee__c FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cartId AND Special_Event__c =: eventId ORDER BY CreatedDate ASC];
            return shoppingItems;
        } catch (Exception e) {
            System.debug('Error: ' + e.getMessage());
        }
        return shoppingItems;
    }

    /*
 * This method checks for attendees in the database based on their email and event ID.
 *
 * @param listOfAttendees The list of attendees to check.
 * @param eventId The ID of the event.
 *
 * @return The list of attendees found in the database.
 */
    @AuraEnabled
    public static List<evt__Attendee__c> checkAttendee(List<evt__Attendee__c> listOfAttendees,
                                                       String eventId) {
        Set<String> listOfAttendeeEmails = new Set<String>();
        for (evt__Attendee__c attendee : listOfAttendees) {
            listOfAttendeeEmails.add(attendee.evt__Reg_Email__c);
        }
        List<evt__Attendee__c> listOfAttendeesInDB = [SELECT Id, Name, evt__Reg_Email__c FROM evt__Attendee__c WHERE evt__Reg_Email__c IN :listOfAttendeeEmails AND evt__Event__c = :eventId AND evt__Invitation_Status__c != 'Cancelled'];

        return listOfAttendeesInDB;
    }

    @AuraEnabled
    public static Discount__c getCouponCode(String codeName, String eventId) {
        System.debug('codeName ' + codeName);
        System.debug('eventId ' + eventId);
        Discount__c DicountResult = null;

        // Use a single SOQL query to retrieve the event
        evt__Special_Event__c myEvent = [SELECT Id, Universal_Discount_Code__c FROM evt__Special_Event__c WHERE Id = :eventId LIMIT 1];
        System.debug('myEvent== ' + myEvent);
        if (myEvent != null && myEvent.Universal_Discount_Code__c != null) {
            String universalCode = myEvent.Universal_Discount_Code__c;

            if (universalCode != null && universalCode.contains(codeName)) {
                DicountResult = [SELECT Id, At_Limit__c, Current_Usage__c, Max_Usage__c, Percent_Discount__c, Dollar_Discount__c, Taxable__c, Start_Date__c, End_Date__c
                                 FROM Discount__c
                                 WHERE Code__c = :codeName
                                 LIMIT 1];
                }else{
                System.debug('eventcodeName== ' + codeName);
                DicountResult = [SELECT Id, At_Limit__c, Current_Usage__c, Max_Usage__c, Percent_Discount__c, Dollar_Discount__c, Taxable__c, Start_Date__c, End_Date__c
                                 FROM Discount__c
                                 WHERE Code__c = :codeName AND Event__c = :eventId
                                 LIMIT 1];
            }
        }else{
            System.debug('eventcodeName== ' + codeName);
            DicountResult = [SELECT Id, At_Limit__c, Current_Usage__c, Max_Usage__c, Percent_Discount__c, Dollar_Discount__c, Taxable__c, Start_Date__c, End_Date__c
            FROM Discount__c
            WHERE Code__c = :codeName AND Event__c = :eventId
            LIMIT 1];
        }

        System.debug('DicountResult ' + DicountResult);
        return DicountResult;
    }

    @AuraEnabled
    public static List<EventCartWrapperNew> getShoppingCartItems(Id cartId, String eventId) {
        //System.debug('==cartId: ' + cartId);
        //System.debug('==eventId: ' + eventId);
        List<EventCartWrapperNew> listOfEventCartWrapper = new List<EventCartWrapperNew>();
        try {
            List<pymt__Shopping_Cart_Item__c> listOfShoppingCartItem = [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c, Special_Event__r.Taxable__c, Taxing_Authority__r.Tax_Rate__c, Special_Event__r.Event_Date_Time__c, Special_Event__r.Subscription_List__c, Special_Event__r.Support_Email__c, Discount__c, Special_Event__c, Discount__r.Code__c, Discount_Amount__c, Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c, Special_Event__r.Thumbnail_Image__c, Special_Event__r.evt__Max_Additional_Attendees__c, Discount__r.Percent_Discount__c, Discount__r.Taxable__c, Discount__r.Dollar_Discount__c, Special_Event__r.evt__Accept_Donations__c FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: cartId AND Special_Event__c = :eventId AND Type__c != 'Donation'];
            Set<Id> mapOfEventInfToListOfSessions = new Set<Id>();
            //Set<Id> mapOfEventIdToListOfFees = new Set<Id>();
            for (pymt__Shopping_Cart_Item__c cartItem : listOfShoppingCartItem) {
                if (cartItem.Special_Event__c != null){
                    //System.debug('==cartItem.Special_Event__c: ' + cartItem.Special_Event__c);
                    mapOfEventInfToListOfSessions.add(cartItem.Special_Event__c);
                }
                //mapOfEventIdToListOfFees.put(cartItem.Special_Event__c, '');
            }
            List<evt__Session__c> listOfSessions = [SELECT Id, Name, evt__Session_Fee__c, evt__Short_Description__c, Session_Date_Time__c, evt__Event__c
            FROM evt__Session__c  WHERE evt__Event__c IN : mapOfEventInfToListOfSessions];

            List<evt__Event_Fee__c> listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c, evt__Event__c, Mailing_Enabled__c, Enable_Accessibility_Info__c, Fields_Recruitment_Admissions__c, Enable_Dietary_Restriction_Info__c, evt__Limit_Per_Purchase__c, evt__Limit__c, Current_Usage__c, evt__Description__c, Customize_FieldName_1__c, Customize_FieldName_2__c, Customize_FieldName_3__c, Customize_FieldName_4__c, Customize_FieldName_5__c, Show_Customize_Field_1__c, Show_Customize_Field_2__c, Show_Customize_Field_3__c, Show_Customize_Field_4__c, Show_Customize_Field_5__c, evt__Active__c, uploadFileLabel__c, uploadFile__c, UploadFile_Mandatory__c, Supplemental_Event_Items__c FROM evt__Event_Fee__c WHERE evt__Event__c IN: mapOfEventInfToListOfSessions AND Type__c != '' AND evt__Active__c = true AND At_Limit__c = false ORDER BY evt__Amount__c ASC];

            //System.debug('list of events fees ' + listOfEventFee);
            //System.debug('list of events session ' + listOfSessions);
            //System.debug('list of shopping cart items ' + listOfShoppingCartItem);

            for (pymt__Shopping_Cart_Item__c cartItem : listOfShoppingCartItem) {
                EventCartWrapperNew wrapperObj = new EventCartWrapperNew();
                wrapperObj.listOfEventSession = listOfSessions;
                if (wrapperObj.listOfEventSession.size() > 0) {
                    wrapperObj.showSession = true;
                }else {
                    wrapperObj.showSession = false;
                }
                wrapperObj.shoppingcartItem = cartItem;
                wrapperObj.showDiscount = cartItem.Discount__c == null ? true : false;
                wrapperObj.showAddGuests = ((cartItem.Special_Event__r.evt__Show_Available_Seats__c) && (
                                cartItem.Special_Event__r.evt__Max_Additional_Attendees__c != null &&
                        Integer.valueOf(cartItem.Special_Event__r.evt__Max_Additional_Attendees__c) >
                        0)) ? true : false;
                if (!wrapperObj.showDiscount) {
                    wrapperObj.couponCode = cartItem.Discount__r.Code__c;
                    wrapperObj.discountCodeMessage = cartItem.Discount__r.Code__c +
                            ' discount code is applied.Please click here to add a new code';
                }
                wrapperObj.showZeroDiscount = (!wrapperObj.showDiscount) ? false : true;
                wrapperObj.listOfGuests = new List<evt__Attendee__c>();
                wrapperObj.listofEventFee = new List<String>();
                for(evt__Event_Fee__c eventfee : listOfEventFee) {
                    Decimal limitNum = eventfee.evt__Limit__c == null ? 0 : eventfee.evt__Limit__c;
                    Decimal limitPerNum = eventfee.evt__Limit_Per_Purchase__c == null ? 0 : eventfee.evt__Limit_Per_Purchase__c;
                    String feeType = eventfee.Type__c == null ? 'null' : eventfee.Type__c ;
                    String feeDescription = eventfee.evt__Description__c == null ? 'null' : eventfee.evt__Description__c;
                    feeDescription = feeDescription.replace('"', '\\"');
                    feeDescription = feeDescription.replace('\n', '\\n');
                    feeDescription = feeDescription.replace('\r', '\\r');
                    String feeCustomizeFieldName1 = eventfee.Customize_FieldName_1__c == null ? 'null' : eventfee.Customize_FieldName_1__c;
                    String feeCustomizeFieldName2 = eventfee.Customize_FieldName_2__c == null ? 'null' : eventfee.Customize_FieldName_2__c;
                    String feeCustomizeFieldName3 = eventfee.Customize_FieldName_3__c == null ? 'null' : eventfee.Customize_FieldName_3__c;
                    String feeCustomizeFieldName4 = eventfee.Customize_FieldName_4__c == null ? 'null' : eventfee.Customize_FieldName_4__c;
                    String feeCustomizeFieldName5 = eventfee.Customize_FieldName_5__c == null ? 'null' : eventfee.Customize_FieldName_5__c;
                    String feeUploadFileLabel = eventfee.uploadFileLabel__c == null ? 'null' : eventfee.uploadFileLabel__c;
                    String feeSupplementalEventItems = eventfee.Supplemental_Event_Items__c == null ? 'null' : eventfee.Supplemental_Event_Items__c;
                    String feeName = eventfee.Name;
                    feeName = feeName.replace('"', '');
                    wrapperObj.listofEventFee.add(JSON.serialize('{ "Id" : "' + eventfee.Id + '", "Name" :"' + feeName + '", "evt__Amount__c" :"' + eventfee.evt__Amount__c + '", "Type__c" : "' + feeType + '", "evt__Event__c" : "' + eventfee.evt__Event__c + '", "Mailing_Enabled__c" : "' + eventfee.Mailing_Enabled__c + '", "Fields_Recruitment_Admissions__c" : "' + eventfee.Fields_Recruitment_Admissions__c + '", "Enable_Accessibility_Info__c" : "' + eventfee.Enable_Accessibility_Info__c + '", "Enable_Dietary_Restriction_Info__c" : "' + eventfee.Enable_Dietary_Restriction_Info__c + '", "evt__Limit_Per_Purchase__c" : "' + limitPerNum + '", "evt__Limit__c" : "' + limitNum + '", "Current_Usage__c" : "' + eventfee.Current_Usage__c + '", "evt__Description__c" : "' + feeDescription + '", "Customize_FieldName_1__c" : "' + feeCustomizeFieldName1 + '", "Customize_FieldName_2__c" : "' + feeCustomizeFieldName2 + '", "Customize_FieldName_3__c" : "' + feeCustomizeFieldName3 + '", "Customize_FieldName_4__c" : "' + feeCustomizeFieldName4 + '", "Customize_FieldName_5__c" : "' + feeCustomizeFieldName5 + '", "Show_Customize_Field_1__c" : "' + eventfee.Show_Customize_Field_1__c + '", "Show_Customize_Field_2__c" : "' + eventfee.Show_Customize_Field_2__c + '", "Show_Customize_Field_3__c" : "' + eventfee.Show_Customize_Field_3__c + '", "Show_Customize_Field_4__c" : "' + eventfee.Show_Customize_Field_4__c + '", "Show_Customize_Field_5__c" : "' + eventfee.Show_Customize_Field_5__c + '", "evt__Active__c" : "' + eventfee.evt__Active__c + '", "uploadFileLabel__c" : "' + feeUploadFileLabel + '", "uploadFile__c" : "' + eventfee.uploadFile__c + '", "UploadFile_Mandatory__c" : "' + eventfee.UploadFile_Mandatory__c + '", "Supplemental_Event_Items__c" : "' + feeSupplementalEventItems + '" }'));
                }
                /*                    wrapperObj.listofEventFee.add(JSON.serialize('{ label : ' + eventfee.Name + ',' + 'value:' + eventfee.Id + ','+ eventfee.evt__Amount__c + ',' +eventfee.Type__c + ',' + eventfee.evt__Event__c + ',' + eventfee.Mailing_Enabled__c + ',' + eventfee.Fields_Recruitment_Admissions__c + ',' + eventfee.evt__Limit_Per_Purchase__c + ',' + eventfee.evt__Limit__c + ',' + eventfee.Current_Usage__c + ',' + eventfee.evt__Description__c + ',' + eventfee.Customize_FieldName_1__c + ',' + eventfee.Customize_FieldName_2__c + ',' + eventfee.Customize_FieldName_3__c + ',' + eventfee.Customize_FieldName_4__c + ',' + eventfee.Customize_FieldName_5__c + ',' + eventfee.Show_Customize_Field_1__c + ',' + eventfee.Show_Customize_Field_2__c + ',' + eventfee.Show_Customize_Field_3__c + ',' + eventfee.Show_Customize_Field_4__c + ',' + eventfee.Show_Customize_Field_5__c + ',' + eventfee.evt__Active__c + ',' + eventfee.uploadFileLabel__c + ',' + eventfee.uploadFile__c + ',' + eventfee.UploadFile_Mandatory__c + ',' + eventfee.Supplemental_Event_Items__c + '}'));
                                }*/
                if(wrapperObj.listofEventFee.size() > 0) {
                    wrapperObj.showPicklist = true;
                } else {
                    wrapperObj.showPicklist = false;
                }
                /*                List<String> lefStr = new List<String>();
                                for (evt__Event_Fee__c eventFee : listOfEventFee) {
                                    if (eventFee != null) {
                                        lefStr.add(eventFee.Id);
                                        //wrapperObj.listOfEventFee.add(eventFee);
                                    }
                                }*/
                //wrapperObj.listofEventFee = listOfEventFee;
                //wrapperObj.listofEventFee = lefStr;
                //hsing => to support dynamic price calulaton in case we have session price. only for front-end
                // wrapperObj.listofEventFee = mapOfEventIdToListOfFees.containsKey(cartItem.Special_Event__c) ? mapOfEventIdToListOfFees.get(cartItem.Special_Event__c) : null;     //hsing => to support dynamic price calulaton in case we have session price. only for front-end
                //if (wrapperObj.listofEventFee.size() > 0) {
                //    wrapperObj.showPicklist = true;
                //} else {
                //    wrapperObj.showPicklist = false;
                //}
                wrapperObj.totalAmount = cartItem.Total_Amount__c;
                wrapperObj.subTotal = cartItem.Gross_Amount__c;
                wrapperObj.estimatedTax = cartItem.Tax_Amount__c;
                wrapperObj.discountAmount = cartItem.Discount_Amount__c;
                wrapperObj.originalSubTotal = cartItem.Gross_Amount__c;
                try {
                    wrapperObj.taxPercentage = cartItem.Taxing_Authority__r.Tax_Rate__c / 100;
                } catch (Exception e) {
                    wrapperObj.taxPercentage = 0;
                }
                wrapperObj.taxable = cartItem.Special_Event__r.Taxable__c;
                wrapperObj.event_image = cartItem.Special_Event__r.Thumbnail_Image__c;
                wrapperObj.showDonationCheckBox = cartItem.Special_Event__r.evt__Accept_Donations__c;
                if (wrapperObj.taxPercentage == 0 && cartItem.Tax_Amount__c != 0 && cartItem.Gross_Amount__c != 0) {
                    wrapperObj.taxPercentage = cartItem.Tax_Amount__c / cartItem.Gross_Amount__c;
                }
                if (cartItem.Discount__c != null) {
                    if (cartItem.Discount__r.Percent_Discount__c != null) {
                        wrapperObj.discountPercent = cartItem.Discount__r.Percent_Discount__c / 100;
                    }
                    wrapperObj.taxable = cartItem.Discount__r.Taxable__c;
                }
                //end of hsing code
                listOfEventCartWrapper.add(wrapperObj);
            }
            //System.debug('001 ' + listOfEventCartWrapper);
            return listOfEventCartWrapper;
        } catch (Exception e) {
            System.debug('002 ' + e.getMessage());
        }
        return listOfEventCartWrapper;
    }

    @AuraEnabled
    public static Map<String, String> processPayment(pymt__PaymentX__c clientPymt,
                                                     String cardNo,
                                                     String cvd,
                                                     String expYr,
                                                     String expMo,
                                                     String accountName,
                                                     String shoppingCartId) {
        Map<String, String> responseMap = new Map<String, String>();
        String returnResponse = null;
        Boolean hasError = false;
        String currentEventId = null;
        String currentContactId = null;
        String currentShoppingCartItemId = null;
        //System.debug('@001-clientPymt: ' + clientPymt);
        //System.debug('@002-shoppingCartId: ' + shoppingCartId);

        Organization[] orgData = [SELECT Id, InstanceName, IsSandbox, Name, OrganizationType FROM Organization];
        if (orgData.size() > 0) {
            if (orgData[0].IsSandbox == true){
                accountName = 'transaction_processing';
            }
        }
        //Compare the amount being acknowledged by the user and record to ensure that the payment is valid
        pymt__PaymentX__c payment;
        try {
            payment = [
                    SELECT Id, pymt__Amount__c, Payment_Response__c, Amount_Paid__c, evt__Event__c, pymt__Contact__c
                    FROM pymt__PaymentX__c
                    WHERE Id = :clientPymt.Id
            ];
            //System.debug('@@@002-New Payment: ' + payment);
            if (payment.evt__Event__c != null) {
                currentEventId = payment.evt__Event__c;
            }
            //System.debug('@@@003-Event ID: ' + currentEventId);
            if (payment.pymt__Contact__c != null) {
                currentContactId = payment.pymt__Contact__c;
            }
            //System.debug('@@@004-Contact ID: ' + currentContactId);
            pymt__Shopping_Cart_Item__c shoppingCartItem = [
                    SELECT Id, pymt__Shopping_Cart__c
                    FROM pymt__Shopping_Cart_Item__c
                    WHERE pymt__Contact__c = :currentContactId AND Special_Event__c = :currentEventId ORDER BY CreatedDate DESC LIMIT 1
            ];
            //System.debug('@@@005-Shopping Cart Item: ' + shoppingCartItem);
            if (shoppingCartItem != null) {
                currentShoppingCartItemId = shoppingCartItem.Id;
            }
            //String sourceIp = Auth.SessionManagement.getCurrentSession().get('SourceIp');
            String sourceIp = '';
            payment.pymt__IP_Address__c = sourceIp;

            //System.debug('imhere 1 ');
            //System.debug('pmt billing --- ' + clientPymt.pymt__Billing_First_Name__c);
            //System.debug('pmt lastname --- ' + clientPymt.pymt__Billing_Last_Name__c);
            //Update address information from the payment component
            payment.pymt__Billing_First_Name__c = clientPymt.pymt__Billing_First_Name__c;
            payment.pymt__Billing_Last_Name__c = clientPymt.pymt__Billing_Last_Name__c;
            payment.pymt__Billing_Street__c = clientPymt.pymt__Billing_Street__c;
            payment.pymt__Billing_City__c = clientPymt.pymt__Billing_City__c;
            payment.pymt__Billing_State__c = clientPymt.pymt__Billing_State__c;
            payment.pymt__Billing_Country__c = clientPymt.pymt__Billing_Country__c;
            payment.pymt__Billing_Postal_Code__c = clientPymt.pymt__Billing_Postal_Code__c;
            payment.Gross_Amount__c = clientPymt.Gross_Amount__c;
            payment.pymt__Amount__c = clientPymt.pymt__Amount__c;
            payment.pymt__Tax__c = clientPymt.pymt__Tax__c;
            payment.pymt__Discount__c = clientPymt.pymt__Discount__c;
            payment.pymt__Payment_Type__c = clientPymt.pymt__Payment_Type__c;
            payment.pymt__Transaction_Type__c = clientPymt.pymt__Transaction_Type__c;
            payment.pymt__Status__c = clientPymt.pymt__Status__c;
            payment.Name = clientPymt.Name;
            payment.Invoice__c = clientPymt.Invoice__c;
            payment.pymt__Payment_Processor__c = clientPymt.pymt__Payment_Processor__c;

            if (payment.Payment_Response__c == NULL) {
                payment.Payment_Response__c = '';
            }

            //System.debug('imhere 2 ');
            //System.debug('payment ' + payment);
            HTTPResponse response = GlobalPayConnect.processPayment(clientPymt, cardNo, cvd, expYr, expMo, accountName);
            System.debug('response: ' + response);
            System.debug('response.getStatusCode '+response.getStatusCode());
            GP_PaymentResponseWrapper wrapper = (GP_PaymentResponseWrapper) JSON.deserialize(response.getBody(), GP_PaymentResponseWrapper.class);
            System.debug('wrapper ' + wrapper);
            System.debug('wrapper.action.result_code '+wrapper.action.result_code);
            System.debug('wrapper error_code ' + wrapper.detailed_error_description);
            System.debug('wrapper status ' + wrapper.status);

            //Error in response
            if (String.isNotBlank(wrapper.detailed_error_description)) {
                returnResponse = wrapper.detailed_error_description;
                payment.pymt__Status__c = 'Error';
                responseMap.put('Status', 'ERROR');
                responseMap.put('ErrMessage', wrapper.detailed_error_description);
                hasError = true;
            } else if (response.getStatusCode() == 200 && wrapper.status == 'CAPTURED'){
                //System.debug('Payment_methodbrand ' + wrapper.Payment_method.Card.brand);
                //System.debug('imhere 1-1');
                returnResponse = wrapper.status;
                payment.pymt__Status__c = 'Completed';
                payment.pymt__Date__c = Date.today();
                payment.pymt__Batch_Id__c = wrapper.batch_id;
                payment.pymt__Reference_Id__c = wrapper.reference;
                payment.pymt__Last_4_Digits__c = cardNo.right(4);
                if (payment.Amount_Paid__c == null) {
                    payment.Amount_Paid__c = 0;
                }
                payment.Amount_Paid__c += clientPymt.pymt__Amount__c;
                payment.pymt__Card_Type__c = getCardType(wrapper.Payment_method.Card.brand);
                responseMap.put('Status', 'CAPTURED');
                hasError = false;
            } else if (response.getStatusCode() == 200 && wrapper.status == 'DECLINED') {
                //System.debug('imhere 1-2');
                returnResponse = wrapper.status;
                payment.pymt__Status__c = 'Declined';
                payment.pymt__Batch_Id__c = wrapper.batch_id;
                payment.pymt__Reference_Id__c = wrapper.reference;
                payment.pymt__Last_4_Digits__c = cardNo.right(4);
                payment.pymt__Card_Type__c = getCardType(wrapper.Payment_method.Card.brand);
                responseMap.put('Status', 'DECLINED');
                hasError = true;
            } else {
                //System.debug('imhere 1-3');
                payment.pymt__Status__c = 'Error';
                String errMsg = 'We\'re sorry, but we cannot complete your payment. Please refresh this page and try again.';
                responseMap.put('Status', 'ERROR');
                responseMap.put('ErrMessage', errMsg);
                hasError = true;
            }
            if (String.isNotBlank(wrapper.id)) {
                payment.pymt__Transaction_Id__c = wrapper.id;
            }
            payment.Payment_Response__c += '\n\n' + DateTime.now() + ' ->' + String.valueOf(response.getBody());
            //System.debug('imhere 2');
            if (hasError) {
                //System.debug('imhere 2-1 ');
                payment.pymt__Status__c = 'Error';
            }
            //System.debug('002payment===== ' + payment);
            update payment;
        } catch (Exception e) {
            //System.debug('imhere 3 ');
            if (payment != null) {
                payment.pymt__Status__c = 'Error';
                payment.Payment_Response__c += '\n\n' + DateTime.now() + ' ->' + e.getMessage();
                try{
                    //System.debug('imhere 3-1');
                    //System.debug('003payment===== ' + payment);
                    update payment;
                } catch (Exception ex1) {
                    System.debug('ex1.getMessage ' + ex1.getMessage());
                }
            }
            responseMap.put('Status', 'ERROR');
            responseMap.put('ErrMessage', e.getMessage() + ':' + e.getStackTraceString());

            //throw new AuraHandledException(e.getMessage());
        }
        if (Test.isRunningTest()) {
            hasError = true;
        }
        if (hasError) {
            //Rollback the changes
            responseMap.put('Status', 'Payment Pending');
            List<pymt__Shopping_Cart_Item__c> csci = [
                    SELECT Id, pymt__Payment_Completed__c, pymt__Shopping_Cart__c
                    FROM pymt__Shopping_Cart_Item__c
                    WHERE pymt__Contact__c = :currentContactId AND Special_Event__c = :currentEventId AND Id = :currentShoppingCartItemId ORDER BY CreatedDate DESC LIMIT 1
            ];
            if ( csci.size()> 0) {
                csci[0].pymt__Payment_Completed__c = False;
                csci[0].pymt__Shopping_Cart__c = shoppingCartId;
                update csci;
            }
            System.debug('@@007csci ' + csci);
/*            try {
                //System.debug('004payment===== ' + payment);
                delete payment;
            } catch (Exception ex2) {
                System.debug('Delete the payment ' + ex2.getMessage());
            }*/
            List<Shopping_Cart_Item_Details__c> updateDetails = new List<Shopping_Cart_Item_Details__c>();
            List<Shopping_Cart_Item_Details__c> lstDetails = [SELECT Id, Void_ticket__c FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItemId AND SC_event__c = :currentEventId AND Contact__c = :currentContactId];
            if (lstDetails.size() > 0) {
                for (Shopping_Cart_Item_Details__c detail : lstDetails) {
                    detail.Void_ticket__c = true;
                    updateDetails.add(detail);
                }
                //System.debug('005lstDetails=== ' + updateDetails);
                delete updateDetails;
            }
            List<evt__Attendee__c> updateAttendees = new List<evt__Attendee__c>();
            List<evt__Attendee__c> attendees = [SELECT Id, evt__Invitation_Status__c FROM evt__Attendee__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItemId AND evt__Event__c = :currentEventId];
            if (attendees.size() > 0) {
                for (evt__Attendee__c attendee : attendees) {
                    attendee.evt__Invitation_Status__c = 'Payment Pending';
                    updateAttendees.add(attendee);
                }
                //System.debug('004attendees=== ' + updateAttendees);
                delete updateAttendees;
            }
            List<pymt__Shopping_Cart_Item__c> mycsci = [
                    SELECT Id, pymt__Payment_Completed__c
                    FROM pymt__Shopping_Cart_Item__c
                    WHERE pymt__Contact__c = :currentContactId AND Special_Event__c = :currentEventId AND Id = :currentShoppingCartItemId ORDER BY CreatedDate DESC LIMIT 1
            ];
            //System.debug('@@mycsci ' + mycsci);
        }else{
            List<Id> attendeeIds = new List<Id>();
            List<evt__Attendee__c> updateAttendees1 = new List<evt__Attendee__c>();
            List<evt__Attendee__c> attendees = [SELECT Id, evt__Invitation_Status__c FROM evt__Attendee__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItemId AND evt__Event__c = :currentEventId AND evt__Contact__c = :currentContactId];
            if (attendees.size() > 0) {
                for (evt__Attendee__c attendee : attendees) {
                    attendee.evt__Invitation_Status__c = 'Registered';
                    updateAttendees1.add(attendee);
                    attendeeIds.add(attendee.Id);
                }
                update updateAttendees1;
                System.debug('@@@0061attendees=== ' + attendeeIds);
                if (!Test.isRunningTest()) {
                    UpdateInvitationStatusQueueable updateJob = new UpdateInvitationStatusQueueable(attendeeIds);
                    Id updateJobID = System.enqueueJob(updateJob);
                    //System.debug('0062updateJobID==' + updateJobID);
                }
            }
            List<Shopping_Cart_Item_Details__c> updateItemDetails = new List<Shopping_Cart_Item_Details__c>();
            List<Shopping_Cart_Item_Details__c> lstItemDetails = [SELECT Id, Void_ticket__c FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItemId AND SC_event__c = :currentEventId AND Contact__c = :currentContactId];
            if (lstItemDetails.size() > 0) {
                for (Shopping_Cart_Item_Details__c detail : lstItemDetails) {
                    detail.Void_ticket__c = false;
                    updateItemDetails.add(detail);
                }
                System.debug('@@007lstDetails=== ' + updateItemDetails);
                update updateItemDetails;
            }
            //Update the shopping cart item completed status
            List<pymt__Shopping_Cart_Item__c> shoppingCartItems = [SELECT Id, pymt__Contact__c, pymt__Payment_Completed__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :currentShoppingCartItemId];
            if (shoppingCartItems.size() > 0) {
                shoppingCartItems[0].pymt__Payment_Completed__c = true;
                try {
                    //System.debug('007shoppingCartItems=== ' + shoppingCartItems);
                    update shoppingCartItems;
                    if (!Test.isRunningTest()) {
                        ID jobID = System.enqueueJob(new SendConfirmationEmail(shoppingCartItems[0].Id, shoppingCartItems[0].pymt__Contact__c));
                        //System.debug('paid ticket jobID==' + jobID);
                    }
                } catch (Exception e) {
                    System.debug('update shopping cart status complete: ' + e.getMessage());
                }
            }
        }
        return responseMap;
    }

    public class cartEvents {
        public String eventId {
            get; set;
        }
        public String eventName {
            get; set;
        }
    }

    public static String getCardType(String cardTypeInResponse) {
        if (cardTypeInResponse == 'AMEX')
            return 'Amex';
        else if (cardTypeInResponse == 'VISA')
            return 'Visa';
        else if (cardTypeInResponse == 'MASTERCARD')
            return 'Mastercard';
        else if (cardTypeInResponse == 'DINERS')
            return 'Diners Club';
        else if (cardTypeInResponse == 'DISCOVER')
            return 'Discover';
        else
            return cardTypeInResponse;
    }

    @AuraEnabled
    public static List<DiscountWrapper> getEventDiscounts(Id eventId) {
        List<DiscountWrapper> discounts = new List<DiscountWrapper>();

        for (Discount__c disc : [SELECT Id, Code__c, Combinable_Discount__c, Current_Usage__c, Active__c, Display_Amount__c, Taxable__c, Start_Date__c, End_Date__c, At_Limit__c
        FROM Discount__c
        WHERE Event__c = :eventId]) {
            discounts.add(new DiscountWrapper(disc.Id, disc.Code__c, null, disc.Display_Amount__c, disc.Combinable_Discount__c, disc.Active__c, disc.Start_Date__c, disc.End_Date__c));
        }

        evt__Special_Event__c event = [SELECT Id, Universal_Discount_Code__c FROM evt__Special_Event__c WHERE Id = :eventId LIMIT 1];
        System.debug('@@event= '+event);
        if (event.Universal_Discount_Code__c != null) {
            List<String> codes = new List<String>();
            codes = event.Universal_Discount_Code__c.split(';');
            if (codes.size() > 0) {
                for (String code : codes) {
                    System.debug('@@code= '+code);
                    List<Discount__c> disc = [SELECT Id, Code__c, Combinable_Discount__c, Current_Usage__c, Active__c, Display_Amount__c, Taxable__c, Start_Date__c, End_Date__c, At_Limit__c FROM Discount__c WHERE Code__c = :code LIMIT 1];
                    if (disc.size() > 0) {
                        discounts.add(new DiscountWrapper(disc[0].Id, disc[0].Code__c, 'Universal Discount', disc[0].Display_Amount__c, disc[0].Combinable_Discount__c, disc[0].Active__c, disc[0].Start_Date__c, disc[0].End_Date__c));
                    }
                }
            }
        }

        System.debug('@@@discounts=== ' + discounts);
        return discounts;
    }

    public class DiscountWrapper {
        @AuraEnabled public String Id;
        @AuraEnabled public String codeName;
        @AuraEnabled public String type;
        @AuraEnabled public String value;
        @AuraEnabled public Boolean combinable;
        @AuraEnabled public Boolean active;
        @AuraEnabled public Date startDate;
        @AuraEnabled public Date endDate;

        public DiscountWrapper(String Id, String codeName, String type, String value, Boolean combinable, Boolean active, Date startDate, Date endDate) {
            this.Id = Id;
            this.codeName = codeName;
            this.type = type;
            this.value = value;
            this.combinable = combinable;
            this.active = active;
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }

}