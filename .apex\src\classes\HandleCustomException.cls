public class HandleCustomException extends Exception {
    public static String ModuleName {get; set;}
    public static String StackTrace {get; set;}
    public static String ClassName {get; set;}
    public static String MethodName {get; set;}
    public static String ExceptionCause {get; set;}
    public static String ExceptionCauseType {get; set;}
    public static String ExceptionMessage {get; set;}
    public static String ExceptionLineNumber {get; set;}
    
        
    public void log(Exception ex)
    {
        System.debug('exception message '+ex.getMessage());
        System.debug('exception stack trace '+ex.getStackTraceString());
        try
        {
            extractExceptionData(ex); 
            LogException(ex);             
        }
        catch(Exception e)
        {
            new HandleCustomException().Module('HandleCustomException').log(e);            
        }                    
    }
    
    public HandleCustomException  Module(String Name)
    {
        ModuleName = name;
        return this;
    }
    
    public HandleCustomException  ExceptionCause(String cause)
    {
        ExceptionCause = cause;
        return this;
    }
    
    public void extractExceptionData(Exception ex)
    {
        System.debug('inside extractException');
        try
        {
            stackTrace = ex.getStackTraceString().substringBefore('\n');
            className = stackTrace.substringAfter('.').substringBefore('.'); 
            methodName = stackTrace.substringBefore(':').substringAfter(className).substringAfter('.');  
        }
        catch(Exception e)
        {
            System.debug('Message==='+e.getMessage());
            new HandleCustomException().Module('HandleCustomException').log(e); 
        }                                    
    }
    
    public static void LogException(Exception ex)
    {
        try
        {                    
            Custom_Exception_Logger__c logger = new Custom_Exception_Logger__c();
            logger.Module_Name__c = ModuleName;
            logger.Stack_Trace__c = ex.getStackTraceString();
            logger.Class_Name__c = className;
            logger.Method_Name__c = methodName;
            logger.Line_Number__c = ex.getLineNumber();
            logger.Exception_Type__c = ex.getTypeName();
            logger.Exception_Cause_Override__c = ExceptionCause; 
            logger.Exception_Cause__c = String.valueOf(ex.getCause());           
            logger.Exception_Message__c = ex.getMessage();
            
            insert logger; 
        }
        
        catch(Exception e)
        {
            new HandleCustomException().Module('HandleCustomException').log(e);     
        }
        
    }
    
}