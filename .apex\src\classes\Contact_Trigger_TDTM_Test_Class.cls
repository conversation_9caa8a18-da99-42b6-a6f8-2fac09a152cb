@isTest
public class Contact_Trigger_TDTM_Test_Class {
	static testMethod void testContactFirstName()
        {
            List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig();

            tokens.add(new hed.TDTM_Global_API.TdtmToken('Contact_StagingHandler_TDTM', 'Contact', 'BeforeUpdate;BeforeInsert;AfterUpdate;AfterInsert', 1.00));
            tokens.add(new hed.TDTM_Global_API.TdtmToken('Contact_Trigger_TDTM', 'Contact', 'BeforeUpdate;BeforeInsert;AfterUpdate;AfterInsert', 1.00));
            
            hed.TDTM_Global_API.setTdtmConfig(tokens);
            
            Test.startTest();
                Contact c = new Contact();
                c.FirstName = 'Manny';
                c.LastName = 'Cott';
                c.Preferred_First_Name__c = null;
                            
                    
                insert c;   
    
                c.FirstName = 'New Name';
                
                update c;
                
            Test.stopTest();
            
            List<Contact> contacts = [SELECT Id, FirstName, LastName, Matching_Preferred_First_Name__c FROM Contact WHERE Id = :c.Id];
            
            system.debug('look: ' + contacts[0].Matching_Preferred_First_Name__c);
            
            for (Contact ct : contacts) {
                system.assertEquals('New Name', ct.Matching_Preferred_First_Name__c, 'The value of Matching_Preferred_First_Name__c should be New Name.');
            }     
            
        }
    
	static testMethod void testContactLastName()
        {
            List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig();

            tokens.add(new hed.TDTM_Global_API.TdtmToken('Contact_StagingHandler_TDTM', 'Contact', 'BeforeUpdate;BeforeInsert;AfterUpdate;AfterInsert', 1.00));
            
            hed.TDTM_Global_API.setTdtmConfig(tokens);
            
            Test.startTest();
                Contact c = new Contact();
                c.FirstName = 'Manny';
                c.LastName = 'Cott';
                c.Former_Last_Name__c = null;
                            
                    
                insert c;   
    
                c.LastName = 'New Name';
                
                update c;
                
            Test.stopTest();
            
            List<Contact> contacts = [SELECT Id, FirstName, LastName, Matching_Former_Last_Name__c FROM Contact WHERE Id = :c.Id];
            
            system.debug('look: ' + contacts[0].Matching_Former_Last_Name__c);
            
            for (Contact ct : contacts) {
                system.assertEquals('New Name', ct.Matching_Former_Last_Name__c, 'The value of Matching_Former_Last_Name__c should be New Name.');
            }     
            
        }
 
	static testMethod void testStagingName()
        {
            List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig();

            tokens.add(new hed.TDTM_Global_API.TdtmToken('Contact_StagingHandler_TDTM', 'Contact', 'BeforeUpdate;BeforeInsert;AfterUpdate;AfterInsert', 1.00));
            
            hed.TDTM_Global_API.setTdtmConfig(tokens);
            
            Contact c = new Contact();
            c.FirstName = 'Manny';
            c.LastName = 'Cott';
            c.Type__c = 'Student';
            
            insert c;
            
            c.Staging_Type__c = 'Alumni';
            
            update c;
            
            List<Contact> contacts = [SELECT Id, FirstName, LastName, Staging_Type__c, Type__c FROM Contact WHERE Id = :c.Id];
            
            
            for (Contact ct : contacts) {
                system.assertEquals('Alumni', ct.Type__c, 'The value of Staging_Type__c should be Alumni.');
                system.assertEquals(null, ct.Staging_Type__c, 'The value of Type__c should be ');
            }     
            
        }
}