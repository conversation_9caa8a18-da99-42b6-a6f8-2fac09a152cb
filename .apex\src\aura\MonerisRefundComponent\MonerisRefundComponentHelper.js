({
	getPaymentWrapper: function(component, event, helper) {
        //console.log('***In helper');
        var a= component.get("v.recordId");
        //console.log('***In helper'+a);
        //call apex class method
        var action = component.get('c.getPaymentDetails');
        action.setParams({
            pid : a//component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            //store state of response
            var state = response.getState();
            if (state === "SUCCESS") {
                console.log("resp--> "+response.getReturnValue());
                //set response value in wrapperList attribute on component.
                component.set('v.paymentCardWrapper', response.getReturnValue());
            }
        });
        $A.enqueueAction(action);
    },
    getRefundPayment: function(component, event, helper){
        var wrapper = component.get('v.paymentCardWrapper');
        
        var action = component.get('c.getRefundPayment');
        action.setParams({
            cardDetails : component.get("v.paymentCardWrapper"),
            pid : component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            var ReceiptId = response.getReturnValue().response.receipt.ReceiptId;
            console.log('ReceiptId: '+ReceiptId)
            if (ReceiptId == null || ReceiptId == "null") {
                this.showToastFun('error','Error Occured','Payment Refund unsuccessful due to: '+response.getReturnValue().response.receipt.Message);
                 
                /*component.find('notifLib').showNotice({
                    "variant": "error",
                    "header": "Error",
                     "message": 'Payment Refund unsuccessful due to: '+response.getReturnValue().response.receipt.Message,
                    closeCallback: function() {
                       $A.get("e.force:refreshView").fire();
                    }
                }); */
            }
            else{
                this.showToastFun('info','Status','Payment Refund: '+response.getReturnValue().response.receipt.ReferenceNum);
                window.location.reload();
            } 
        
        });
        $A.enqueueAction(action);
    },
    getRefundForCashCheck : function(component, event, helper){
        //getRefundPaymentcashCheck
        var wrapper = component.get('v.paymentCardWrapper');
        console.log('cash/check refund= '+wrapper);
        var action = component.get('c.getRefundPaymentcashCheck');
        action.setParams({
            cardDetails : component.get("v.paymentCardWrapper"),
            pid : component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log('cash/check refund= '+response.getReturnValue());
            if (state === "SUCCESS") {
                /*var toastmsg = component.get('c.showToastFun');
                toastmsg.setParams({
                    type : 'success',
                    title :'Refund Complete',
                    msg : response.getReturnValue()
                });
                component.find('notifLib').showNotice({
                    "variant": "Success",
                    "header": "Payment Refund Successful!",
                    "message": 'Payment Refund: '+response.getReturnValue(),
                    closeCallback: function() {
                       $A.get("e.force:refreshView").fire();
                    }
                });*/
                this.showToastFun('success','Refund Complete',response.getReturnValue());
                window.location.reload();
                //$A.enqueueAction(toastmsg);
            }
            else{
                this.showToastFun('Error','Refund Unsuccessful',response.getReturnValue());
            }
            
        });
        $A.enqueueAction(action);
    },
    showToastFun : function(type,title,msg){
        console.log('Inside show toast:'+ msg);
        var showToast = $A.get("e.force:showToast");
                showToast.setParams({ 
                    'type' : type,//"success",  // info, success, warning, error
                    'title' : title,//'Cart Updated!', 
                    'message' : msg//"Success" 
                }); 
                showToast.fire(); 
    },
})