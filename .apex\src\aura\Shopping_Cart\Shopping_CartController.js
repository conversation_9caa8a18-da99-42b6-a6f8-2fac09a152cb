({
    initData : function(component, event, helper) {
        //var Map //ShoppingMap
        
        var action = component.get("c.getEvents");
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state === "SUCCESS") {
                var custs = [];
                var mapVAlues = response.getReturnValue();
                for ( var key in mapVAlues ) {
                    custs.push({key:key,value:mapVAlues[key] });
                    console.log('key- '+ key);
                    //console.log('mapVAlues[key]- '+ mapVAlues[key].evt__Event_Fee__c.evt__Amount__c);
                    //console.log('mapVAlues[key]- '+ mapVAlues[key].evt__Event_Fee__r.evt__Amount__c);
                    //console.log('mapVAlues[key]- '+ mapVAlues[key].evt__Event_Fees__r.evt__Amount__c);
                }
                console.log('values--> '+custs);
                component.set("v.ItemMap", custs);
                //component.set(“v.ShowTest”, true);
            }
        });
        $A.enqueueAction(action);
    },
    addToCart: function(component, event, helper){
        component.set("v.showCart",true);
        var showCart = component.get("v.showCart");
        var childComponent = component.find('ShopCart');
        childComponent.initData(showCart);
        var mapLen = component.get("v.CartMap");
        
        console.log('len= '+mapLen.length);
        if(mapLen.length > 0){
            var cartmap = component.get("v.CartMap");
        }
        else{
            var cartmap = []; 
        }
        
        var Allitems = component.get('v.ItemMap');
        //var Cid = event;
        console.log('event= '+event.getSource().get("v.value").value.Id);
        for(var k in Allitems){
            console.log('k= '+k);
            console.log('Allitems[k]= '+Allitems[k].value.Name);
            if(event.getSource().get("v.value").value.Id == Allitems[k].value.Id)
            {
                cartmap.push(Allitems[k].value.Id,Allitems[k]);
            }
        }
        console.log('cartmap= '+cartmap);
        component.set("v.CartMap",cartmap);
        //value.evt__Event_Fees__r[0].evt__Amount__c
        var amt = event.getSource().get("v.value").value.evt__Event_Fees__r[0].evt__Amount__c;
        console.log('amt= '+event.getSource().get("v.value").value.evt__Event_Fees__r[0].evt__Amount__c);
        console.log(component.get("v.PaymentId"));
        if(component.get("v.PaymentId") == undefined || component.get("v.PaymentId") == null){
            var action = component.get("c.createPayment");
            action.setParams({
                event : event.getSource().get("v.value").value.Id,
                amount :amt
            });
            action.setCallback(this, function(response) {
                var state = response.getState();
                if (state === "SUCCESS") {
                    console.log("resp--> "+response.getReturnValue());
                    component.set("v.PaymentId",response.getReturnValue());
                }
                
            });
            console.log('paymentId-- '+component.get("v.PaymentId"));
            $A.enqueueAction(action);
        }
        
        
    },
    closeCart: function(component, event, helper){
        component.set("v.showCart",false);
    },
    onSubmit: function(component, event, helper){
        console.log('paymentId-- '+component.get("v.PaymentId"));
        var pid = component.get("v.PaymentId");
        window.open(
            'https://rotmancrm--ldev1--pymt.visualforce.com/apex/SiteCheckout?pid='+pid,
            '_blank' 
        );
        window.open(
            'https://rotmancrm--ldev1--c.visualforce.com/apex/CustomPaymentCheckout?pid='+pid,
            '_blank' 
        );
        console.log('nav-->'+ $A.get("e.force:navigateToComponent"));
       /* var evt = $A.get("e.force:navigateToComponent");
        evt.setParams({
            componentDef : "c:Payment_Terminal_Moneris",
            componentAttributes: {
                "recordId" : component.get("v.PaymentId")
            }
        });
        evt.fire();*/
        //https://rotmancrm--ldev1--c.visualforce.com/apex/CustomPaymentCheckout?pid
        //window.location.href = 'https://rotmancrm--ldev1--pymt.visualforce.com/apex/SiteCheckout?pid='+pid;
    },
    
})