/** 
* @description    Trigger helper class for date stamping to for duplicate/matching rule.
* <AUTHOR> 
* @version        1.0 
*/
global class TestDateStampDupRulesApex_TDTM extends hed.TDTM_Runnable{
	/**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Interaction records from trigger new 
     * @param oldList the list of Interaction records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Interactions 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
    	hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        //Stamp Date
        if ( triggerAction == hed.TDTM_Runnable.Action.BeforeInsert || triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate) {
        	for(hed__Test__c affltn : (List<hed__Test__c>) newList) {
                if(affltn.hed__Test_Date__c!=null)
                affltn.Test_Matching_Key__c = String.ValueOf(affltn.hed__Test_Date__c);
            }
        } 
        return dmlWrapper;
    }
}