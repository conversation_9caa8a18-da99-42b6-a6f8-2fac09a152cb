/**
* @description    Test class for InteractionEnggmntScoreAuto_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-13-04
* @modified 2020-13-04
*/
@isTest
public class InteractionEnggmntScoreAutoTest {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('InteractionEnggmntScoreAuto_TDTM', 'Staging_Table__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens); 
        
        //Create Program and Administrative Accounts 
         Account paTest = new Account(Name = 'TestPA', RecordTypeId = AccountService.AcademicProgramRTId ); 
         Account paTest2 = new Account(Name = 'TestPA2', RecordTypeId = AccountService.AcademicProgramRTId ); 
         Account adminAcct = new Account(Name = 'Admin1 Account', RecordTypeId = AccountService.AdminRTId); 
         insert new List<Account>{paTest, paTest2, adminAcct};
        
        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        insert c; 
        
        //Create Opportunities (with no PA)
        Opportunity testOppNoPA = new Opportunity(Name = 'Test Oppty No PA', AccountId = adminAcct.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', RecordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Degree Program Prospect').getRecordTypeId()); 
        Opportunity testOppWithPA = new Opportunity(Name = 'Test Oppty with PA', AccountId = adminAcct.id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', 
                                                        Program_Account__c = paTest2.Id, Assistant_Director__c = UserInfo.getUserId(), Recruitment_Officer__c = UserInfo.getUserId(), 
                                                         IsExcludedFromTerritory2Filter = true, RecordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByName().get('Degree Program Prospect').getRecordTypeId()); 
        insert new List<Opportunity>{testOppNoPA, testOppWithPA};
        
        //Create staging table records: 
        list<Staging_Table__c> stsToInsert = new list<Staging_Table__c>();
        
        Staging_Table__c st1 = new Staging_Table__c(
            Access__c            = 'Public',
            Opportunity__c      =   testOppNoPA.Id,
            Type__c = 'Email',
            Processing_Status__c = 'Unprocessed',
            First_Name__c = 'Joe',
            Last_Name__c = 'TestLast1',
            Personal_Email__c = '<EMAIL>'
        );
        stsToInsert.add(st1);
        
        Staging_Table__c st2 = new Staging_Table__c(
            Access__c            = 'Public',
            Opportunity__c      =   testOppNoPA.Id,
            Type__c = 'Email',
            Processing_Status__c = 'Unprocessed',
            First_Name__c = 'Joe',
            Last_Name__c = 'TestLast1',
            Personal_Email__c = '<EMAIL>'
        );
        stsToInsert.add(st2);
        
        Staging_Table__c st3 = new Staging_Table__c(
            Access__c            = 'Public',
            Opportunity__c      =   testOppNoPA.Id,
            Type__c = 'Event',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            Last_Name__c = 'TestLast2',
            Email__c = '<EMAIL>',
            Opportunity_Name__c = 'Case Subject'
        );
        stsToInsert.add(st3);
        
        Staging_Table__c st4 = new Staging_Table__c(
            Access__c            = 'Public',
            Opportunity__c      =   testOppWithPA.Id,
            Type__c = 'Event',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            Last_Name__c = 'TestLast3',
            Email__c = '<EMAIL>',
            Mailing_Country__c = 'USA'
        );
        stsToInsert.add(st4);

        Staging_Table__c st5 = new Staging_Table__c(
            Access__c            = 'Public',
            Opportunity__c      =   testOppWithPA.Id,
            Type__c = 'Email',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            Email__c = '<EMAIL>',
            Mailing_Country__c = 'USA'
        );
        stsToInsert.add(st5);
        
        database.insert(stsToInsert); 

    }
    @isTest 
    static void engagementScoreAutoest() {
        List<Staging_Table__c> tstLst = new List<Staging_Table__c>();
            tstLst = [Select Id,Mailing_Country__c from Staging_Table__c];
            for(Staging_Table__c tst : tstLst){
                tst.Mailing_Country__c = 'Canada';
            }
        Test.StartTest();
            update tstLst;
        Test.StopTest();
    }
}