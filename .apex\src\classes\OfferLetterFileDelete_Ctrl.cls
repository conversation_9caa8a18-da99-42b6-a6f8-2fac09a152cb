/**
 * Created by <PERSON><PERSON> on 2022-05-05.
 */

public without sharing class OfferLetterFileDelete_Ctrl {
    @AuraEnabled
    public static List<ContentDocument> fetchContentDocument(String appId){
        System.debug('appId: '+appId);
        Set<Id> cdlIds = new Set<Id>();
        List<ContentDocumentLink> cdl = [SELECT ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId=:appId];
        if (cdl.size()>0) {
            for(ContentDocumentLink cl: cdl){
                cdlIds.add(cl.ContentDocumentId);
            }
        }
        System.debug('List of cdl '+cdlIds);
        return [SELECT Id,Title,FileType,CreatedBy.Name,ContentModifiedDate FROM ContentDocument WHERE (Title LIKE 'Offer%') AND (Id IN: cdlIds) ORDER BY ContentModifiedDate ASC];
    }

    @AuraEnabled
    public static String delOfferLetter(String olRecId){
        try{
            delete [SELECT Id FROM ContentDocument WHERE Id=:olRecId];
            return 'SUCCESS';
        } catch(System.DmlException ex){
            throw new AuraHandledException(ex.getMessage());
        }
    }
}