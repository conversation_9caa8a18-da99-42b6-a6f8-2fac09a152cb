@isTest
public class InboundEmailRecieverTest {
    
    static testMethod void testApproveApplicationsMethod(){
        
        //Create test contact
        Contact c1 = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student', Email = '<EMAIL>'));
        insert c1;
        // create a new email and envelope object
        Messaging.InboundEmail email = new Messaging.InboundEmail() ;
        Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();
        
        // setup the data for the email
        email.subject = 'Create Contact';
        email.fromAddress = '<EMAIL>';
        email.plainTextBody = 'some body text';
        
        test.startTest();
            InboundEmailReciever testInbound=new InboundEmailReciever ();
            testInbound.handleInboundEmail(email, env);
        test.stopTest();
    }
}