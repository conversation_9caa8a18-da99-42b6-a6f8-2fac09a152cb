/**
* @description    Creates course connections for the specified course on application to an EP program to remove the need to handle this manually
* <AUTHOR>
* @version        1.0 
* @created 2020-06-01
* @modified 2020-06-03
*/
public class SyncCourseConnectionForEPApplication {
    public static final Set<String> appStatuses = new Set<String>{'In Progress','Submitted','Accepted Offer', 'Offer', 'Refused'}; 
    public static final Set<String> appliedStatuses = new Set<String>{'Submitted','Accepted Offer', 'Offer', 'Refused'}; 
	public static void syncCourseConnectionForEPApplication(List<hed__Application__c> applicationLst){
    	List<hed__Course_Enrollment__c> courseEnrollmntListToUpsert = new List<hed__Course_Enrollment__c>();

        //Course Offering Ids 
        Set<Id> cOffIds = new Set<Id>();
        for(hed__Application__c app : applicationLst){
            cOffIds.add(app.Course_Offering__C);
        }
        Map<Id, hed__Course_Offering__c> cOfferingParentIdToChildMap  = new Map<Id, hed__Course_Offering__c>([SELECT Id,Parent_Course__c,(Select Id from Child_Course_Offerings__r) from hed__Course_Offering__c where Id=:cOffIds]);

        
        
        for(hed__Application__c app : applicationLst){
            if(appStatuses.contains(app.hed__Application_Status__c)){
                courseEnrollmntListToUpsert.add(setCourseConnection(app, app.Course_Offering__c));
            } //Insert/Update course connections of child(ren) course offering records 
            if(cOfferingParentIdToChildMap.containsKey(app.Course_Offering__c) && cOfferingParentIdToChildMap.get(app.Course_Offering__c).Child_Course_Offerings__r.size() > 0){
                for(hed__Course_Offering__c cofferingChild:cOfferingParentIdToChildMap.get(app.Course_Offering__c).Child_Course_Offerings__r){
                    courseEnrollmntListToUpsert.add(setCourseConnection(app, cofferingChild.Id)); 
                }
            }
        }
        Schema.SObjectField f = hed__Course_Enrollment__c.Fields.Upsert_Key__c;
        Database.UpsertResult [] cr = Database.upsert(courseEnrollmntListToUpsert, f, true);	// updated the boolean value to "true", so that EP Course registration process does not proceed further in case of
        																						// conflicts and shows error on the community form page.
        System.Debug('<<courseEnrollmntListToUpsert>>'+courseEnrollmntListToUpsert);
    }

    public static List<hed__Course_Enrollment__c> deleteCourseConnectionForEPApplication(List<hed__Application__c> applicationLst){
    	//Application Ids
        Set<Id> appIds = new Set<Id>();
        for(hed__Application__c app : applicationLst){
        	appIds.add(app.Id);
        }
        List<hed__Course_Enrollment__c> cenrollmentTodelete = new List<hed__Course_Enrollment__c>();
        cenrollmentTodelete = [Select Id from hed__Course_Enrollment__c where Application__c =:appIds];
        return cenrollmentTodelete;
    }

    private static hed__Course_Enrollment__c setCourseConnection(hed__Application__c app, Id coId){
        hed__Course_Enrollment__c cenrollment = new hed__Course_Enrollment__c(Application__c = app.Id, 
                                                                                    hed__Course_Offering__c = coId, 
                                                                                    hed__Contact__c = app.hed__Applicant__c);
        if(app.hed__Application_Status__c == 'In Progress'){
            cenrollment.Upsert_Key__c = String.ValueOf(app.hed__Applicant__c)+String.ValueOf(coId);
        }else if(appliedStatuses.contains(app.hed__Application_Status__c)){
            //re-set upsert key: 
            cenrollment.Upsert_Key__c = String.ValueOf(app.hed__Applicant__c)+String.ValueOf(coId) + '_APPLIED';
            if(app.hed__Application_Status__c =='Accepted Offer' ){
                cenrollment.hed__Status__c = 'Current';
            }else if(app.hed__Application_Status__c=='Offer' || app.hed__Application_Status__c=='Submitted'){
                cenrollment.hed__Status__c = 'Held';
            }else if(app.hed__Application_Status__c=='Refused'){
                cenrollment.hed__Status__c = 'Cancelled';
            }
        }
        return cenrollment; 
    }
}