/**
 * @description Batch job to merge Course Connection PDFs into a single PDF.
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-14
 * @modified 2020-08-17
 */
global class EpCoursePDFLightningBatch implements Database.Batchable<sObject>,Database.AllowsCallouts {
    public String cOfferingIdToQuery;
    global EpCoursePDFLightningBatch (String cOfferingId){
		this.cOfferingIdToQuery = cOfferingId;
    }
	/**
     * @description Start method to query all Course Offerings 
     * @return Application's List 
     * @param   
     * @param  
     */
    global Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id,Name FROM hed__Course_Offering__c where Id=:cOfferingIdToQuery Order By name ASC';
        return Database.getQueryLocator(query);
    }
   /**
     * @description Execute method to query all Course Connections to make conga callout and merge pdfs
     * @return  
     * @param Standard Batchable Context 
     * @param  Standard start query return
     */
    global void execute(Database.BatchableContext BC, List<hed__Course_Offering__c> scope) {
        List<Id> linkedEntityIdsList = new List<Id>();
        List<hed__Course_Offering__c> cOfferingWithCourseConnectionsrecords = [SELECT Id,Name,(SELECT Id FROM hed__Course_Enrollment__r) FROM hed__Course_Offering__c WHERE Id =: scope];
        for(hed__Course_Offering__c coffering : cOfferingWithCourseConnectionsrecords){
            if(coffering.hed__Course_Enrollment__r.size()>0){
                for(hed__Course_Enrollment__c aci : coffering.hed__Course_Enrollment__r){
                    linkedEntityIdsList.add(aci.Id);
                }
            }
        }
        String documentIds = '';
        List<ContentDocumentLink> cdlnkLst = [SELECT ContentDocumentId  from ContentDocumentLink where LinkedEntityId=:linkedEntityIdsList AND ContentDocument.FileExtension = 'pdf'];
        System.Debug('<<<<cdlnkLst>>>>'+cdlnkLst);
        for(ContentDocumentLink cdlnk : cdlnkLst){
            if(documentIds==''){
                documentIds=cdlnk.ContentDocumentId;
            } else {
                documentIds+=','+cdlnk.ContentDocumentId;
            }
        }  
        System.Debug('<<<<documentIds>>>>'+documentIds);
        String serverURL = Url.getSalesforceBaseUrl().getHost();
        serverURL = serverURL.replace('apxtconga4.','');
        serverURL = serverURL.replace('visual.force', 'salesforce');
        serverURL = serverURL.replace('c.', '');
        serverUrl = 'https://' + serverURL + '/services/Soap/u/29.0/' + UserInfo.getOrganizationId();
        String sessionId = UserInfo.getSessionId();
        Http h = new Http();
        HttpRequest httpReq = new HttpRequest();  
        httpReq.setMethod('GET');
        httpReq.setTImeout(120000);
        String endpoint;
        if(documentIds!=null && documentIds!=''){
        	endpoint = 'https://composer.congamerge.com/composer8/index.html?SessionId='+sessionId+'&ServerUrl='+serverUrl+'&QVar0ID='+documentIds+'&TemplateID='+documentIds+'&id='+cOfferingIdToQuery+'&DefaultPDF=1'+'&APIMode=1';
            httpReq.setEndpoint(endpoint);
            HttpResponse httpRes = new HttpResponse();
            if(!Test.isRunningTest())
            	httpRes = h.send(httpReq);
            else{
                List<Attachment> att = [SELECT Id, Name from Attachment LIMIT 1];
                // Create a fake response
                if(att.size()>0)
                    httpRes.setBody(att[0].Id);
                	httpRes.setStatusCode(200);
            }
            System.Debug('<<<<ResponseStatusCode>>>>'+httpRes.getStatusCode());
            System.Debug('<<<<ResponseBody>>>>'+httpRes.getBody());
            if(httpRes.getStatusCode()==200){
                Attachment attach;
                //Get attachment
                if(httpRes.getBody()!=null && httpRes.getBody()!='')
                attach = [SELECT Id, Name, Body, ContentType, ParentId,OwnerId From Attachment where Id=:httpRes.getBody() LIMIT 1];
                 
                //Insert ContentVersion
                ContentVersion cVersion = new ContentVersion();
                cVersion.ContentLocation = 'S'; //S-Document is in Salesforce. E-Document is outside of Salesforce. L-Document is on a Social Netork.
                cVersion.PathOnClient = attach.Name;//File name with extention
                cVersion.Origin = 'H';//C-Content Origin. H-Chatter Origin.
                cVersion.OwnerId = attach.OwnerId;//Owner of the file
                cVersion.Title = attach.Name;//Name of the file
                cVersion.VersionData = attach.Body;//File content
                Insert cVersion;
                 
                //After saved the Content Verison, get the ContentDocumentId
                Id conDocument = [SELECT ContentDocumentId FROM ContentVersion WHERE Id =:cVersion.Id].ContentDocumentId;
                 
                //Insert ContentDocumentLink
                ContentDocumentLink cDocLink = new ContentDocumentLink();
                cDocLink.ContentDocumentId = conDocument;//Add ContentDocumentId
                cDocLink.LinkedEntityId = attach.ParentId;//Add attachment parentId
                cDocLink.ShareType = 'I';//V - Viewer permission. C - Collaborator permission. I - Inferred permission.
                cDocLink.Visibility = 'InternalUsers';//AllUsers, InternalUsers, SharedUsers
                Insert cDocLink;
            }
        }
    }   
    /**
     * @description Finish Method to perform operation after batch run
     * @return  
     * @param Standard Batchable Context 
     */
    global void finish(Database.BatchableContext BC) {

    }
}