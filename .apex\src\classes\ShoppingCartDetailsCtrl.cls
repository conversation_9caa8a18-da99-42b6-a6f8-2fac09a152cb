public without sharing class ShoppingCartDetailsCtrl {

	public String eventImg {get; set;}
	public String eventName {get; set;}
	public String isWebUrl {get; set;}
	public String evenWebLink {get; set;}
	public String supportEmail {get; set;}
	public String eventDateTime {get; set;}
	public String buyerName {get; set;}
	public String totalPrice {get; set;}
	public String buyTime {get; set;}
	public String paymentInfo {get; set;}
	public String totalTicket {get; set;}
	public List<attendeeWrapperClass> attendees {get; set;}

	public ShoppingCartDetailsCtrl() {
		List<String> attCancelled = new List<String>();
		Boolean isCancellAction = false;
		// Get the URL parameters
		String parameterValue1 = ApexPages.currentPage().getParameters().get('shoppingCartId');
		String parameterValue2 = ApexPages.currentPage().getParameters().get('attIds');
		if (parameterValue2 != null ) {
			attCancelled = parameterValue2.split(',');
			isCancellAction = true;
		}else{
			isCancellAction = false;
		}
		System.debug('==parameterValue1: ' + parameterValue1);
		System.debug('==parameterValue2: ' + parameterValue2);

		List<Shopping_Cart_Item_Details__c> orders = getOrderInfo(parameterValue1, isCancellAction);
		System.debug('==orders: ' + orders);
		attendees = getAttendees(orders, attCancelled);
		Map<String, String> eventInfo = getEventInfo(orders);
		System.debug('==eventInfo: ' + eventInfo);
	}

	public List<Shopping_Cart_Item_Details__c> getOrderInfo(Id scId, Boolean isCancellAction){
		List<Shopping_Cart_Item_Details__c> orders = new List<Shopping_Cart_Item_Details__c>();

		if (isCancellAction){
			orders = [SELECT Id, Name, Shopping_Cart_Item__r.Name, SC_event__r.Name, SC_event__r.evt__Web_Meeting_Join_URL__c, SC_event__r.Thumbnail_Image__c, SC_event__r.Event_Date_Time__c, SC_event__r.Registration_Confirmation__c, SC_event__r.Support_Email__c, Contact__r.Name, Contact__r.Email, Item_Total_Amount__c, CreatedDate, Attendee__c, Attendee__r.Check_In_QR_Code__c, Attendee__r.Check_In_QR_Code_URL__c, Attendee__r.evt__Reg_First_Name__c, Attendee__r.evt__Reg_Last_Name__c, Attendee__r.evt__Reg_Email__c, Attendee__r.CreatedDate, Attendee__r.evt__Contact__c, Attendee__r.Attendee_Name__c, Attendee__r.evt__Event__c, Attendee__r.evt__Event_Fee__r.Name, Attendee__r.Customize_FieldValue_1__c, Attendee__r.Customize_FieldValue_2__c, Attendee__r.Customize_FieldValue_3__c, Attendee__r.Customize_FieldValue_4__c, Attendee__r.Customize_FieldValue_5__c, Attendee__r.evt__Mailing_Street__c , Attendee__r.evt__Mailing_City__c , Attendee__r.evt__Mailing_State__c , Attendee__r.evt__Mailing_Country__c , Attendee__r.evt__Mailing_Postal_Code__c , Attendee__r.evt__Invitation_Status__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Payment_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Last_4_Digits__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Card_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.Payment_Date__c FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :scId AND Void_ticket__c = true];
		}else{
			orders = [SELECT Id, Name, Shopping_Cart_Item__r.Name, SC_event__r.Name, SC_event__r.evt__Web_Meeting_Join_URL__c, SC_event__r.Thumbnail_Image__c, SC_event__r.Event_Date_Time__c, SC_event__r.Registration_Confirmation__c, SC_event__r.Support_Email__c, Contact__r.Name, Contact__r.Email, Item_Total_Amount__c, CreatedDate, Attendee__c, Attendee__r.Check_In_QR_Code__c, Attendee__r.Check_In_QR_Code_URL__c, Attendee__r.evt__Reg_First_Name__c, Attendee__r.evt__Reg_Last_Name__c, Attendee__r.evt__Reg_Email__c, Attendee__r.CreatedDate, Attendee__r.evt__Contact__c, Attendee__r.Attendee_Name__c, Attendee__r.evt__Event__c, Attendee__r.evt__Event_Fee__r.Name, Attendee__r.Customize_FieldValue_1__c, Attendee__r.Customize_FieldValue_2__c, Attendee__r.Customize_FieldValue_3__c, Attendee__r.Customize_FieldValue_4__c, Attendee__r.Customize_FieldValue_5__c, Attendee__r.evt__Mailing_Street__c , Attendee__r.evt__Mailing_City__c , Attendee__r.evt__Mailing_State__c , Attendee__r.evt__Mailing_Country__c , Attendee__r.evt__Mailing_Postal_Code__c , Attendee__r.evt__Invitation_Status__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Payment_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Last_4_Digits__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Card_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.Payment_Date__c FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :scId AND Void_ticket__c = false];
		}

		return orders;
	}

	public Map<String, String> getEventInfo(List<Shopping_Cart_Item_Details__c> myOrders){
		Map<String, String> eventInfo = new Map<String, String>();
		Decimal total = 0;

		if (myOrders.size() > 0) {
			Shopping_Cart_Item_Details__c order = myOrders[0];
			eventImg = order.SC_event__r.Thumbnail_Image__c;
			evenWebLink = order.SC_event__r.evt__Web_Meeting_Join_URL__c;
			if (evenWebLink != null && evenWebLink != '') {
				if ((Boolean)EmailWithAttachmentController.isValidURL(evenWebLink)){
					isWebUrl = 'true';
				}else{
					isWebUrl = 'false';
				}
			} else{
				isWebUrl = 'false';
			}
			eventName = order.SC_event__r.Name;
			eventDateTime = order.SC_event__r.Event_Date_Time__c;
			buyerName = order.Contact__r.Name;
			buyTime = order.CreatedDate.format('MM/dd/yyyy HH:mm');
			supportEmail = order.SC_event__r.Support_Email__c;
			String paymentType = order.Shopping_Cart_Item__r.pymt__Payment__c != null ? order.Shopping_Cart_Item__r.pymt__Payment__r.pymt__Payment_Type__c : '';
			String paymentCardType = order.Shopping_Cart_Item__r.pymt__Payment__c != null ? order.Shopping_Cart_Item__r.pymt__Payment__r.pymt__Card_Type__c : '';
			String last4Digits = order.Shopping_Cart_Item__r.pymt__Payment__c != null ? order.Shopping_Cart_Item__r.pymt__Payment__r.pymt__Last_4_Digits__c : '';
			System.debug('paymentType: ' + paymentType);
			System.debug('paymentCardType: ' + paymentCardType);
			System.debug('last4Digits: ' + last4Digits);
			if (paymentType == null || paymentType == '') {
				paymentInfo = '';
			}else{
				if (paymentType == 'Credit Card'){
					if (last4Digits == null || last4Digits == '') {
						paymentInfo = 'Paid by ' + paymentType;
					}else{
						paymentInfo = 'Paid by ' + paymentType + ' Last 4 digits: ' + last4Digits;
					}
				}else{
					paymentInfo = 'Paid by ' + paymentType;
				}
			}

			for (Shopping_Cart_Item_Details__c sc : myorders) {
				total += sc.Item_Total_Amount__c;
			}
			totalTicket = String.valueOf(myOrders.size());
		}

		if (eventImg == null || eventImg == '' || !eventImg.contains('<img')){
			eventImg = 'https://rotmancrm--uat.sandbox.my.site.com/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
		}else{
			Pattern imgSrcPattern = Pattern.compile('<img[^>]*src="([^"]*)');
			Matcher matcher = imgSrcPattern.matcher(eventImg);
			while (matcher.find()) {
				eventImg = matcher.group(1);
			}
			eventImg = eventImg.replaceAll('amp;', '');
		}
		if (total > 0){
			totalPrice = '$'+String.valueOf(total.setScale(2));
		} else {
			totalPrice = 'Free';
			paymentInfo = 'Free Order';
		}

		eventInfo.put('eventImg', eventImg);
		eventInfo.put('eventName', eventName);
		eventInfo.put('eventDateTime', eventDateTime);
		eventInfo.put('buyerName', buyerName);
		eventInfo.put('totalPrice', totalPrice);
		eventInfo.put('buyTime', buyTime);
		eventInfo.put('paymentInfo', paymentInfo);
		eventInfo.put('totalTicket', totalTicket);
		eventInfo.put('supportEmail', supportEmail);

		return eventInfo;
	}

	public List<attendeeWrapperClass> getAttendees(List<Shopping_Cart_Item_Details__c> myOrders, List<String> lstAttendeeIds){
		List<attendeeWrapperClass> attendees = new List<attendeeWrapperClass>();

		Integer i = 1;
		Decimal total = 0;
		for (Shopping_Cart_Item_Details__c sc : myOrders) {
			 if (lstAttendeeIds.size()> 0){
				 if (lstAttendeeIds.contains(sc.Attendee__c)) {
					 attendeeWrapperClass attendee = new attendeeWrapperClass();
					 attendee.num = String.valueOf(i);
					 attendee.barCode = sc.Attendee__r.Check_In_QR_Code_URL__c == null ? '' : sc.Attendee__r.Check_In_QR_Code_URL__c;
					 attendee.attendeeName = sc.Attendee__r.evt__Reg_First_Name__c + ' ' + sc.Attendee__r.evt__Reg_Last_Name__c;
					 attendee.attendeeEmail = sc.Attendee__r.evt__Reg_Email__c;
					 attendee.ticketType = sc.Attendee__r.evt__Event_Fee__r.Name;
					 attendee.ticketStatus = sc.Attendee__r.evt__Invitation_Status__c;
					 total += sc.Item_Total_Amount__c;
					 if (sc.Item_Total_Amount__c > 0){
						 attendee.ticketPrice = '$'+String.valueOf(sc.Item_Total_Amount__c.setScale(2));
					 } else {
						 attendee.ticketPrice = 'Free';
					 }
					 attendees.add(attendee);
				 }
			 }else{
				 attendeeWrapperClass attendee = new attendeeWrapperClass();
				 attendee.num = String.valueOf(i);
				 attendee.barCode = sc.Attendee__r.Check_In_QR_Code_URL__c == null ? '' : sc.Attendee__r.Check_In_QR_Code_URL__c;
				 attendee.attendeeName = sc.Attendee__r.evt__Reg_First_Name__c + ' ' + sc.Attendee__r.evt__Reg_Last_Name__c;
				 attendee.attendeeEmail = sc.Attendee__r.evt__Reg_Email__c;
				 attendee.ticketType = sc.Attendee__r.evt__Event_Fee__r.Name;
				 attendee.ticketStatus = sc.Attendee__r.evt__Invitation_Status__c;
				 total += sc.Item_Total_Amount__c;
				 if (sc.Item_Total_Amount__c > 0){
					 attendee.ticketPrice = '$'+String.valueOf(sc.Item_Total_Amount__c.setScale(2));
				 } else {
					 attendee.ticketPrice = 'Free';
				 }
				 attendees.add(attendee);
			 }

			i++;
		}

		System.debug('@@==attendees: ' + attendees);
		return attendees;
	}

	public class attendeeWrapperClass {
		public String Id{get; set;}
		public string num{get; set;}
		public string barCode{get; set;}
		public string attendeeName{get; set;}
		public string attendeeEmail{get; set;}
		public string ticketType{get; set;}
		public string ticketCustomText{get; set;}
		public string ticketPrice{get; set;}
		public string ticketStatus{get; set;}
	}
}