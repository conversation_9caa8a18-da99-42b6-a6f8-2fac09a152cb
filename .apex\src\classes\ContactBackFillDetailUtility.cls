public with sharing class ContactBackFillDetailUtility {
    
    public static Map<Id, hed__Application__c> getTheMostRecentRecord(Map<Id, List<hed__Application__c>> mapOfIdToListOfApplication) {
        Map<Id, hed__Application__c> mapOfContactIdToAppicable = new Map<Id, hed__Application__c>();
        for(Id contactId : mapOfIdToListOfApplication.keySet()) {
            if(mapOfIdToListOfApplication.get(contactId).size() > 0) {
                mapOfContactIdToAppicable.put(contactId,getApplicableRecord(mapOfIdToListOfApplication.get(contactId)));
            }
        }
        return mapOfContactIdToAppicable;
    }

    public static hed__Application__c getApplicableRecord(List<hed__Application__c> listOfApplication) {
        Boolean isApplicationDateNull = false;
        DateTime maxDate = listOfApplication[0].hed__Application_Date__c;
        hed__Application__c recordWithMaxApplicationDate = listOfApplication[0];
        for (Integer i=1; i < listOfApplication.size(); i++) {
            if (listOfApplication[i].hed__Application_Date__c > maxDate) {
                recordWithMaxApplicationDate = listOfApplication[i];   // new maximum
            }
        }
        if(recordWithMaxApplicationDate.hed__Application_Date__c == null) {
            isApplicationDateNull = true;
        }

        if(isApplicationDateNull) {
            DateTime maximumDate = listOfApplication[0].LastModifiedDate;
            for (Integer i=1; i < listOfApplication.size(); i++) {
                if (listOfApplication[i].LastModifiedDate > maximumDate) {
                    recordWithMaxApplicationDate = listOfApplication[i];   // new maximum
                }
            }
        }
        return recordWithMaxApplicationDate;
    }
}