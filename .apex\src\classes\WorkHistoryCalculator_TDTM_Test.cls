/**
* @description    Test class for WorkHistoryCalculator_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-03-06
* @modified 2020-04-27
*/
@isTest
public class WorkHistoryCalculator_TDTM_Test {

    static final Date firstWHStartDate =  Date.newInstance(2015, 1, 1); 
    static final Date firstWHEndDate = Date.newInstance(2016, 1, 1); 
    static final Date secondWHStartDate = Date.newInstance(2016, 2, 1); 
    static final Date secondWHEndDate = Date.newInstance(2017, 2, 1); 
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('WorkHistoryCalculator_TDTM', 'Affiliation__c', 'AfterInsert;AfterUpdate;AfterDelete', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        Contact mgtC = new Contact(FirstName = 'TestM', LastName = 'StudentM'); 
        insert new List<Contact>{c, mgtC}; 

        //Create related work history records: 
        List<hed__Affiliation__c> workHistoriesToInsert = new List<hed__Affiliation__c>(); 
        workHistoriesToInsert.add(new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = firstWHStartDate, hed__EndDate__c = firstWHEndDate)); 
        workHistoriesToInsert.add(new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = secondWHStartDate, hed__EndDate__c = secondWHEndDate)); 
        workHistoriesToInsert.add(new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = mgtC.Id, Qualifies_As_Management_Experience__c = true, hed__StartDate__c = firstWHStartDate, hed__EndDate__c = firstWHEndDate)); 
        insert workHistoriesToInsert; 

    }
    /**
     * @description Assert that when a new work history record and a new management history record with start and end dates are inserted, 
     * only previous_years_work_experience__c and previous_years_Management_experience__c are updated 
     */
    @isTest
    public static void testInsertWorkAndMgtHistories(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student' LIMIT 1]; 
        //New work history record & New Management History record to insert: 
        hed__Affiliation__c newWorkHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = Date.newInstance(2018, 2, 1), hed__EndDate__c = Date.newInstance(2019, 2, 1)); 
        hed__Affiliation__c newMgtHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, Qualifies_As_Management_Experience__c = true, hed__StartDate__c = Date.newInstance(2019, 2, 1), hed__EndDate__c = Date.newInstance(2020, 2, 1)); 
        //insert new work history record: 
        Test.startTest(); 
            insert new List<hed__Affiliation__c>{newWorkHistory,newMgtHistory}; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 

        //Assert previous years of management experience and work experience has been updated correctly: 
        Decimal expectedMHValue = (newMgtHistory.hed__StartDate__c.monthsBetween(newMgtHistory.hed__EndDate__c)/12); 
        Decimal expectedWEValue = c.Previous_Years_Work_Experience__c + (newWorkHistory.hed__StartDate__c.monthsBetween(newWorkHistory.hed__EndDate__c)/12) + expectedMHValue; 
        SYSTEM.assertEquals(expectedMHValue.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Management_Experience__c); 
        SYSTEM.assertEquals(expectedWEValue.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Work_Experience__c); 

        //Assert that  current management effective start date && current work effective start date was not updated: 
        SYSTEM.assertEquals(c.Current_Management_Effective_Start_Date__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 
        SYSTEM.assertEquals(c.Current_Work_Effective_Start_Date__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 

    }
    /**
     * @description Assert that when a new  current work history record is inserted (a record with a start date but no end date), 
     * only current_work_effective_start_date__c is updated 
     */
    @isTest 
    public static void testInsertOngoingWorkHistory(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student' LIMIT 1]; 

        //New work history record to insert: 
        hed__Affiliation__c newWorkHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = Date.newInstance(2018, 2, 1)); 
        
        //insert new work history record: 
        Test.startTest(); 
            insert newWorkHistory; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c,Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 


        //Assert previous years of work experience was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Work_Experience__c, cAfterInsert.Previous_Years_Work_Experience__c); 
        //Assert Previous_Years_Management_Experience__c was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Management_Experience__c, cAfterInsert.Previous_Years_Management_Experience__c); 
        //Assert Current_Management_Effective_Start_Date__c was not updated: 
        SYSTEM.assertEquals(c.Current_Management_Effective_Start_Date__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 

        //Assert that current work effective start date was updated with the correct value: 
        SYSTEM.assertNotEquals(c.Current_Work_Effective_Start_Date__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 
        SYSTEM.assertEquals(cAfterInsert.Current_Work_Effective_Start_Date__c, newWorkHistory.hed__StartDate__c); 
    }

    /**
    * @description Assert that when a new  current mgmt history record is inserted (a record with a start date but no end date), 
    * Current_Work_Start_Date__c and Current_Management_Effective_Start_Date__c are updated 
    */
    @isTest 
    public static void testInsertOngoingMgtHistory(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student'LIMIT 1]; 

        //New Mgt history record to insert: 
        hed__Affiliation__c newMgtHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, Qualifies_As_Management_Experience__c = true, hed__StartDate__c = Date.newInstance(2018, 2, 1)); 
        
        //insert new mgt history record: 
        Test.startTest(); 
            insert newMgtHistory; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c,Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 


        //Assert previous years of work experience was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Work_Experience__c, cAfterInsert.Previous_Years_Work_Experience__c); 
        //Assert Previous_Years_Management_Experience__c was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Management_Experience__c, cAfterInsert.Previous_Years_Management_Experience__c); 


        //Assert that current work effective start date && current management effective start date was updated with the correct value: 
        SYSTEM.assertNotEquals(c.Current_Work_Effective_Start_Date__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 
        SYSTEM.assertEquals(cAfterInsert.Current_Work_Effective_Start_Date__c, newMgtHistory.hed__StartDate__c); 
        SYSTEM.assertNotEquals(c.Current_Management_Effective_Start_Date__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 
        SYSTEM.assertEquals(cAfterInsert.Current_Management_Effective_Start_Date__c, newMgtHistory.hed__StartDate__c); 
    }

    /**
    * @description Assert that when a new work history record is inserted which overlaps with an existing work history record, 
    *  previous_years_work_experience__c is updated correctly
    */
    @isTest  
    public static void testInsertOverLappingWorkHistory(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student' LIMIT 1]; 

        //New work history record to insert: 
        hed__Affiliation__c newWorkHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = Date.newInstance(2016, 6, 1), hed__EndDate__c = Date.newInstance(2018, 2, 1)); 
        
        //insert new work history record: 
        Test.startTest(); 
            insert newWorkHistory; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 

        //Assert previous years of work experience was updated: 
        Decimal expectedYears = (firstWHStartDate.monthsBetween(firstWHEndDate) + secondWHStartDate.monthsBetween(newWorkHistory.hed__EndDate__c))/12; 
        SYSTEM.assertEquals(expectedYears.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Work_Experience__c); 

        //Assert that current work effective start date was not updated: 
        SYSTEM.assertEquals(c.Current_Work_Effective_Start_Date__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 
        //Assert Previous_Years_Management_Experience__c was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Management_Experience__c, cAfterInsert.Previous_Years_Management_Experience__c); 
        //Assert Current_Management_Effective_Start_Date__c was not updated: 
        SYSTEM.assertEquals(c.Current_Management_Effective_Start_Date__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 
    }

    /**
    * @description Assert that when a new mgt history record is inserted which overlaps with an existing work history record, 
    *  previous_years_work_experience__c and previous_years_management_experience__c are updated correctly
    */
    @isTest  
    public static void testInsertOverLappingMgtHistory(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student' LIMIT 1]; 

        //New work history record to insert: 
        hed__Affiliation__c newMgtHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, Qualifies_As_Management_Experience__c = true, hed__StartDate__c = Date.newInstance(2016, 6, 1), hed__EndDate__c = Date.newInstance(2018, 2, 1)); 
        
        //insert new work history record: 
        Test.startTest(); 
            insert newMgtHistory; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 

        //Assert previous years of work experience was updated: 
        Decimal expectedYears = (firstWHStartDate.monthsBetween(firstWHEndDate) + secondWHStartDate.monthsBetween(newMgtHistory.hed__EndDate__c))/12; 
        SYSTEM.assertEquals(expectedYears.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Work_Experience__c); 
        //Assert previous years of management experience was updated: 
        Decimal expectedMgtYears = (Decimal)(newMgtHistory.hed__StartDate__c.monthsBetween(newMgtHistory.hed__EndDate__c))/12; 
        System.assertEquals(expectedMgtYears.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Management_Experience__c); 

        //Assert that current work effective start date was not updated: 
        SYSTEM.assertEquals(c.Current_Work_Effective_Start_Date__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 
        //Assert Current_Management_Effective_Start_Date__c was not updated: 
        SYSTEM.assertEquals(c.Current_Management_Effective_Start_Date__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 
    }


    /**
    * @description Assert that when new mgt history and work history records are inserted, whoses start and end dates are in the same time frame
    *  as another record's start and end dates, the previous work experience field is not updated, but previous management experience field is updated
    */
    @isTest  
    public static void testSameRangeMgtandWorkHistory(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student' LIMIT 1]; 

        //New work history records to insert: 
        hed__Affiliation__c mhRecord = new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, Qualifies_As_Management_Experience__c = true, hed__Contact__c = c.Id, hed__StartDate__c = firstWHStartDate.addMonths(3), hed__EndDate__c = firstWHEndDate); 
        hed__Affiliation__c whRecord = new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = secondWHStartDate, hed__EndDate__c = firstWHEndDate.addMonths(8)); 

        //insert new work history record: 
        Test.startTest(); 
            insert new List<hed__Affiliation__c>{mhRecord, whRecord}; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c,  Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 

        //Assert previous years of work experience was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Work_Experience__c, cAfterInsert.Previous_Years_Work_Experience__c); 
        //Assert that previous years of management experience was updated: 
        Decimal expectedMgtYears = (Decimal)(mhRecord.hed__StartDate__c.monthsBetween(mhRecord.hed__EndDate__c))/12; 
 
        System.assertEquals(expectedMgtYears.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Management_Experience__c); 

        //Assert that current work effective start date was not updated: 
        SYSTEM.assertEquals(c.Current_Work_Effective_Start_Date__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 
        //Assert that current management effective start date was not updated: 
        SYSTEM.assertEquals(c.Current_Management_Effective_Start_Date__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 
    }

    /**
    * @description Assert that when a new current work history record is inserted (start date but no end date) which starts in the time frame
    * of a previous work history, previous_years_work_experience__c and current_work_effective_start_date__c are updated, 
    */
    @isTest  
    public static void testCurrentWHInRange(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c,Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student' LIMIT 1]; 

        //New work history record to insert: 
        hed__Affiliation__c newWorkHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c =firstWHStartDate.addMonths(6)); 
        
        //insert new work history record: 
        Test.startTest(); 
            insert newWorkHistory; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 

        //Assert previous years of work experience was updated:
        Decimal calculated = firstWHStartDate.monthsBetween(newWorkHistory.hed__StartDate__c) ; 
        Decimal expectedYears = (Decimal) firstWHStartDate.monthsBetween(newWorkHistory.hed__StartDate__c)/12 ; 
        SYSTEM.assertEquals(expectedYears.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Work_Experience__c, calculated + ' ' + expectedYears); 

        //Assert that current work effective start date was updated: 
        SYSTEM.assertEquals(newWorkHistory.hed__StartDate__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 
        //Assert that previous management experience was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Management_Experience__c, cAfterInsert.Previous_Years_Management_Experience__c); 
        //Assert that current management effective start date was not updated: 
        SYSTEM.assertEquals(c.Current_Management_Effective_Start_Date__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 
    }

    /**
    * @description Assert that when a new current management history record is inserted (start date but no end date) which starts in the time frame
    * of a previous work history, previous_years_work_experience__c, current_management_effective_start_date__c and current_work_effective_start_date__c are updated, 
    */
    @isTest  
    public static void testCurrentMHInRange(){
        //query for test contact record: 
        Contact c = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c,Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE LastName = 'Student' LIMIT 1]; 

        //New management history record to insert: 
        hed__Affiliation__c newWorkHistory =  new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, Qualifies_as_Management_Experience__c=true, hed__Contact__c = c.Id, hed__StartDate__c =firstWHStartDate.addMonths(6)); 
        
        //insert new work history record: 
        Test.startTest(); 
            insert newWorkHistory; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 

        //Assert previous years of work experience was updated:
        Decimal calculated = firstWHStartDate.monthsBetween(newWorkHistory.hed__StartDate__c) ; 
        Decimal expectedYears = (Decimal) firstWHStartDate.monthsBetween(newWorkHistory.hed__StartDate__c)/12 ; 
        SYSTEM.assertEquals(expectedYears.setScale(1, RoundingMode.HALF_UP), cAfterInsert.Previous_Years_Work_Experience__c, calculated + ' ' + expectedYears); 

        //Assert that current work effective start date was updated: 
        SYSTEM.assertEquals(newWorkHistory.hed__StartDate__c, cAfterInsert.Current_Work_Effective_Start_Date__c); 
        //Assert that Previous years of management experience was not updated: 
        SYSTEM.assertEquals(c.Previous_Years_Management_Experience__c, cAfterInsert.Previous_Years_Management_Experience__c); 
        //Assert that current management effective start date was updated: 
        SYSTEM.assertEquals(newWorkHistory.hed__StartDate__c, cAfterInsert.Current_Management_Effective_Start_Date__c); 
    }

    /**
    * @description Assert that when a work history record is deleted, 
    * previous_years_work_experience__c is updated
    */
    @isTest  
    public static void testDeleteWH(){
        //Query for an existing work history record with start date = firstWHStartDate
        hed__Affiliation__c workHistoryToDelete = [SELECT ID, hed__Contact__c, hed__Contact__r.Previous_Years_Work_Experience__c, hed__Contact__r.Previous_Years_Management_Experience__c, hed__Contact__r.Current_Management_Effective_Start_Date__c, hed__Contact__r.Current_Work_Effective_Start_Date__c FROM hed__Affiliation__c 
                                WHERE hed__StartDate__c = :firstWHStartDate AND hed__EndDate__c != null AND recordTypeId = :AffiliationService.workHistoryRTId AND Qualifies_As_Management_Experience__c != true LIMIT 1]; 
        Test.startTest(); 
            delete workHistoryToDelete; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterDelete = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :workHistoryToDelete.hed__Contact__c]; 


        //Assert that previous_years_work_experience__c has been re-calculated: 
        SYSTEM.assertNotEquals(workHistoryToDelete.hed__Contact__r.Previous_Years_Work_Experience__c, cAfterDelete.Previous_Years_Work_Experience__c); 
        Decimal expectedYears = (Decimal) secondWHStartDate.monthsBetween(secondWHEndDate)/12; 
        SYSTEM.AssertEquals(expectedYears.setScale(1, RoundingMode.HALF_UP), cAfterDelete.Previous_Years_Work_Experience__c);
        //Assert that  current management effective start date && current work effective start date were not updated: 
        SYSTEM.assertEquals(workHistoryToDelete.hed__Contact__r.Current_Management_Effective_Start_Date__c, cAfterDelete.Current_Management_Effective_Start_Date__c); 
        SYSTEM.assertEquals(workHistoryToDelete.hed__Contact__r.Current_Work_Effective_Start_Date__c, cAfterDelete.Current_Work_Effective_Start_Date__c); 
    }

    /**
    * @description Assert that when a work history record is updated, 
    * previous_years_work_experience__c is updated
    */
    @isTest  
    public static void testUpdateOverlap(){
        //Query for an existing work history record with start date = secondWHStartDate
        hed__Affiliation__c workHistoryToUpdate = [SELECT ID, hed__Contact__c, hed__Contact__r.Previous_Years_Work_Experience__c, hed__StartDate__c, hed__Contact__r.Previous_Years_Management_Experience__c, hed__Contact__r.Current_Management_Effective_Start_Date__c, hed__Contact__r.Current_Work_Effective_Start_Date__c FROM hed__Affiliation__c 
                                WHERE hed__StartDate__c = :secondWHStartDate AND hed__EndDate__c != null AND recordTypeId = :AffiliationService.workHistoryRTId AND Qualifies_As_Management_Experience__c != true LIMIT 1]; 
        
        //update start date so that it overlaps with first startDate:
        workHistoryToUpdate.hed__StartDate__c = firstWHStartDate.addMonths(6); 

        Test.startTest(); 
            update workHistoryToUpdate; 
        Test.stopTest(); 

        //Updated contact record 
        Contact cAfterUpdate = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :workHistoryToUpdate.hed__Contact__c]; 

        //Assert that previous_years_work_experience__c has been re-calculated: 
        SYSTEM.assertNotEquals(workHistoryToUpdate.hed__Contact__r.Previous_Years_Work_Experience__c, cAfterUpdate.Previous_Years_Work_Experience__c); 
        Decimal expectedYears = (Decimal) firstWHStartDate.monthsBetween(secondWHEndDate)/12; 
        SYSTEM.AssertEquals(expectedYears.setScale(1, RoundingMode.HALF_UP), cAfterUpdate.Previous_Years_Work_Experience__c); 
        //Assert that  current management effective start date && current work effective start date were not updated: 
        SYSTEM.assertEquals(workHistoryToUpdate.hed__Contact__r.Current_Management_Effective_Start_Date__c, cAfterUpdate.Current_Management_Effective_Start_Date__c); 
        SYSTEM.assertEquals(workHistoryToUpdate.hed__Contact__r.Current_Work_Effective_Start_Date__c, cAfterUpdate.Current_Work_Effective_Start_Date__c);    
    }
    /**
    * @description Assert that when a current work history record is inserted, 
    * previous_years_work_experience__c, previous_years_management_experience__c, and current_management_effective_start_date__c are not updated but current effective start date is updated
    */
    @isTest  
    public static void testInsertCurrentWH(){
        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        insert c; 
 
        //Create related current work history records: 
        List<hed__Affiliation__c> workHistoriesToInsert = new List<hed__Affiliation__c>(); 
        workHistoriesToInsert.add(new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = firstWHStartDate)); 
        workHistoriesToInsert.add(new hed__Affiliation__c(recordTypeId = AffiliationService.workHistoryRTId, hed__Contact__c = c.Id, hed__StartDate__c = secondWHStartDate)); 

        Test.startTest(); 
            insert workHistoriesToInsert; 
        Test.stopTest();

        Contact cAfterInsert = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :c.Id]; 

        //Assert that previous years of work experience  is null 
        System.assertEquals(null, cAfterInsert.Previous_Years_Work_Experience__c); 
        //Assert that previous years of management experience  is null 
        System.assertEquals(null, cAfterInsert.Previous_Years_Management_Experience__c); 
        //Assert that current management effective start date is null 
        System.assertEquals(null, cAfterInsert.Current_management_effective_start_date__c); 

        //Assert that current work effective start date is the earliest current start date: 
        Date expectedStartDate = firstWHStartDate < secondWHStartDate ? firstWHStartDate : secondWHStartDate; 
        System.assertEquals(expectedStartDate, cAfterInsert.Current_Work_Effective_Start_Date__c); 
    }
    /**
    * @description Assert that when a management history record's Qualifies_As_Management_Experience__c is unchecked, 
    * previous_years_management_experience__c is updated, and current_management_effective_start_date__c, current_work_effective_start_date__c, previous_Years_Work_Experience__c are not updated
    */
    @isTest  
    public static void testUpdateUnqualifiedMgt(){
        hed__Affiliation__c mgtHistory = [SELECT ID, Qualifies_As_Management_Experience__c, hed__Contact__r.Previous_Years_Work_Experience__c, hed__Contact__r.Previous_Years_Management_Experience__c, hed__Contact__r.Current_Management_Effective_Start_Date__c,
                                            hed__Contact__r.Current_Work_Effective_Start_Date__c, hed__Contact__c 
                                            FROM hed__Affiliation__c
                                            WHERE Qualifies_As_Management_Experience__c = true 
                                            AND hed__StartDate__c != null
                                            AND hed__EndDate__c != null 
                                            AND recordTypeId = :AffiliationService.workHistoryRTId
                                            LIMIT 1]; 
        
        mgtHistory.Qualifies_As_Management_Experience__c = false; 

        Test.StartTest(); 
            update mgtHistory; 
        Test.stopTest(); 

        Contact cAfterUpdate = [SELECT ID, Previous_Years_Work_Experience__c, Current_Work_Effective_Start_Date__c, Previous_Years_Management_Experience__c, Current_Management_Effective_Start_Date__c FROM Contact WHERE Id = :mgtHistory.hed__Contact__c]; 

        //Assert previous_years_work_Experience__c, Current_management_Effective_Start_Date__c, and Current_Work_Effective_Start_Date__c were not updated: 
        System.assertEquals(mgtHistory.hed__Contact__r.Previous_Years_Work_Experience__c, cAfterUpdate.Previous_Years_Work_Experience__c); 
        System.assertEquals(mgtHistory.hed__Contact__r.Current_Management_Effective_Start_Date__c, cAfterUpdate.Current_Management_Effective_Start_Date__c); 
        System.assertEquals(mgtHistory.hed__Contact__r.Current_Work_Effective_Start_Date__c, cAfterUpdate.Current_Work_Effective_Start_Date__c); 

        //Assert Previous_Years_Management_Experience__c has been updated to null: 
        System.assertNotEquals(mgtHistory.hed__Contact__r.Previous_Years_Management_Experience__c, cAfterUpdate.Previous_Years_Management_Experience__c); 
        System.assertEquals(null, cAfterUpdate.Previous_Years_Management_Experience__c); 
    }
}