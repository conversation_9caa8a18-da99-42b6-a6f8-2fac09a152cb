@isTest
public with sharing class ContactDetailsBackFillBatchTest {
    @TestSetup
    static void makeData(){
        Map<String, Schema.RecordTypeInfo> acctTypesByDevName = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName();
        insert new List<SObject>{
            new Account(
                RecordTypeId                            = acctTypesByDevName.get('Academic_Program').getRecordTypeId(),
                Name                                    = 'Degree Program',
                Applicant_Opportunity_Record_Type__c    = 'Degree_Program_Prospect'
            ),
            new Account(
                RecordTypeId                            = acctTypesByDevName.get('Academic_Program').getRecordTypeId(),
                Name                                    = 'Degree Program',
                Applicant_Opportunity_Record_Type__c    = 'Executive_Program_Prospect'
            ),
            new Contact(
                FirstName   = 'Test Fn 1',
                LastName    = 'Test LN 1'
            ),
            new Contact(
                FirstName    = 'Test Fn 2',
                LastName     = 'Test Ln 2'
            ),
            new hed__Trigger_Handler__c(
                hed__Active__c              = true,
                hed__Class__c               = 'Application_OpportunitySync_TDTM', 
                hed__Load_Order__c          = 1, 
                hed__Object__c              = 'Application__c',
                hed__Owned_by_Namespace__c  = 'hed',
                hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate'
            ),
            new hed__Trigger_Handler__c(
                hed__Active__c              = true,
                hed__Class__c               = 'Opportunity_IdentifyDuplicates_TDTM', 
                hed__Load_Order__c          = .5, 
                hed__Object__c              = 'Opportunity',
                hed__Trigger_Action__c      = 'BeforeInsert'
            ),
            new hed__Trigger_Handler__c(
                hed__Active__c              = true,
                hed__Class__c               = 'Application_DateService_TDTM', 
                hed__Load_Order__c          = 2, 
                hed__Object__c              = 'Application__c',
                hed__Owned_by_Namespace__c  = 'hed',
                hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate'
            )
        };
    }

    @isTest
    static void testContactBatchBackFillBatch() {
        List<Contact> cons = [SELECT Id FROM Contact];
        List<Account> progs = [SELECT Id FROM Account WHERE RecordType.DeveloperName = 'Academic_Program'];

        hed__Application__c application1 = new hed__Application__c();
        application1.hed__Applicant__c = cons[0].Id;
        application1.hed__Applying_To__c = progs[0].Id;
            
        hed__Application__c application2 = new hed__Application__c();
        application2.hed__Applicant__c = cons[0].Id;
        application1.hed__Applying_To__c = progs[1].Id;

        hed__Application__c application3 = new hed__Application__c();

        List<hed__Application__c> applications = new List<hed__Application__c>{application1, application2, application3};
        insert applications;

        Test.startTest();
            ContactDetailsBackFillBatch obj = new ContactDetailsBackFillBatch();
            DataBase.executeBatch(obj); 
        Test.stopTest();
    }
}