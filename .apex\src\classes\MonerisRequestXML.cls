public without sharing class MonerisRequestXML {
    
    /*
     * Purchase Method to genereate a Purchase for new Order.
     * 
     */
     @AuraEnabled
    Public Static string PurchaseStructure(CardWrapperClass card){ 
         String xmlString =  '<request>' +
            '<store_id>store3</store_id>'+
            '<api_token>yesguy</api_token>'+
            '<status_check>false</status_check>'+
            '<purchase>'+
                '<amount>'+card.pymt.pymt__Amount__c.setScale(2)+'</amount>'+
                '<order_id>'+'Payment:'+datetime.now()+'</order_id>'+
                '<cust_id>ll</cust_id>'+
                '<pan>'+card.cardNo+'</pan>'+ //****************
                '<expdate>'+card.expireDate+'</expdate>'+
                '<crypt_type>7</crypt_type>'+
                '<dynamic_descriptor></dynamic_descriptor>'+
             /*
              * CUSTOMER INFORMATION 
              */
                '<cust_info>'+
                    '<billing>'+
                        '<first_name>'+card.pymt.pymt__Billing_First_Name__c+'</first_name>'+
                        '<last_name>'+card.pymt.pymt__Billing_Last_Name__c+'</last_name>'+
                        '<company_name>DE</company_name>'+
                        '<address>'+card.pymt.pymt__Billing_Street__c+'</address>'+
                        '<city>'+card.pymt.pymt__Billing_City__c+'</city>'+
                        '<province>'+card.pymt.pymt__Billing_State__c+'</province>'+
                        '<country>'+card.pymt.pymt__Billing_Country__c+'</country>'+
                        '<postal_code>'+card.pymt.pymt__Billing_Postal_Code__c+'</postal_code>'+
                        '<phone_number>1112223333</phone_number>'+
                        '<fax></fax>'+
                        '<tax1></tax1>'+
                        '<tax2></tax2>'+
                        '<tax3></tax3>'+
                        '<shipping_cost></shipping_cost>'+
                    '</billing>'+
                    '<shipping>'+
                        '<first_name>'+card.pymt.pymt__Billing_First_Name__c+'</first_name>'+
                        '<last_name>'+card.pymt.pymt__Billing_Last_Name__c+'</last_name>'+
                        '<company_name>DE</company_name>'+
                        '<address>'+card.pymt.pymt__Billing_Street__c+'</address>'+
                        '<city>'+card.pymt.pymt__Billing_City__c+'</city>'+
                        '<province>'+card.pymt.pymt__Billing_State__c+'</province>'+
                        '<country>'+card.pymt.pymt__Billing_Country__c+'</country>'+
                        '<postal_code>'+card.pymt.pymt__Billing_Postal_Code__c+'</postal_code>'+
                        '<phone_number>1112223333</phone_number>'+
                        '<fax></fax>'+
                        '<tax1></tax1>'+
                        '<tax2></tax2>'+
                        '<tax3></tax3>'+
                        '<shipping_cost></shipping_cost>'+
                    '</shipping>'+
                    '<item>'+
                        '<name>PackagesSingleTicket</name>'+
                        '<quantity>1</quantity>'+
                        '<product_code>3318-3209</product_code>'+
                        '<extended_amount>2.2600</extended_amount>'+
                    '</item>'+
                    '<email></email>'+
                    '<instructions></instructions>'+
                '</cust_info>'+
                /*
                * CVD Information
                */
                '<cvd_info>'+
                    '<cvd_indicator>1</cvd_indicator>'+
                    '<cvd_value>'+card.CVD+'</cvd_value>'+
                '</cvd_info>'+
                 /*
                 * AVS Information
                 */
                '<avs_info>'+
                    '<avs_street_number>212</avs_street_number>'+
                    '<avs_street_name>Payton Street</avs_street_name>'+
                    '<avs_zipcode>M1M1M1</avs_zipcode>'+
					'<avs_email><EMAIL></avs_email>'+
					'<avs_email><EMAIL></avs_email>'+
                '</avs_info>'+
            '</purchase>'+
            '</request>';
        return xmlString;
    }
    
    /*
     * Refund Method to genereate a Refund for Existing Order.
     * 
     */
    Public Static string RefundStructure(CardWrapperClass card){ 
         String xmlString =  '<request>' +
            '<store_id>store3</store_id>'+
            '<api_token>yesguy</api_token>'+
            '<refund>'+
                '<amount>'+card.Amount.setScale(2)+'</amount>'+
                '<order_id>'+card.pymt.Order_Id__c+'</order_id>'+
                '<txn_number>'+card.pymt.pymt__Transaction_Id__c+'</txn_number>'+
                '<crypt_type>7</crypt_type>'+
            '</refund>'+
            '</request>'; 
        
       // <request>
       // '<store_id>store3</store_id>'+
         //   '<api_token>yesguy</api_token>'+
       // <completion>
       // <order_id>'+'MonerisPurchase'+datetime.now()+'</order_id>
       // <amount>10.11</amount>
       // <txn_number>31-0_11</txn_number>
       // <crypt_type>7</crypt_type>
       // </completion>
       // </request>
       /* String xmlString =  '<request>' +
                                '<store_id>store3</store_id>'+
                                '<api_token>yesguy</api_token>'+
            					'<processing_country_code>CA</processing_country_code>'+
                                '<completion>'+
                                    '<amount>5.00</amount>'+
                                    '<order_id>'+'MonerisPurchase'+datetime.now()+'</order_id>' +
                                    '<txn_number>324764-0_15</txn_number>'+
                                    '<pan>****************</pan>'+
                                    '<expdate>2105</expdate>'+
                                    '<crypt_type>7</crypt_type>'+
                                '</completion>'+
                            '</request>'; */
        return xmlString;
    }
    Public Static string VoidStructure(string order, string tnx){ 
     String xmlString =  '<request>' +
            '<store_id>store3</store_id>'+
            '<api_token>yesguy</api_token>'+
            '<purchasecorrection>'+
                '<order_id>'+order+'</order_id>'+
                '<txn_number>'+tnx+'</txn_number>'+
                '<crypt_type>7</crypt_type>'+
            '</purchasecorrection>'+
            '</request>'; 
        return xmlString;
    }
}