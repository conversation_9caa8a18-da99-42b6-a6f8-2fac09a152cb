/**
 * Created by <PERSON><PERSON> on 2023-05-09.
 */

public without sharing class Event<PERSON>ee<PERSON>artWrapper {
    @AuraEnabled public Id eventFeeId {get;set;}
    @AuraEnabled public String Name {get;set;}
    @AuraEnabled public Boolean mailingenabled {get; set;}
    @AuraEnabled public Decimal price {get;set;}
    @AuraEnabled public Decimal quantity {get; set;}
    @AuraEnabled public Decimal tax {get; set;}
    @AuraEnabled public Boolean taxable {get;set;}
    @AuraEnabled public Id eventId {get; set;}
    @AuraEnabled public Integer currentUsage  {get;set;}
    @AuraEnabled public Integer maxUsage  {get;set;}
    @AuraEnabled public Integer LimitPerPurchase  {get;set;}
}