/* ---------------------------------------
Class Name: EngagementTimelineTest
Author: Huron
Purpose: Test class for the EngagementTimeline class
------------------------------------------*/

@isTest(SeeAllData=false)
private class EngagementTimelineTest { 
    
    static testMethod void validateLoadTimeline_Contact() {
        test.startTest();
        
        createEngagementTimelineItems();
        string conId = createContactInfo();
        
        list<EngagementTimeline.TimelineItemWrp> items = EngagementTimeline.allTimelineItems('Contact', conId);
        
        test.stopTest();
    }
    
    static testMethod void validateLoadTimeline_Account() {
        test.startTest();
        
        createEngagementTimelineItems();
        string acctId = createAccountInfo();
        
        list<EngagementTimeline.TimelineItemWrp> items = EngagementTimeline.allTimelineItems('Account', acctId);
        
        test.stopTest();
    }
    
    static testMethod void validateLoadEngagementLabel() {
        test.startTest();
        
        EngagementTimeline et = new EngagementTimeline();
        string label = EngagementTimeline.engagementLabel('Contact');
        
        test.stopTest();
    }
    
    static void createEngagementTimelineItems() {
        // insert engagement timeline item to display on contact, corresponding to case
        Engagement_Timeline_Item__c eti1 = new Engagement_Timeline_Item__c();
        eti1.Object_Name__c = 'Case';
        eti1.Related_Object__c = 'Contact';
        eti1.Relationship_Reference__c = 'ContactId';
        eti1.Primary_Date_Reference__c = 'CreatedDate';
        eti1.Title_Reference__c = 'CaseNumber';
        eti1.Description_Reference__c = 'Subject';
        eti1.Detail_1__c = 'Reason';
        eti1.Detail_2__c = 'Origin';
        eti1.Detail_3__c = 'Priority';
        eti1.Icon__c = 'standard:case';
        eti1.Active__c = TRUE;
        eti1.Where_Clause__c = 'CaseNumber != null';
        insert eti1;
        
    }
    
    static string createContactInfo() {
        Contact con = new Contact(FirstName='Fname',LastName='LName',Email='<EMAIL>');
        insert con;
        
        String acctId = [SELECT Id, AccountId FROM Contact WHERE Id = :con.Id].AccountId;
        
        // insert task (past activity)
        Task t = new Task();
        t.WhoId = con.Id;
        t.Subject = 'Task';
        t.Status = 'Completed';
        t.ActivityDate = system.today().AddDays(-2);
        insert t;
        
        // insert event (future activity)
        Event e = new Event();
        e.WhoId = con.Id;
        e.StartDateTime = system.now().AddDays(2);
        e.EndDateTime = system.now().AddDays(3);
        insert e;
        
        // insert cases
        list<Case> cases = new list<Case>();
        Case cs1 = new Case();
        cs1.ContactId = con.Id;
        cs1.Subject = 'Case1 for Engagement Timeline Test Class';
        cs1.Reason = 'Testing';
        cs1.Origin = 'Web';
        cs1.Priority = 'Medium';
        cases.add(cs1);
        
        Case cs2 = new Case();
        cs2.ContactId = con.Id;
        cs2.Subject = 'Case2 for Engagement Timeline Test Class';
        cs2.Reason = 'Testing';
        cs2.Origin = 'Web';
        cs2.Priority = 'Medium';
        cases.add(cs2);
        
        insert cases;
        
        return con.Id;
    }
    
    static string createAccountInfo() {
        /*Contact con = new Contact(FirstName='Fname',LastName='LName',Email='<EMAIL>');
        insert con;
        
        String acctId = [SELECT Id, AccountId FROM Contact WHERE Id = :con.Id].AccountId;*/
        
        Account acct = new Account(Name = 'TestAccount');
        insert acct;
        
        return acct.Id;
    }
    
    static testMethod void validateActivityItem() {
        Contact con = new Contact(FirstName='Fname',LastName='LName',Email='<EMAIL>');
        insert con;
        Task t = new Task(WhoId = con.Id, OwnerId = UserInfo.getUserId(), Subject='Test', Status='Not Started', Priority='Normal');
        insert t;
        
        Contact c = Database.query('SELECT Id, (SELECT Id, ActivityDate, Subject, Description, ActivitySubtype, StartDateTime, EndDateTime, CreatedDate FROM OpenActivities ORDER BY ActivityDate ASC, LastModifiedDate DESC LIMIT 499), (SELECT Id, ActivityDate, Subject, Description, ActivitySubtype, StartDateTime, EndDateTime, CreatedDate FROM ActivityHistories ORDER BY ActivityDate DESC, LastModifiedDate DESC LIMIT 499) FROM Contact WHERE Id =\'' + con.Id + '\'');
        List<OpenActivity> oac = c.getSObjects('OpenActivities');
        test.startTest();
        
        EngagementTimeline.activityItem(oac[0]);
        
        test.stopTest();
    }
    
}