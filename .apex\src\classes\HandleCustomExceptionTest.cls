@isTest
public class HandleCustomExceptionTest {
   @isTest
    public static void testCase1()
    {
        try
        {
            Integer a= 4;
            Integer b=0;
            Integer d=a/b;
        }
        catch(exception ex)
        {
           HandleCustomException hc= new HandleCustomException();
           hc.log(ex); 
           hc.<PERSON>('Arithmatic Exception!!');
           hc.ExceptionCause('Arithmatic Exception!!');
        }
        
    }
}