/** 

* @description    TDTM driven handler settigng Opportunity names on create and identifying duplicate contacts
* <AUTHOR> 
* @version        1.0 

*/ 
global class Opportunity_StagingHandler_TDTM extends hed.TDTM_Runnable {

    // the Trigger Handler’s Run method we must provide
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
 
        if (triggerAction == hed.TDTM_Runnable.Action.BeforeInsert) {

            for ( Opportunity o : (List<Opportunity>)newlist ) {
                
                //If the staging source is populated, setthe source as the staging source before insert, otherwise blank
                if ( o.Staging_Source__c != null ) {
                    o.LeadSource        = o.Staging_Source__c;
                    o.Staging_Source__c = null;

                }

                //If the staging stage is populated, set the stage as the staging stage before insert, otherwise blank
                if ( o.Staging_Stage__c != null ) {
                    o.StageName         = o.Staging_Stage__c;
                    o.Staging_Stage__c  = null;

                }

                //If the staging close date is populated, set the stage as the staging close date before insert
                if ( o.Staging_Close_Date__c != null ) {
                    o.CloseDate              = o.Staging_Close_Date__c;
                    o.Staging_Close_Date__c  = null;

                }
            }

        } else if (triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate) {

            List<Opportunity> stageRegressionOpptys = new List<Opportunity>();

            for ( Opportunity o : (List<Opportunity>)newlist ) {
               
                //Clear staging close date on update, do not re-evaluate the close date
                if ( o.Staging_Close_Date__c != null )
                    o.Staging_Close_Date__c = null;

                //Clear staging source on update, do not re-evaluate the source
                if ( o.Staging_Source__c != null )
                    o.Staging_Source__c = null;

                //If the staging stage field is populated, add to the list of records to have stagign stage evaluated
                if ( o.Staging_Stage__c != null && o.Allow_Stage_Regression__c != true ) 
                    stageRegressionOpptys.add( o );  
                
                //If the staging stage has been set to ignore regression rules, set stage regardless and reset flags
                else if ( o.Staging_Stage__c != null ) {
                    o.StageName                  = o.Staging_Stage__c;
                    o.Staging_Stage__c           = null;

                }
                
                //Reset allow flag
                if ( o.Allow_Stage_Regression__c == true )
                    o.Allow_Stage_Regression__c = false;
            
            }

            if ( stageRegressionOpptys.size() > 0 )
                evaluateOpptysForRegression( stageRegressionOpptys );

        }


        return dmlWrapper;
    }

    private static void evaluateOpptysForRegression ( List<Opportunity> stageRegressionOpptys ) {

        Map<String, OpportunityStage> stagebyLabel = new Map<String, OpportunityStage>();
        for ( OpportunityStage os : [ SELECT ApiName, IsClosed, DefaultProbability, MasterLabel FROM OpportunityStage WHERE IsActive = true ] )
            stagebyLabel.put( os.MasterLabel, os );

        for ( Opportunity o : stageRegressionOpptys ) {

            if ( stagebyLabel.containsKey( o.Staging_Stage__c ) && ( stagebyLabel.get( o.Staging_Stage__c ).isClosed || stagebyLabel.get( o.Staging_Stage__c ).DefaultProbability > o.Probability ) )
                o.StageName = o.Staging_Source__c;

            o.Staging_Source__c = null;

        }

    }

}