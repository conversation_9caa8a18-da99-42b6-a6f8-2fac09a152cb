({
    doInit : function(component, event, helper) {
        var caseRecordId = component.get("v.recordId");
        var actions = [
            { label: 'View', name: 'view' }
        ];

        var action = component.get("c.getTasksByCase");
        action.setParams({
            caseId: caseRecordId
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log('value====: ' + JSON.stringify(response.getReturnValue()));
            if (state === "SUCCESS") {
                let records = response.getReturnValue().map((row) => ({
                    Id : row.Id,
                    Subject : row.Subject,
                    linkName : window.location.hostname + '/lightning/r/Task/'+row.Id+'/view',
                    Status : row.Status,
                    Priority : row.Priority,
                    ActivityDate : row.ActivityDate
                }));
                component.set("v.openTasks", records);
                component.set("v.columns", [
                    {label: 'Subject', fieldName: 'linkName', type: 'url',
                        typeAttributes: {label: { fieldName: 'Subject' },value:{fieldName: 'linkName'}, target: '_blank'}},
                    { label: 'Status', fieldName: 'Status', type: 'text' },
                    {label: 'Priority', fieldName: 'Priority'},
                    {label: 'Due Date', fieldName: 'ActivityDate', type: 'text'},
                    {type: 'action', typeAttributes: { rowActions: actions } }
                ]);
            }
        });
        $A.enqueueAction(action);

        let action1 = component.get("c.getCaseWithContact");
        action1.setParams({
            caseId: component.get("v.recordId")
        });
        action1.setCallback(this, function(response1) {
            var state1 = response1.getState();
            if (state1 === "SUCCESS") {
                component.set("v.contactId", response1.getReturnValue().ContactId);
            }else {
                console.log('Error retrieving contactId from case');
            }
        });
        $A.enqueueAction(action1);
    },

    createTask: function(component, event, helper) {
        var createRecordEvent = $A.get("e.force:createRecord");
        createRecordEvent.setParams({
            "entityApiName": "Task",
            "defaultFieldValues": {
                "WhoId": component.get("v.contactId"),
                "WhatId": component.get("v.recordId"),
                "Status": "Not Started"
            }
        });
        createRecordEvent.fire();
    },

    handleRowAction: function(component, event, helper) {
        var action = event.getParam('action');
        var row = event.getParam('row');
        switch (action.name) {
            case 'view':
                var navEvt = $A.get("e.force:navigateToSObject");
                navEvt.setParams({
                    "recordId": row.Id,
                    "slideDevName": "detail"
                });
                navEvt.fire();
                break;
        }
    }
})