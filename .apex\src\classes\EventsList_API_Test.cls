@IsTest
private class EventsList_API_Test {
	@TestSetup
	static void makeData(){
		Account acc = (Account)TestFactory.createSObject(new Account(Name = 'Dept Account'));
		insert acc;
		Contact con = (Contact)TestFactory.createSObject(new Contact(lastName = 'Last Name', firstName = 'First Name',hed__WorkEmail__c	= '<EMAIL>'));
		insert con;

		evt__Special_Event__c event1 = new evt__Special_Event__c();
		event1.Name = 'Special event';
		event1.Start_Date__c = Date.today();
		event1.End_Date__c = Date.today();
		event1.evt__Short_Description__c = 'Short description 1';
		event1.Start_Local__c = Datetime.now();
		event1.End_Local__c = Datetime.now();
		event1.evt__Event_Type__c = 'Student Event';
		event1.Venue_Type__c = 'Both';
		event1.evt__Topics__c = 'Business';
		event1.Thumbnail_Image__c = '/events/servlet/rtaImage?eid=a1VG1000000yAAz&feoid=00N2B000000Qe9O&refid=0EMG10000007q3V';
		event1.evt__Status__c = 'Published';
		event1.Agenda_RichText__c = 'Agenda';
		event1.Price__c = 'Price: 100';

		evt__Special_Event__c event2 = new evt__Special_Event__c();
		event2.Name = 'Special event 2';
		event2.Start_Date__c = Date.today();
		event2.End_Date__c = Date.today();
		event2.evt__Short_Description__c = 'Short description 2';
		event2.Start_Local__c = Datetime.now();
		event2.End_Local__c = Datetime.now();
		event2.evt__Event_Type__c = 'Student Event';
		event2.Venue_Type__c = 'Both';
		event2.evt__Topics__c = 'Business';
		event2.Thumbnail_Image__c = '/events/servlet/rtaImage?eid=a1VG1000000yAAz&feoid=00N2B000000Qe9O&refid=0EMG10000007q3V';
		event2.evt__Status__c = 'Published';
		event2.Price__c = 'Price: 100';
		event2.Agenda_RichText__c = 'Agenda';

		List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
		listOfEventToInsert.add(event1);
		listOfEventToInsert.add(event2);

		insert listOfEventToInsert;
	}

	@isTest
	static void testGetEvents1() {
		// Mock the RestContext response
		RestRequest req = new RestRequest();
		RestResponse res = new RestResponse();
		RestContext.request = req;
		RestContext.response = res;

		// Call the method
		Test.startTest();
		EventsList_API.getEvents();
		Test.stopTest();
	}

}