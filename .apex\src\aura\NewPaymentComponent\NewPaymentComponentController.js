({
    initData: function (component, event, helper){
        var today = new Date();
        var yearList = [];
        console.log('today= '+today);
        var year = today.getFullYear();
        console.log(today.getFullYear());
        for (var i=0; i< 12; i++)
        {
            var newyear = parseInt(year) + parseInt(i);
            yearList.push(newyear);
        }
        console.log(yearList);
        //var ExpYearList = component.get("v.ExpiryYear");
        component.set('v.ExpiryYear',yearList);
    },
	onContactChange: function(component, event, helper){
        var con = component.get("v.contactId");
        console.log('con= '+con);
        helper.ContactDetails(component);
        
    },
    onSubmit: function(component, event, helper){
        var con = component.get("v.contactId");
       var wrapper = component.get('v.paymentCardWrapper');
        wrapper.pymt.pymt__Contact__c = con;
        console.log('name= '+wrapper.pymt.Name);
        console.log('con= '+wrapper.pymt.pymt__Contact__c);
        console.log('name= '+wrapper.pymt.pymt__Billing_First_Name__c);
        console.log(wrapper.expireYear);
        //var wrapper = component.get('v.paymentCardWrapper');
        var total = component.get('v.TotalAmount');
        console.log('Total-- '+total)
        wrapper.pymt.pymt__Amount__c = total;
        var checkFields= false;
        if(wrapper.pymt.pymt__Amount__c <= 0  ){
            alert('Invalid Amount.');
            return false;
        }
        console.log(wrapper.pymt.Name+' - '+wrapper.pymt.pymt__Billing_First_Name__c+'  '+wrapper.cardNo);
        console.log(wrapper.expireMonth+' - '+total+' - '+wrapper.pymt.pymt__Billing_Postal_Code__c);
        console.log(wrapper.expireYear+' - '+wrapper.pymt.pymt__Billing_Email__c+' - '+wrapper.CVD);
         console.log(wrapper.pymt.pymt__Billing_Last_Name__c+' - '+wrapper.pymt.pymt__Billing_Street__c+' - '+wrapper.pymt.pymt__Billing_City__c);
        if(wrapper.pymt.Name != undefined && total != undefined &&  
           wrapper.pymt.pymt__Billing_First_Name__c != undefined && wrapper.pymt.pymt__Billing_Last_Name__c != undefined &&
           wrapper.pymt.pymt__Billing_Street__c != undefined && wrapper.pymt.pymt__Billing_City__c != undefined &&
           wrapper.pymt.pymt__Billing_Postal_Code__c != undefined && wrapper.pymt.pymt__Billing_Email__c != undefined
          ){
            checkFields = true;
            
        }
        else{
            alert('Please fill all Mandatory(*) Fields');
            return false;
        }
      /*  console.log('outside if else'); */
        if(wrapper.pymt.pymt__Payment_Type__c =='Credit Card' ){
            var expMon = wrapper.expireMonth;
            var expYear = wrapper.expireYear;
            wrapper.expireDate = expYear.substring(2,4)+expMon;
        }
        
        //component.set('v.paymentCardWrapper',wrapper);
        //console.log('Expire Date==> '+component.get('v.paymentCardWrapper').expireDate);
        //helper.MakePayment(component, event, helper);
        console.log('wrapper= '+wrapper);
        //debugger;
        if(wrapper.pymt.pymt__Payment_Type__c =='Credit Card'  && wrapper.pymt.pymt__Payment_Processor__c != "Moneris"){
            console.log('No processor');
            helper.showToastFun('warning','Incorrect Processor','Processor not set to Moneris');
            return false;
        }
       helper.MakePayment(component,wrapper);
       
    },
    onCancel: function(component, event, helper){
        window.history.go(-1);
    },
    updateTotalFromTaxAmount: function(component, event, helper) {
        var wrapper = component.get('v.paymentCardWrapper');
        var subtotal = component.get('v.subtotal');
        var tax = wrapper.pymt.pymt__Tax__c;
        var ship =wrapper.pymt.pymt__Shipping__c;
        console.log('subtotal--'+subtotal);
        console.log('tax='+tax);
        console.log('ship='+ship);
        var total;
        if(component.get('v.subtotal') != null && component.get('v.subtotal') > 0){
            subtotal = component.get('v.subtotal');
            console.log('subtotal='+subtotal);
        }
        else
        {
            subtotal = "0.00";
        }  
        
        
        if(wrapper.pymt.pymt__Tax__c != null && wrapper.pymt.pymt__Tax__c > 0){
            console.log('tax='+tax);
            tax = wrapper.pymt.pymt__Tax__c;
        }
        else
        {
            tax = "0.00";
        }  
        if(wrapper.pymt.pymt__Shipping__c != null && wrapper.pymt.pymt__Shipping__c > 0){
            ship = wrapper.pymt.pymt__Shipping__c;
            console.log('ship--'+ship);
        }else
        {
            ship = "0.00";
        }
        total = parseFloat(subtotal) + parseFloat(tax)+ parseFloat(ship);//subtotal + tax+ ship;
        wrapper.pymt.pymt__Amount__c = parseFloat(total).toFixed(2) ;
        component.set("v.TotalAmount",total);
        
    },
})