/**
* @description    Test class for DiscountCalculator_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-08-04
* @modified 2020-10-19
*/
@isTest
public class DiscountCalculator_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for TestScoreCalculator_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('DiscountCalculator_TDTM', 'pymt__Shopping_Cart_Item__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Test Student record: 
        Contact c =  (Contact) TestFactory.createSObject(new Contact()); 
        //Inserting contact will create admin account 
        insert c;

        String cookieName = String.valueOf(dateTime.now());
        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
        cart.pymt__Cart_UID__c = cookieName;
        insert cart;

        //Test Payment record: 
        pymt__PaymentX__c payment = new pymt__PaymentX__c(Name='Test Payment', pymt__contact__c = c.Id, Type__c = 'EP Program Payment', pymt__Status__c = 'Online Checkout'); 
        insert payment; 
    
        //Test program balance record:
        //Discount__c programBalance = new Discount__c(Name = 'Program Balance', Available_for__c = 'Individual', Type__c = 'Program Balance', Program_Balance_Amount__c = 200);
        //insert programBalance;

        Discount__c programBalance = new Discount__c(Name = 'Program Balance', Available_for__c = 'All', Type__c = 'Event Discount',Percent_Discount__c=10);
        insert programBalance;

        evt__Special_Event__c event = new evt__Special_Event__c();
        event.Name = 'Special event';
        event.Price__c =
                '$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
        event.Start_Local__c = Date.today().addDays(30);
        event.End_Local__c = Date.today().addDays(31);
        event.Start_Date__c = Date.today().addDays(30);
        event.End_Date__c = Date.today().addDays(31);
        event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
        event.evt__Registration_Deadline__c = Date.today().addDays(29);
        event.evt__By_Invitation__c = false;
        event.Venue_Type__c = 'In-Person';
        event.evt__Publish_To__c = 'Public Events';
        event.evt__Event_Type__c = 'Session Event';
        event.evt__Status__c = 'Published';
        event.evt__Max_Attendees__c = 100;
        event.Tags__c = 'Strategic Communications';
        event.Thumbnail_Image__c =
                '<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';
        insert event;

        Invoice__c inv = new Invoice__c();
        inv.Invoice_Status__c = 'Open-Personal';
        inv.Type__c = 'Events';
        inv.contact__c = c.Id;
        inv.Tax_Amount__c = 15;
        inv.Gross_Amount__c = 100;
        inv.Total_Amount_SCI__c = 115;
        insert inv;

        pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
        pymnt.Name = 'Event Checkout';
        pymnt.pymt__Transaction_Type__c = 'Payment';
        pymnt.pymt__Payment_Type__c	 = 'Credit Card';
        pymnt.pymt__Status__c = 'Online Checkout';
        pymnt.pymt__Contact__c = c.Id;
        pymnt.pymt__Amount__c = 90;
        pymnt.Gross_Amount__c = 100;
        pymnt.pymt__Tax__c = 10;
        pymnt.pymt__Discount__c = 20;
        pymnt.Type__c = 'Event Registration';
        pymnt.pymt__Payment_Processor__c = 'Global Pay';
        pymnt.invoice__c = inv.Id;
        insert pymnt;

        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event.Name;
        cartItem.Special_Event__c = event.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = c.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        cartItem.pymt__Shopping_Cart__c = cart.Id;
        cartItem.Discount__c = programBalance.Id;
        cartItem.Invoice__c = inv.Id;
        cartItem.pymt__Payment__c = pymnt.Id;

        insert cartItem;
    }

    /**
     * @description test insertion of paid shopping cart item with program balance 
     * Assert that discount's program balance has been adjusted correctly
     * Assert that discount's current usage has been updated 
     */
    @isTest
    public static void testInsertSCI(){
        Discount__c discount = [SELECT ID, Current_Usage__c, Program_Balance_Amount__c FROM Discount__c WHERE type__c = 'Event Discount' AND Active__c = true LIMIT 1];
        pymt__PaymentX__c payment = [SELECT ID FROM pymt__PaymentX__c WHERE Type__c = 'EP Program Payment' LIMIT 1]; 

        pymt__Shopping_Cart_Item__c sci = new pymt__Shopping_Cart_Item__c(Name='Test Program Balance SCI', Discount__c = discount.Id, pymt__Unit_Price__c = -50, pymt__Quantity__c = 1, pymt__Payment_Completed__c = true, pymt__Payment__c = payment.Id); 

        Test.startTest(); 
            insert sci; 
        Test.stopTest(); 

        Discount__c discountAfter = [SELECT iD, Current_Usage__c, Program_Balance_Amount__c FROM Discount__c WHERE Id = :discount.Id]; 
        System.debug('Discount Before: ' + discount);
        System.debug('Discount After: ' + discountAfter);
        //Assert that discount's program balance has been recalculated:
        //System.assert(discountAfter.Program_Balance_Amount__c == (discount.Program_Balance_Amount__c - 50),
        //'Program Balance amount was not calculated correctly. Expected Amount: ' +  (discount.Program_Balance_Amount__c - 50) + ' Actual Amount: ' + discountAfter.Program_Balance_Amount__c);

        //Assert current usage has been recalculated: 
        //System.assert(DiscountAfter.Current_Usage__c == (discount.Current_Usage__c + 1),
        //'Current Usage was not calculated correctly. Expected usage: ' + (discount.Current_Usage__c + 1) + ' Actual Usage: ' + DiscountAfter.Current_Usage__c);
    }

    /**
     * @description test update of a shopping cart item with program balance to payment completed
     * Assert that discount's program balance has been adjusted correctly
     * Assert that discount's current usage has been updated 
     */
    @isTest
    public static void testUpdateSCI(){
        pymt__Shopping_Cart_Item__c sci = [SELECT ID, pymt__Total__c, pymt__Payment_Completed__c, Discount__c, Discount__r.Current_Usage__c, Discount__r.Program_Balance_Amount__c, Program_Balance_Amount__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Discount__r.Type__c = 'Event Discount' LIMIT 1];

        Test.startTest(); 
            update new pymt__Shopping_Cart_Item__c(Id = sci.id, pymt__Payment_Completed__c = true); 
        Test.stopTest(); 

        Discount__c discountAfter = [SELECT iD, Current_Usage__c, Program_Balance_Amount__c FROM Discount__c WHERE Id = :sci.Discount__c]; 
        System.debug('Discount Before: ' + sci.Discount__r);
        System.debug('Discount After: ' + discountAfter);

        //Assert that discount's program balance has been recalculated: 
        //System.assert(discountAfter.Program_Balance_Amount__c == (sci.Discount__r.Program_Balance_Amount__c + sci.Program_Balance_Amount__c),
        //'Program Balance amount was not calculated correctly. Expected Amount: ' +  (sci.Discount__r.Program_Balance_Amount__c + sci.Program_Balance_Amount__c) + ' Actual Amount: ' + discountAfter.Program_Balance_Amount__c);

        //Assert current usage has been recalculated: 
        //System.assert(DiscountAfter.Current_Usage__c == (sci.Discount__r.Current_Usage__c + 1),
        //'Current Usage was not calculated correctly. Expected usage: ' + (sci.Discount__r.Current_Usage__c + 1) + ' Actual Usage: ' + DiscountAfter.Current_Usage__c);
    }

    @isTest
    public static void testUpdateSCI1(){
        pymt__Shopping_Cart_Item__c sci = [SELECT ID, pymt__Total__c, pymt__Payment_Completed__c, Discount__c, Discount__r.Current_Usage__c, Discount__r.Program_Balance_Amount__c, Program_Balance_Amount__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Discount__r.Type__c = 'Event Discount' LIMIT 1];
        sci.pymt__Payment__c = null;
        update sci;

        Test.startTest();
        update new pymt__Shopping_Cart_Item__c(Id = sci.id, pymt__Payment_Completed__c = true);
        Test.stopTest();

        Discount__c discountAfter = [SELECT iD, Current_Usage__c, Program_Balance_Amount__c FROM Discount__c WHERE Id = :sci.Discount__c];
        System.debug('Discount Before: ' + sci.Discount__r);
        System.debug('Discount After: ' + discountAfter);
    }

}