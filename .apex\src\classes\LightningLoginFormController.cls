global class LightningLoginFormController {

    /**
     * @description Response Wrapper Class
     */
    public class Response {
        @AuraEnabled public Boolean hasError = false;
        @AuraEnabled public String errorMessage = '';
        @AuraEnabled public String redirectURL = '';
    }


    @AuraEnabled
    public static String login(String username, String password, String startUrl) {
        try {
            ApexPages.PageReference lgn = Site.login(username, password, startUrl);
            aura.redirect(lgn);
            return null;
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @AuraEnabled
    public static Response loginCustom(String username, String password, String startUrl, String sitecore) {
        Response res = new Response();
        try {
            if (sitecore != null) {
                if (sitecore.equalsIgnoreCase('mbaapp')) {
                    ApexPages.PageReference lgn = Site.login(username, password, '/s/apply');
                    //system.debug(' ***Get URL: *** ' + lgn.getURL());
                    res.redirectURL = lgn.getURL();
                } else if (sitecore.equalsIgnoreCase('ftmba-request')) {
                    ApexPages.PageReference lgn = Site.login(username, password, '/s/ftmba-request-pre-application-meeting');
                    res.redirectURL = lgn.getURL();
                } else if (sitecore.equalsIgnoreCase('ftmba-introduce')) {
                    ApexPages.PageReference lgn = Site.login(username, password, '/s/ftmba-introduce-yourself-form');
                    res.redirectURL = lgn.getURL();
                } else {
                    ApexPages.PageReference lgn = Site.login(username, password, startUrl);
                    res.redirectURL = lgn.getURL();    //return null;
                    system.debug(' ***Get URL: *** ' + lgn.getURL());
                }
            } else {
                ApexPages.PageReference lgn = Site.login(username, password, startUrl);
                res.redirectURL = lgn.getURL();    //return null;
                system.debug(' ***Get URL 1: *** ' + lgn.getURL());
            }
        } catch (Exception ex) {
            res.hasError = true;
            res.errorMessage = ex.getMessage();
        }
        return res;
    }

    @AuraEnabled
    public static Boolean getIsUsernamePasswordEnabled() {
        Auth.AuthConfiguration authConfig = getAuthConfig();
        return authConfig.getUsernamePasswordEnabled();
    }

    @AuraEnabled
    public static Boolean getIsSelfRegistrationEnabled() {
        Auth.AuthConfiguration authConfig = getAuthConfig();
        return authConfig.getSelfRegistrationEnabled();
    }

    @AuraEnabled
    public static String getSelfRegistrationUrl() {
        Auth.AuthConfiguration authConfig = getAuthConfig();
        if (authConfig.getSelfRegistrationEnabled()) {
            return authConfig.getSelfRegistrationUrl();
        }
        return null;
    }

    @AuraEnabled
    public static String getForgotPasswordUrl() {
        Auth.AuthConfiguration authConfig = getAuthConfig();
        return authConfig.getForgotPasswordUrl();
    }

    @TestVisible
    private static Auth.AuthConfiguration getAuthConfig() {
        Id networkId = Network.getNetworkId();
        Auth.AuthConfiguration authConfig = new Auth.AuthConfiguration(networkId, '');
        return authConfig;
    }

    @AuraEnabled
    global static String setExperienceId(String expId) {
        // Return null if there is no error, else it will return the error message 
        try {
            if (expId != null) {
                Site.setExperienceId(expId);
            }
            return null;
        } catch (Exception ex) {
            return ex.getMessage();
        }
    }

    @AuraEnabled
    public static void updateUserPhoto(String userId, String contentDocumentId, Boolean isPublic) {
        if (contentDocumentId != null && contentDocumentId != '') {
            ContentVersion contentVersion = [
                    SELECT Id, VersionData, FileType, Title
                    FROM ContentVersion
                    WHERE ContentDocumentId = :contentDocumentId
                    LIMIT 1
            ];

            String lowerCaseFileType = contentVersion.FileType.toLowerCase();
            ConnectApi.UserProfiles.setPhoto(
                    null,
                    UserInfo.getUserId(),
                    new ConnectApi.BinaryInput(
                            contentVersion.VersionData,
                            'image/' + lowerCaseFileType,
                            contentVersion.Title + '.' + lowerCaseFileType
                    )
            );
        }

        User MyUser = [
                SELECT Id, UserPreferencesShowProfilePicToGuestUsers
                FROM User
                WHERE Id = :userId
                LIMIT 1
        ];
        MyUser.UserPreferencesShowProfilePicToGuestUsers = isPublic;
        update MyUser;
    }
}