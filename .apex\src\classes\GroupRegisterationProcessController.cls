/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 01-20-2022
 * @last modified by  : <EMAIL>
**/
public without sharing class GroupRegisterationProcessController {
    
    @AuraEnabled
    public static pymt__PaymentX__c createAttendeeRecord(evt__Special_Event__c event, Contact loggedInContact, 
        List<evt__Attendee__c> listOfAttendy, evt__Event_Fee__c registrationType, Discount__c discount, String currentEmployer, String currentTitle){//Map of key-->fieldName, value--->fieldvalue is expected
        
        String errorMsg = '';
        try{
            List<evt__Attendee__c> atndynewList = new List<evt__Attendee__c>();
            List<evt__Attendee__c> atndyupdatelist = new List<evt__Attendee__c>();
            List<pymt__Shopping_Cart_Item__c> sciupdateList = new List<pymt__Shopping_Cart_Item__c>();
            Id eventFeeId;
            evt__Special_Event__c eventDetails;
            List<Taxing_Authority__c> taxAuth = new List<Taxing_Authority__c>();
            
            /* original code 2022.01.25
            // evt__Special_Event__c eventDetails = [select id, name, (select id, name,evt__Amount__c from evt__Event_Fees__r where Type__c =: registrationType.Name) from evt__Special_Event__c where id = : event.Id];
            // Taxing_Authority__c taxAuth = [select id, name from Taxing_Authority__c where State_Province__c =: loggedInContact.MailingState];
            */ 
            // Vbo's version start 2022.01.26  - query Tax authority based on State_Province__c field on Special Event, if no State_Province__c use Ontario "ON" as default
            //get details of the event and event fees. 
            if (event.State_Province__c != null){
                taxAuth = [select Id, name, State_Province__c from Taxing_Authority__c where State_Province__c =: event.State_Province__c];
            }
            else {
                taxAuth = [select Id, name, State_Province__c from Taxing_Authority__c where State_Province__c ='ON'];
            }
            // Vbo's version end
            if(registrationType != null){
                eventFeeId = registrationType.Id;
            }
            //if logged in user is a authenticated user
            if(loggedInContact!=null && loggedInContact.Id != null){
                //create SC record
                pymt__Shopping_Cart__c	sc = new pymt__Shopping_Cart__c();
                sc.pymt__Contact__c = loggedInContact.Id;
                insert sc;
                //create SCI record
                pymt__Shopping_Cart_Item__c sci = new pymt__Shopping_Cart_Item__c();
                string sciname = loggedInContact.Name+' - '+event.name;
                sci.Name = sciname.length()<=80 ? loggedInContact.Name+' - '+event.name : event.name;
                sci.pymt__Contact__c = loggedInContact.Id;
                sci.Type__c	= 'Event Registration';
                sci.pymt__Shopping_Cart__c = sc.Id;
                sci.Special_Event__c = event.ID;
                if(registrationType != null){
                    //system.debug('Going inside null');
                    sci.Event_Fee__c = registrationType.Id;
                }
                sci.pymt__Quantity__c = listOfAttendy.size();
                sci.pymt__Unit_Price__c = registrationType != null? registrationType.evt__Amount__c : 0;
                //Vbo 2022.01.26 added index for taxAuth because it is a List "taxAuth" defined above -- used taxAuth[0].Id since it's the only value of the list
                sci.Taxing_Authority__c = taxAuth[0].Id != null ? taxAuth[0].Id : null;
                sci.pymt__Taxable__c = taxAuth[0].Id != null ? true : false;
                sci.Discount__c = discount!=null ? discount.Id : null;
                insert sci;
                //create attendy records for event
                if(listOfAttendy.size()>0){
                    for(evt__Attendee__c atndynew : listOfAttendy){
                        evt__Attendee__c atndy = new evt__Attendee__c();
                        atndy.evt__Reg_First_Name__c  = atndynew.evt__Reg_First_Name__c!=null ? atndynew.evt__Reg_First_Name__c : null;
                        atndy.evt__Reg_Last_Name__c   = atndynew.evt__Reg_Last_Name__c!=null ? atndynew.evt__Reg_Last_Name__c : null;
                        atndy.evt__Reg_Phone__c       = atndynew.evt__Reg_Phone__c!=null ? atndynew.evt__Reg_Phone__c : null;
                        atndy.evt__Reg_Email__c       = atndynew.evt__Reg_Email__c!=null ? atndynew.evt__Reg_Email__c : null;
                        atndy.evt__Reg_Company__c     = atndynew.evt__Reg_Company__c!=null ? atndynew.evt__Reg_Company__c : null;
                        atndy.evt__Reg_Title__c       = atndynew.evt__Reg_Title__c!=null ? atndynew.evt__Reg_Title__c : null;
                        atndy.evt__Reg_Country__c     = atndynew.evt__Reg_Country__c!=null ? atndynew.evt__Reg_Country__c : null;
                        atndy.evt__Reg_State__c       = atndynew.evt__Reg_State__c!=null ? atndynew.evt__Reg_State__c : null;
                        atndy.evt__Reg_City__c        = atndynew.evt__Reg_City__c!=null ? atndynew.evt__Reg_City__c : null;
                        atndy.evt__Reg_Street__c      = atndynew.evt__Reg_Street__c!=null ? atndynew.evt__Reg_Street__c : null;
                        atndy.evt__Reg_Postal_Code__c = atndynew.evt__Reg_Postal_Code__c!=null ? atndynew.evt__Reg_Postal_Code__c : null;
                        atndy.evt__Reg_Company__c = currentEmployer;
                        atndy.evt__Reg_Title__c = currentTitle;
                        atndy.Accessibility_Requirements__c = atndynew.Accessibility_Requirements__c!=null ? atndynew.Accessibility_Requirements__c : null;
                        atndy.Accessibility_Requirements_Other__c = atndynew.Accessibility_Requirements_Other__c!=null ? atndynew.Accessibility_Requirements_Other__c : null;
                        atndy.Shopping_Cart_Item__c = sci.Id!=null? sci.Id : null;
                        atndy.Event_Respondent__c = loggedInContact.Id;
                        atndy.evt__Event__c = event.Id;
                        atndy.evt__Event_Fee__c = eventFeeId != null ? eventFeeId : null;
                        atndy.evt__Invitation_Status__c = 'Payment Pending';
                        atndy.Group_Registration__c = true;
                        atndynewList.add(atndy);                       
                    }
                    if(atndynewList.size()>0){
                        insert atndynewList;
                    }
                }
                
                //create invoice records
                Invoice__c inv = new Invoice__c();
                inv.Invoice_Status__c = 'Open-Group';
                inv.Type__c = 'Events';
                inv.Gross_Amount__c	= listOfAttendy.size()*sci.pymt__Unit_Price__c;
                inv.Tax_Amount__c = sci.Tax_Amount__c !=null ? sci.Tax_Amount__c : null;
                inv.Total_Amount_SCI__c = inv.Tax_Amount__c!= null ? inv.Gross_Amount__c + inv.Tax_Amount__c : inv.Gross_Amount__c;
                inv.Contact__c = loggedInContact.Id;
                insert inv;
                
                //create payment reocrds
                pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
                pymnt.Name = 'Multi-Event Checkout'+' - '+loggedInContact.Name;
                pymnt.pymt__Contact__c = loggedInContact.Id;
                pymnt.pymt__Payment_Processor__c = 'Global Pay';
                pymnt.pymt__Status__c = 'Online Checkout';
                pymnt.pymt__Payment_Type__c = 'Credit Card';
                pymnt.pymt__Transaction_Type__c = 'Payment';
                pymnt.Type__c = 'Event Registration';
                pymnt.Gross_Amount__c = inv.Gross_Amount__c; 
                pymnt.pymt__Tax__c = inv.Tax_Amount__c!=null ? inv.Tax_Amount__c : null ;
                pymnt.pymt__Amount__c = inv.Total_Amount_SCI__c;  
                pymnt.invoice__c = inv.Id;
                insert pymnt;
                
                //update payemnt on attendy record
                for(evt__Attendee__c atndyupdate : atndynewList){
                    atndyupdate.evt__Payment__c = pymnt.Id;
                    atndyupdatelist.add(atndyupdate);
                }
                if(atndyupdatelist.size()>0){
                    update atndyupdatelist;
                }
                
                //update payment & invoice on SCI
                for(pymt__Shopping_Cart_Item__c sciUpdate :[select id, name,pymt__Payment__c,Invoice__c from pymt__Shopping_Cart_Item__c where id =:sci.Id]){
                    sciUpdate.pymt__Payment__c = pymnt.Id;
                    sciUpdate.Invoice__c = inv.Id;
                    sciupdateList.add(sciUpdate);
                }
                if(sciupdateList.size()>0){
                    update sciupdateList;
                }
                return pymnt;                
            }else{
                return null;
            }
        }catch (Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getStackTraceString());
            throw new AuraHandledException(e.getMessage());
        }
    }
    
}