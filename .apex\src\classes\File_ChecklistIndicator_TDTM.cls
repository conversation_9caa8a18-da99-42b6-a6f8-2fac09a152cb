/**
* TDTM class for marking items on an applicaiton as being complete when a relevant document is supplied
*
* <AUTHOR> 
* @since   2020-03-05 
*/

global class File_ChecklistIndicator_TDTM extends hed.TDTM_Runnable {

    global static Boolean alreadyRan = false;

    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
 
        //Stamp date fields on the application
        if ( triggerAction == hed.TDTM_Runnable.Action.AfterInsert && objResult.getName() == 'ContentDocumentLink' && !File_ChecklistIndicator_TDTM.alreadyRan ) {

            Map<String,Schema.SobjectType> describe = Schema.getGlobalDescribe();
            List<String> keys = new List<String>{
                describe.get('hed__Application__c').getDescribe().getKeyPrefix(),
                describe.get('Contact').getDescribe().getKeyPrefix() 
            };

            List<SObject> relevantDocs = new List<SObject>();
            for ( Integer i=0; i < newList.size(); i++ )
                if ( keys.contains( ((String)newList[i].get('LinkedEntityId')).substring(0,3) ) )
                    relevantDocs.add( newList[i] );

            if ( relevantDocs.size() > 0 )
                updateRelatedCis( relevantDocs, objResult.getName() );

        } else if ( triggerAction == hed.TDTM_Runnable.Action.AfterUpdate && !File_ChecklistIndicator_TDTM.alreadyRan ) {

            Map<String,Schema.SobjectType> describe = Schema.getGlobalDescribe();
            List<String> keys = new List<String>{
                describe.get('hed__Application__c').getDescribe().getKeyPrefix(),
                describe.get('Contact').getDescribe().getKeyPrefix() 
            };

            List<SObject> relevantDocs = new List<SObject>();
            for ( Integer i=0; i < newList.size(); i++ )
                if ( objResult.getName() == 'ContentVersion' || keys.contains( ((String)newList[i].get('LinkedEntityId')).substring(0,3) ) )
                    relevantDocs.add( newList[i] );

            if ( relevantDocs.size() > 0 )
                updateRelatedCis( relevantDocs, objResult.getName() );

        }

        return dmlWrapper;
    }

    private void updateRelatedCis ( List<SObject> sObjs, String objName ) {
 
        Application_ChecklistManager_TDTM.alreadyRan = true;
        ApplicationChecklistService acs = new ApplicationChecklistService ( sObjs, objName );

    }

}