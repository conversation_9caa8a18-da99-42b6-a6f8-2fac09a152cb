public without sharing class EventEmailService {
	@AuraEnabled
	public static void sendEmailToAttendees(List<Contact> attendees) {
		List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
		List<String> toAddresses = new List<String>();
		List<String> toBccAddresses = new List<String>();
		Boolean isBcc = false;

		for (Contact attendee : attendees) {
			Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();

			// Query for the Organization-Wide Email Address
			OrgWideEmailAddress[] owea = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'Rotman Events'];

			if (!owea.isEmpty()) {
				// Set the organization-wide email address
				email.setOrgWideEmailAddressId(owea[0].Id);
			}

			email.setToAddresses(new List<String>{attendee.Email});
			email.setSubject('Email Subject');
			email.setHtmlBody('Email Body');
			emails.add(email);
		}

		if (Test.isRunningTest()) {
			return;
		}
		Messaging.sendEmail(emails);
	}

	@AuraEnabled
	public static String sendMessage(String eventId,
								   List<String> toAddress,
								   List<String> typeList,
								   String subject,
								   String message,
								   String senderName,
								   String replyTo) {
		System.debug('@0-3-1eventId: ' + eventId);
		System.debug('@0-3-2toAddress: ' + toAddress);
		System.debug('@0-3-3typeList: ' + typeList);
		System.debug('@0-3-4subject: ' + subject);
		System.debug('@0-3-5message: ' + message);
		System.debug('@0-3-6senderName: ' + senderName);
		System.debug('@0-3-7replyTo: ' + replyTo);

		String res = 'Success';
		List<String> listAddresses = new List<String>();
		if (typeList != null && typeList.size() > 0) {
			List<evt__Attendee__c> attendees = [SELECT Id, evt__Reg_Email__c FROM evt__Attendee__c WHERE evt__Event__c = :eventId AND evt__Event_Fee__r.Name IN :typeList];
			System.debug('@0-3-2attendees: ' + attendees);
			if (attendees.size() > 0) {
				for (evt__Attendee__c attendee : attendees) {
					if (attendee.evt__Reg_Email__c != null && attendee.evt__Reg_Email__c != '') {
						listAddresses.add(attendee.evt__Reg_Email__c);
					}
				}
			}
			System.debug('@0-3-3listAddresses: ' + listAddresses);
			toAddress.addAll(listAddresses);
		}
		System.debug('@0-3-4toAddress: ' + toAddress);

		String orgWideId = '';
		OrgWideEmailAddress[] owea;
		String supportEmail;
		evt__Special_Event__c myevent = [SELECT Support_Email__c FROM evt__Special_Event__c WHERE Id = :eventId];
		System.debug('myevent: ' + myevent);
		if (!Test.isRunningTest()) {
			if (myevent.Support_Email__c != null) {
				supportEmail = myevent.Support_Email__c;
				owea = [SELECT Id FROM OrgWideEmailAddress WHERE Address = :supportEmail];
			}
			if (!owea.isEmpty()) {
				orgWideId = owea[0].Id;
				//mail.setOrgWideEmailAddressId(owea[0].Id);
			} else {
				owea = [SELECT Id FROM OrgWideEmailAddress WHERE DisplayName = 'Rotman Events'];
				orgWideId = owea[0].Id;
			}
		}
		try {
			List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
			for (String mainRecipient : toAddress) {
				Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
				if (orgWideId != ''){
					email.setOrgWideEmailAddressId(orgWideId);
				}
				email.setToAddresses(new List<String>{mainRecipient});
				//email.setBccAddresses(bccAddresses);
				email.setReplyTo(replyTo);
				email.setSubject(subject);
				email.setHtmlBody(message);
				emails.add(email);
			}
			if (Test.isRunningTest()) {
				return 'Test is running';
			}
			Messaging.sendEmail(emails, false);
		} catch (Exception e) {
			System.debug('Error: ' + e.getMessage());
			res = 'Error: ' + e.getMessage();
		}
		return res;
	}

	@AuraEnabled
	public static String sendScheduledEmail(Datetime emailDateTime,
											List<String> toAddress,
											List<String> typeList,
											String subject,
											String htmlbody,
											String senderName,
											String replyTo,
											String eventId,
											String beforeDateString) {
		System.debug('@-0-1emailDateTime: ' + emailDateTime);
		System.debug('@-0-2toAddress: ' + toAddress);
		System.debug('@-0-3subject: ' + subject);
		System.debug('@-0-4htmlbody: ' + htmlbody);
		System.debug('@-0-5senderName: ' + senderName);
		System.debug('@-0-6replyTo: ' + replyTo);
		System.debug('@-0-7eventId: ' + eventId);
		System.debug('@-0-8beforeDateString: ' + beforeDateString);
		System.debug('@-0-9typeList: ' + typeList);

		String res = '';
		String jobID = '';
		List<String> listAddresses = new List<String>();
		if (toAddress != null && toAddress.size() > 0) {
			for (String address : toAddress) {
				if (address != null && address != '') {
					listAddresses.add(address);
				}
			}
		}
		if (typeList != null && typeList.size() > 0) {
			List<evt__Attendee__c> attendees = [SELECT Id, evt__Reg_Email__c FROM evt__Attendee__c WHERE evt__Event__c = :eventId AND evt__Event_Fee__r.Name IN :typeList];
			System.debug('@0-2-2attendees: ' + attendees);
			if (attendees.size() > 0) {
				for (evt__Attendee__c attendee : attendees) {
					if (attendee.evt__Reg_Email__c != null && attendee.evt__Reg_Email__c != '') {
						listAddresses.add(attendee.evt__Reg_Email__c);
					}
				}
			}
		}
		System.debug('@0-2-1listAddresses: ' + listAddresses);

		try {
			String cronExpression = generateCronExpression(emailDateTime);
			myEmailScheduler scheduler =
					new myEmailScheduler(listAddresses, subject, htmlbody, senderName, replyTo, eventId);
			String scheduleName = 'Events emails sending Scheduler ' + subject;
			if (Test.isRunningTest()) {
				res = 'Test is running';
			} else {
				jobID = System.schedule(scheduleName, cronExpression, scheduler);

				if (jobID == null || jobID == '') {
					return res;
				}
			}
			Event_Email_Sent__c emailSent = new Event_Email_Sent__c();
			emailSent.Subject_Of_Email__c = subject;
			emailSent.Sender_Name__c = senderName;
			emailSent.Reply_to_Address__c = replyTo;
			emailSent.Recipients_List__c = String.join(listAddresses, ',');
			emailSent.Email_Body__c = htmlbody;
			emailSent.Emails_Sent__c = true;
			emailSent.Recipients__c = listAddresses.size() > 0 ? listAddresses.size() : 0;
			emailSent.Scheduled_DateTime__c = emailDateTime;
			emailSent.Scheduled_Date__c = beforeDateString;
			emailSent.Special_Event__c = eventId;
			If (Test.isRunningTest()) {
				res = 'Test is running';
			} else {
				emailSent.Scheduled_Job_Id__c = jobID;
				insert emailSent;

				res = jobID;
			}
		} catch (Exception e) {
			System.debug('Error: ' + e.getMessage());
			res = e.getMessage();
		}

		return res;
	}

	@AuraEnabled
	public static evt__Special_Event__c getEventName(Id eventId) {
		// Query the Event Name
		return [
				SELECT Name, Price__c, evt__Short_Description__c, evt__Web_Meeting_Join_URL__c, evt__Venue_Name__c, Display_Event_DateTime_Formatted__c, Support_Email__c
				FROM evt__Special_Event__c
				WHERE Id = :eventId
		];
	}

	@AuraEnabled
	public static List<evt__Attendee__c> getAttendees(Id eventId) {
		return [
				SELECT Id, evt__First_Name__c, evt__Last_Name__c, evt__Email__c, evt__Event_Fee__r.Name, evt__Event_Fee__r.evt__Amount__c
				FROM evt__Attendee__c
				WHERE evt__Event__c = :eventId AND evt__Event_Fee__c <> NULL
		];
	}

	private static String generateCronExpression(Datetime emailDateTime) {
		String cronExp = '';
		if (emailDateTime != null) {
			String minute = String.valueOf(emailDateTime.minute());
			String hour = String.valueOf(emailDateTime.hour());
			String day = String.valueOf(emailDateTime.day());
			String month = String.valueOf(emailDateTime.month());
			String year = String.valueOf(emailDateTime.year());
			cronExp = '0 ' + minute + ' ' + hour + ' ' + day + ' ' + month + ' ? ' + year;
		}
		System.debug('Cron expression: ' + cronExp);
		return cronExp;
	}

	@AuraEnabled
	public static List<Event_Email_Sent__c> getScheduledEmails(Id eventId) {
		Datetime currentDateTime = System.now();
		return [
				SELECT Id, Subject_Of_Email__c, Recipients__c, Scheduled_Date__c, Scheduled_DateTime__c, Scheduled_Job_Id__c
				FROM Event_Email_Sent__c
				WHERE Special_Event__c = :eventId AND (Scheduled_DateTime__c > :currentDateTime)
		];
	}

	@AuraEnabled
	public static void abortJob(String jobId) {
		List<Event_Email_Sent__c> scheduledJob = [
				SELECT Id, Scheduled_Job_Id__c, Subject_Of_Email__c
				FROM Event_Email_Sent__c
				WHERE Scheduled_Job_Id__c = :jobId
		];
		if (scheduledJob.size() > 0) {
			delete scheduledJob;
		}
		System.abortJob(jobId);
	}

	@AuraEnabled
	public static List<Event_Email_Sent__c> getSentScheduledEmails(Id eventId) {
		Datetime currentDateTime = System.now();
		return [
				SELECT Id, Subject_Of_Email__c, Recipients__c, Scheduled_Date__c, Scheduled_DateTime__c
				FROM Event_Email_Sent__c
				WHERE Special_Event__c = :eventId AND (Scheduled_DateTime__c < :currentDateTime)
		];
	}
}