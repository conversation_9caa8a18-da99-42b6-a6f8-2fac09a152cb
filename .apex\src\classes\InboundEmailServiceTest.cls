@isTest//(SeeAllData=true)


public class InboundEmailServiceTest {
    
    static testMethod void testApproveApplicationsMethod(){
 	  // create a new email and envelope object
      Messaging.InboundEmail email = new Messaging.InboundEmail() ;
      Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();

      // setup the data for the email
      email.subject = 'Create Contact';
      email.fromAddress = '<EMAIL>';
      email.plainTextBody = 'some body text';
        
      InboundEmailService testInbound=new InboundEmailService ();
      testInbound.handleInboundEmail(email, env);

    }
    
    static testMethod void testApproveApplicationsMethod2(){
        
        Contact c = new Contact();
        c.FirstName = 'Test';
        c.LastName = 'Name';
        c.hed__UniversityEmail__c = '<EMAIL>';
        
        insert c;
         // RecordType rt =[select id,Name from RecordType where sObjectType='Staging_Table__c' AND Name='Template' limit 1];
          Id RecordTypeIdContact = Schema.SObjectType.Staging_Table__c.getRecordTypeInfosByDeveloperName().get('Template').getRecordTypeId();
          Staging_Table__c template = new Staging_Table__c();
       // system.debug('YOOOOOO=======================================================================================================');
       // system.debug(Schema.SObjectType.Staging_Table__c.getRecordTypeInfosByName().get('Template'));
          template.Type__c = 'Draft';
          template.RecordTypeId = RecordTypeIdContact;
          template.Template_Type__c = 'Inbound Email';
          template.Inbound_Email_Address__c = '<EMAIL>';

        	insert template;
          // create a new email and envelope object
          Messaging.InboundEmail email = new Messaging.InboundEmail() ;
          Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();
    
        
          // setup the data for the email
          email.subject = 'Create Contact';
          email.fromAddress = '<EMAIL>';
          email.plainTextBody = 'some body text';
            
          InboundEmailService testInbound=new InboundEmailService ();
          testInbound.handleInboundEmail(email, env);

    }
    static testMethod void testApproveApplicationsMethod3(){
        
      
         // RecordType rt =[select id,Name from RecordType where sObjectType='Staging_Table__c' AND Name='Template' limit 1];
          Id RecordTypeIdContact = Schema.SObjectType.Staging_Table__c.getRecordTypeInfosByDeveloperName().get('Template').getRecordTypeId();
          Staging_Table__c template = new Staging_Table__c();
       // system.debug('YOOOOOO=======================================================================================================');
       // system.debug(Schema.SObjectType.Staging_Table__c.getRecordTypeInfosByName().get('Template'));
          template.Type__c = 'Draft';
          template.RecordTypeId = RecordTypeIdContact;
          template.Template_Type__c = 'Inbound Email';
          template.Inbound_Email_Address__c = 'Example';

        	insert template;
          // create a new email and envelope object
          Messaging.InboundEmail email = new Messaging.InboundEmail() ;
          Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();
    
        
          // setup the data for the email
          email.subject = 'Create Contact';
          email.fromAddress = '<EMAIL>';
          email.plainTextBody = 'some body text';
            
          InboundEmailService testInbound=new InboundEmailService ();
          testInbound.handleInboundEmail(email, env);

    }
}