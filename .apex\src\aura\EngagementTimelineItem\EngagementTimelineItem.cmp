<aura:component >
    <aura:attribute name="timelineItem" type="EngagementTimeline.TimelineItemWrp" />
    
	<div class="slds-timeline__item">
        <!-- span class="slds-assistive-text">{!v.timelineItem.icon}</span -->
        <div class="slds-media">
            <div class="slds-media__body">
                <div class="slds-media slds-timeline__media slds-timeline__media_email">
                    <div class="slds-media__figure slds-timeline__icon">
                        <div class="slds-icon_container slds-icon-standard-email" title="{!v.timelineItem.icon}">
                            <lightning:icon iconName="{!v.timelineItem.icon}" size="small" />
                        </div>
                    </div>
                    
                    <div class="slds-media__body">
                        <h3 class="slds-truncate" ><a onclick="{!c.goToRecord}">{!v.timelineItem.name}</a></h3>
                        <p class="slds-truncate">{!v.timelineItem.description}</p>
                        
                        <ul class="slds-list_horizontal slds-wrap">
                            <aura:renderIf isTrue="{!v.timelineItem.detail1 != null}" >
                                <li class="slds-m-right_large" >
                                    <span class="slds-text-title">{!v.timelineItem.detail1Label}</span>
                                    <span class="slds-text-body_small">
                                        <span>{!v.timelineItem.detail1}</span>
                                    </span>
                                </li>
                            </aura:renderIf>
                            <aura:renderIf isTrue="{!v.timelineItem.detail2 != null}" >
                                <li class="slds-m-right_large" >
                                    <span class="slds-text-title">{!v.timelineItem.detail2Label}</span>
                                    <span class="slds-text-body_small">
                                        <span>{!v.timelineItem.detail2}</span>
                                    </span>
                                </li>
                            </aura:renderIf>
                            <aura:renderIf isTrue="{!v.timelineItem.detail3 != null}" >
                                <li class="slds-m-right_large" >
                                    <span class="slds-text-title">{!v.timelineItem.detail3Label}</span>
                                    <span class="slds-text-body_small">
                                        <span>{!v.timelineItem.detail3}</span>
                                    </span>
                                </li>
                            </aura:renderIf>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="slds-media__figure slds-media__figure_reverse">
                <div class="slds-timeline__actions">
                    <p class="slds-timeline__date">{!v.timelineItem.formatDate}</p>
                </div>
            </div>
        </div>
    </div>
</aura:component>