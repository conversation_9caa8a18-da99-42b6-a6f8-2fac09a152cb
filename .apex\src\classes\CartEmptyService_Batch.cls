/**
 * @description Empties abandoned shopping cart/payments based on the 'Expires' formula on the pymt__PaymentX__c object
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-26
 * @modified 2020-10-05 <EMAIL>
 */
public class CartEmptyService_Batch implements Database.Batchable<SObject> {  

    //Get all payments, which based on the formula field 'Expires' have expired
    public Database.QueryLocator start ( Database.BatchableContext BC ) { 
        Datetime now = System.now();
        return Database.getQueryLocator ( 'SELECT Id, (SELECT Application__c, Application__r.hed__Application_Status__c, Id, Type__c FROM R00N40000001tGNtEAM WHERE pymt__Payment_Completed__c = false), (SELECT Id, evt__invitation_Status__c FROM evt__Attendees__r) FROM pymt__PaymentX__c WHERE Expires__c != null AND Expires__c < :now AND pymt__Status__c != \'Invoiced\'' ); 
    }

    //Delete payment records, triggering an update to the child records
    public void execute ( Database.BatchableContext BC, List<sObject> scope ) { 
   
        List<SObject> recsToDelete = new List<SObject>( scope );
        List<Id> parentIds=new List<Id>();	
        
        for ( pymt__PaymentX__c p : (List<pymt__PaymentX__c>)scope ){
            for ( pymt__Shopping_Cart_Item__c sci : p.R00N40000001tGNtEAM ) { 
                //Vince and Bryce commented out lines below so Applications wouldn't get deleted when cart clear batch runs
                //if ( sci.Type__c == 'EP Program Balance' && sci.Application__c != null && sci.Application__r.hed__Application_Status__c == 'In Progress' )
                    //recsToDelete.add( new hed__Application__c(Id = sci.Application__c ) );

                //Delete shopping cart items which are generated on checkout
                if ( sci.Type__c == 'Program Deposit' || sci.Type__c == 'Application Fee' || sci.Type__c == 'EP Program Deposit' || sci.Type__c == 'Donation' || sci.Type__c == 'Session' )
                    recsToDelete.add( sci );

            }

            for(evt__Attendee__c attendee : p.evt__Attendees__r){
                //Delete abandoned event registrations: 
                if(attendee.evt__Invitation_Status__c == 'Payment Pending'){
                    recsToDelete.add(attendee); 
                    parentIds.add(attendee.Id); 
                }
            }
        }
        List<evt__Attendee__c> guestAttendee = [Select Id from evt__Attendee__c where evt__Primary_Attendee__c in :parentIds];
        if(guestAttendee!=null && guestAttendee.size()>0){
            recsToDelete.addAll(guestAttendee);
        }
        delete recsToDelete; 
    }
    public void finish ( Database.BatchableContext BC ) {}

}