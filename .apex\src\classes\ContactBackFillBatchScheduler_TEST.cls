/**
 * Created by <PERSON><PERSON> on 2022-05-02.
 */

@IsTest
private class ContactBackFillBatchScheduler_TEST {
    @IsTest
    static void testContactBackFillBatch() {
        Map<String, Schema.RecordTypeInfo> acctTypesByDevName = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName();
        Map<String, Schema.RecordTypeInfo> appTypesByDevName = Schema.SObjectType.hed__Application__c.getRecordTypeInfosByDeveloperName();

        Test.startTest();
        Account acc = new Account(
                RecordTypeId                            = acctTypesByDevName.get('Academic_Program').getRecordTypeId(),
                Name                                    = 'Test Degree Program',
                Applicant_Opportunity_Record_Type__c    = 'Degree_Program_Prospect'
        );
        insert acc;

        Contact con = new Contact(LastName = 'TestCon', Email = '<EMAIL>');
        insert con;

        hed__Application__c app = new hed__Application__c(
                RecordTypeId                = appTypesByDevName.get('Full_Time_and_Specialized').getRecordTypeId(),
                hed__Applicant__c           = con.Id,
                hed__Applying_To__c         = acc.Id,
                hed__Application_Status__c  = 'In Progress'
        );
        insert app;

        ContactBackFillBatchScheduler cb = new ContactBackFillBatchScheduler();
        // Seconds Minutes Hours Day_of_month Month Day_of_week optional_year
        String sch = '1 0 * * * ?';
        System.schedule('ContactBackFillBatchJob', sch, cb);

        Test.stopTest();
    }
}