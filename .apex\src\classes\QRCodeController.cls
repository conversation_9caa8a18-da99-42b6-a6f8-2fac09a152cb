public class QRCodeController {
    public String currentRecordId {get;set;}
    public String eventName {get;set;}
    public String eventFee {get;set;}
    public String academicProgram {get;set;}
    public String academicTerm {get;set;}
    public String sessionName {get;set;}
    public Boolean isRegistered {get;set;}
    public Boolean isError {get;set;}
    public Boolean isPermission {get;set;}
    public evt__Attendee__c atndee {get;set;}
    public QRCodeController(){
        isRegistered = false;
        isError = false;
        currentRecordId  = ApexPages.CurrentPage().getparameters().get('id');
        if (Schema.sObjectType.evt__Attendee__c.isUpdateable() && Schema.sObjectType.evt__Attendee__c.fields.evt__Invitation_Status__c.isUpdateable()) {
        	isPermission = true;
        }
        if(isPermission==false){
            isError = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'You don\'t have permission to checkin'));
        }
        if (currentRecordId!=null && Pattern.compile( '[a-zA-Z0-9]{15}|[a-zA-Z0-9]{18}' ).matcher( currentRecordId ).matches() ){
        	atndee = [SELECT Id,Name,evt__First_Name__c,evt__Last_Name__c,evt__Event__c,evt__Invitation_Status__c,evt__Event__r.Name,evt__Event__r.Start_Date_Time_Formula__c,evt__Event__r.End_Date_Time_Formula__c,evt__Event_Fee__r.evt__Description__c,evt__Attended__c, evt__Registration_Type__c, Dietary_Restrictions__c, Academic_Program__r.Name, Academic_Term__r.Name from evt__Attendee__c where Id=:currentRecordId Limit 1];
            if (atndee != null && atndee.evt__Event__c != null){
                eventName = atndee.evt__Event__r.Name;
                eventFee = atndee.evt__Registration_Type__c;
                academicTerm = atndee.Academic_Term__r.Name;
                academicProgram = atndee.Academic_Program__r.Name;
                List<evt__Session_Assignment__c> sessions = [SELECT Id, Name, evt__Event_Session__r.Name from evt__Session_Assignment__c where evt__Attendee__c=:atndee.Id AND evt__Status__c='Registered' Limit 1];
                if(sessions.size() > 0 ){
                    sessionName = sessions[0].evt__Event_Session__r.Name;
                }
            } else {
                isError = true;
                ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Attendee Id is not correct'));
                return;
            }
        } else {
            isError = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Attendee Id is not correct'));
            return;
        }
        if(atndee!=null && atndee.evt__Invitation_Status__c!='Registered'){
            isError = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Attendee is not registered'));
            return;
        }
        if(atndee!=null && atndee.evt__Event__r.Start_Date_Time_Formula__c ==null){
            isError = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Event is not yet defined with start time.'));
            return;
        }
        if(atndee!=null && atndee.evt__Event__r.End_Date_Time_Formula__c < System.now()){
            isError = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'This event has already been passed'));
            return;
        }
        if(atndee!=null && atndee.evt__Event__r.Start_Date_Time_Formula__c > System.now().addHours(2)){
            isError = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.ERROR,'Check-in process can begin only 2 hrs from starting date time of the event'));
            return;
        }
        if(atndee!=null && atndee.evt__Attended__c==true){
            isError = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.WARNING,atndee.evt__First_Name__c+' '+atndee.evt__Last_Name__c+' already marked attended for '+atndee.evt__Event__r.Name));
            return;
        }
        if(atndee!=null && atndee.evt__Invitation_Status__c=='Registered' && isError == false && atndee.evt__Attended__c==false){
            isRegistered = true;
            ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,atndee.evt__First_Name__c+' '+atndee.evt__Last_Name__c+' marked attended successfully for '+atndee.evt__Event__r.Name));
        }
	}
    public void markAttended(){
    	if (isRegistered && atndee.evt__Attended__c==false && isError == false && isPermission ==true){
        	atndee.evt__Attended__c = true;
            update atndee;
      	}
    }
}