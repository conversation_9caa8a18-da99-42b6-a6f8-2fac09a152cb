@isTest
private class CustomPanelController_TEST {
	@testSetup
	static void testSetup () {
		List<Account> accList = new List<Account>();
		Account acc1 = (Account)TestFactory.createSObject(new Account(Name = 'test Account'));
		accList.add(acc1);
		insert accList;

		Contact con = (Contact)TestFactory.createSObject(new Contact(AccountId = accList[0].Id, Email = '<EMAIL>', FirstName = 'Joe', LastName  = 'Curve'));
		insert con;

		// insert task (past activity)
		Task t = new Task();
		t.WhoId = con.Id;
		t.Subject = 'Task';
		t.Status = 'Completed';
		t.ActivityDate = system.today().AddDays(-2);
		insert t;

		// insert event (future activity)
		Event e = new Event();
		e.WhoId = con.Id;
		e.StartDateTime = system.now().AddDays(2);
		e.EndDateTime = system.now().AddDays(3);
		insert e;

		// insert cases
		list<Case> cases = new list<Case>();
		Case cs1 = new Case();
		cs1.ContactId = con.Id;
		cs1.Subject = 'Case Controller Test';
		cs1.Reason = 'Testing';
		cs1.Origin = 'Web';
		cs1.Priority = 'Medium';
		cases.add(cs1);

		insert cases;

		// Create test task
		Task task = new Task();
		task.WhoId = con.Id;
		task.WhatId = cases[0].Id;
		task.Subject = 'Test Task';
		task.Status = 'Completed';
		task.TaskSubtype = 'Call';
		task.ActivityDate = system.today();
		insert task;

	}

	@isTest
	static void testFetchFields() {
		Case myCase = [SELECT Id FROM Case LIMIT 1];
		Schema.FieldSet fieldSet = Schema.SObjectType.Case.fieldSets.getMap().get('StudentRecord_CustomHighlightsPanel');
		List<Schema.FieldSetMember> fieldSetMembers = fieldSet.getFields();

		for (Schema.FieldSetMember fieldSetMember : fieldSetMembers) {
			String fieldName = fieldSetMember.getFieldPath();
			if (fieldName == 'Subject') {
				Object fieldValue = 'Test Subject';
				myCase.put(fieldName, fieldValue);
			}
		}

		String recordId = myCase.Id;
		String objectName = 'Case';

		Test.startTest();
		CustomPanelController.ReturnWP result = CustomPanelController.fetchFields(recordId, objectName, 'StudentRecord_CustomHighlightsPanel');
		Test.stopTest();
	}

	@isTest
	static void testMessage() {
		CustomPanelController.ReturnWP returnWP = new CustomPanelController.ReturnWP();
		returnWP.message = 'Test Message';
		System.assertEquals('Test Message', returnWP.message);
	}

	@isTest
	static void testInvalidId() {
		Case myCase = [SELECT Id FROM Case LIMIT 1];

		String objectName = 'Case';

		Test.startTest();
		CustomPanelController.ReturnWP result = CustomPanelController.fetchFields('', objectName, 'Student_Record - Custom Highlights Panel');
		Test.stopTest();
	}

	@isTest
	static void testInvalidFieldset() {
		Case myCase = [SELECT Id FROM Case LIMIT 1];

		String objectName = 'Case';

		Test.startTest();
		CustomPanelController.ReturnWP result = CustomPanelController.fetchFields(myCase.Id, objectName, '');
		Test.stopTest();
	}
}