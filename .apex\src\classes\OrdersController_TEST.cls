@isTest
private class OrdersController_TEST {
	@TestSetup
	private static void testDataSetup() {
		Taxing_Authority__c tax1 = new Taxing_Authority__c(
				Name                = 'Ontario',
				State_Province__c   = 'ON',
				Country__c          = 'CA',
				Tax_Rate__c         = 13
		);
		insert tax1;

		Id fulltimeId = ApplicationService.FTandSpecializedRTId;
		Account testAccount = new Account(Name = 'Test Account');
		insert testAccount;

		Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>', AccountId = testAccount.Id);
		insert testContact;

		String cookieName = String.valueOf(dateTime.now());

		pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
		cart.pymt__Cart_UID__c = cookieName;
		insert cart;

		hed__Term__c trm = new hed__Term__c(hed__Account__c=testAccount.Id,name='Spring 2020');
		insert trm;

		Cohort__c cohort = new Cohort__c(
				Name = 'FTMBA - 2025',
				Start_Term__c = trm.Id,
				Program__c = testAccount.Id,
				Orientation_Date__c = Date.valueOf('2024-04-09 09:30:40'),
				Key__c = 'FTMBA - 2025'
		);
		insert cohort;

		Program_Term_Availability__c pta = new Program_Term_Availability__c(
				Active__c = true,
				Program__c = testAccount.Id,
				Cohort__c = cohort.Id,
				Program_Start_Date__c = Date.valueOf('2022-12-09 10:15:30'),
				Program_End_Date__c = Date.valueOf('2024-04-09 09:30:40'),
				Term__c = trm.Id
		);
		insert pta;

		hed__Application__c app = new hed__Application__c(
				Program_Term_Availability__c = pta.Id,
				hed__Application_Status__c = 'In Progress',
				RecordTypeId = fulltimeId,
				hed__Applying_To__c = testAccount.Id,
				hed__Term__c = trm.Id
		);
		insert app;

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c = '$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.Start_Date__c = Date.today().addDays(30);
		event.End_Date__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.Venue_Type__c = 'In-Person';
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.Tags__c = 'Strategic Communications';
		event.Thumbnail_Image__c =
				'<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';

		List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
		listOfEventToInsert.add(event);

		insert listOfEventToInsert;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = listOfEventToInsert[0].Id;
		fee.evt__Amount__c = 0.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = testContact.Id;
		item.Event_Fee__c = fee.Id;
		item.pymt__Unit_Price__c = 0.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';

		insert item;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = testContact.Id;
		at.evt__Invitation_Status__c = 'Registered';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.Shopping_Cart_Item__c = item.Id;
		at.evt__Event__c = listOfEventToInsert[0].Id;
		at.evt__Reg_Email__c = '<EMAIL>';
		at.Industry__c = 'Technology';

		insert at;

		shopping_cart_item_details__c testCartItemDetail = new shopping_cart_item_details__c();
		testCartItemDetail.SC_event__c = listOfEventToInsert[0].Id;
		testCartItemDetail.Event_Fee__c = fee.Id;
		testCartItemDetail.Contact__c = testContact.Id;
		testCartItemDetail.Shopping_Cart_Item__c = item.Id;
		testCartItemDetail.Attendee__c = at.Id;
		testCartItemDetail.Item_Unit_Price__c = 5.0;
		testCartItemDetail.Item_Quantity__c = 1.0;
		testCartItemDetail.Item_Discount_Amount__c = 0.0;
		testCartItemDetail.Item_Gross_Amount__c = 5.0;
		testCartItemDetail.Item_Tax_Amount__c = 0.0;
		testCartItemDetail.Item_Total_Amount__c = 5.0;

		insert testCartItemDetail;

	}

	@IsTest
	static void testGetOrders() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		System.debug('testEvent: ' + testEvent);
		test.startTest();
		List<Shopping_Cart_Item_Details__c> orders = OrdersController.getOrders(null, null, 3, testEvent.Id, false);
		test.stopTest();
		System.debug('orders: ' + orders);
	}

	@IsTest
	static void testgetMyOrders() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
		test.startTest();
		List<Shopping_Cart_Item_Details__c> myorders = OrdersController.getMyOrders(null, null, 3, testEvent.Id, false);
		test.stopTest();
		System.debug('myorders: ' + myorders);
		//System.assertEquals(1, myorders.size());
	}

	@IsTest
	static void testgetEventFeeTypes() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
		test.startTest();
		List<evt__Event_Fee__c> eventFeeTypes = OrdersController.getEventFeeTypes(testEvent.Id);
		test.stopTest();
		System.debug('eventFeeTypes: ' + eventFeeTypes);
		System.assertEquals(1, eventFeeTypes.size());
	}

	@IsTest
	static void testgetEventInfo() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event'];
		test.startTest();
		evt__Special_Event__c event = OrdersController.getEventInfo(testEvent.Id);
		test.stopTest();
		System.debug('event: ' + event);
		System.assertEquals('Special event', event.Name);
	}

	@IsTest
	static void testResendOrder() {
		// Call the method under test
		pymt__Shopping_Cart_Item__c testitem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];

		Test.startTest();
		String result = OrdersController.ResendOrder(testitem.Id, testContact.Id);
		Test.stopTest();

	}

	@IsTest
	static void testGetIndustryValues() {
		evt__Attendee__c testAttendee = [SELECT Id FROM evt__Attendee__c LIMIT 1];

		Test.startTest();
		List<String> industryValues = OrdersController.getIndustryValues();
		Test.stopTest();
	}

	@IsTest
	static void testGetProgramValues() {
		evt__Special_Event__c event = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		Test.startTest();
			List<Map<String, String>> programMaps = OrdersController.getProgramValues(event.Id);
		Test.stopTest();
	}

	@IsTest
	static void testGetProgramTermValues() {
		Account testAccount = [SELECT Id FROM Account LIMIT 1];
		Test.startTest();
		List<Map<String, String>> result = OrdersController.getProgramTermValues(testAccount.Id);
		Test.stopTest();
	}

	@IsTest
	static void testUpdateSelectedOrder1() {
		UserRole userrole = [Select Id, DeveloperName From UserRole Where DeveloperName = 'Site_Administrators' Limit 1];
		User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		adminUser.UserRoleId = userRole.Id;
		update adminUser;
		System.debug('---admin ' + adminUser);

		System.runAs(adminUser){
			evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
			pymt__Shopping_Cart_Item__c testItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
			Contact testContact = [SELECT Id FROM Contact LIMIT 1];
			evt__Event_Fee__c testFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];

			evt__Attendee__c attendee = new evt__Attendee__c();
			attendee.evt__Event__c = testEvent.Id;
			attendee.evt__Invitation_Status__c = 'Registered';
			attendee.evt__Reg_Email__c = '<EMAIL>';
			attendee.Shopping_Cart_Item__c = testItem.Id;
			attendee.evt__Event_Fee__c = testFee.Id;
			insert attendee;

			Shopping_Cart_Item_Details__c order = new Shopping_Cart_Item_Details__c();
			order.SC_event__c = testEvent.Id;
			order.Attendee__c = attendee.Id;
			order.Contact__c = testContact.Id;
			order.Item_Unit_Price__c = 1;
			order.Item_Gross_Amount__c = 0;
			order.Item_Tax_Amount__c = 0;
			order.Item_Total_Amount__c = 0;
			order.Void_ticket__c = false;
			order.Shopping_Cart_Item__c = testItem.Id;
			order.Event_Fee__c = testFee.Id;
			insert order;

			Test.startTest();
			Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
			Map<String, String> result =
					OrdersController.updateSelectedOrder(new List<String>{ attendee.Id }, 'Cancellation Reason');
			Test.stopTest();
		}
	}

	@IsTest
	static void testUpdateSelectedOrder2() {
		UserRole userrole = [Select Id, DeveloperName From UserRole Where DeveloperName = 'Site_Administrators' Limit 1];
		User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		adminUser.UserRoleId = userRole.Id;
		update adminUser;
		System.debug('---admin ' + adminUser);

		System.runAs(adminUser){
			evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
			pymt__Shopping_Cart_Item__c testItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
			Contact testContact = [SELECT Id FROM Contact LIMIT 1];
			evt__Event_Fee__c testFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];

			pymt__PaymentX__c pymt = new pymt__PaymentX__c();
			pymt.Name = 'payment_' + string.valueOf(datetime.now());
			pymt.Order_Id__c = 'Payment:' + string.valueOf(datetime.now());
			pymt.pymt__Transaction_Type__c = 'Payment';
			pymt.pymt__Transaction_Id__c = '123456';
			pymt.pymt__Status__c = 'Completed';
			pymt.pymt__Amount__c = 10;
			pymt.pymt__Payment_Type__c = 'Credit Card';
			pymt.pymt__Payment_Processor__c = 'Global Pay';
			pymt.pymt__Billing_First_Name__c = 'TEST';
			pymt.pymt__Billing_Last_Name__c = 'kite';
			insert pymt;

			testItem.pymt__Payment__c = pymt.Id;
			update testItem;

			evt__Attendee__c attendee = new evt__Attendee__c();
			attendee.evt__Event__c = testEvent.Id;
			attendee.evt__Invitation_Status__c = 'Registered';
			attendee.evt__Reg_Email__c = '<EMAIL>';
			attendee.Shopping_Cart_Item__c = testItem.Id;
			attendee.evt__Event_Fee__c = testFee.Id;
			insert attendee;

			Shopping_Cart_Item_Details__c order = new Shopping_Cart_Item_Details__c();
			order.SC_event__c = testEvent.Id;
			order.Attendee__c = attendee.Id;
			order.Contact__c = testContact.Id;
			order.Item_Unit_Price__c = 1;
			order.Item_Gross_Amount__c = 5;
			order.Item_Tax_Amount__c = 0;
			order.Item_Total_Amount__c = 5;
			order.Void_ticket__c = false;
			order.Shopping_Cart_Item__c = testItem.Id;
			order.Event_Fee__c = testFee.Id;
			insert order;

			Test.startTest();
			Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
			Map<String, String> result =
					OrdersController.updateSelectedOrder(new List<String>{ attendee.Id }, 'Cancellation Reason');
			Test.stopTest();
		}
	}

	@IsTest
	static void testCreateInputContact() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];

		String contactFName = 'TestFirstName2';
		String contactLName = 'TestLastName2';
		String contactEmail = '<EMAIL>';
		String eventName = 'Special event';
		Decimal subTotal = 100.00;
		Decimal discount = 0.00;
		Decimal tax = 0.00;
		Decimal total = 100.00;
		String eventId = testEvent.Id;
		Integer quantity = 1;

		Test.startTest();
		Map<String, String> result = OrdersController.createInputContact(
				contactFName,
				contactLName,
				contactEmail,
				eventName,
				subTotal,
				discount,
				tax,
				total,
				eventId,
				quantity
		);
		Test.stopTest();
	}

	@IsTest
	static void testCreateNewAttendeeRecords1() {
		// Set up test data
		evt__Special_Event__c testEvent = [SELECT Id, Name FROM evt__Special_Event__c LIMIT 1];
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];
		evt__Event_Fee__c testFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];

		pymt__Shopping_Cart_Item__c testItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		pymt__PaymentX__c newayment = new pymt__PaymentX__c();

		List<evt__Attendee__c> lstAttendees = new List<evt__Attendee__c>();
		evt__Attendee__c attendee = new evt__Attendee__c();
		attendee.evt__Event__c = testEvent.Id;
		attendee.evt__Reg_Email__c = '<EMAIL>';
		attendee.Shopping_Cart_Item__c = testItem.Id;
		attendee.evt__Event_Fee__c = testFee.Id;
		attendee.evt__Reg_First_Name__c = 'test4';
		attendee.evt__Reg_Last_Name__c = 'test4';
		lstAttendees.add(attendee);

		List<EventFeeCartWrapper> myTickets = new List<EventFeeCartWrapper>();
		EventFeeCartWrapper myFeeWrapper = new EventFeeCartWrapper();
		myFeeWrapper.eventFeeId = testFee.Id;
		myFeeWrapper.eventId = testEvent.Id;
		myFeeWrapper.quantity = 1;
		myFeeWrapper.price = 10.00;
		myFeeWrapper.tax = 1.30;
		myTickets.add(myFeeWrapper);

		Test.startTest();
		pymt__PaymentX__c result = OrdersController.createNewAttendeeRecords(
				testItem.Id, testEvent.Name, testContact.Id, new List<evt__Attendee__c>(), new pymt__PaymentX__c(),
				10.0, 0.0, 1.3, 11.3, testEvent.Id, new List<EventFeeCartWrapper>(), 1, 'paid with credit/debit card'
		);
		Test.stopTest();

	}

	@IsTest
	static void testCreateNewAttendeeRecords2() {
		// Set up test data
		evt__Special_Event__c testEvent = [SELECT Id, Name FROM evt__Special_Event__c LIMIT 1];
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];
		evt__Event_Fee__c testFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];

		pymt__Shopping_Cart_Item__c testItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		pymt__PaymentX__c newayment = new pymt__PaymentX__c();

		List<evt__Attendee__c> lstAttendees = new List<evt__Attendee__c>();
		evt__Attendee__c attendee = new evt__Attendee__c();
		attendee.evt__Event__c = testEvent.Id;
		attendee.evt__Reg_Email__c = '<EMAIL>';
		attendee.Shopping_Cart_Item__c = testItem.Id;
		attendee.evt__Event_Fee__c = testFee.Id;
		attendee.evt__Reg_First_Name__c = 'test3';
		attendee.evt__Reg_Last_Name__c = 'test3';
		lstAttendees.add(attendee);

		List<EventFeeCartWrapper> myTickets = new List<EventFeeCartWrapper>();
		EventFeeCartWrapper myFeeWrapper = new EventFeeCartWrapper();
		myFeeWrapper.eventFeeId = testFee.Id;
		myFeeWrapper.eventId = testEvent.Id;
		myFeeWrapper.quantity = 1;
		myFeeWrapper.price = 0.00;
		myFeeWrapper.tax = 0.00;
		myTickets.add(myFeeWrapper);

		Test.startTest();
		pymt__PaymentX__c result = OrdersController.createNewAttendeeRecords(
				testItem.Id, 'test3', testContact.Id, lstAttendees, newayment,
				0.0, 0.0, 0.0, 0.0, testEvent.Id, myTickets, 1, ''
		);
		Test.stopTest();
	}

	@IsTest
	static void testUpdateRefundEventFeeType() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Event_Fee__c testFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		evt__attendee__c attendee = [SELECT Id FROM evt__attendee__c LIMIT 1];

		evt__Event_Fee__c newFee = new evt__Event_Fee__c();
		newFee.Name = 'New Fee Type';
		newFee.evt__Event__c = testEvent.Id;
		newFee.evt__Amount__c = 10.00;
		insert newFee;

		Test.startTest();
		String result = OrdersController.updateRefundEventFeeType(testEvent.Id, attendee.Id, 'Test Cart', testFee.Id, newFee.Id);
		Test.stopTest();
	}

/*	@IsTest
	static void testProcessPayment() {
		pymt__PaymentX__c pymt = new pymt__PaymentX__c();
		pymt.Name = 'payment_'+string.valueOf(datetime.now());
		pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
		pymt.pymt__Transaction_Type__c = 'Payment';
		pymt.pymt__Status__c = 'Scheduled';
		pymt.pymt__Amount__c = 1000;
		pymt.pymt__Payment_Type__c ='Credit Card';
		pymt.pymt__Billing_First_Name__c ='TEST';
		pymt.pymt__Billing_Last_Name__c ='kite';
		insert pymt;

		String cardNo = '****************';
		String cvd = '123';
		String expYr = '2026';
		String expMo = '04';
		String accountName = 'transaction_processing';

		Test.startTest();
		Map<String, String> responseMap = OrdersController.processPayment(pymt, cardNo, cvd, expYr, expMo, accountName);
		Test.stopTest();
	}*/

	@IsTest
	static void testPaymentTransfer() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		pymt__Shopping_Cart_Item__c shoppingCartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		evt__attendee__c attendee = [SELECT Id FROM evt__attendee__c LIMIT 1];
		evt__Event_Fee__c testFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];

		evt__Event_Fee__c newFee = new evt__Event_Fee__c();
		newFee.Name = 'New Fee Type';
		newFee.evt__Event__c = testEvent.Id;
		newFee.evt__Amount__c = 10.00;
		insert newFee;

		Test.startTest();
		Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
		String result = OrdersController.PaymentTransfer(
				attendee.Id,
				testEvent.Id,
				newFee.Id,
				testFee.Id,
				'cardName',
				'cardNo',
				'cvv',
				'expMonth',
				'expYear',
				'accountName',
				100.00,
				10.00,
				13.00,
				123.00,
				'customize1',
				'customize2',
				'customize3',
				'customize4',
				'customize5',
				'mailingStreet',
				'mailingCity',
				'mailingState',
				'mailingPostalCode',
				'mailingCountry'
		);
		Test.stopTest();
	}

	@IsTest
	static void testTicketRefundTransfer() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		pymt__Shopping_Cart_Item__c shoppingCartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		evt__attendee__c attendee = [SELECT Id FROM evt__attendee__c LIMIT 1];
		evt__Event_Fee__c testFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];

		evt__Event_Fee__c newFee = new evt__Event_Fee__c();
		newFee.Name = 'New Fee Type';
		newFee.evt__Event__c = testEvent.Id;
		newFee.evt__Amount__c = 10.00;
		newFee.evt__Active__c = true;
		insert newFee;

		pymt__PaymentX__c pymt = new pymt__PaymentX__c();
		pymt.Name = 'payment_'+string.valueOf(datetime.now());
		pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
		pymt.pymt__Transaction_Type__c = 'Payment';
		pymt.pymt__Status__c = 'Scheduled';
		pymt.pymt__Amount__c = 10;
		pymt.pymt__Payment_Type__c ='Credit Card';
		pymt.pymt__Billing_First_Name__c ='TEST';
		pymt.pymt__Billing_Last_Name__c ='kite';
		insert pymt;

		shoppingCartItem.pymt__Payment__c = pymt.Id;
		update shoppingCartItem;

		Test.startTest();
		Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
		String result = OrdersController.TicketRefundTransfer(attendee.Id, testEvent.Id, newFee.Id, testFee.Id, 10, 0);
		Test.stopTest();
	}

	@isTest
	static void testGetPaymentDetails() {
		pymt__PaymentX__c testPayment = new pymt__PaymentX__c(
				pymt__Status__c = 'Completed',
				pymt__Transaction_Type__c = 'Payment',
				pymt__AMount__c = 100.00,
				Amount_Paid__c = 100.00,
				Amount_Refunded__c = 0.00,
				pymt__Transaction_Id__c = '123456789'
		);
		insert testPayment;

		Test.startTest();
			pymt__PaymentX__c result = RefundController.getPaymentDetails(testPayment.Id);
		Test.stopTest();
	}

	@IsTest
	static void testProcessRefund() {
		pymt__PaymentX__c testPayment = new pymt__PaymentX__c(
				pymt__Status__c = 'Completed',
				pymt__Transaction_Type__c = 'Payment',
				pymt__AMount__c = 100.00,
				Amount_Paid__c = 100.00,
				Amount_Refunded__c = 0.00,
				pymt__Transaction_Id__c = '123456789'
		);
		insert testPayment;

		// Call the method to be tested
		Test.startTest();
		Test.setMock(HttpCalloutMock.class, new GlobalPayConnectMockCallout());
		Map<String, String> response = RefundController.processRefund(testPayment.Id, 100.00, 5.00, 'Refund Reason');
		Test.stopTest();
	}
}