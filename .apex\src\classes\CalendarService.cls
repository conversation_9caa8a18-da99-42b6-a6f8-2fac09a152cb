/*********************************************************
 * @description Class for Calendar Related methods
 * <AUTHOR>
 * @created 7-JUNE-2020 
*********************************************************/
public class CalendarService {

/*********************************************************
* @description method to create Calendar Invite. UTR-1210
* <AUTHOR>
* @created 7-JUNE-2020
*********************************************************/
    public static Blob createInvite(DateTime startDate, DateTime endDate, String title, String summary, String location) {
        System.debug('startDate : '+startDate+' : endDate : '+endDate+' : title : '+title+' : summary : '+summary+' : location : '+location);

        String tzid = UserInfo.getTimeZone().getID();

        DateTime dt=DateTime.now();
        String currentdatetime = dt.format('yyyyMMdd\'T\'HHmmss');
        //DateTime currentDateTime = dt.addSeconds(estTimeZone.getOffset(dt)/1000);
        //String currentdatetime=dt.formatGMT('yyyyMMdd\'T\'HHmmss\'Z\'');	//dt.format('MM-dd-yyyy HH:mm:ss', 'EST');//(UTC-05:00) Eastern Time (US & Canada)');
        //String currentdatetime=dt.formatGMT('yyyyMMdd\'T\'HHmmss\'Z\'');	//dt.format('MM-dd-yyyy HH:mm:ss', 'EST');//(UTC-05:00) Eastern Time (US & Canada)');
        DateTime dt1=startDate;
        String startdatetime = dt1.format('yyyyMMdd\'T\'HHmmss');
        //DateTime estStartDateTime = new TimeZoneService().convertDateTimeToOtherTimeZone(dt1, 'America/Toronto');
        //DateTime estStartDateTime = dt1.addSeconds(estTimeZone.getOffset(dt1) / 1000);
        //System.debug('estStartDateTime : '+estStartDateTime);
        DateTime dt2=endDate;
        String enddatetime = dt2.format('yyyyMMdd\'T\'HHmmss');
        //DateTime estEndDateTime = dt2.addSeconds(estTimeZone.getOffset(dt2)/1000);
        //DateTime estEndDateTime = new TimeZoneService().convertDateTimeToOtherTimeZone(dt2, 'America/Toronto');
        //DateTime estEndDateTime = dt2.addSeconds(estTimeZone.getOffset(dt2) / 1000);
        //System.debug('estEndDateTime : '+estStartDateTime);
        //String startdatetime=dt1.formatGMT('yyyyMMdd\'T\'HHmmss\'Z\'');		//dt1.format('MM-dd-yyyy HH:mm:ss', 'EST');
        //String enddatetime=dt2.formatGMT('yyyyMMdd\'T\'HHmmss\'Z\'');		//dt2.format('MM-dd-yyyy HH:mm:ss', 'EST');
		system.debug('startdatetime : '+startdatetime+' :enddatetime : '+enddatetime);
        String txtInvite = ''; 
        txtInvite += 'BEGIN:VCALENDAR\n';        
        txtInvite += 'PRODID:-//Microsoft Corporation//Outlook 12.0 MIMEDIR//EN\n';			//'PRODID::-//hacksw/handcal//NONSGML v1.0//EN\n';
        txtInvite += 'VERSION:2.0\n';
        txtInvite += 'METHOD:REQUEST\n';
        txtInvite += 'CALSCALE:GREGORIAN\n'; 
        txtInvite += 'X-MS-OLK-FORCEINSPECTOROPEN:TRUE\n';
        //txtInvite += 'BEGIN:VTIMEZONE\n';
        //txtInvite += 'TZID:America/Toronto\n';
        //txtInvite += 'END:VTIMEZONE\n';
        txtInvite += 'BEGIN:VEVENT\n';
        txtInvite += 'CLASS:PUBLIC\n';
        //txtInvite += 'CREATED;TZID=America/Toronto:'+currentdatetime.format('yyyyMMdd\'T\'HHmmss')+'\n';
        //txtInvite += 'DTSTAMP:'+currentdatetime+'\n';
        txtInvite += 'LAST-MODIFIED;TZID='+ tzid + ':' + currentdatetime + '\n';
        txtInvite += 'DTSTART;TZID='+ tzid + ':' + startdatetime + '\n';
        txtInvite += 'DTEND;TZID='+ tzid + ':' + enddatetime + '\n';
        txtInvite += 'LOCATION:'+location+'\n';
        txtInvite += 'ORGANIZER;CN="Rotman School of Management University of Toronto":mailto:'+'<EMAIL>'+'\n';
        txtInvite += 'PRIORITY:5\n';
        txtInvite += 'SEQUENCE:0\n';
        txtInvite += 'SUMMARY:'+summary+'\n';
        txtInvite += 'LANGUAGE=en-us:Meeting Reminder\n';	//+title+'\n';
        txtInvite += 'TRANSP:OPAQUE\n';
        txtInvite += 'STATUS:CONFIRMED\n';
        txtInvite += 'UID:'+dt1.format('yyyyMMdd\'T\'HHmmss')+'<EMAIL>\n';
        txtInvite += 'X-ALT-DESC;FMTTYPE=text/html:<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN"><HTML><HEAD><META NAME="Generator" CONTENT="MS Exchange Server version 08.00.0681.000"><TITLE></TITLE></HEAD><BODY><!-- Converted from text/plain format --></BODY></HTML>\n';
        txtInvite += 'X-MICROSOFT-CDO-BUSYSTATUS:BUSY\n';
        txtInvite += 'X-MICROSOFT-CDO-IMPORTANCE:1\n';
        txtInvite += 'END:VEVENT\n';
        txtInvite += 'END:VCALENDAR'; 
        return Blob.valueOf(txtInvite);
    }

/*********************************************************
 * @description method to create Calendar Event. UTR-2203
 * <AUTHOR> Singh
 * @created 21-JULY-2020
**********************************************************/
	public static List<Event> createEvent(List<SObject> objectList, String startDate, String endDate, String owner, String contact, String location, String description) {

        List<Event> eventList = new List<Event>();
        /*
        RecordType recType = [Select Id, Developername from RecordType Where Developername='Non_Advising_Event' LIMIT 1];

        for(SObject rec : objectList){
            //system.debug('......................Session Assignment ='+(String)rec.get(location));
            Event e = new Event();
            e.WhatId = (Id)rec.get('Id');
            if(!String.isBlank(owner)) 			e.OwnerId = (Id)rec.get(owner);
            if(!String.isBlank(contact)) 		e.WhoId = (Id)rec.get(contact);
            if(!String.isBlank(location)) 		e.Location = (String)rec.get(location);
            if(!String.isBlank(description)) 	e.Description = (String)rec.get(description);
            if(!String.isBlank(endDate)) 		e.EndDateTime = (DateTime)rec.get(endDate);
            if(!String.isBlank(startDate))	 	e.StartDateTime = (DateTime)rec.get(startDate);
            e.Subject 		= 'Meeting';
            e.Type 			= 'Scheduled';
            e.RecordTypeId 	= recType.Id;
            eventList.add(e);
        }
        */
        return eventList;
    }

/*********************************************************
 * @description method to find Calendar Event. UTR-2203
 * <AUTHOR> Singh
 * @created 21-JULY-2020
**********************************************************/
	public static List<Event> findEvent(List<SObject> objectList, String startDate, String endDate, String owner, String contact, String location, String description) {

        List<Event> eventList = new List<Event>();
        /*
        RecordType recType = [Select Id, Developername from RecordType Where Developername='Standard_Event' LIMIT 1];
        
        for(SObject rec : objectList){
            system.debug('......................Session Assignment ='+(String)rec.get(location));

            String queryStr = 'Select Id from Event Where WhatId = \'' + (Id)rec.Id + '\'';
            system.debug('Query String = '+ queryStr);

            List<Event> eList = Database.query(queryStr);
            eventList.addAll(eList);
        }
        */
        
        return eventList;
    }
    
}