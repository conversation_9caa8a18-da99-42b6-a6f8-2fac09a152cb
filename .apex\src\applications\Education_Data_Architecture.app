<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <brand>
        <headerColor>#0070D2</headerColor>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Education Data Architecture</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Active Student Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Active Student Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <tabs>standard-home</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>hed__Course__c</tabs>
    <tabs>hed__Term__c</tabs>
    <tabs>hed__Course_Offering__c</tabs>
    <tabs>hed__Program_Plan__c</tabs>
    <tabs>hed__Trigger_Handler__c</tabs>
    <tabs>hed__Education_Cloud_Settings</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>Education_Data_Architecture_UtilityBar</utilityBar>
</CustomApplication>
