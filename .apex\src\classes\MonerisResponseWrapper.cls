public class MonerisResponseWrapper {
    @AuraEnabled 
    public response response{get; set;}
    
    public class response{
      @AuraEnabled  public receipt receipt{get; set;}
    }
   
    public class receipt{
        @AuraEnabled  public string ReceiptId{get; set;}
        @AuraEnabled  public string ReferenceNum{get; set;}
        @AuraEnabled  public string ResponseCode{get; set;}	
        @AuraEnabled  public string ISO{get; set;}
        @AuraEnabled  public string AuthCode{get; set;}
        @AuraEnabled  public string TransTime{get; set;}
        @AuraEnabled  public string TransDate{get; set;}
        @AuraEnabled  public string TransType{get; set;}
        @AuraEnabled  public string Complete{get; set;}
        @AuraEnabled  public string Message{get; set;}
        @AuraEnabled  public string TransAmount{get; set;}
        @AuraEnabled  public string CardType{get; set;}
        @AuraEnabled  public string TransID{get; set;}
        @AuraEnabled  public string TimedOut{get; set;}
        @AuraEnabled  public string BankTotals{get; set;}
        @AuraEnabled  public string Ticket{get; set;}
        @AuraEnabled  public string IsVisaDebit{get; set;}
    }
}