/**
* @description    Test class for TestDateStampDupRulesApex_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-13-04
* @modified 2020-13-04
*/
@isTest
public class TestDateStampDupRulesApexTest {
	@testSetup
    static void testSetup () {
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('TestDateStampDupRulesApex_TDTM', 'Test__c', 'BeforeInsert;BeforeUpdate', 2.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        insert c; 
		
        //Create test Account:
        Account a = new Account(Name='TestAccount');
		insert a;
        
        //Create related work history records: 
        List<hed__Test__c> workHistoriesToInsert = new List<hed__Test__c>(); 
        workHistoriesToInsert.add(new hed__Test__c(hed__Test_Type__c = 'GRE', hed__Contact__c = c.Id, hed__Test_Date__c = Date.Today().adddays(3))); 
        workHistoriesToInsert.add(new hed__Test__c(hed__Test_Type__c = 'GRE', hed__Contact__c = c.Id, hed__Test_Date__c = Date.Today().adddays(3))); 
        insert workHistoriesToInsert;
    }
    @isTest 
    static void testDateStampTest() {
        List<hed__Test__c> tstLst = new List<hed__Test__c>();
        tstLst = [Select Id,hed__Test_Type__c from hed__Test__c];
        for(hed__Test__c tst : tstLst){
            tst.hed__Test_Type__c = 'GMAT';
        }
        Test.StartTest();
        	update tstLst;
        Test.StopTest();
    }
}