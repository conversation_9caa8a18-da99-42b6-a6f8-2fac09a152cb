@isTest
public class EnrolledStudentsUpdate_TEST {

    @TestSetup
    static void testSetup () {
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('CourseCnctnCreateReqmntfulfillment_TDTM', 'Course_Enrollment__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        //Create test Account
        Account a = (Account)TestFactory.createSObject(new Account(Name='TestAccount'));
        insert a;

        //Create test contacts 
        Contact c1 = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test1', LastName = 'Student', Email = '<EMAIL>'));
        insert c1;
        Contact c2 = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test2', LastName = 'Student'));
        insert c2;
        Contact c3 = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test3', LastName = 'Student'));
        insert c3;
        Contact c4 = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test4', LastName = 'Student'));
        insert c4;
		Contact c5 = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test5', LastName = 'Student'));
        insert c5;

        //Create Term
        hed__Term__c term = new hed__Term__c(hed__Account__c=a.Id,name='Fall 2021');
        insert term;
		
        //Create course
        hed__Course__c course = new hed__Course__c(hed__Account__c=a.Id,name='MBA 2021');
        insert course;
		
        //Create Course Offering
        hed__Course_Offering__c cOffering = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering',hed__Term__c=term.Id,hed__End_Date__c = Date.Today().addDays(30));
        insert cOffering;
		
        //Create a Program Plan
        hed__Program_Plan__c progPlan = new hed__Program_Plan__c(name='Test Program Plan',hed__Account__c=a.Id,hed__Start_Date__c=Date.Today(),hed__End_Date__c = Date.Today().addDays(30));
        insert progPlan;

    }
	
	@isTest
    public static void testBatch(){
        
		Test.startTest();
        //Test for enrolled students field update functionality
            Contact c1 = [Select id from Contact limit 1];
            Contact c2 = [Select id from Contact limit 1];
            Contact c3 = [Select id from Contact limit 1];
            Contact c4 = [Select id from Contact limit 1];
    
            hed__Course_Offering__c cOffering = [Select Id, Enrolled_Students__c from hed__Course_Offering__c WHERE hed__End_Date__c >= Today limit 1];
            hed__Course_Enrollment__c crsEnrollment1 = new hed__Course_Enrollment__c(hed__Contact__c = c1.Id,hed__Course_Offering__c=cOffering.Id,hed__Status__c='Current');
            insert crsEnrollment1;
            hed__Course_Enrollment__c crsEnrollment2 = new hed__Course_Enrollment__c(hed__Contact__c = c2.Id,hed__Course_Offering__c=cOffering.Id,hed__Status__c='Incomplete');
            insert crsEnrollment2;
            hed__Course_Enrollment__c crsEnrollment3 = new hed__Course_Enrollment__c(hed__Contact__c = c3.Id,hed__Course_Offering__c=cOffering.Id,hed__Status__c='Former');
            insert crsEnrollment3;
            hed__Course_Enrollment__c crsEnrollment4 = new hed__Course_Enrollment__c(hed__Contact__c = c4.Id,hed__Course_Offering__c=cOffering.Id,hed__Status__c='Deferred');
            insert crsEnrollment4;
        
            Database.executeBatch(new EnrolledStudentsUpdate_Batch());
        Test.stopTest();
        
		hed__Course_Offering__c co = [SELECT Id, Enrolled_Students__c from hed__Course_Offering__c WHERE Id =:cOffering.Id];
        System.assertEquals(3, co.Enrolled_Students__c);
        
	}
	
    @isTest
    public static void testEnrolledStudents (){

        Test.startTest();
            String jobId = System.schedule('Update Enrolled Students', '0 0 0 1 1 ? 2030', new EnrolledStudentsUpdate_Scheduler());
        Test.stopTest();
		
		System.assert(!String.isBlank(jobId), 'Batch does not have an job Id so did not get scheduled.');
    }
}