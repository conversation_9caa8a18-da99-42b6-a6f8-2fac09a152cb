<aura:component implements="flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,force:lightningQuickAction,forceCommunity:availableForAllPageTypes" 
                access="global" controller="PaymentTerminalController" >
    <!-- force:lightningQuickAction,-->
    <aura:handler name="init" value="{!this}" action="{!c.initData}"/>
    <aura:attribute name="contactId" type="Contact" />
    <aura:attribute name="recordId" type="String" />
    <aura:attribute name="subtotal" type="Double" default="0.00" />
    <aura:attribute name="TotalAmount" type="Double" default="0.00"/>
    <aura:attribute name="ExpiryYear" type="List" />
    <aura:attribute name="paymentCardWrapper" type="Object"/>
    <lightning:overlayLibrary aura:id="overlayLib"/>
    <aura:attribute name="showSuccess" type="boolean" default="false"/>
    <aura:attribute name="Msg" type="string" />
    <!--  <section role="dialog" style="height:500px;" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
   -->

    <aura:if isTrue="{!v.showSuccess}"> 
        <div class="slds-notify_container">
            <div class="slds-notify slds-notify--toast slds-theme--success" role="alert">
                <span class="slds-assistive-text"></span>
                <div class="slds-notify__content">
                    <h5 class="slds-text-heading_small slds-align_absolute-center">Response </h5>
                    <br/>
                    <p class="slds-align_absolute-center">{!v.Msg}</p>                
                </div>
            </div>
        </div>
    </aura:if>
    

    <aura:if isTrue="{!v.paymentCardWrapper.pymt.pymt__Status__c=='Completed'}">
        <div>
            <p>
                <center>
                    <h2>
                        Payment is Completed!!!
                    </h2>
                </center>
            </p>
        </div>
    </aura:if>
  <!--  <force:recordData aura:id="recordLoader"
    recordId="{!v.recordId}"
    fields="Name,pymt__Payment_Processor__c,Phone,Industry"
    targetFields="{!v.accountRecord}"
    targetError=""
    /> -->
    
    <aura:if isTrue="{!v.paymentCardWrapper.pymt.pymt__Status__c!='Completed'}">
        <div aura:id="mainModal" class="" id="modal-content-id-1" >

            <lightning:recordEditForm recordId="{!v.recordId}"  objectApiName="pymt__PaymentX__c">
                <lightning:notificationsLibrary aura:id="notifLib"/>
                
                <lightning:helptext content="" class="slds-hidden"></lightning:helptext>
           <!--                <tr>
                    <label for="processor">Processor</label> 
                    <lightning:inputField aura:id="processor" variant="label-hidden"
                                          fieldName="pymt__Payment_Processor__c"
                                          value="{!v.paymentCardWrapper.pymt.pymt__Payment_Processor__c}"
                                          />
                </tr><br/>
                <tr >
                    <td style="width: 495px;">
                        <lightning:input aura:id="Name" label="Payment Name"
                                         name="Name"
                                         value="{!v.paymentCardWrapper.pymt.Name}"  required="true"
                                         /> 
                    </td> </tr>
                <br/>
                <tr>
                    <td style="width: 250px;">
                        <lightning:input aura:id="Subtotal" label="Subtotal"
                                         name="Subtotal" default="0.00" type="double"
                                         value="{!v.subtotal}" onchange="{!c.updateTotalFromTaxAmount}"
                                         /> 
                    </td>
                    
                    <td style="width: 250px;padding-left: 2%;">
                        <lightning:input aura:id="Shipping" label="Shipping" default="0.00" type="double"
                                         name="Shipping"  placeholder="0.00" onchange="{!c.updateTotalFromTaxAmount}"
                                         value="{!v.paymentCardWrapper.pymt.pymt__Shipping__c}" 
                                         /> 

                    </td>
                </tr> <br/>
                <tr>
                    <lightning:input aura:id="tax" label="Tax"  onchange="{!c.updateTotalFromTaxAmount}"
                                     name="tax"  placeholder="0.00" default="0.00" type="double"
                                     value="{!v.paymentCardWrapper.pymt.pymt__Tax__c}" 
                                     /> 

                </tr><br/>
                <tr> 

                    <td style="width: 250px;padding-left: 2%;">
                        <label for="CurrencyType">Currency</label>
                        <lightning:input aura:id="CurrencyType" variant="label-hidden"
                                              Name="pymt__Currency_ISO_Code__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Currency_ISO_Code__c}"
                                              />
                    </td>
                </tr><br/>
                <tr>
                    <td style="width: 250px;">
                        <label for="invoice">Invoice Number </label>
                        <lightning:input aura:id="invoice" variant="label-hidden"
                                              Name="pymt__Invoice_Number__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Invoice_Number__c}"
                                              />
                    </td>
                    <td style="width: 250px;padding-left: 2%;">
                        <label for="PONumber">PO Number</label>
                        <lightning:input aura:id="PONumber" variant="label-hidden"
                                              Name="pymt__PO_Number__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__PO_Number__c}"/>
                    </td>
                </tr><br/>-->
                <!-- CARD Details-->
                <tr>
                    <td style="width: 250px;">
                        
                        <lightning:input aura:id="CardNo" label="Card Number"
                                         name="CardNo"
                                         value="{!v.paymentCardWrapper.cardNo}" required="true" 
                                         /> 
                    </td>
                    <td style="width: 250px;padding-left: 2%;">
                        <label for="cardType">Card Type</label>
                        <lightning:inputfield aura:id="cardType" variant="label-hidden"
                                              fieldName="pymt__Card_Type__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Card_Type__c}"/>
                    </td>
                </tr><br/>
                <tr>
                    <td style="width: 150px;">
                        <lightning:select name="ExpiryMonth"  value="{!v.paymentCardWrapper.expireMonth}" label="Month" required="true">
                            <option value="" >--Select Month--</option>
                            <option value="01" >Jan</option>
                            <option value="02">Feb</option>
                            <option value="03">Mar</option>
                            <option value="04">Apr</option>
                            <option value="05">May</option>
                            <option value="06">Jun</option>
                            <option value="07">Jul</option>
                            <option value="08">Aug</option>
                            <option value="09">Sep</option>
                            <option value="10">Oct</option>
                            <option value="11">Nov</option>
                            <option value="12">Dec</option>
                        </lightning:select>
                    </td>
                    <td style="width: 150px;padding-left: 2%;">
                        <lightning:select name="Year" label="Year" value="{!v.paymentCardWrapper.expireYear}" required="true">
                            <option value="" >--Select year--</option>
                            <aura:iteration items="{!v.ExpiryYear}" var="year">
                                <option value="{!year}">{!year}</option>
                            </aura:iteration>
                        </lightning:select>
                    </td>
                    <!-- <lightning:input aura:id="CardExpire" label="Card Expiry Date"
                                         name="CardExpire" 
                                         value="{!v.paymentCardWrapper.expireDate}" max="4"
                                         required="true" placeholder="YYMM"  type="password"
                                         /> -->
                    
                    <td style="width: 200px;padding-left: 2%;">
                        <lightning:input aura:id="CVD" label="CVD Number" 
                                         name="CVD" type="password" max="3"
                                         value="{!v.paymentCardWrapper.CVD}" required="true" placeholder="CVD Number"
                                         />
                    </td>
                </tr><br/>
                <!-- Customer and Address Details-->
                <label for="">Billing Address</label> <br/><br/>
                
                <tr>
                    <td style="width: 250px;">
                        <label for="fName">*First Name</label>
                        <lightning:input aura:id="fName" variant="label-hidden"
                                              Name="pymt__Billing_First_Name__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_First_Name__c}"
                                              required="true" style="width: 100%"/>
                    </td>
                    <td style="width: 250px;padding-left: 2%;">
                        <label for="lName">*Last Name</label>
                        <lightning:input aura:id="lName" variant="label-hidden"
                                              Name="pymt__Billing_Last_Name__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_Last_Name__c}"
                                              required="true"  style="width: 100%"/>
                    </td>
                </tr><br/>
                <tr>
                    <td style="width: 250px;">
                        <label for="street">*Address</label>
                        <lightning:input aura:id="street" variant="label-hidden"
                                              Name="pymt__Billing_Street__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_Street__c}"
                                              required="true" style="width: 100%"/>
                    </td>
                    <td style="width: 250px;padding-left: 2%;">
                        <label for="City">*City</label>
                        <lightning:input aura:id="street" variant="label-hidden"
                                              Name="pymt__Billing_City__c"
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_City__c}"
                                              required="true" style="width: 100%"/>
                    </td>
                </tr><br/>
                <tr>
                    <td style="width: 250px;">
                        <label for="Country">Country</label>
                        <lightning:input aura:id="Country" variant="label-hidden"
                                              Name="pymt__Billing_Country__c"
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_Country__c}"
                                              style="width: 100%"/>
                    </td>
                    <td style="width: 250px;padding-left: 2%;">
                        <label for="State">State/Province</label>
                        <lightning:input aura:id="State" variant="label-hidden"
                                              Name="pymt__Billing_State__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_State__c}"
                                              style="width: 100%"/>
                    </td>
                </tr><br/>
                <tr>
                    <td style="width: 250px;">
                        <label for="postal">*Postal Code</label>
                        <lightning:input aura:id="postal" variant="label-hidden"
                                              Name="pymt__Billing_Postal_Code__c"
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_Postal_Code__c}"
                                              required="true" style="width: 100%"/>
                    </td>
                    <td style="width: 250px; padding-left: 2%;">
                       <label for="email">*Email</label> 
                        <lightning:input aura:id="email" variant="label-hidden"
                                              Name="pymt__Billing_Email__c" 
                                              value="{!v.paymentCardWrapper.pymt.pymt__Billing_Email__c}"
                                              required="true" style="width: 100%" />
                    </td>
                </tr><br/>
                
                <lightning:button class="slds-m-top_small" variant="brand" type="submit" name="PurchaseCall" onclick="{!c.onSubmit}" label="Process Payment" />
                
            </lightning:recordEditForm>
            
        </div> 
    </aura:if>
    
</aura:component>