global class EngagementTimeline {

    @AuraEnabled
    public static string engagementLabel(String objectName) {
        try {
            Engagement_Setting__c stng = Engagement_Setting__c.getValues(objectName);
            return stng.Label__c;
        } catch (exception ex) {
            return '';
        }
    }
    
    @AuraEnabled
    public static list<TimelineItemWrp> allTimelineItems (String objectName, Id recordId) {
        list<TimelineItemWrp> items = new list<TimelineItemWrp>();

        Map<String, Schema.SObjectType> objectMap = Schema.getGlobalDescribe();

        // retrieve all engagement timeline items related to the specified object
        for (Engagement_Timeline_Item__c eti : [SELECT Id, Background_Color__c, Object_Name__c, Primary_Date_Reference__c, Title__c, Title_Reference__c, Relationship_Reference__c, Icon__c,
                Description_Reference__c, Detail_1__c, Detail_2__c, Detail_3__c, Name, Where_Clause__c 
                FROM Engagement_Timeline_Item__c WHERE Related_Object__c = :objectName AND Active__c = TRUE LIMIT 100]) {
            SYSTEM.DEBUG(eti); 
            Map <String, String> relationshipsByField= new Map<String, String>();
            List<String> fields = new List<String>();
            Map<String, SObjectField> sobjFields = objectMap.get(eti.Object_Name__c).getDescribe().fields.getMap();
            if (!string.isEmpty(eti.Detail_1__c)) { 
                fields.add(eti.Detail_1__c);
                Schema.DescribeFieldResult fieldDescribe1 = sobjFields.get(eti.Detail_1__c).getDescribe();
                if ( fieldDescribe1.getType().name() == 'REFERENCE' ) {
                    fields.add( fieldDescribe1.getRelationshipName() + '.Name' );
                    relationshipsByField.put( eti.Detail_1__c, fieldDescribe1.getRelationshipName() );
                }

            } 
            if (!string.isEmpty(eti.Detail_2__c)) { 
                fields.add(eti.Detail_2__c);
                Schema.DescribeFieldResult fieldDescribe2 = sobjFields.get(eti.Detail_2__c).getDescribe();
                if ( fieldDescribe2.getType().name() == 'REFERENCE' ) {
                    fields.add( fieldDescribe2.getRelationshipName() + '.Name' );
                    relationshipsByField.put( eti.Detail_2__c, fieldDescribe2.getRelationshipName() );
                }

            }
            if (!string.isEmpty(eti.Detail_3__c)) { 
                fields.add(eti.Detail_3__c);
                Schema.DescribeFieldResult fieldDescribe3 = sobjFields.get(eti.Detail_3__c).getDescribe();
                if ( fieldDescribe3.getType().name() == 'REFERENCE' ) {
                    fields.add( fieldDescribe3.getRelationshipName() + '.Name' );
                    relationshipsByField.put( eti.Detail_3__c, fieldDescribe3.getRelationshipName() );

                }

            }

            String soql = 'SELECT Id, ' + eti.Primary_Date_Reference__c + (eti.Title_Reference__c == null ? '' : ', ' + eti.Title_Reference__c); 
            if (!string.isEmpty(eti.Description_Reference__c)) soql += ', ' + eti.Description_Reference__c;
            if ( fields.size() > 0 )
                soql = soql + ',' + String.join( fields, ',' );
            //if (!string.isEmpty(eti.Icon__c)) soql += ', ' + eti.Icon__c;
            
            soql += ' FROM ' + eti.Object_Name__c + ' WHERE ' + eti.Relationship_Reference__c + ' =\'' + recordId + '\'';
            if (!string.isEmpty(eti.Where_Clause__c)) soql += ' AND ' + eti.Where_Clause__c;
			SYSTEM.DEBUG(soql); 
            // for each engagement timeline item, dynamically generate soql query
            for (sObject obj : Database.query(soql)) {
                items.add(new TimelineItemWrp(obj, eti, relationshipsByField));
            }
        }
        
        // string activitySOQL = 'SELECT Id, (SELECT Id, ActivityDate, Subject, Description, ActivitySubtype, StartDateTime, EndDateTime, CreatedDate FROM OpenActivities ORDER BY ActivityDate ASC, LastModifiedDate DESC LIMIT 499), (SELECT Id, ActivityDate, Subject, Description, ActivitySubtype, StartDateTime, EndDateTime, CreatedDate FROM ActivityHistories ORDER BY ActivityDate DESC, LastModifiedDate DESC LIMIT 499) FROM ' + objectName + ' WHERE Id =\'' + recordId + '\'';
        // for (SObject obj : Database.query(activitySOQL)) {
        //     List<SObject> oas = obj.getSObjects('OpenActivities');
        //     List<SObject> ahs = obj.getSObjects('ActivityHistories');
        //     if (oas != null) { 
        //         for(SObject a : oas) { items.add(activityItem(a)); }
        //     }
        //     if (ahs != null) {
        //         for(SObject a : ahs) { items.add(activityItem(a)); }
        //     }
        // }
        
        // sort items
        items.sort();
        
        return items;
    }
    
    public static TimelineItemWrp activityItem(SObject a) {
        date aDate;
        if (a.get('ActivityDate') == null) { 
            aDate = ((datetime)a.get('CreatedDate')).date();
        } else {
            aDate = (date)a.get('ActivityDate');
        }
        
        string aSubject = (string)a.get('Subject');
        string aDescription = (string)a.get('Description');
        string aSubtype = (string)a.get('ActivitySubtype');
        
        datetime aStart;
        if (a.get('StartDateTime') == null) { 
            aStart = (datetime)a.get('CreatedDate');
        } else {
            aStart = (datetime)a.get('StartDateTime');
        }
        
        datetime aEnd;
        if (a.get('StartDateTime') == null) { 
            aEnd = (datetime)a.get('CreatedDate');
        } else {
            aEnd = (datetime)a.get('EndDateTime');
        }
        
        return new TimelineItemWrp(a.Id, aDate, aSubject, aDescription, aSubtype, aStart, aEnd);
    }
    
    global class TimelineItemWrp implements Comparable {

        @AuraEnabled public Engagement_Timeline_Item__c eti;
        @AuraEnabled public boolean isFuture;
        @AuraEnabled global dateTime primaryDatetime; // used for sorting the list
        @AuraEnabled public date primaryDate; // displayed on the timeline component
        public Map<String, Schema.SobjectField> fieldsMap;
        
        @AuraEnabled public string formatDate { 
            get { return primaryDate.format(); }
        }
        @AuraEnabled public string id;
        @AuraEnabled public string color;
        @AuraEnabled public string name;
        @AuraEnabled public string icon;
        @AuraEnabled public string description;
        @AuraEnabled public string detail1Label;
        @AuraEnabled public string detail1;
        @AuraEnabled public string detail1LinkLabel;
        @AuraEnabled public string detail2Label;
        @AuraEnabled public string detail2;
        @AuraEnabled public string detail2LinkLabel;
        @AuraEnabled public string detail3Label;
        @AuraEnabled public string detail3;
        @AuraEnabled public string detail3LinkLabel;
        
        // for activities
        public TimelineItemWrp(string aId, date aDate, string aSubject, string aDescription, string aSubtype, datetime aStart, datetime aEnd) {
            primaryDatetime = DateTime.newInstance(aDate.year(), aDate.month(), aDate.day()); //aStart;
            primaryDate = aDate;
            
            if (primaryDatetime > system.now()) isFuture = true;
            else isFuture = false;
            
            id = aId;
            name = aSubject;
            description = aDescription;
            icon = 'standard:' + aSubtype.toLowerCase();
            
            if (aEnd != null) {
                if (date.valueOf(aStart) != date.valueOf(aEnd)) {
                    detail1 = date.valueOf(aStart).format() + ' - ' + date.valueOf(aEnd).format();
                }
            }
        }
        
        // for engagement timeline items
        public TimelineItemWrp(sObject obj, Engagement_Timeline_Item__c eti, Map<String, String> relationshipsByField) {
            // get all fields from this object

            this.eti = eti;
            Schema.SObjectType ctype = Schema.getGlobalDescribe().get(eti.Object_Name__c); 
            fieldsMap = ctype.getDescribe().fields.getMap();

            color = eti.Background_Color__c;
            
            string dateType = string.valueOf(fieldsMap.get(eti.Primary_Date_Reference__c).getDescribe().getType());
            if (dateType == 'DATE') {
                primaryDate = date.valueOf(obj.get(eti.Primary_Date_Reference__c));
                primaryDatetime = datetime.newInstance(primaryDate.year(), primaryDate.month(), primaryDate.day());
            } else if (dateType == 'DATETIME') {
                primaryDatetime = datetime.valueOf(obj.get(eti.Primary_Date_Reference__c));
            } else {
                primaryDatetime = system.now();
            }
            system.debug('--- primaryDate: ' + primaryDate);
            
            primaryDate = primaryDatetime.date();
            if (primaryDatetime >= system.now()) isFuture = true;
            
            // collect all remaining fields
            id = string.valueOf(obj.get('Id'));
            name = String.isBlank(eti.Title__c) ? string.valueOf(obj.get(eti.Title_Reference__c)) : eti.Title__c;
            if (!string.isEmpty(eti.Description_Reference__c)) {
                description = string.valueOf(obj.get(eti.Description_Reference__c));
            }
            
            // collect details
            if (!string.isEmpty(eti.Detail_1__c)) {
                detail1 = formattedValue(eti.Detail_1__c, obj);
                detail1Label = string.valueOf(fieldsMap.get(eti.Detail_1__c).getDescribe().getLabel()) + ': ';
                if ( relationshipsByField.containsKey(eti.Detail_1__c) && obj.get(eti.Detail_1__c) != null )
                    detail1LinkLabel = (String)obj.getSObject( relationshipsByField.get(eti.Detail_1__c) ).get('Name');
            
            }
            if (!string.isEmpty(eti.Detail_2__c)) {
                detail2 = formattedValue(eti.Detail_2__c, obj);
                detail2Label = string.valueOf(fieldsMap.get(eti.Detail_2__c).getDescribe().getLabel()) + ': ';
                if ( relationshipsByField.containsKey(eti.Detail_2__c) && obj.get(eti.Detail_2__c) != null )
                    detail2LinkLabel = (String)obj.getSObject( relationshipsByField.get(eti.Detail_2__c) ).get('Name');

            }
            if (!string.isEmpty(eti.Detail_3__c)) {
                detail3 = formattedValue(eti.Detail_3__c, obj);
                detail3Label = string.valueOf(fieldsMap.get(eti.Detail_3__c).getDescribe().getLabel()) + ': ';
                if ( relationshipsByField.containsKey(eti.Detail_3__c) && obj.get(eti.Detail_3__c) != null )
                    detail3LinkLabel = (String)obj.getSObject( relationshipsByField.get(eti.Detail_3__c) ).get('Name');

            }
            
            // set icon
            icon = eti.Icon__c;
            /*if (!string.isEmpty(eti.Icon__c)) {
                icon = string.valueOf(obj.get(eti.Icon__c));
                //detail3 = string.valueOf(obj.get(eti.Detail_3__c));
                //detail3Label = string.valueOf(fieldsMap.get(eti.Detail_3__c).getDescribe().getLabel()) + ': ';
            }*/
        }
        
        public string formattedValue(string field, sObject obj) {
            string val;

            if ( obj.get(field) == null )
                return '';
            
            string type = string.valueOf(fieldsMap.get(field).getDescribe().getType());
            if (type == 'DATE') {
                date tempDate = date.valueOf(obj.get(field));
                val = tempDate.format();
            } else if (type == 'DATETIME') {
                datetime tempDatetime = datetime.valueOf(obj.get(field));
                val = tempDatetime.date().format();
            } else {
                val = string.valueOf(obj.get(field));
            }
            
            return val;
        }
        
        global Integer compareTo(Object compareTo) {
            TimelineItemWrp other = (TimelineItemWrp) compareTo;
            if (other.primaryDatetime < this.primaryDatetime) {
                return 1;
            } else if (other.primaryDatetime > this.primaryDatetime) {
                return -1;
            } else {
                return 0;
            }
        }
    }
}