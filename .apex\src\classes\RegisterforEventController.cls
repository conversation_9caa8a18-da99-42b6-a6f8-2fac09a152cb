/**
 * @description       :
 * <AUTHOR> <EMAIL>
 * @group             :
 * @last modified on  : 12-23-2021
 * @last modified by  : <EMAIL>
**/
public without sharing class RegisterforEventController{

    @AuraEnabled
    public static pymt__PaymentX__c createAttendeeRecord(Id cartId, Contact loggedInContact, List<evt__Session__c> listOfSessions,
                                                         String currentTitle, String currentEmployer, List<EventCartWrapper> listOfEventCartWrapper, Contact registeredForm){

        String errorMsg = '';
        try{
            system.debug('loggedInContact: '+loggedInContact);
            system.debug('FormData: '+registeredForm);
            system.debug('listOfEventCartWrapper: '+listOfEventCartWrapper);
            system.debug('listOfSessions: '+listOfSessions);
            Id contactmatchingId;
            Id conNewId;
            Decimal discount = 0;
            decimal tax = 0;
            decimal amount = 0;
            decimal grossamount = 0;
            List<evt__Attendee__c> registeredAttendy = new List<evt__Attendee__c>();
            pymt__PaymentX__c pymntCreated;
            Contact loggedContact;
            Contact loggedNewContact;
            List<evt__Attendee__c> atndylist = new List<evt__Attendee__c>(); //List to insert atndy record if matching contact exist
            List<evt__Attendee__c> atndylistupdate = new List<evt__Attendee__c>();//List to update atndy record if matching contact exist
            List<evt__Attendee__c> atndylistnew = new List<evt__Attendee__c>();//List to insert atndy record if matching contact does not exist
            List<evt__Attendee__c> atndylistnewupdate = new List<evt__Attendee__c>();//List to update atndy record if matching contact does not exist
            List<evt__Attendee__c> atndynewlyaddedlist = new List<evt__Attendee__c>();
            List<evt__Attendee__c> addOldnewAttendy = new List<evt__Attendee__c>();
            List<evt__Attendee__c> guestatndylistnew = new List<evt__Attendee__c>();//guest attendee list for new event added second time
            List<pymt__Shopping_Cart_Item__c> scItemlistupdate = new List<pymt__Shopping_Cart_Item__c>();//List to update sci item list.
            List<pymt__Shopping_Cart_Item__c> scItemupdatefornewEvent = new List<pymt__Shopping_Cart_Item__c>();//List to update sci item list.
            List<pymt__Shopping_Cart_Item__c> scifeelist = new List<pymt__Shopping_Cart_Item__c>();
            List<Invoice__c> invObjList = new List<Invoice__c>();
            Set<Id> atndylistwithMatchContact = new Set<Id>(); //set of id of atndy record if matching contact exist
            Set<Id> atndylistwithNewContact = new Set<Id>();   //set of id of atndy record if matching contact does not exist
            Set<Id> specialEventIdFromSession = new Set<Id>();
            Set<Id> eventsessionId = new Set<Id>();
            Set<Id> eventIdlist = new Set<Id>();
            Set<Id> scIdlist = new Set<Id>();
            Set<Id> sessnAssgnIds = new Set<Id>();
            Id discountId;
            Id invId;
            Id pymntId;
            List<evt__Session_Assignment__c> sessnAsmntList = new List<evt__Session_Assignment__c>();//to insert session assignment record
            List<evt__Attendee__c> guestatndylist = new List<evt__Attendee__c>();//to insert guest attendy records

            List<pymt__Shopping_Cart_Item__c> sciList = [select id, name,Gross_Amount__c, Total_Amount__c, Tax_Amount__c,Discount__c,Discount_Amount__c,
            Event_Fee__c,Special_Event__c, pymt__Quantity__c from pymt__Shopping_Cart_Item__c where pymt__Shopping_Cart__c =: cartId];
            for(Integer i=0;i<sciList.size();i++){
                eventIdlist.add(sciList[i].Special_Event__c);
                scIdlist.add(sciList[i].Id);
            }
            Subscription__c scs = [select id from Subscription__c where name = 'Upcoming events'];
            if(loggedInContact!=null && loggedInContact.Id != null){
                loggedContact = [select id, name, (select id,evt__Payment__c from evt__attendees__r where evt__Event__c in :eventIdlist and evt__Is_Primary__c = true and evt__Invitation_Status__c NOT IN ('Declined', 'Wait Listed', 'Cancelled','Invited')),
                (select id, name,Subscription__c from Subscription_Memberships__r where subscription__r.name = 'Upcoming events') from Contact WHERE Id =: loggedInContact.Id];
                system.debug('registeredAttendy: '+loggedContact.evt__attendees__r);
                system.debug('registeredAttendy: '+loggedContact.Subscription_Memberships__r);
                //create subcription membership record if no subscription membership exist for contact
                if(loggedContact.Subscription_Memberships__r.size()==0){
                    Subscription_Membership__c smc = new Subscription_Membership__c();
                    smc.contact__c = loggedInContact.Id;
                    smc.Subscription__c = scs.Id;
                    smc.Subscribed_Date__c = system.now();
                    smc.Subscription_Status__c = 'Subscribed';
                    insert smc;
                }
                if(loggedContact.evt__attendees__r!=null && loggedContact.evt__attendees__r.size()>0){
                    registeredAttendy = loggedContact.evt__attendees__r;
                    system.debug('registeredAttendy: '+registeredAttendy);
                    if(registeredAttendy.size() == eventIdlist.size()){
                        pymntCreated = [select id, name from pymt__PaymentX__c where id =: registeredAttendy[0].evt__Payment__c];
                        return pymntCreated;
                    }else{
                        //create attendee for new event and update the payment record.
                        //map of SCIId and attendy List
                        Map<Id,List<evt__Attendee__c>> scilistMap = new Map<Id, List<evt__Attendee__c>>();
                        for(pymt__Shopping_Cart_Item__c sci : [select id, name, Special_Event__c, Event_Fee__c,pymt__Payment__c,Invoice__c,Discount__c, Discount_Amount__c, Tax_Amount__c,Total_Amount__c, Gross_Amount__c,
                        (select id ,name from attendees__r) from pymt__Shopping_Cart_Item__c where id in:scIdlist]){
                            scilistMap.put(sci.Id, sci.attendees__r);
                            system.debug('scilistMap :'+scilistMap);
                            //create attendee record for new event added to the cart
                            if(scilistMap.get(sci.Id).isEmpty()){
                                //insert attendy for the new event
                                system.debug('Inside scilistMap null check');
                                evt__Attendee__c atndynewlyadded = new evt__Attendee__c();
                                atndynewlyadded.evt__Reg_First_Name__c =  loggedInContact.FirstName;
                                atndynewlyadded.evt__Reg_Last_Name__c =  loggedInContact.LastName;
                                atndynewlyadded.evt__Reg_Phone__c =  loggedInContact.MobilePhone;
                                atndynewlyadded.evt__Reg_Email__c =  loggedInContact.Email;
                                atndynewlyadded.evt__Reg_Street__c =  loggedInContact.MailingStreet;
                                atndynewlyadded.evt__Reg_City__c =  loggedInContact.MailingCity;
                                atndynewlyadded.evt__Reg_State__c =  loggedInContact.MailingState;
                                atndynewlyadded.evt__Reg_Postal_Code__c =  loggedInContact.MailingPostalCode;
                                atndynewlyadded.evt__Reg_Country__c =  loggedInContact.MailingCountry;
                                atndynewlyadded.evt__Is_Primary__c =  true;
                                atndynewlyadded.evt__Contact__c =  loggedInContact.Id;
                                atndynewlyadded.Shopping_Cart_Item__c =  sci.Id;
                                atndynewlyadded.evt__Event__c =  sci.Special_Event__c;
                                atndynewlyadded.evt__Event_Fee__c =  sci.Event_Fee__c;
                                atndynewlyadded.evt__Reg_Company__c = currentEmployer;
                                atndynewlyadded.evt__Reg_Title__c = currentTitle;
                                atndynewlyaddedlist.add(atndynewlyadded);
                            }
                            //update the payment record & the invoice record or delete the old invoice and payment record and create the new one with updated values.
                            if(sci.Discount__c!= null){
                                discount = discount + sci.Discount_Amount__c;
                            }
                            tax = tax + sci.Tax_Amount__c;
                            amount = amount + sci.Total_Amount__c;
                            grossamount = grossamount +sci.Gross_Amount__c;

                            if(sci.Invoice__c!=null){
                                invId = sci.Invoice__c;
                            }
                            if(sci.pymt__Payment__c!=null){
                                pymntId = sci.pymt__Payment__c;
                            }
                        }
                        system.debug('atndynewlyaddedlist: '+atndynewlyaddedlist);
                        if(atndynewlyaddedlist.size()>0){
                            system.debug('atndynewlyaddedlist inside if condition: '+atndynewlyaddedlist);
                            insert atndynewlyaddedlist;
                        }
                        //get the old invoice and payment record
                        Invoice__c invold = [select id, name, Tax_Amount__c, Gross_Amount__c, Total_Amount_SCI__c from Invoice__c where id =:invId];
                        pymt__PaymentX__c pymntold = [select id, name, pymt__Amount__c, Gross_Amount__c,pymt__Tax__c, pymt__Discount__c from pymt__PaymentX__c where id =:pymntId];
                        //update invoice record
                        invold.Tax_Amount__c = tax;
                        invold.Gross_Amount__c = grossamount;
                        invold.Total_Amount_SCI__c = tax + grossamount;
                        update invold;

                        //update payment record
                        pymntold.pymt__Amount__c = amount;
                        pymntold.Gross_Amount__c = grossamount;
                        pymntold.pymt__Tax__c = tax;
                        pymntold.pymt__Discount__c = discount;
                        update pymntold;

                        //update payment and invoice record on new SCI and attendee
                        for(pymt__Shopping_Cart_Item__c sci : sciList){
                            sci.Invoice__c = invold.Id;
                            sci.pymt__Payment__c = pymntold.Id;
                            scItemupdatefornewEvent.add(sci);
                        }
                        update scItemupdatefornewEvent;

                        //update payment reocrd on the attendee records
                        for(evt__Attendee__c oldnewAttendy : [select id, name, evt__Event__c from evt__Attendee__c where Shopping_Cart_Item__c in :scIdlist and evt__Is_Primary__c=true]){
                            oldnewAttendy.evt__Payment__c = pymntold.Id;
                            addOldnewAttendy.add(oldnewAttendy);

                            //if new event added has added guest in it, create guest attendy records
                            for(EventCartWrapper newwrapper : listOfEventCartWrapper){
                                system.debug('newwrapper: '+newwrapper);
                                if(string.valueOf(oldnewAttendy.evt__Event__c) == newwrapper.shoppingcartItem.Special_Event__c){
                                    system.debug('inside event id comparison attendy: '+string.valueOf(oldnewAttendy.evt__Event__c));
                                    system.debug('inside event id comparison wrapper: '+newwrapper.shoppingcartItem.Special_Event__c);
                                    if(newwrapper.listOfGuests!=null && newwrapper.listOfGuests.size()>0){
                                        for(evt__Attendee__c guestattendynew : newwrapper.listOfGuests){
                                            evt__Attendee__c gstatndy = new evt__Attendee__c();
                                            gstatndy.evt__Reg_First_Name__c = guestattendynew.evt__Reg_First_Name__c !=null ? guestattendynew.evt__Reg_First_Name__c : null;
                                            gstatndy.evt__Reg_Last_Name__c = guestattendynew.evt__Reg_Last_Name__c !=null ? guestattendynew.evt__Reg_Last_Name__c : null;
                                            gstatndy.evt__Reg_Email__c = guestattendynew.evt__Reg_Email__c !=null ? guestattendynew.evt__Reg_Email__c : null;
                                            gstatndy.evt__Reg_Street__c = guestattendynew.evt__Reg_Street__c !=null ? guestattendynew.evt__Reg_Street__c : null;
                                            gstatndy.evt__Reg_City__c = guestattendynew.evt__Reg_City__c !=null ? guestattendynew.evt__Reg_City__c : null;
                                            gstatndy.evt__Reg_State__c = guestattendynew.evt__Reg_State__c !=null ? guestattendynew.evt__Reg_State__c : null;
                                            gstatndy.evt__Reg_Postal_Code__c = guestattendynew.evt__Reg_Postal_Code__c !=null ? guestattendynew.evt__Reg_Postal_Code__c : null;
                                            gstatndy.evt__Reg_Country__c = guestattendynew.evt__Reg_Country__c !=null ? guestattendynew.evt__Reg_Country__c : null;
                                            gstatndy.evt__Reg_Company__c = guestattendynew.evt__Reg_Company__c !=null ? guestattendynew.evt__Reg_Company__c : null;
                                            gstatndy.evt__Reg_Title__c = guestattendynew.evt__Reg_Title__c !=null ? guestattendynew.evt__Reg_Title__c : null;
                                            gstatndy.Relationship__c = guestattendynew.Relationship__c !=null ? guestattendynew.Relationship__c : null;
                                            gstatndy.evt__Is_Primary__c = false;
                                            gstatndy.evt__Category__c = 'Attendee';
                                            gstatndy.evt__Primary_Attendee__c = oldnewAttendy.Id;
                                            gstatndy.Shopping_Cart_Item__c = newwrapper.shoppingcartItem.Id;
                                            gstatndy.evt__Event__c = newwrapper.shoppingcartItem.Special_Event__c;
                                            guestatndylistnew.add(gstatndy);
                                        }
                                    }
                                }
                            }
                        }
                        if(addOldnewAttendy.size()>0){
                            System.debug('1');
                            System.debug('---addOldnewAttendy---:'+addOldnewAttendy);
                            update addOldnewAttendy;
                        }
                        if(guestatndylistnew.size()>0){
                            System.debug('2');
                            System.debug('---guestatndylistnew---:'+guestatndylistnew);
                            insert guestatndylistnew;
                        }
                        return pymntold;
                    }
                }else{
                    //map contact field values with the registration values
                    Contact con = new Contact();
                    con.FirstName = loggedInContact.FirstName;
                    con.Preferred_First_Name__c = loggedInContact.FirstName;
                    con.LastName = loggedInContact.LastName;
                    con.Former_Last_Name__c = loggedInContact.LastName;
                    con.Email = loggedInContact.Email;
                    con.hed__Preferred_Email__c = 'Work Email';
                    con.hed__AlternateEmail__c = loggedInContact.Email;
                    con.hed__WorkEmail__c = loggedInContact.Email;
                    con.Phone = loggedInContact.MobilePhone;
                    con.MobilePhone = loggedInContact.MobilePhone;
                    con.MailingStreet = loggedInContact.MailingStreet;
                    con.MailingCity = loggedInContact.MailingCity;
                    con.MailingState = loggedInContact.MailingState;
                    con.MailingCountry = loggedInContact.MailingCountry;
                    con.MailingPostalCode = loggedInContact.MailingPostalCode;
                    con.Dietary_Restrictions__c = loggedInContact.Dietary_Restrictions__c;
                    con.Accessibility_Requirements__c = loggedInContact.Accessibility_Requirements__c;
                    Database.SaveResult saveResult = Database.insert(con, false);
                    System.debug(saveResult.getErrors());

                    if(!saveResult.isSuccess()){
                        for(Database.Error error : saveResult.getErrors()){
                            // If there are duplicates, an error occurs, Process only duplicates and not other errors
                            if (error instanceof Database.DuplicateError){
                                Database.DuplicateError duplicateError = (Database.DuplicateError)error;
                                SYSTEM.DEBUG(duplicateError.getDuplicateResult().getDuplicateRule());
                                Datacloud.MatchResult matchResult = duplicateError.getDuplicateResult().getMatchResults()[0];
                                Datacloud.MatchRecord[] matchRecords = matchResult.getMatchRecords();

                                // capture id of record that duplicate error is thrown on. If there are more than one, match on the first duplicate found.
                                if (matchRecords.size() > 0){
                                    contactmatchingId = matchRecords[0].getRecord().Id;
                                }
                                if (!string.isBlank(contactmatchingId)){
                                    //Contact Exist, create attendee record and associate attendee and contact, attendee to sci and event record
                                    for(Integer j=0;j<sciList.size();j++){
                                        evt__Attendee__c atndy = new evt__Attendee__c();
                                        atndy.evt__Reg_First_Name__c = loggedInContact.FirstName;
                                        atndy.evt__Reg_First_Name__c = loggedInContact.FirstName;
                                        atndy.evt__Reg_Last_Name__c = loggedInContact.LastName;
                                        //atndy.evt__Reg_Phone__c = loggedInContact.MobilePhone;
                                        atndy.evt__Reg_Phone__c = registeredForm.MobilePhone;
                                        //atndy.evt__Reg_Email__c = loggedInContact.Email;
                                        atndy.evt__Reg_Email__c = registeredForm.Email;
                                        //atndy.evt__Reg_Street__c = loggedInContact.MailingStreet;
                                        atndy.evt__Reg_Street__c = registeredForm.MailingStreet;
                                        //atndy.evt__Reg_City__c = loggedInContact.MailingCity;
                                        atndy.evt__Reg_City__c = registeredForm.MailingCity;
                                        //atndy.evt__Reg_State__c = loggedInContact.MailingState;
                                        atndy.evt__Reg_State__c = registeredForm.MailingState;
                                        //atndy.evt__Reg_Postal_Code__c = loggedInContact.MailingPostalCode;
                                        atndy.evt__Reg_Postal_Code__c = registeredForm.MailingPostalCode;
                                        //atndy.evt__Reg_Country__c = loggedInContact.MailingCountry;
                                        atndy.evt__Reg_Country__c = registeredForm.MailingCountry;
                                        atndy.evt__Is_Primary__c = true;
                                        atndy.evt__Contact__c = contactmatchingId !=null ? contactmatchingId : null;
                                        atndy.Shopping_Cart_Item__c = sciList[j].Id;
                                        atndy.evt__Event__c = sciList[j].Special_Event__c;
                                        atndy.evt__Event_Fee__c = sciList[j].Event_Fee__c;
                                        atndy.evt__Category__c = 'Attendee';
                                        atndy.evt__Reg_Company__c = currentEmployer;

                                        atndy.evt__Reg_Title__c = currentTitle;

                                        atndylist.add(atndy);
                                    }
                                }
                            }
                        }
                        System.debug('3');
                        System.debug('---atndylist---:'+atndylist);
                        insert atndylist;

                        for(evt__Attendee__c atndy1 :atndylist){
                            atndylistwithMatchContact.add(atndy1.Id);
                        }
                    }
                }
            }else{
                //check if contact exist with the email address
                List<Contact> conList = [SELECT Id,FirstName,LastName,Email,MobilePhone,MailingStreet,MailingCity,MailingState,MailingCountry,MailingPostalCode,Dietary_Restrictions__c,Accessibility_Requirements__c FROM Contact WHERE Email =: loggedInContact.Email];
                if (conList.size()>0){
                    conNewId = conList[0].Id;
                }else{
                    //create contact with registration values.
                    //map contact field values with the registration values
                    Contact conNew = new Contact();
                    conNew.FirstName = loggedInContact.FirstName;
                    conNew.Preferred_First_Name__c = loggedInContact.FirstName;
                    conNew.LastName = loggedInContact.LastName;
                    conNew.Former_Last_Name__c = loggedInContact.LastName;
                    conNew.Email = loggedInContact.Email;
                    conNew.hed__Preferred_Email__c = 'Work Email';
                    conNew.hed__AlternateEmail__c = loggedInContact.Email;
                    conNew.hed__WorkEmail__c = loggedInContact.Email;
                    conNew.Phone = loggedInContact.MobilePhone;
                    conNew.MobilePhone = loggedInContact.MobilePhone;
                    conNew.MailingStreet = loggedInContact.MailingStreet;
                    conNew.MailingCity = loggedInContact.MailingCity;
                    conNew.MailingState = loggedInContact.MailingState;
                    conNew.MailingCountry = loggedInContact.MailingCountry;
                    conNew.MailingPostalCode = loggedInContact.MailingPostalCode;
                    conNew.Dietary_Restrictions__c = loggedInContact.Dietary_Restrictions__c;
                    conNew.Accessibility_Requirements__c = loggedInContact.Accessibility_Requirements__c;
                    Database.SaveResult saveResult = Database.insert(conNew, false);

                    if(saveResult.isSuccess()){
                        conNewId = conNew.Id;
                    }else{
                        for(Database.Error error : saveResult.getErrors()){
                            // If there are duplicates, an error occurs, Process only duplicates and not other errors
                            if (error instanceof Database.DuplicateError){
                                Database.DuplicateError duplicateError = (Database.DuplicateError)error;
                                //SYSTEM.DEBUG(duplicateError.getDuplicateResult().getDuplicateRule());
                                Datacloud.MatchResult matchResult = duplicateError.getDuplicateResult().getMatchResults()[0];
                                Datacloud.MatchRecord[] matchRecords = matchResult.getMatchRecords();

                                if (matchRecords.size() > 0){
                                    conNewId = matchRecords[0].getRecord().Id;
                                }
                            }
                        }
                        loggedNewContact = [select id, name, (select id,evt__Payment__c from evt__attendees__r where evt__Event__c in :eventIdlist
                        and evt__Is_Primary__c = true) from Contact WHERE Id =:conNewId];
                        if(loggedNewContact.evt__attendees__r!=null && loggedNewContact.evt__attendees__r.size()>0){
                            if(loggedNewContact.evt__attendees__r.size() == eventIdlist.size()){
                                pymntCreated = [select id, name from pymt__PaymentX__c where id =: loggedNewContact.evt__attendees__r[0].evt__Payment__c];
                                return pymntCreated;
                            }
                        }
                    }
                }

                //check subcriptionmembership on new contact
                Contact conwithSbm = [select id, name, (select id, name, Subscription__c from Subscription_Memberships__r where subscription__r.name = 'Upcoming events') from Contact where id = :conNewId];
                if(conwithSbm!=null){
                    if(conwithSbm.Subscription_Memberships__r.size()==0){
                        Subscription_Membership__c smc = new Subscription_Membership__c();
                        smc.contact__c = conNewId;
                        smc.Subscription__c = scs.Id;
                        smc.Subscribed_Date__c = system.now();
                        smc.Subscription_Status__c = 'Subscribed';
                        insert smc;
                    }
                }

                //create attendee and associate contact and attendee, attendee to sci and event record
                for(Integer i=0;i<sciList.size();i++){
                    //check if attendee exist with the email address and contact id and event id
                    List<evt__Attendee__c> existAtndyList = [select id, name, evt__Contact__c, evt__Event__c from evt__Attendee__c where evt__Contact__c =: conNewId and evt__Event__c =: sciList[i].Special_Event__c and evt__Reg_Email__c =: loggedInContact.Email and evt__Reg_First_Name__c =: loggedInContact.FirstName and evt__Reg_Last_Name__c =: loggedInContact.LastName];
                    if(existAtndyList.size()==0){
                        //create attendee
                        evt__Attendee__c atndynew = new evt__Attendee__c();
                        atndynew.evt__Reg_First_Name__c = loggedInContact.FirstName;
                        atndynew.evt__Reg_Last_Name__c = loggedInContact.LastName;
                        atndynew.evt__Reg_Phone__c = loggedInContact.MobilePhone;
                        atndynew.evt__Reg_Email__c = loggedInContact.Email;
                        atndynew.evt__Reg_Street__c = loggedInContact.MailingStreet;
                        atndynew.evt__Reg_City__c = loggedInContact.MailingCity;
                        atndynew.evt__Reg_State__c = loggedInContact.MailingState;
                        atndynew.evt__Reg_Postal_Code__c = loggedInContact.MailingPostalCode;
                        atndynew.evt__Reg_Country__c = loggedInContact.MailingCountry;
                        atndynew.evt__Is_Primary__c = true;
                        atndynew.evt__Contact__c = conNewId !=null ? conNewId : null;
                        atndynew.Shopping_Cart_Item__c = sciList[i].Id;
                        atndynew.evt__Event__c = sciList[i].Special_Event__c;
                        atndynew.evt__Event_Fee__c = sciList[i].Event_Fee__c;
                        atndynew.evt__Reg_Company__c = currentEmployer;
                        atndynew.evt__Reg_Title__c = currentTitle;
                        atndynew.evt__Category__c = 'Attendee';
                        atndylistnew.add(atndynew);
                    }
                }
                System.debug('4');
                System.debug('---atndylistnew---:'+atndylistnew);
                if (atndylistnew.size() > 0){
                    insert atndylistnew;
                    for(evt__Attendee__c atndy2 :atndylistnew){
                        atndylistwithNewContact.add(atndy2.Id);
                    }
                }
            }
            //create session assignment records for multiple event sessions
            if(listOfSessions != null && listOfSessions.size()>0){
                for(evt__Session__c evtsession : listOfSessions){
                    specialEventIdFromSession.add(evtsession.evt__Event__c);
                    eventsessionId.add(evtsession.Id);
                }
                system.debug('specialEventIdFromSession: '+specialEventIdFromSession);
                system.debug('eventsessionId: '+eventsessionId);
                List<evt__Special_Event__c> evntlist = [select id, name,(select id from evt__attendees__r where evt__Event__c in :specialEventIdFromSession and evt__Is_Primary__c	= true), (select id from evt__Event_Sessions__r where id in:eventsessionId) from evt__Special_Event__c where id in :specialEventIdFromSession];
                system.debug('evntlist: '+evntlist);
                for(evt__Special_Event__c evnt : evntlist){
                    for(evt__Attendee__c attendee : evnt.evt__attendees__r){
                        system.debug('attendeelist: '+evnt.evt__attendees__r);
                        for(evt__Session__c evntSessn : evnt.evt__Event_Sessions__r){
                            system.debug('evntSessnlist: '+evnt.evt__Event_Sessions__r);
                            //check if session assignment exist
                            List<evt__Session_Assignment__c> sessnAssgnmtList = [select id from evt__Session_Assignment__c where evt__Attendee__c =: attendee.Id and evt__Event_Session__c =: evntSessn.Id];
                            if(sessnAssgnmtList.size() == 0){
                                evt__Session_Assignment__c sessnAssgnmt = new evt__Session_Assignment__c();
                                sessnAssgnmt.evt__Attendee__c = attendee.Id;
                                sessnAssgnmt.evt__Event_Session__c = evntSessn.Id;
                                sessnAssgnmt.evt__Status__c = 'Confirmed';
                                sessnAsmntList.add(sessnAssgnmt);
                            }
                        }
                    }
                }
                system.debug('sessnAsmntList: '+sessnAsmntList);
                if(sessnAsmntList.size()>0){
                    insert sessnAsmntList;
                    for(evt__Session_Assignment__c sesnAsgnId : sessnAsmntList){
                        sessnAssgnIds.add(sesnAsgnId.Id);
                    }
                }
            }
            for(pymt__Shopping_Cart_Item__c sci :sciList ){
                if(sci.Discount_Amount__c != null){
                    discount = discount + sci.Discount_Amount__c;
                    discountId = sci.Discount__c;
                }
                tax = tax + sci.Tax_Amount__c;
                amount = amount + sci.Total_Amount__c;
                grossamount = grossamount + sci.Gross_Amount__c;
            }

            //Create invoice record, associate contact with invoice.
            Invoice__c inv = new Invoice__c();
            inv.Invoice_Status__c = 'Open-Personal';
            inv.Type__c = 'Events';
            inv.Tax_Amount__c = tax;
            inv.Gross_Amount__c = grossamount;
            inv.Total_Amount_SCI__c = tax + grossamount;
            inv.Contact__c = contactmatchingId !=null ? contactmatchingId : conNewId;
            insert inv;

            //create payment record, to update the payment record, we need to get the details from the shopping cart item.
            //to calculate the total of amount, tax and payments fields from sci to add it in payment record
            pymt__PaymentX__c pymnt = new pymt__PaymentX__c();
            pymnt.Name = 'Event Checkout';
            pymnt.pymt__Transaction_Type__c = 'Payment';
            pymnt.pymt__Payment_Type__c	 = 'Credit Card';
            pymnt.pymt__Status__c = 'Online Checkout';
            pymnt.pymt__Contact__c = contactmatchingId !=null ? contactmatchingId : conNewId;
            pymnt.pymt__Amount__c = amount;
            pymnt.Gross_Amount__c = grossamount;
            pymnt.pymt__Tax__c = tax;
            pymnt.pymt__Discount__c = discount;
            pymnt.Type__c = 'Event Registration';
            pymnt.pymt__Payment_Processor__c = 'Global Pay';
            pymnt.pymt__Contact__c = contactmatchingId !=null ? contactmatchingId : conNewId;
            pymnt.invoice__c = inv.Id;
            insert pymnt;

            for(pymt__Shopping_Cart_Item__c scItem: sciList){
                scItem.Invoice__c = inv.Id;
                scItem.pymt__Payment__c = pymnt.Id;
                //Added as part adding fee for guest attendee, Line# 423-427
                for(EventCartWrapper wrprEvntCartObj1 : listOfEventCartWrapper){
                    System.debug('Wrapper Object' + wrprEvntCartObj1);
                    if(wrprEvntCartObj1.listOfGuests != null && wrprEvntCartObj1.listOfGuests.size()>0){
                        System.debug('*******' + scItem.pymt__Quantity__c);
                        System.debug('#########' + wrprEvntCartObj1.listOfGuests.size());
                        scItem.pymt__Quantity__c = wrprEvntCartObj1.listOfGuests.size()+1;
                    }
                }
                scItemlistupdate.add(scItem);
            }
            update scItemlistupdate;

            //create shoppingCartItem for event session with session fee > 0 and add it to payment
            if(listOfSessions != null && listOfSessions.size()>0){
                for(evt__Session__c evtsession : listOfSessions){
                    specialEventIdFromSession.add(evtsession.evt__Event__c);
                    eventsessionId.add(evtsession.Id);

                }
                system.debug('specialEventIdFromSession: '+specialEventIdFromSession);
                system.debug('eventsessionId: '+eventsessionId);
                List<evt__Special_Event__c> evntlist = [select id, name,(select id,name from evt__attendees__r where Shopping_Cart_Item__c in:sciList),
                (select id,name,evt__Session_Fee__c,evt__Event__c from evt__Event_Sessions__r where id in:eventsessionId and evt__Session_Fee__c !=null),
                (select id, name, discount__c,Special_Event__c, pymt__Taxable__c, Taxing_Authority__c,pymt__Quantity__c from Shopping_Cart_Items__r where id in:sciList)
                from evt__Special_Event__c where id in :specialEventIdFromSession];
                system.debug('evntlist');
                List<evt__Session_Assignment__c> sessnAsmntListNew = [select id, name,evt__Event_Session__c from evt__Session_Assignment__c where id in:sessnAssgnIds and evt__Event_Session__r.evt__Session_Fee__c > 0];
                for(evt__Special_Event__c evnt : evntlist){
                    for(evt__Session__c evntSessn : evnt.evt__Event_Sessions__r){
                        if(string.valueOf(evntSessn.evt__Event__c) == string.valueOf(evnt.Id)){
                            for(evt__Session_Assignment__c ssnAsgn : sessnAsmntListNew){
                                if(ssnAsgn.evt__Event_Session__c == evntSessn.Id){
                                    for(pymt__Shopping_Cart_Item__c sci : evnt.Shopping_Cart_Items__r){
                                        if(string.valueOf(evnt.Id) == string.valueOf(sci.Special_Event__c)){
                                            if(evntSessn.evt__Session_Fee__c!=null){
                                                pymt__Shopping_Cart_Item__c scifee = new pymt__Shopping_Cart_Item__c();
                                                scifee.Name = 'Session'+' - '+evntSessn.Name;
                                                scifee.pymt__Contact__c = contactmatchingId!=null ? contactmatchingId : conNewId;
                                                scifee.Type__c = 'Session';
                                                scifee.Special_Event__c = evnt.Id;
                                                scifee.Session_Assignment__c = ssnAsgn.Id;
                                                scifee.pymt__Payment__c = pymnt.Id;
                                                scifee.Invoice__c = inv.Id;
                                                //Updated quantity for adding session fee for guest attendee,old version of code had quantity as 1.
                                                scifee.pymt__Quantity__c = sci.pymt__Quantity__c;
                                                scifee.pymt__Unit_Price__c = evntSessn.evt__Session_Fee__c;
                                                scifee.Discount__c = sci.Discount__c != null ? sci.Discount__c : null;
                                                scifee.pymt__Taxable__c = sci.pymt__Taxable__c;
                                                scifee.Taxing_Authority__c = sci.pymt__Taxable__c == true ? sci.Taxing_Authority__c : null;
                                                scifeelist.add(scifee);
                                            }
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
                insert scifeelist;
            }

            if(!string.isBlank(contactmatchingId)){
                system.debug('atndylistwithMatchContact3: '+atndylistwithMatchContact);
                List<evt__Attendee__c> matchingatndyupdatelist = [select id, name,evt__Payment__c,evt__Event__c,Shopping_Cart_Item__c,evt__Event_Fee__c from evt__Attendee__c where id in:atndylistwithMatchContact and evt__Is_Primary__c = true];
                system.debug('Matching attendee list:'+matchingatndyupdatelist);
                for(evt__Attendee__c atndymatch : matchingatndyupdatelist){
                    atndymatch.evt__Payment__c = pymnt.Id; //associate payment record to the attendee
                    atndymatch.evt__Is_Primary__c = true;
                    atndylistupdate.add(atndymatch);

                    //create guest attendee records for add guest functionality, populate corresponding SCI, event id and primary attendee id
                    for(EventCartWrapper wrprEvntCartObj : listOfEventCartWrapper){
                        if(atndymatch.evt__Event__c == wrprEvntCartObj.shoppingcartItem.Special_Event__c){
                            if(wrprEvntCartObj.listOfGuests!=null){
                                for(evt__Attendee__c gstatndy : wrprEvntCartObj.listOfGuests){
                                    evt__Attendee__c guestatndy = new evt__Attendee__c();
                                    guestatndy.evt__Reg_First_Name__c  = gstatndy.evt__Reg_First_Name__c != null ? gstatndy.evt__Reg_First_Name__c : null;
                                    guestatndy.evt__Reg_Last_Name__c   = gstatndy.evt__Reg_Last_Name__c != null ? gstatndy.evt__Reg_Last_Name__c : null;
                                    guestatndy.evt__Reg_Email__c       = gstatndy.evt__Reg_Email__c != null ? gstatndy.evt__Reg_Email__c : null;
                                    guestatndy.evt__Reg_Street__c      = gstatndy.evt__Reg_Street__c != null ? gstatndy.evt__Reg_Street__c : null;
                                    guestatndy.evt__Reg_City__c        = gstatndy.evt__Reg_City__c != null ? gstatndy.evt__Reg_City__c : null;
                                    guestatndy.evt__Reg_State__c       = gstatndy.evt__Reg_State__c != null ? gstatndy.evt__Reg_State__c : null;
                                    guestatndy.evt__Reg_Postal_Code__c = gstatndy.evt__Reg_Postal_Code__c != null ? gstatndy.evt__Reg_Postal_Code__c : null;
                                    guestatndy.evt__Reg_Country__c     = gstatndy.evt__Reg_Country__c != null ? gstatndy.evt__Reg_Country__c : null;
                                    guestatndy.evt__Reg_Title__c       = gstatndy.evt__Reg_Title__c != null ? gstatndy.evt__Reg_Title__c : null;
                                    guestatndy.evt__Reg_Company__c     = gstatndy.evt__Reg_Company__c != null ? gstatndy.evt__Reg_Company__c : null;
                                    guestatndy.Relationship__c         = gstatndy.Relationship__c != null ? gstatndy.Relationship__c : null;
                                    guestatndy.Shopping_Cart_Item__c   = wrprEvntCartObj.shoppingcartItem.Id;
                                    guestatndy.evt__Event__c           = wrprEvntCartObj.shoppingcartItem.Special_Event__c;
                                    guestatndy.evt__Is_Primary__c      = false;
                                    guestatndy.evt__Category__c         = 'Attendee';
                                    guestatndy.evt__Primary_Attendee__c = atndymatch.Id;
                                    guestatndy.evt__Payment__c          = pymnt.Id;
                                    guestatndylist.add(guestatndy);
                                }
                            }
                        }
                    }
                }
                System.debug('5');
                System.debug('---atndylistupdate---:'+atndylistupdate);
                update atndylistupdate;
                if(guestatndylist.size()>0){
                    System.debug('6');
                    System.debug('---guestatndylist---:'+guestatndylist);
                    insert guestatndylist;
                }
            }else{
                List<evt__Attendee__c> newatndyupdatelist = [select id, name,evt__Payment__c,Shopping_Cart_Item__c,evt__Event_Fee__c, evt__Event__c from evt__Attendee__c where id in:atndylistwithNewContact];
                for(evt__Attendee__c atndynotmatch : newatndyupdatelist){
                    atndynotmatch.evt__Payment__c = pymnt.Id; //associate payment record to the attendee
                    atndylistnewupdate.add(atndynotmatch);
                    //create guest attendee records for add guest functionality, populate corresponding SCI, event id and primary attendee id
                    for(EventCartWrapper wrprEvntCartObj : listOfEventCartWrapper){
                        if(atndynotmatch.evt__Event__c == wrprEvntCartObj.shoppingcartItem.Special_Event__c){
                            if(wrprEvntCartObj.listOfGuests!=null){
                                for(evt__Attendee__c gstatndy : wrprEvntCartObj.listOfGuests){
                                    evt__Attendee__c guestatndy = new evt__Attendee__c();
                                    guestatndy.evt__Reg_First_Name__c  = gstatndy.evt__Reg_First_Name__c != null ? gstatndy.evt__Reg_First_Name__c : null;
                                    guestatndy.evt__Reg_Last_Name__c   = gstatndy.evt__Reg_Last_Name__c != null ? gstatndy.evt__Reg_Last_Name__c : null;
                                    guestatndy.evt__Reg_Email__c       = gstatndy.evt__Reg_Email__c != null ? gstatndy.evt__Reg_Email__c : null;
                                    guestatndy.evt__Reg_Street__c      = gstatndy.evt__Reg_Street__c != null ? gstatndy.evt__Reg_Street__c : null;
                                    guestatndy.evt__Reg_City__c        = gstatndy.evt__Reg_City__c != null ? gstatndy.evt__Reg_City__c : null;
                                    guestatndy.evt__Reg_State__c       = gstatndy.evt__Reg_State__c != null ? gstatndy.evt__Reg_State__c : null;
                                    guestatndy.evt__Reg_Postal_Code__c = gstatndy.evt__Reg_Postal_Code__c != null ? gstatndy.evt__Reg_Postal_Code__c : null;
                                    guestatndy.evt__Reg_Country__c     = gstatndy.evt__Reg_Country__c != null ? gstatndy.evt__Reg_Country__c : null;
                                    guestatndy.evt__Reg_Title__c       = gstatndy.evt__Reg_Title__c != null ? gstatndy.evt__Reg_Title__c : null;
                                    guestatndy.evt__Reg_Company__c     = gstatndy.evt__Reg_Company__c != null ? gstatndy.evt__Reg_Company__c : null;
                                    guestatndy.Relationship__c         = gstatndy.Relationship__c != null ? gstatndy.Relationship__c : null;
                                    guestatndy.Shopping_Cart_Item__c   = wrprEvntCartObj.shoppingcartItem.Id;
                                    guestatndy.evt__Event__c           = wrprEvntCartObj.shoppingcartItem.Special_Event__c;
                                    guestatndy.evt__Is_Primary__c      = false;
                                    guestatndy.evt__Category__c         = 'Attendee';
                                    guestatndy.evt__Primary_Attendee__c = atndynotmatch.Id;
                                    guestatndy.evt__Payment__c          = pymnt.Id;
                                    guestatndylist.add(guestatndy);
                                }
                            }
                        }
                    }
                }
                System.debug('7');
                System.debug('---atndylistnewupdate---:'+atndylistnewupdate);
                update atndylistnewupdate;
                if(guestatndylist.size()>0){
                    System.debug('8');
                    System.debug('---guestatndylist---:'+guestatndylist);
                    insert guestatndylist;
                }
            }
            List<pymt__Shopping_Cart_Item__c> listOfShoppingCartItemToInsert = new List<pymt__Shopping_Cart_Item__c>();
            for(EventCartWrapper wrapperObj : listOfEventCartWrapper) {
                if(wrapperObj.showDonationCheckBox && wrapperObj.donationAmount != null) {
                    pymt__Shopping_Cart_Item__c shoppingCartItemToInsert = new pymt__Shopping_Cart_Item__c();
                    shoppingCartItemToInsert.Name = 'Donation Event' + wrapperObj.shoppingcartItem.Special_Event__c;
                    shoppingCartItemToInsert.Type__c = 'Donation';
                    shoppingCartItemToInsert.pymt__Unit_Price__c = wrapperObj.donationAmount;
                    shoppingCartItemToInsert.pymt__Quantity__c = 1;
                    shoppingCartItemToInsert.Special_Event__c = wrapperObj.shoppingcartItem.Special_Event__c;
                    shoppingCartItemToInsert.pymt__Payment__c = pymnt.Id;
                    shoppingCartItemToInsert.pymt__Shopping_Cart__c = wrapperObj.shoppingcartItem.pymt__Shopping_Cart__c;

                    listOfShoppingCartItemToInsert.add(shoppingCartItemToInsert);
                }
            }
            if(!listOfShoppingCartItemToInsert.isEmpty()) {
                insert listOfShoppingCartItemToInsert;
            }
            return pymnt;

        }/*catch (DMLException de){
            for (Integer i = 0; i < de.getNumDml(); i++) {
                //Get Validation Rule & Trigger Error Messages
                errorMsg =+ de.getDmlMessage(i) +  '\n' ;
            }
            //throw DML exception message
            throw new AuraHandledException(errorMsg);

            }*/
        catch (Exception e) {
            System.debug(e.getMessage());
            System.debug(e.getStackTraceString());
            throw new AuraHandledException(e.getMessage());
        }
    }
}