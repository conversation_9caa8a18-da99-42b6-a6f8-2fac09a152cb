global class EticketController {
	public String[] RecordIds {get; set;}
	public Boolean isMutiple {get; set;}
	public Map<attendeeWrapper, Integer> itemIndexMap {get; set;}
	public Integer lastIndex {get; set;}
	public List<evt__Attendee__c> lstAttendeeRecords {get; set;}
	public List<attendeeWrapper> lstObj {get; set;}
	public String MyFacilityID = ApexPages.currentPage().getParameters().get('Ids');

	global EticketController(ApexPages.StandardController standardpageController) {
 		this.RecordIds = MyFacilityID.split(',');
 		lstAttendeeRecords = [SELECT Name, evt__Event__r.Name, evt__Reg_First_Name__c, evt__Reg_Last_Name__c, evt__Event_Fee__r.Name, Check_In_QR_Code_Url__c, Event_Venue__c, Event_Time__c FROM evt__Attendee__c WHERE Id IN :this.RecordIds];
 		System.debug('+++'+lstAttendeeRecords);
 		if (lstAttendeeRecords.size()>0){
			lastIndex = lstAttendeeRecords.size();
			if (lstAttendeeRecords.size()>1) {
				isMutiple = true;
			}else{
				isMutiple = false;
			}
			lstObj = new List<attendeeWrapper>();
			itemIndexMap = new Map<attendeeWrapper, Integer>();
			Integer index = 0;
			for(evt__Attendee__c objAttendee : lstAttendeeRecords){
				attendeeWrapper o = new attendeeWrapper();
				o.AttendeeName = objAttendee.evt__Reg_First_Name__c + ' ' + objAttendee.evt__Reg_Last_Name__c;
				o.AttendeeBarcodeUrl = objAttendee.Check_In_QR_Code_Url__c;
				o.EventVenue = objAttendee.Event_Venue__c;
				o.EventTime = objAttendee.Event_Time__c;
				o.EventFeeName = objAttendee.evt__Event_Fee__r.Name;
				o.EventName = objAttendee.evt__Event__r.Name;
				itemIndexMap.put(o, index+1);
				lstObj.add((attendeeWrapper)o);
				index++;
			}
			System.debug('1'+lastIndex);
			System.debug('2'+lstObj);
			System.debug('3'+itemIndexMap);
		}
	}

	global class attendeeWrapper{
		public String AttendeeName {get; set;}
		public String AttendeeBarcodeUrl {get; set;}
		public String EventVenue {get; set;}
		public String EventTime {get; set;}
		public String EventFeeName {get; set;}
		public String EventName {get; set;}
	}

}