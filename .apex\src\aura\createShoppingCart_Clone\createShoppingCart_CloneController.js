({
    removeItem : function(component, event, helper) {
        //addToCart
        var cookieName = "cartID";
        var cooki = helper.getCookie(cookieName);
        var cart = component.get("v.cartId");
        var url = window.location.search.substr(1);
        console.log('url - '+url);
        var urlstring = url.split('&');
        console.log(urlstring);
        if( !cart ){
            for(var item in urlstring){
                console.log(item);
                if(urlstring[item].includes("cartId=")){
                    console.log('cart: '+urlstring[item].substring(7));
                    cart = urlstring[item].substring(7);
                    console.log('cart: '+cart);
                    component.set("v.cartId",cart);
                }
            }
        }
        var source = event.getSource().get("v.name");//event.getSource().getLocalId();
        console.log(source);
        var action = component.get("c.removeFromCart");
        action.setParams({
            EventCourseId : source,//component.get("v.recordId")
            scartId : cart//component.get("v.cartId")
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log('delete Resp: '+response.getReturnValue());
                component.set("v.CartMap",response.getReturnValue());
                component.set("v.showCartInfo",'true');
                var subTotal = 0.00;
                var tax = 0.00;
                var resp = response.getReturnValue();
                for(var item in resp ){
                    console.log('item= '+resp[item].price);
                    console.log('item= '+resp[item].qty);
                    subTotal += resp[item].price * resp[item].qty ;
                    if(resp[item].shoppingCartTax >0){
                        tax = resp[item].shoppingCartTax;
                    }
                    console.log('tax='+tax);
                }
                console.log('subTotal='+subTotal);
                //component.set("v.cartId",sCartId);
                component.set("v.SubTotal",subTotal);
                component.set("v.salesTax",parseFloat(tax));
                var total = parseFloat(subTotal)+parseFloat(tax);
                component.set("v.Total",total);
                //             $A.get('e.force:refreshView').fire();
            }
            else if(state === "ERROR"){
                var msg = "";
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        msg = "Error message: "+ errors[0].message;
                        console.log("Error message: " +errors[0].message);
                    }
                } else {
                    msg = "Unknown error";
                    console.log("Unknown error");
                }
                helper.showToastFun("error","Error",msg);
            }
        });
        $A.enqueueAction(action);
    },
    initFun : function(component, event, helper){
        var cartId = component.get("v.cartId");
        console.log("Init cartId= "+cartId);
        var stamp = new Date().getTime();
        console.log("stamp= "+stamp);        
        var cookieName = "cartID";
        var cooki = helper.getCookie(cookieName);
        console.log("Init cooki= "+cooki);
        
        console.log("cookieName= "+cookieName);
        //console.log(Date().getTime());
        if(!cooki){
            console.log("In null");
            helper.setCookie(cookieName,stamp,30);
        }
        
        
        var action = component.get('c.onLoad');
        $A.enqueueAction(action);
    },
    onLoad : function(component, event, helper){
        var recId = component.get("v.recordId");
        var cartId = component.get("v.cartId");
        var cookieName = "cartID";
        var cooki = helper.getCookie(cookieName);
        if(!cooki){
            console.log("In null");
            var stamp = new Date().getTime();
            helper.setCookie(cookieName,stamp,30);
        }
        
        cooki = helper.getCookie(cookieName);
        var url = window.location.search.substr(1);
        console.log('In onload - '+url);
        var urlstring = url.split('&');
        console.log(urlstring);
        if(!recId || !cartId ){
            for(var item in urlstring){
                console.log(urlstring[item]);
                if(urlstring[item].includes("cartId=")){
                    console.log('cart: '+cartId);
                    cartId = urlstring[item].substring(7);
                    console.log('cart: '+cartId);
                }
                if(urlstring[item].includes('Eid=')){
                    recId = urlstring[item].substring(4);
                    console.log(recId);
                }
            }
        }
        console.log('cart- '+component.get("v.cartId"));
        if(recId && cartId){
            console.log(cartId);
            var callTo = component.get("c.addToCart");
            //debugger;
            callTo.setParams({
                EventCourseId : recId,//component.get("v.recordId"),
                sCartId : cartId,
                cookie: cooki
            });
            helper.getCartInfo(component,callTo).then(
                function(response) {
                    //console.log("response on js-> "+response.cartWrapper.item);
                    console.log("response on js-> "+response.item);
                    component.set("v.showCartInfo",'true');
                    var subTotal = 0.00;
                    var sCartId;
                    var tax = 0.00;
                    for(var item in response){
                        console.log('item= '+response[item].price);
                        console.log('item= '+response[item].shoppingcartId);
                        sCartId = response[item].shoppingcartId;
                        subTotal += response[item].price * response[item].qty;
                        if(response[item].shoppingCartTax > 0){
                            tax = response[item].shoppingCartTax;
                            console.log('Tax=='+tax);
                        }
                        
                        
                    }
                    component.set("v.cartId",sCartId);
                    //pymt__Payment__r.pymt__Tax__c
                    component.set("v.SubTotal",subTotal);
                    component.set("v.salesTax",parseFloat(tax));
                    var total = parseFloat(subTotal)+parseFloat(tax);
                    component.set("v.Total",total);
                    component.set("v.CartMap",response);
                    
                }).catch(
                function(error) {
                    //component.set("v.status" ,error ) ; 
                    console.log(error);
                }
            );
        }
        else if(cartId){
            var action = component.get("c.noRecord");
            action.setParams({
                scartId : cartId
                //EventCourseId : source//component.get("v.recordId")
            });
            action.setCallback(this, function(response) {
                var state = response.getState();
                console.log(state);
                if (state === "SUCCESS") {
                    console.log(' Resp: '+response.getReturnValue());
                    component.set("v.CartMap",response.getReturnValue());
                    component.set("v.showCartInfo",'true');
                    var subTotal = 0.00;
                    var tax = 0.00;
                    var resp = response.getReturnValue();
                    // var sCartId;
                    for(var item in resp ){
                        console.log('item= '+resp[item].price);
                        console.log('item= '+resp[item].qty);
                        subTotal += resp[item].price * resp[item].qty;
                        console.log('subTotal= '+subTotal);
                        if(resp[item].shoppingCartTax > 0){
                            tax = resp[item].shoppingCartTax;
                            console.log('Tax=='+tax);
                        }
                    }
                    //  component.set("v.cartId",sCartId);
                    component.set("v.SubTotal",subTotal);
                    component.set("v.salesTax",parseFloat(tax));
                    //debugger;
                    var total = parseFloat(subTotal)+parseFloat(tax);
                    component.set("v.Total",total);
                    //             $A.get('e.force:refreshView').fire();
                    var UserDetail = component.get("c.getUserDetails");
                    UserDetail.setCallback(this, function(response) {
                        var state = response.getState();
                        console.log(state);
                        if (state === "SUCCESS") {
                            console.log('UserName: '+response.getReturnValue());
                            if(response.getReturnValue() =="Events"){
                                component.set("v.userName",'Guest\'s');
                            }
                            else{
                                component.set("v.userName",response.getReturnValue()+'\'s');
                            }
                            
                        }
                        else if(state === "ERROR"){
                            var msg = "";
                            var errors = response.getError();
                            if (errors) {
                                if (errors[0] && errors[0].message) {
                                    msg = "Error message: "+ errors[0].message;
                                    console.log("Error message: " +errors[0].message);
                                }
                            } else {
                                msg = "Unknown error";
                                console.log("Unknown error");
                            }
                            helper.showToastFun("error","Error",msg);
                        }
                    });
                    $A.enqueueAction(UserDetail);
                }
                else if(state === "ERROR"){
                    var msg = "";
                    var errors = response.getError();
                    if (errors) {
                        if (errors[0] && errors[0].message) {
                            msg = "Error message: "+ errors[0].message;
                            console.log("Error message: " +errors[0].message);
                        }
                    } else {
                        msg = "Unknown error";
                        console.log("Unknown error");
                    }
                    helper.showToastFun("error","Error",msg);
                }
            });
            $A.enqueueAction(action);
        }
            else if( !cartId){
                
                var action = component.get("c.noCart");
                action.setParams({
                    cookie : cooki
                    //EventCourseId : recId//component.get("v.recordId")
                });
                action.setCallback(this, function(response) {
                    var state = response.getState();
                    console.log(state);
                    if (state === "SUCCESS") {
                        console.log(' Resp: '+response.getReturnValue());
                        component.set("v.cartId",response.getReturnValue());
                        var baseurl = window.location.href;
                        if(baseurl.includes("id=")){
                            window.location.href = baseurl+'&cartId='+response.getReturnValue();
                        }
                        else{
                            window.location.href = baseurl+'?cartId='+response.getReturnValue();
                        }
                    }
                    else if(state === "ERROR"){
                        var msg = "";
                        var errors = response.getError();
                        if (errors) {
                            if (errors[0] && errors[0].message) {
                                msg = "Error message: "+ errors[0].message;
                                console.log("Error message: " +errors[0].message);
                            }
                        } else {
                            msg = "Unknown error";
                            console.log("Unknown error");
                        }
                        helper.showToastFun("error","Error",msg);
                    }
                });
                $A.enqueueAction(action);
            }
    },
    continueShopping : function(component, event, helper){
        var dismissActionPanel = $A.get("e.force:closeQuickAction");
        console.log(dismissActionPanel);
        var url = window.location.href ;//= "https://ldev1-rotman.cs138.force.com/events/s/";
        var urlList = url.split('shoppingcart');
        console.log(urlList);
        window.location.href = urlList[0];
        if(dismissActionPanel){// != undefined
            dismissActionPanel.fire();
        }
        else{
            console.log('inside close');
            //window.history.go(-2);
            window.close();
        }
    }, 
    checkout : function(component, event, helper){
        //getPaymentId
        var baseUrl = $A.get("$Label.c.base_url");
        var checkoutURL = $A.get("$Label.c.Events_Checkout_URL");
        //console.log('base= '+baseUrl);
        var source = event.getSource().get("v.name");//event.getSource().getLocalId();
        //alert(source);
        //console.log('checkoutURL= '+checkoutURL);
        console.log('recordId='+component.get("v.recordId"));
        var action = component.get("c.PaymentId");
        action.setParams({
            event : component.get("v.recordId"),
            cookie : helper.getCookie("cartID")
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log('payment ID: '+response.getReturnValue());
                alert('Payment URL='+baseUrl+checkoutURL+'?pid='+response.getReturnValue());
                window.location.href = baseUrl+checkoutURL+'?pid='+response.getReturnValue();
                ///component.set("v.CartMap",response.getReturnValue());
                //             $A.get('e.force:refreshView').fire();
            }
        });
        $A.enqueueAction(action);
        
        //window.location.href = baseUrl+checkoutURL;
    },
    UpdateCart : function(component, event, helper){
        var cookieName = "cartID";
        var cooki = helper.getCookie(cookieName);
        var cart = component.get("v.cartId");
        var url = window.location.search.substr(1);
        console.log('url - '+url);
        var urlstring = url.split('&');
        console.log(urlstring);
        if( !cart ){
            for(var item in urlstring){
                console.log(item);
                if(urlstring[item].includes("cartId=")){
                    console.log('cart: '+urlstring[item].substring(7));
                    cart = urlstring[item].substring(7);
                    console.log('cart: '+cart);
                    component.set("v.cartId",cart);
                }
            }
        }
        //console.log('cookie: '+ cooki);
        console.log('cart: '+component.get("v.CartMap"));
        console.log('cartId: '+component.get("v.cartId"));
        var action = component.get("c.continueCartSave");
        console.log('action= '+action);
        action.setParams({
            cartItems : component.get("v.CartMap"),
            scartId : cart,
            cookie : cooki
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log(' Resp: '+response.getReturnValue());
                component.set("v.CartMap",response.getReturnValue());
                var subTotal = 0.00;
                var tax = 0.00;
                var resp = response.getReturnValue();
                for(var item in resp){
                    console.log('item= '+resp[item].price);
                    console.log('item= '+resp[item].qty);
                    subTotal += resp[item].price * resp[item].qty;
                    if(resp[item].shoppingCartTax > 0){
                        tax = resp[item].shoppingCartTax;
                        console.log('Tax=='+tax);
                    }
                }
                //  component.set("v.cartId",sCartId);
                component.set("v.SubTotal",subTotal);
                component.set("v.salesTax",parseFloat(tax));
                var total = parseFloat(subTotal)+parseFloat(tax);
                component.set("v.Total",total);
                //alert('Cart Updated!');
                helper.showToastFun("success","Success","Cart Updated!");
            }
            else if(state === "ERROR"){
                var msg = "";
                var errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        msg = "Error message: "+ errors[0].message;
                        console.log("Error message: " +errors[0].message);
                    }
                } else {
                    msg = "Unknown error";
                    console.log("Unknown error");
                }
                helper.showToastFun("error","Error",msg);
            }
        });
        $A.enqueueAction(action);
    },
    
    changeQty : function(component, event, helper){
        var source = event.getSource().get("v.value");
        console.log('source: '+source);
        if(parseInt(source) < 0 ){
            alert('Quantity Cannot be less than 0');
            event.getSource().set("v.value",1);
            return false;
        }
        
        
        
    }
    
})