@isTest
public class MassUpdateContactEmailsToInvalid_TEST {

    @isTest
    public static void testBatch(){
        test.startTest();
            Contact c1 = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test1', LastName = 'Student', 
                                                                    Email = '<EMAIL>',
                                                                    hed__Preferred_Email__c = '	Alternate Email',
                                                                    hed__AlternateEmail__c = '<EMAIL>', 
                                                                    SFMC_Email__c = '<EMAIL>', 
                                                                    hed__UniversityEmail__c = '<EMAIL>',
                                                                    hed__WorkEmail__c = '<EMAIL>'));
            insert c1;
        	Database.executeBatch(new MassUpdateContactEmailsToInvalid());
        test.stopTest();
    }
}