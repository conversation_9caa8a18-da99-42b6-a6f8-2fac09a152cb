public without sharing class ShoppingCartDiscountController {
    @AuraEnabled
    public static string getDisocuntCode(string couponCode, Id cartId){
        system.debug('cartitemId:'+cartId);
        system.debug('couponCode:'+couponCode);
        pymt__Shopping_Cart_Item__c sci = [select id, name, Discount__c, Special_Event__c from pymt__Shopping_Cart_Item__c where id =:cartId];

        Discount__c DicountCode = [SELECT Id, name, Active__c, Code__c, Event__c, Current_Usage__c, Max_Usage__c,Percent_Discount__c FROM Discount__c
        WHERE Code__c =:couponCode and Automatically_Apply__c = false];
        system.debug('Disocunt:'+DicountCode);
        if(DicountCode != null){ // First If - Discount code is valid
            if(DicountCode.Event__c == null || sci.Special_Event__c == DicountCode.Event__c){ // if 2 Event check
                system.debug('Check inside loop1');
                if(DicountCode.Max_Usage__c!=null){ // IF 3 discount usage check
                    system.debug('Check inside loop2');
                    if(DicountCode.Current_Usage__c < DicountCode.Max_Usage__c ){ // usage check 2
                        system.debug('Check inside loop3');
                        //call method to update discount id on sci
                        //increment current usage on discount to +1
                        //when there is no auto applied discount.
                        if(sci.Discount__c==null){
                            system.debug('Check inside loop4');
                            system.debug('Disocunt Id:'+DicountCode.Id);
                            sci.Discount__c = DicountCode.Id;
                            DicountCode.Current_Usage__c +=1;
                            update sci;
                            update DicountCode;
                            return 'Coupon Code Applied Successfully';
                        }else{
                            Discount__c disccode = [select id, name, Current_Usage__c from discount__c where id =: sci.Discount__c];
                            if(disccode !=null){
                                disccode.Current_Usage__c -=1;
                                sci.Discount__c = DicountCode.Id;
                                DicountCode.Current_Usage__c +=1;
                                update new List<Discount__c>{disccode, DicountCode};
                                update sci;
                                return 'Coupon Code Applied Successfully';
                            } else{
                                return null;}
                        }
                    }else{
                        return 'Maximun limit of this Coupon reached';
                    }// end usage check 2
                }else{
                    sci.discount__c = DicountCode.Id;
                    update sci;
                    return 'Coupon Code Applied Successfully';
                } // End Discount usage
            }

            else{ // If 2 = events dont match
                return 'This coupon can not be applied to this event';
            }
        }else{// First if discount = Null
            return 'Coupon code not valid';
        }
    }

    @AuraEnabled
    public static Boolean isEmailExisted(String emailStr) {
        Boolean checkEmail = false;
        try {
            if (String.isNotBlank(emailStr)) {
                List<Contact> lstContacts = [SELECT Id, Email FROM Contact WHERE Email = :emailStr];
                if (lstContacts.size() > 0) {
                    checkEmail = true;
                }
            }
            return checkEmail;
        } catch (Exception e) {
            throw new AuraHandledException(e.getStacktraceString());
        }
    }

    /**
 * @description Gets the list of countries used for the shipping portion
 * @param objConfigId - Unique ID of the object configuration
 * @param cookieId - Unique ID of the cookie dropped into the user's browser, used to get a shopping cart for an anonymous user
 * @return Map of cart ID, link to the payemnt page, number of items in the cart, and whether a the status of whether they're logged in, associated to a cart, or must have a cookie set
 */
    @AuraEnabled
    public static ShippingAddressWrapper getCountries () {

        return new ShippingAddressWrapper();

    }

    public class ShippingAddressWrapper {

        @AuraEnabled public contact user;
        User loggedInUser = [SELECT Id, ContactId FROM User where Id = : UserInfo.getUserId()];
        @AuraEnabled public List<Map<String,String>> countries;

        public ShippingAddressWrapper () {

            //Add all counties to combobox list format, if default, add to beginning of list
            this.countries = new List<Map<String, String>>{
                    new Map<String, String>{ 'label' => 'Canada', 'value' => 'Canada' },
                    new Map<String, String>{ 'label' => 'United States', 'value' => 'U.S.A.' }
            };
            for ( PicklistEntry pe : SObjectType.Account.fields.BillingCountryCode.getPicklistValues() )
                if ( pe.getValue() != 'US' && pe.getValue() != 'CA' )
                    //this.countries.add( new Map<String, String>{ 'label' => pe.getLabel(), 'value' => pe.getValue() } );
                    this.countries.add( new Map<String, String>{ 'label' => pe.getLabel(), 'value' => pe.getLabel() } );

        }

    }
}