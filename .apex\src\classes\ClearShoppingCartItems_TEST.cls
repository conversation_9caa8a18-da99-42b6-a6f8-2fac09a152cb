@isTest
public class ClearShoppingCartItems_TEST {
	@testSetup
    static void testSetup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for ClearShoppingCartItems_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('ClearShoppingCartItems_TDTM', 'pymt__Shopping_Cart_Item__c', 'BeforeInsert;BeforeUpdate', 1.00));
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        //Create contact
        Contact con = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student', Email = '<EMAIL>'));
        insert con;
        //Insert store configuration
        Store_Configuration__c sc = new Store_Configuration__c (
            Name = 'EP Courses'
        );
        insert sc;
        //Insert shopping cart
        String cookieId = '2398410734';
        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c(
            Cart_Key__c = cookieId + '.' + sc.Id,
            Store_Configuration__c = sc.Id
        );
        insert cart;
        //Create payment
        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c    = con.Id,
            Name                = 'Test Update Payment',
            pymt__Status__c     = 'Invoice'
        );
        insert pymt;
		//Create shopping cart item
        pymt__Shopping_Cart_Item__c sci = new pymt__Shopping_Cart_Item__c (
            Type__c                 = 'Event Registration',
            Name                    = 'Event Registration',
            pymt__Shopping_Cart__c 	= cart.Id
        );
        insert sci;
    }
    
    @isTest
    public static void testPymtInvoiceInsert(){
        Contact con = [SELECT Id FROM Contact Limit 1];
        pymt__Shopping_Cart__c cart = [SELECT Id FROM pymt__Shopping_Cart__c LIMIT 1];
        //Create payment
        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c    = con.Id,
            Name                = 'Test Insert Payment',
            pymt__Status__c     = 'Invoice'
        );
        insert pymt;
        //Create shopping cart item
        pymt__Shopping_Cart_Item__c sci = new pymt__Shopping_Cart_Item__c (
            pymt__Payment__c        = pymt.Id,
            Type__c                 = 'Event Registration',
            Name                    = 'Event Registration' ,
            pymt__Shopping_Cart__c 	= cart.Id        
        );
       
        test.startTest();
        	insert sci;
       	test.stopTest();
    }
    
    @isTest
    public static void testPymtInvoiceUpdate(){
        pymt__PaymentX__c pymt = [SELECT Id FROM pymt__PaymentX__c WHERE pymt__Status__c = 'Invoice' LIMIT 1];
        //Update shopping cart item
        pymt__Shopping_Cart_Item__c sci = [SELECT Id, pymt__Payment__c, pymt__Shopping_Cart__c FROM pymt__Shopping_Cart_Item__c LIMIT 1];
        
        test.startTest();
        	sci.pymt__Payment__c = pymt.Id;
        	update sci;
        test.stopTest();
		
    }
}