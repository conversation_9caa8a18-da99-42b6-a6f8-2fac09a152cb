public without sharing class <PERSON><PERSON><PERSON>Wrapper {
    @AuraEnabled public pymt__Shopping_Cart_Item__c shoppingcartItem {get;set;}
    @AuraEnabled public Boolean showDiscount {get;set;}
    @AuraEnabled public List<evt__Session__c> listOfEventSession {get;set;} 
    @AuraEnabled public Boolean showAddGuests {get; set;}
    @AuraEnabled public Boolean showSession {get; set;}
    @AuraEnabled public List<evt__Attendee__c> listOfGuests {get; set;}
    @AuraEnabled public String discountCodeMessage {get; set;}
    @AuraEnabled public String couponCode {get; set;}
    @AuraEnabled public Boolean showZeroDiscount {get;set;}
    @AuraEnabled public Decimal totalAmount {get;set;}
    @AuraEnabled public Decimal originalSubTotal {get; set;}
    @AuraEnabled public Decimal subTotal {get; set;}
    @AuraEnabled public Decimal estimatedTax {get;set;}
    @AuraEnabled public Decimal discountAmount {get; set;}
    @AuraEnabled public Decimal taxPercentage {get;set;}
    @AuraEnabled public Decimal discountPercent {get;set;}
    @AuraEnabled public Boolean taxable {get;set;}
    @AuraEnabled public List<evt__Event_Fee__c> listofEventFee {get; set;}
    //Prod
    //@AuraEnabled public List<String> listofEventFee {get; set;}
    @AuraEnabled public Boolean showPicklist {get; set;}
    @AuraEnabled public Boolean showDonationCheckBox {get; set;}
    @AuraEnabled public Boolean showDonationSection {get; set;}
    @AuraEnabled public Decimal donationAmount {get; set;}
    @AuraEnabled public String event_image {get; set;}
}