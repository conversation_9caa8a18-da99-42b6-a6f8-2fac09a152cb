<!--
 - Created by <PERSON><PERSON> on 2022-05-05.
 -->

<aura:component
        implements="lightning:actionOverride,force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
        controller="OfferLetterFileDelete_Ctrl">
    <ltng:require styles="{!$Resource.ModalWidthCSS}"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <!--aura attributes-->
    <aura:attribute name="recordId" type="string"/>
    <aura:attribute name="selectedDocumentId" type="string"/>
    <aura:attribute name="lstContentDoc" type="List"/>
    <aura:attribute name="hasOfferLetter" type="boolean" default="false"/>
    <aura:attribute name="isOpen" type="boolean" default="false"/>

    <!-- Custom DataTable to Display List Of Available ContentDocuments Start-->
    <div class="slds-m-around_xx-large" style="width: 55em!important;position: relative;">
        <aura:if isTrue="{!v.hasOfferLetter}">
            <table class="slds-table slds-table_cell-buffer slds-table_bordered">
                <thead>
                <tr class="slds-line-height_reset">
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Title">Title</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="File Type">File Type</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="Created By">Created By</div>
                    </th>
                    <th class="slds-text-title_caps" scope="col">
                        <div class="slds-truncate" title="size">Created Date</div>
                    </th>
                </tr>
                </thead>
                <tbody>
                <aura:iteration items="{!v.lstContentDoc}" var="CD">
                    <tr>
                        <th scope="row">
                            <div class="slds-truncate" title="Offer Letter">Offer Letter</div>
                        </th>
                        <th scope="row">
                            <div class="slds-truncate" title="{!CD.FileType}">{!CD.FileType}</div>
                        </th>
                        <th scope="row">
                            <div class="slds-truncate" title="{!CD.CreatedBy.Name}">{!CD.CreatedBy.Name}</div>
                        </th>
                        <th scope="row">
                            <div class="slds-truncate"
                                 title="{!CD.ContentModifiedDate}">{!CD.ContentModifiedDate}</div>
                        </th>
                        <th scope="row">
                            <div class="slds-truncate" style="padding-left: 10px;">
                                <lightning:buttonIcon iconName="utility:delete" alternativeText="Delete"
                                                      class="slds-button uiButton--brand uiButton"
                                                      title="Delete Offer Letter" name="{!CD.Id}"
                                                      onclick="{!c.handleConfirmDialog}"/>
                            </div>
                        </th>
                    </tr>
                </aura:iteration>
                </tbody>
            </table>
            <aura:set attribute="else">
                <div style="position: relative;">
                    <h3 style="font-weight: 600; font-size: 16px;">There is currently not any Offer Letter PDF file
                        generated for this applicant. </h3>
                </div>
            </aura:set>
        </aura:if>
    </div>
</aura:component>