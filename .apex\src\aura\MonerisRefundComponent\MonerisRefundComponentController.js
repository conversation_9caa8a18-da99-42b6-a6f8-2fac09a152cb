({
	initData: function (component, event, helper){
        helper.getPaymentWrapper(component, event, helper);
    },
    RefundSubmit: function(component, event, helper){
        var wrap = component.get("v.paymentCardWrapper");
        console.log('Amount = '+wrap.Amount);
        var amt = parseFloat(wrap.Amount) + parseFloat(0.00);
        wrap.Amount = amt.toFixed(2);
        console.log('Amount = '+wrap.Amount);
        console.log('AdFee = '+ wrap.pymt.Administration_Fee__c);
        console.log(parseFloat(wrap.Amount) + parseFloat(wrap.pymt.Administration_Fee__c)+' =Total= '+parseFloat(wrap.pymt.pymt__Amount__c));
        if(wrap.Amount > wrap.pymt.pymt__Amount__c){
            alert('Refund Amount should be less than or equal to the total Amount: '+wrap.pymt.pymt__Amount__c);
            return false;
        }
        
        /*else if( parseFloat(wrap.Amount) + parseFloat(wrap.pymt.Administration_Fee__c) > parseFloat(wrap.pymt.pymt__Amount__c)){
            alert('Sum of Refund Amount and Administration Fee should be less than or equal to the total Amount: '+wrap.pymt.pymt__Amount__c);
            return false;
        }*/
        else{
            if(wrap.pymt.pymt__Payment_Type__c =="Credit Card"){
                console.log('Credit Card Refund');
                helper.getRefundPayment(component, event, helper);
            }
            else if(wrap.pymt.pymt__Payment_Type__c == "Cash" || wrap.pymt.pymt__Payment_Type__c == "Check or Money Order")
            {
                console.log('CAsh/Check Refund');
                helper.getRefundForCashCheck(component, event, helper);
            }
            else {
                helper.showToastFun('Error','Payment Type Not Set!','Please Set Payment Type before Refund.');
                return false;
            }
            
        }
        
       },
})