@isTest
private class SubscriptionCenterHashUtilityTest {
  
    // Test to make sure hash gets set properly
    @isTest
    static void test_hash_utility() {
        
        // Query for contacts created earlier
        Contact c1 = [SELECT Id, HASH_Id__c FROM Contact WHERE LastName = :'test1'];
        Contact c2 = [SELECT Id, HASH_Id__c FROM Contact WHERE LastName = :'test2'];
        
        Test.startTest();
        
        // Calculate hashes
        SubscriptionCenterHashUtility.calculateHash(new List<Contact>{c1, c2});
        
        Test.stoptest();
        
        // Calculate what hashes are supposed to be
        String c1IdHash = EncodingUtil.convertToHex(Crypto.generateDigest(SubscriptionCenterHashUtility.ALGORITHM, Blob.valueOf(c1.Id + SubscriptionCenterHashUtility.HASH_SEED)));
        String c2IdHash = EncodingUtil.convertToHex(Crypto.generateDigest(SubscriptionCenterHashUtility.ALGORITHM, Blob.valueOf(c2.Id + SubscriptionCenterHashUtility.HASH_SEED)));
        
        // Assert that hashes are correct
        System.assertEquals(c1IdHash, c1.HASH_Id__c);
        System.assertEquals(c2IdHash, c2.HASH_Id__c);
    }
    
    // Set-up test data
    @testSetup
    static void setup() {
        
        // Create two contacts
        Contact c1 = new Contact(LastName = 'test1');
        Contact c2 = new Contact(LastName = 'test2');
        
        insert new List<Contact>{c1, c2}; // Insert contacts
    }
}