/**
 * @description Auto-applies any relevant discounts where auto-apply = true
 * <AUTHOR>
 * @version 1.0
 * @created 2020-09-25
 * @modified 2020-09-25
 */
global class ShoppingCartDiscountService_TDTM extends hed.TDTM_Runnable{
    final List<String> sciTypesToInclude = new List<String>{'EP Program Balance', 'Event Registration', 'Application Fee'}; 
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of  records from trigger new 
     * @param oldList the list of  records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();        
        
        Set<Id> userIds = new Set<Id>(); 
        Set<Id> evtAndPrgIds = new Set<Id>(); 
        List<pymt__Shopping_Cart_Item__c> sciToUpdate = new List<pymt__Shopping_Cart_Item__c>(); 
        Set<String> discountTypesToInclude = new Set<String>(); 

        //Check new shopping cart items which are not paud and have a special event but not a fee
        if ( triggerAction ==  hed.TDTM_Runnable.Action.BeforeInsert ) {

            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                if( !sci.pymt__Payment_Completed__c && sci.Discount__c == null && sci.pymt__Quantity__c == 1 && (sci.Special_Event__c != null || sci.course_offering__c!= null || sci.type__c == 'Application Fee') && sciTypesToInclude.contains(sci.Type__c) && sci.ownerId != null ){
                    //Add owner Id to set: 
                   userIds.add(sci.ownerId); 
                   //add event or program to set: 
                   evtAndPrgIds.add(sci.Special_Event__c != null ? sci.Special_Event__c : sci.Course_Offering__c);
                   //add sci to sci list: 
                   sciToUpdate.add(sci); 
                   // discount types to filter by: 
                   if(sci.Type__c == 'Event Registration') discountTypesToInclude.add('Event Discount'); 
                   else if(sci.Type__c == 'EP Program Balance') discountTypesToInclude.add('Program Discount'); 
                   else if(sci.Type__c == 'Application Fee') discountTypesToInclude.add('Fee Waiver'); 
                }
            }
        //Check all shopping cart items updated to include an event
        } else if ( triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate ) {
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList);

            for ( pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList ){ 
                if ( !sci.pymt__Payment_Completed__c && sci.Discount__c == null && sci.ownerId != null &&
                ((oldMap.get(sci.Id).Special_Event__c == null && sci.Special_Event__c != null) || (oldMap.get(sci.Id).Course_Offering__c == null && sci.Course_Offering__c != null) || (oldMap.get(sci.Id).Type__c == null && sci.Type__c == 'Application Fee')) 
                && sciTypesToInclude.contains(sci.Type__c) && sci.pymt__Quantity__c == 1){
                    //Add owner Id to set: 
                   userIds.add(sci.ownerId); 
                   //add event or program to set: 
                   evtAndPrgIds.add(sci.Special_Event__c != null ? sci.Special_Event__c : sci.Course_Offering__c);
                   //add sci to sci list: 
                   sciToUpdate.add(sci); 
                   // discount types to filter by: 
                   if(sci.Type__c == 'Event Registration') discountTypesToInclude.add('Event Discount'); 
                   else if(sci.Type__c == 'EP Program Balance') discountTypesToInclude.add('Program Discount'); 
                   else if(sci.Type__c == 'Application Fee') discountTypesToInclude.add('Fee Waiver'); 
                }
            } 
        }

        
        
        if ( userIds.size() > 0 ) {

            Map<Id, User> userMap = new Map<Id, User>([SELECT ID, Profile.Name, Rotman_Alumni__c FROM User WHERE Id IN :userIds]); 
            //Maps eventOrProgramId to current Index in discount list: 
            Map<String, Integer> discCurrIndexMap = new Map<String, Integer>(); 


            //Event or program Id --> discount type --> list of discounts 
            Map<Id, Map<String, List<Discount__c>>> discountsByIdAndType = new Map<Id, Map<String,List<Discount__c>>>(); 
            //User Identifier to Discounts of type = individual: 
            Map<String, List<Discount__c>> indivDiscountsByUsers = new Map<String, List<Discount__c>>(); 

            //Query for active discounts where auto apply is true
            for(Discount__c discount : [SELECT Id, Name, Event__c, Executive_Program__c, Current_Usage__c, Max_Usage__c, type__c, available_for__c, ownerId FROM Discount__c 
                                            WHERE Active__c = true 
                                            AND (Event__c IN :evtAndPrgIds OR Event__c = null OR Executive_Program__c IN :evtAndPrgIds OR Executive_Program__c = null) 
                                            AND Automatically_Apply__c = true 
                                            AND Requires_Approval__c = false
                                            AND available_for__c != null
                                            AND Type__c IN :discountTypesToInclude
                                            ORDER BY Start_Date__c ASC NULLS LAST])
            {
                //if discount is for individual, map discount owner to discount: 
                if(discount.Available_for__c == 'Individual'){
                    String identifier = ''; 
                    //if discount type is for a program or event, set the identifier as user ID + program or event Id 
                    if(discount.type__c == 'Program Discount' || discount.type__c == 'Event Discount') identifier = (String)discount.ownerId + (discount.event__c != null ? (String)discount.Event__c : (String)discount.Executive_Program__c); 
                    //if discount type is for a fee waiver, set the identifier as the user ID: 
                    else if(discount.type__c == 'Fee Waiver' ) identifier = (String)discount.ownerId;

                    if(indivDiscountsByUsers.containsKey(identifier)){
                        indivDiscountsByUsers.get(identifier).add(discount); 
                    }else{
                        indivDiscountsByUsers.put(identifier, new List<discount__c>{discount}); 
                        //index idenfier for fee waiver = ownerId: 
                        discCurrIndexMap.put(identifier, 0); 
                    }
                }
                //if discount =  Program Discounts and Event Discounts, map discount by event or program ID --> type --> list of discounts: 
                else if(discount.event__c != null || discount.executive_program__c != null){
                    Id eventOrProgramId = discount.Event__c != null ? discount.Event__c : discount.Executive_Program__c; 
                    if(discountsByIdAndType.containsKey(eventOrProgramId)){
                        if(discountsByIdAndType.get(eventOrProgramId).containsKey(discount.available_for__c)){
                            discountsByIdAndType.get(eventOrProgramId).get(discount.available_for__c).add(discount); 
                        }else{
                            discountsByIdAndType.get(eventOrProgramId).put(discount.available_for__c, new List<Discount__c>{discount}); 

                            //Map event or program of same type to current index:
                            String indexIdenfier = (String)eventOrProgramId + discount.available_for__c.toLowerCase(); 
                            discCurrIndexMap.put(indexIdenfier, 0); 
                        }
                    }else{
                        Map<String, List<Discount__c>> discountsByType = new Map<String, List<Discount__c>>(); 
                        discountsByType.put(discount.available_for__c, new List<Discount__c>{discount}); 
                        discountsByIdAndType.put(eventOrProgramId, discountsByType); 

                        //Map event or program of same type to current index:
                        String indexIdenfier = (String)eventOrProgramId + discount.available_for__c.toLowerCase(); 
                        discCurrIndexMap.put(indexIdenfier, 0); 
                    }
                }              
            }

            //Assign Discounts: 
            for(pymt__Shopping_Cart_Item__c sci : sciToUpdate){
                User sciOwner = userMap.get(sci.OwnerId);
                
                //Assign event or program discount: 
                if(sci.type__c == 'EP Program Balance' || sci.type__c == 'Event Registration'){
                    Id evtOrPrgId = sci.Special_Event__c != null ? sci.Special_Event__c : sci.Course_offering__c; 
                    String individualIdentifier = (String)sciOwner.Id + (String)evtOrPrgId; 

                    //Users with individually assigned discounts will be auto-assigned their individual discounts: 
                    if(indivDiscountsByUsers.containsKey(individualIdentifier) && sci.Discount__c == null){
                        //Auto-Assign Student Discount: 
                        autoAssignDiscount(sci, indivDiscountsByUsers.get(individualIdentifier), discCurrIndexMap, individualIdentifier); 
                    
                    //Users with Applicant community user profile qualify for student discount:
                    }if(sciOwner.Profile.Name == 'Student Community User' && discountsByIdAndType.containsKey(evtOrPrgId) && discountsByIdAndType.get(evtOrPrgId).containsKey('Rotman Student') && sci.Discount__c == null){
                        //Auto-Assign Student Discount: 
                        autoAssignDiscount(sci, discountsByIdAndType.get(evtOrPrgId).get('Rotman Student'), discCurrIndexMap, (String)evtOrPrgId + 'Rotman Student'.toLowerCase());

                    //Users identified as Rotman Alumni qualify for Rotman Alumni Discount
                    }if(sciOwner.Rotman_Alumni__c && discountsByIdAndType.containsKey(evtOrPrgId) && discountsByIdAndType.get(evtOrPrgId).containsKey('Rotman Alumni')  && sci.Discount__c == null){
                        //Auto-Assign Rotman Alumni Discount: 
                        autoAssignDiscount(sci, discountsByIdAndType.get(evtOrPrgId).get('Rotman Alumni'), discCurrIndexMap, (String)evtOrPrgId + 'Rotman Alumni'.toLowerCase());

                    //All users qualify for UofT Alumni Discount: 
                    }if(discountsByIdAndType.containsKey(evtOrPrgId) && discountsByIdAndType.get(evtOrPrgId).containsKey('U of T Alumni')  && sci.Discount__c == null){
                        //Auto-Assign Rotman UofT Alumni Discount: 
                        autoAssignDiscount(sci, discountsByIdAndType.get(evtOrPrgId).get('U of T Alumni'), discCurrIndexMap, (String)evtOrPrgId + 'U of T Alumni'.toLowerCase());
                    
                    //All users qualify for All Discounts: 
                    }if(discountsByIdAndType.containsKey(evtOrPrgId) && discountsByIdAndType.get(evtOrPrgId).containsKey('All')  && sci.Discount__c == null){
                        //Auto-Assign All type discount: 
                        autoAssignDiscount(sci, discountsByIdAndType.get(evtOrPrgId).get('All'), discCurrIndexMap, (String)evtOrPrgId + 'All'.toLowerCase() );
                    }
                //Assign fee waiver discount: 
                }else if(sci.type__c == 'Application Fee' && indivDiscountsByUsers.containsKey((String)sciOwner.Id)){
                    autoAssignDiscount(sci, indivDiscountsByUsers.get((String)sciOwner.Id), discCurrIndexMap, (String)sciOwner.Id); 
                }
            }
        }

        return dmlWrapper; 
    }

    /**
    * @description auto assigns discount on the shopping cart items 
    * @param sci shopping cart item 
    * @param discounts list of potential discounts to stamp 
    * @param discCurrIndexMap determines index position of most relevant discount from discounts list 
    * @param indexIdentifer string identifier used in discCurrIndexMap to indentify index position in list 
    */
    private void autoAssignDiscount(pymt__Shopping_Cart_Item__c sci, List<Discount__c> discounts, Map<String, Integer> discCurrIndexMap, String indexIdentifier){
        Integer curindex = discCurrIndexMap.get(indexIdentifier); 
        Discount__c curDiscount = discounts.size() > curIndex ? discounts[curIndex] : null;
        if(curDiscount != null){
            //if there is a usage limit on the discount: 
            if(curDiscount.max_usage__c != null){
                if(curDiscount.current_usage__c < curDiscount.max_usage__c){
                    sci.discount__c = curDiscount.Id; 
                    //increment current usage: 
                    curDiscount.current_usage__c += 1; 
                    //update curIndex if current usage == max usage: 
                    if(curDiscount.current_usage__c == curDiscount.max_usage__c){ 
                        //increment current index: 
                        discCurrIndexMap.put(indexIdentifier, discCurrIndexMap.get(indexIdentifier) + 1); 
                    }
                }
            }else{ //if there is no limit on the discount,set discount as the first discount on the list
                sci.discount__c = curDiscount.Id; 
            }
        }
    }
}