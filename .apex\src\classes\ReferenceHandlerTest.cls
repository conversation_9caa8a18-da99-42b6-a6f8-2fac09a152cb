/**
* Test class for InboundReferenceHandler, OutboundReferenceHandler classes
* 
* <AUTHOR>
*/
@isTest
public class ReferenceHandlerTest {

        @TestSetup
        static void setup(){

            //Inserting contact will create admin account 
            Contact c =  (Contact) TestFactory.createSObject(new Contact()); 
            insert c; 

            //Query for admin account: 
            Account a = [SELECT ID FROM Account WHERE hed__Primary_Contact__c = :c.Id]; 

            //create Course, CourseTerms, COurse Offering
            hed__Course__c crs = new hed__Course__c(Name = 'Test',hed__Account__c = a.Id);
            insert crs;
            List<hed__Term__c> trmLst = new List<hed__Term__c>();
            hed__Term__c trm1 = new hed__Term__c(Name='Test1',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
            trmLst.add(trm1);
            hed__Term__c trm2 = new hed__Term__c(Name='Test2',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
            trmLst.add(trm2);
            hed__Term__c trm3 = new hed__Term__c(Name='Test3',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
            trmLst.add(trm3);
            hed__Term__c trm4 = new hed__Term__c(Name='Test4',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
            trmLst.add(trm4);
            hed__Term__c trm5 = new hed__Term__c(Name='Test5',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
            trmLst.add(trm5);
            insert trmLst;
            hed__Course_Offering__c coffrng1 = new hed__Course_Offering__c(Name = '150',hed__Term__c=trm1.id,hed__Course__c = crs.Id);
            insert coffrng1;

            //Create Test App record
            hed__Application__c appEP = new hed__Application__c(RecordTypeId = ApplicationService.ExecutiveProgramsRTId, 
                                                                hed__Applicant__c = c.Id, 
                                                                Course_Offering__c = coffrng1.Id,
                                                                Referrer_1_Email__c = '<EMAIL>',
                                                                Referrer_2_Email__c = '<EMAIL>',
                                                                Referrer_3_Email__c = '<EMAIL>'
            );
            insert appEP;
        }

        @isTest
        public static void sendReferenceEmailsTest(){
            
            OutboundReferenceHandler.OutboundRefEmailWrapper emWrap = new OutboundReferenceHandler.OutboundRefEmailWrapper();
            hed__Application__c appEP = [Select Name, hed__Applicant__c, Referrer_1_Email__c from hed__Application__c Limit 1];
            emWrap.application = appEP;
            emWrap.emailAddress = appEp.Referrer_1_Email__c;
            List<OutboundReferenceHandler.OutboundRefEmailWrapper> emWrapList = new List<OutboundReferenceHandler.OutboundRefEmailWrapper>();
            emWrapList.add(emWrap);

            Test.startTest();
            OutboundReferenceHandler.sendReferenceEmails(emWrapList);
            Test.stopTest();
        }
        
        @isTest
        public static void handleInboundEmailTest(){

            Messaging.InboundEmail email = new Messaging.InboundEmail() ;
            Messaging.InboundEnvelope env = new Messaging.InboundEnvelope();

            hed__Application__c appEP = [Select Name, hed__Applicant__c, Referrer_1_Email__c, Referrer_2_Email__c, Referrer_3_Email__c from hed__Application__c Limit 1];
            appEP.Reference_1__c='';
            update appEP;
            
            EmailServicesAddress[] emSerAdd = [SELECT Id,AuthorizedSenders, EmailDomainName, IsActive,LocalPart FROM EmailServicesAddress 
                WHERE Function.FunctionName ='InboundReferenceService' and isActive=true LIMIT 1];
            if(emSerAdd.size() == 1){
                String sender = emSerAdd[0].LocalPart+'@'+emSerAdd[0].EmailDomainName;
                List<String> strList = new List<String>();
                strList.add(sender);
                email.CcAddresses = strList;
            }
            
            email.fromAddress = '<EMAIL>';
            email.subject = 'Re: Application: '+appEP.Name;
            email.plainTextBody = 'This is referral confirmation for the applicant';
            
            InboundReferenceHandler inEmail = new InboundReferenceHandler();
            inEmail.handleInboundEmail(email, env);

			email.fromAddress = '<EMAIL>';
            inEmail.handleInboundEmail(email, env);

			email.fromAddress = '<EMAIL>';
            inEmail.handleInboundEmail(email, env);

			email.fromAddress = '<EMAIL>';
            inEmail.handleInboundEmail(email, env);

        }

}