public class SubscriptionCenterTriggerHandler {
    
    public static void handleSubCenterHash_Contact(List<Contact> contacts, boolean isInsert) {
        system.debug('--- running handleSubCenterHash_Contact');
        if (isInsert) {
            // Execute hash logic
            SubscriptionCenterHashUtility.calculateHash(contacts);
        } else {
            list<Contact> consToHash = new list<Contact>();
            for (Contact con : [SELECT Id, Hash_ID__c FROM Contact WHERE ID IN :contacts]) {
                system.debug('--- con: ' + con.Id);
                if (string.isBlank(con.Hash_ID__c)) {
                    consToHash.add(con);
                }
            }
            
            if (consToHash.size() > 0) {
                // Execute hash logic
                list<Contact> consToUpdate = SubscriptionCenterHashUtility.calculateHash(consToHash);
                if (consToUpdate.size() > 0) {
                    update consToUpdate;
                }
            }
        }
    }
    
    public static void handleSubCenterHash_Lead(List<Lead> leads, boolean isInsert) {
        system.debug('--- running handleSubCenterHash_Lead');
        if (isInsert) {
            // Execute hash logic
            SubscriptionCenterHashUtility.calculateHash_Lead(leads);
        } else {
            list<Lead> ldsToHash = new list<Lead>();
            for (Lead ld : [SELECT Id, Hash_ID__c FROM Lead WHERE ID IN :leads]) {
                system.debug('--- lead: ' + ld.Id);
                if (string.isBlank(ld.Hash_ID__c)) {
                    ldsToHash.add(ld);
                }
            }
            
            if (ldsToHash.size() > 0) {
                // Execute hash logic
                list<Lead> ldsToUpdate = SubscriptionCenterHashUtility.calculateHash_Lead(ldsToHash);
                if (ldsToUpdate.size() > 0) {
                    update ldsToUpdate;
                }
            }
        }
    }
    
}