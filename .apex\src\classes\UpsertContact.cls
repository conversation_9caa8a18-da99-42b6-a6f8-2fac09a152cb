public class UpsertContact {
  @InvocableMethod(label='Upsert Contact' description='Returns a list of contact records that have been upserted (search matches on email)')
  public static List<Contact> upsertContact(List<Contact> contacts) {
    
      // Collect emails for existing contact search
      Set<String> emails = new Set<String>{};
          for (Contact c :contacts) {
              if (!String.isEmpty(c.Email)) {
                  emails.add(c.Email);
              }
          }
      Map<String, Contact> existing = new Map<String,Contact>{};
      if (emails.size()>0) {
          // Find existing contacts matching emails
          Contact[] existingContacts = [SELECT Id, Name, FirstName, LastName, Email, MailingStreet,
                                        MailingCity, MailingState, MailingCountry, MailingPostalCode, Phone,
                                        AccountId FROM 
                                        Contact WHERE Email in :emails];
          if (existingContacts.size()>0) {
              for (Contact c :existingContacts) {
                  existing.put(c.Email, c);
              }
          }
      }
      
      Contact[] toInsert = new Contact[]{};
      Contact[] toReturn = new Contact[]{};
      for (Contact c :contacts) {
              if (existing.get(c.Email) <> null) {
                  toReturn.add(existing.get(c.Email));
              } else {
                  toInsert.add(c);
              }
          }
      Contact[] results = toReturn;
      if (toInsert.size()>0) {
          Database.DMLOptions dml = new Database.DMLOptions();
          dml.DuplicateRuleHeader.AllowSave = true; 
          
          Database.insert(toInsert,dml);
          results.addAll(toInsert);
      }
      return results;
      
  }
}