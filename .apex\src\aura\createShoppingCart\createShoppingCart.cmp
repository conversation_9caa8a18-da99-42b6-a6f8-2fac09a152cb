<aura:component implements="flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" 
                access="global" controller="createShoppingCartController"> <!-- force:appHostable,force:hasRecordId,-->
    <aura:handler name="init" value="{!this}" action="{!c.onLoad}"/>
    
    <aura:attribute Name="recordId" type="Id" />
    <aura:attribute Name="cartId" type="Id" />
    <aura:attribute Name="SuccessMsg" type="String" />
    <aura:attribute Name="CartMap" type="LIST" default="[]"/>
    <aura:attribute Name="showCartInfo" type="boolean" default="false"/> 
    <aura:attribute Name="userName" type="string" default="" />
    <aura:attribute Name="Total" type="Double"  />
    <aura:attribute Name="cartEmpty" type="boolean" default="false"/> 
    <center>
        <div layout="block" style="font-size: x-large;">
            {!v.userName} Shopping Cart
        </div>
        <aura:if isTrue="{!and(empty(v.CartMap),v.showCartInfo)}">
            <div style="font-size: x-large;">Your Shopping Cart Seems To Be Empty!!!</div>
        </aura:if>
    </center>
    <aura:if isTrue="{!not(empty(v.CartMap))}"> 
        <div id="CartId" layout="block"  >
            <div style="float: right; border: 1px solid;padding: 2%;">
                <div style="font-size: large;border-bottom:1px solid">
                    Order Summary	
                </div> <br/>
                <div>
                    <table>
                        <thead>
                            <tr>
                                <th>  </th>
                                <th>  </th>
                            </tr>
                        </thead>
                        <tbody>
                         <!--   <tr>
                                <td>Subtotal</td>
                                <td>$200</td>
                            </tr>
                            <tr>
                                <td>Tax</td>
                                <td>$20</td>
                            </tr> -->
                            <tr>
                                <td><b>Total</b></td>
                                <td><b>{!v.Total}</b></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <lightning:button class="slds-m-top_small" variant="brand" 
                                  type="submit" name="" value="" onclick="{!c.checkout}"
                                  label="Proceed To Checkout" />
                </div>
                
            </div>
            <!--      <apex:outputpanel id="CartId" layout="block" style="Display:none;"  styleclass="customPopup"> -->
            <!--    <div layout="block" id="CartDiv"  >
       
            <div>
                <table class="cartTable" id="CartTable">
                    <thead>
                        <tr>
                            <th> Item Name </th>
                            <th> Description </th>
                            <th> Price </th>
                            <th> Quantity </th>
                            <th> Total Price </th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody id="tbodyCart">
                        
                        <aura:iteration items="{!v.CartMap}" var="cus" indexVar="key">
                            
                            <aura:if isTrue="{!not(empty(cus.item))}">
                                <tr>
                                    <td style="width:15%; "> {!cus.item} 
                                     
                                    </td>
                                    <td style="width:15%; "> {!cus.description}</td>
                                    <td style="width:15%; "> 
                                        ${!cus.price}
                                    </td>
                                    <td style="width:15%; ">
                                        <lightning:input type="number" name="qty"   value="{!cus.qty}" />
                                    </td>
                                    <td style="width:15%;padding-left: 2%; ">${!cus.price * cus.qty}</td>
                                    <td style="/*padding-left: 74%;padding-top: 1%;*/">
                                        <lightning:button class="" variant="brand" aura:id="{!cus.cid}"
                                                          type="submit" name="{!cus.cid}" onclick="{!c.removeItem}" 
                                                          label="Remove" />
                                    </td>
                                
                            </aura:if>
                        </aura:iteration>
                        <br/>
                    </tbody>
                </table>
            </div>
            
            
            
            
        </div> -->
            <!--       </apex:outputpanel>-->
        </div> 
        <div style="padding-left:3%;">
            <br/><br/>
            <table class="cartTable2" id="CartTable2" style="width:65%;">
                <thead>
                    <tr 
                        style="border-top: 1px solid grey;border-bottom: 1px solid grey;height: 30px;
                               font-size: medium;">
                        <th><center>Item</center>   </th>
                        <th> <center>Price</center></th>
                        <th><center> Qty</center></th>
                        <th><center> Total</center></th>
                    </tr>
                </thead>
                <tbody>
                    <aura:iteration items="{!v.CartMap}" var="cus" indexVar="key">
                        
                        <tr style="height:100px; border:none; ">
                            <td style="padding-left:3%;padding-right: 3%;overflow: visible;">
                                <Span style="font-size: large">{!cus.item}</Span> <br/> 
                                <Span style="font-size: small">{!cus.description}</Span>  
                            </td>
                            <td style="width:12%;">
                                ${!cus.price}
                            </td>
                            <td style="width:12%; ">
                                <lightning:input type="number" name="qty"   value="{!cus.qty}" />
                            </td>
                            <td style="width:20%;padding-left: 3%;padding-top:8%; ">
                                <div>${!cus.price * cus.qty}</div>
                                
                                <BR/>
                                
                                <div >
                                    <lightning:button class="" variant="base" aura:id="{!cus.cid}"
                                                      type="submit" name="{!cus.cid}" onclick="{!c.removeItem}" 
                                                      label="Remove" >
                                        <!-- <lightning:icon iconName="action:delete" 
                                      alternativeText="Delete" title="Delete"
                                      Size="xx-small"/> -->
                                    </lightning:button> 
                                </div>
                            </td>
                            
                        </tr>
                        
                        
                    </aura:iteration>
                </tbody>
            </table>
            <div style="padding-left:28%;">
                <lightning:button class="slds-m-top_small" variant="brand" 
                                  type="submit" name="AddToCart" onclick="{!c.continueShopping}" 
                                  label="Continue Shopping" />
                
                <lightning:button class="slds-m-top_small" variant="brand" 
                                  type="submit" name="Update" value="Update" onclick="{!c.UpdateCart}"
                                  label="Update Cart" />
            </div>
        </div>
    </aura:if> 
</aura:component>