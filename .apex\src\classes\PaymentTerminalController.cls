public without sharing class PaymentTerminalController {
    
    @AuraEnabled
    public static Id recId{get;set;} 
    //@AuraEnabled
    //public static contact contactId{get; set;}
    public PaymentTerminalController(ApexPages.StandardController controller){
        //CardWrapperClass cardDetails = new CardWrapperClass();
        recId= ApexPages.currentpage().getparameters().get('pid'); //'a0y4c0000004gCQAAY';///
        //contactId = new Contact();
    }
    @AuraEnabled
    public static CardWrapperClass NewContact(string contactId){
        system.debug('contactId = '+contactId);
        CardWrapperClass cardDetails = new CardWrapperClass();
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        
        pymt.Name = 'Payment'; 
        pymt.pymt__Contact__c = contactId;// '0034c0000028Bk2AAE';
        pymt.pymt__Payment_Processor__c = 'Moneris';
        pymt.pymt__Status__c = 'Scheduled';
        insert pymt;
        pymt__PaymentX__c pymtW = [
            SELECT 
            Name,pymt__Payment_Processor__c, pymt__Tax__c, pymt__Shipping__c, pymt__Amount__c,
            pymt__Currency_ISO_Code__c, pymt__Invoice_Number__c, pymt__Date__c,pymt__Transaction_Id__c,
            pymt__PO_Number__c, pymt__Card_Type__c, pymt__Billing_First_Name__c,
            pymt__Billing_Last_Name__c, pymt__Billing_Street__c,Order_Id__c,
            pymt__Billing_City__c, pymt__Billing_Country__c,pymt__Status__c,pymt__Contact__c,
            pymt__Billing_State__c, pymt__Billing_Postal_Code__c, pymt__Billing_Email__c,
            Refund_Reason__c,
            (Select id, name,pymt__Quantity__c,pymt__Total__c FROM R00N40000001tGNtEAM)
            FROM pymt__PaymentX__c
            WHERE Id=:pymt.Id LIMIT 1
        ];
        if(pymtW.R00N40000001tGNtEAM.size() > 0 ){
            cardDetails.cartItem = pymtW.R00N40000001tGNtEAM;
        }
        system.debug('cardDetails= '+cardDetails);
        cardDetails.pymt = pymtW;
        //recId = pymt.Id;
        return cardDetails;
    }
    //public CardWrapperClass cardDetails{get;set;}
    @AuraEnabled
    public static CardWrapperClass getPaymentDetails(Id pid){
        try{
            //pid = 'a0y4c0000004gCQAAY';
            system.debug('pid== '+pid);
            //system.debug('contactId= '+contactId);
            CardWrapperClass cardDetails = new CardWrapperClass();
            pymt__PaymentX__c pymt = new pymt__PaymentX__c();
            if(pid != null){
                pymt = [
                    SELECT 
                    Name,pymt__Payment_Processor__c, pymt__Tax__c, pymt__Shipping__c, pymt__Amount__c,
                    pymt__Currency_ISO_Code__c, pymt__Invoice_Number__c, pymt__Date__c,pymt__Transaction_Id__c,
                    pymt__PO_Number__c, pymt__Card_Type__c, pymt__Billing_First_Name__c,pymt__Payment_Type__c,
                    pymt__Billing_Last_Name__c, pymt__Billing_Street__c,Order_Id__c,Administration_Fee__c,
                    pymt__Billing_City__c, pymt__Billing_Country__c,pymt__Status__c,pymt__Contact__c,
                    pymt__Billing_State__c, pymt__Billing_Postal_Code__c, pymt__Billing_Email__c,
                    Refund_Reason__c,pymt__Discount__c, 
                    (Select id, name,pymt__Quantity__c,pymt__Total__c FROM R00N40000001tGNtEAM)
                    FROM pymt__PaymentX__c
                    WHERE Id=:pid LIMIT 1
                ];
                if(pymt.R00N40000001tGNtEAM.size() > 0 ){
                    cardDetails.cartItem = pymt.R00N40000001tGNtEAM;
                }
            }
            if(pymt.pymt__Amount__c != Null && pymt.pymt__Amount__c >0){
                pymt.pymt__Amount__c = pymt.pymt__Amount__c.SetScale(2);
            }
            if(pymt.pymt__Tax__c != Null && pymt.pymt__Tax__c >0){
                pymt.pymt__Tax__c = pymt.pymt__Tax__c.SetScale(2);
            }
            if(pymt.pymt__Discount__c != Null && pymt.pymt__Discount__c >0){
                pymt.pymt__Discount__c = pymt.pymt__Discount__c.SetScale(2);
            }
            cardDetails.pymt = pymt;
            system.debug('cardDetails== '+cardDetails);
            return cardDetails;
        }catch(Exception e){
            
            throw new AuraHandledException('Exception :'+e.getMessage() + ' , line number: '+ e.getLineNumber());           
            
        }
    }
    
    @AuraEnabled
    public static MonerisResponseWrapper  getPaymentDone(CardWrapperClass cardDetails,Id pid){
        
        try{ 
            system.debug('card= '+cardDetails);
            system.debug('pid= '+pid);
            if(pid != null){
                
                String res =  MonerisConnectClass.monerisPurchase(cardDetails);
                system.debug('payment res==> '+ res);
                String jsonContent = XmlParser.xmlToJson(res);
                system.debug('jsonContent=> '+jsonContent);
                MonerisResponseWrapper response = (MonerisResponseWrapper) json.deserialize(jsonContent, MonerisResponseWrapper.class);
                pymt__PaymentX__c pymt =cardDetails.pymt; 
                /*[
                SELECT 
                Name, pymt__Transaction_Id__c, Order_Id__c,
                Payment_Response__c, pymt__Check_Number__c,pymt__Status__c
                FROM pymt__PaymentX__c
                WHERE Id=:pid LIMIT 1
                ];*/
                if(pymt.Payment_Response__c == null){
                    pymt.Payment_Response__c = '';
                }
                if(response.response.receipt.ReceiptId != null && 
                   response.response.receipt.Message.contains('APPROVED') ){
                    pymt.pymt__Transaction_Id__c = response.response.receipt.TransID;
                    pymt.Order_Id__c = response.response.receipt.ReceiptId;
                    //pymt.pymt__Check_Number__c = response.response.receipt.ReferenceNum;
                    pymt.Payment_Response__c += '\n Payment Success:'+string.valueOf(response.response)+'\n------------------\n';
                    pymt.pymt__Status__c = 'Completed';
                    pymt.pymt__Last_4_Digits__c = cardDetails.cardNo.right(4);
                    pymt.pymt__Date__c = date.today();
                }
                else if(response.response.receipt.ReceiptId != null && response.response.receipt.Message.contains('DECLINED')){
                    pymt.pymt__Transaction_Id__c = response.response.receipt.TransID;
                    pymt.Order_Id__c = response.response.receipt.ReceiptId;
                    //pymt.pymt__Check_Number__c = response.response.receipt.ReferenceNum;
                    pymt.Payment_Response__c += '\n Payment Declined:'+string.valueOf(response.response)+'\n------------------\n';
                    pymt.pymt__Status__c = 'Declined';
                    //pymt.pymt__Last_4_Digits__c = cardNo.right(4);
                }
                else{
                    pymt.Payment_Response__c += '\n Payment Error:'+string.valueOf(response.response)+'\n------------------\n';
                }
                update pymt;
                //Update Payment Record/
                
                return response;
            }
            return null;
        }catch(Exception e){
            
            throw new AuraHandledException('Exception : '+e.getMessage() + ', ' + e.getLineNumber());           
            
        }
    }
    
    @AuraEnabled
    public static MonerisResponseWrapper  getVoidPayment(CardWrapperClass cardDetails,Id pid){
        try{ 
        pymt__PaymentX__c pymt = [
            SELECT 
            Name, pymt__Transaction_Id__c, Order_Id__c,
            Payment_Response__c, pymt__Check_Number__c,pymt__Status__c,pymt__Payment_Type__c
            FROM pymt__PaymentX__c
            WHERE Id=:pid LIMIT 1
        ];
        system.debug('pymt--> '+ pymt);
        string order = pymt.Order_Id__c ;
        string tnx = pymt.pymt__Transaction_Id__c;
        String res =  MonerisConnectClass.monerisPurchaseVoid(order, tnx);
        
        String jsonContent = XmlParser.xmlToJson(res);
        system.debug('jsonContent=> '+jsonContent);
        
        MonerisResponseWrapper response = (MonerisResponseWrapper) json.deserialize(jsonContent, MonerisResponseWrapper.class);
        if(pymt.Payment_Response__c == null){
            pymt.Payment_Response__c = '';
        }
        if(response.response.receipt.ReceiptId != null && response.response.receipt.ReceiptId !='Null'){
            pymt.pymt__Transaction_Id__c = response.response.receipt.TransID;
            pymt.Order_Id__c = response.response.receipt.ReceiptId;
            //pymt.pymt__Check_Number__c = response.response.receipt.ReferenceNum;
            pymt.Payment_Response__c += '\n Payment Void Success:'+string.valueOf(response.response)+'\n------------------\n';
            pymt.pymt__Status__c = 'Voided';
        }
        else{
            pymt.Payment_Response__c += '\n Payment Void Error:'+string.valueOf(response.response)+'\n------------------\n';
        }
        update pymt;
        system.debug('Result-->'+ response);
        return response;
            }catch(Exception e){
            
            throw new AuraHandledException('Exception :'+e.getMessage());           
            
        }
    }
    
    /* Description: This Method is called when Payment type is "Credit Card"
     * Date: 30/07/2020(Updated)
     * Parameters: CardWrapper and Payment ID
     */
    @AuraEnabled 
    public static MonerisResponseWrapper  getRefundPayment(CardWrapperClass cardDetails,Id pid){
        double refundAmt = 0.00;
        try{ 
            pymt__PaymentX__c pymt = [
                SELECT 
                Name, pymt__Transaction_Id__c, Order_Id__c,pymt__Amount__c,pymt__Payment_Type__c,
                Payment_Response__c, pymt__Check_Number__c,pymt__Status__c, Administration_Fee__c
                FROM pymt__PaymentX__c
                WHERE Id=:pid LIMIT 1
            ];
            system.debug('Order_Id__c==> '+cardDetails.pymt.Order_Id__c);
            system.debug('pymt__Transaction_Id__c==> '+cardDetails.pymt.pymt__Transaction_Id__c);
            refundAmt = cardDetails.Amount;
            if(cardDetails.pymt.Administration_Fee__c > 0){
                cardDetails.Amount -= cardDetails.pymt.Administration_Fee__c;
                if(pymt.Administration_Fee__c != null && pymt.Administration_Fee__c >= 0.00){
                    pymt.Administration_Fee__c += cardDetails.pymt.Administration_Fee__c;
                }
                else{
                    pymt.Administration_Fee__c = cardDetails.pymt.Administration_Fee__c;
                }
                
            }
            cardDetails.pymt.Order_Id__c = pymt.Order_Id__c;
            cardDetails.pymt.pymt__Transaction_Id__c = pymt.pymt__Transaction_Id__c;
            String res =  MonerisConnectClass.monerisCardRefund(cardDetails);
            
            String jsonContent = XmlParser.xmlToJson(res);
            system.debug('jsonContent=> '+jsonContent);
            
            MonerisResponseWrapper response = (MonerisResponseWrapper) json.deserialize(jsonContent, MonerisResponseWrapper.class);
            if(pymt.Payment_Response__c == null){
                pymt.Payment_Response__c = '';
            }
            if(response.response.receipt.ReceiptId != null && response.response.receipt.ReceiptId !='Null' &&
               response.response.receipt.Message.contains('APPROVED')){
                pymt__PaymentX__c pymtRefund = new pymt__PaymentX__c();
                pymtRefund.Name = pymt.Name +'_Refund';
                pymtRefund.pymt__Transaction_Id__c = response.response.receipt.TransID;
                pymtRefund.Order_Id__c = response.response.receipt.ReceiptId;
                pymtRefund.pymt__Transaction_Type__c = 'Refund';
                pymtRefund.Payment_Response__c = '\n Payment Refund Success:'+string.valueOf(response.response.receipt)+'\n------------------\n';
                pymtRefund.pymt__Status__c = 'Reversed';
                pymtRefund.pymt__Parent_Transaction__c= pymt.Id;
                pymtRefund.pymt__Amount__c = double.valueOf(cardDetails.Amount);
                pymtRefund.pymt__Date__c = date.today();
                insert pymtRefund;
                pymt.pymt__Status__c = 'Reversed';
                pymt.pymt__Amount__c = pymt.pymt__Amount__c - refundAmt;//double.valueOf(cardDetails.Amount) - double.valueOf(pymt.Administration_Fee__c);
            }
            else{
                pymt.Payment_Response__c += '\n Payment Refund Error:'+string.valueOf(response.response.receipt)+'\n------------------\n';
            }
            update pymt;
            system.debug('Result-->'+ response);
            return response;
        }catch(Exception e){
            
            throw new AuraHandledException('Exception : '+e.getMessage()+', at Line '+ e.getLineNumber());           
            
        }
    }
    /* Description: This Refund Method is called if Payment type is "Check" or "cash"
     * Date: 30/07/2020
     * Parameters: CardWrapper and Payment ID
     */
    @AuraEnabled //This Refund Method is called if Payment type is "Check" or "cash"
    public static string getRefundPaymentcashCheck(CardWrapperClass cardDetails,Id pid){
        string response = '';
        try{ 
            pymt__PaymentX__c pymt = [
                SELECT 
                Name, pymt__Transaction_Id__c, Order_Id__c,pymt__Amount__c,pymt__Payment_Type__c,
                Payment_Response__c, pymt__Check_Number__c,pymt__Status__c, Administration_Fee__c
                FROM pymt__PaymentX__c
                WHERE Id=:pid LIMIT 1
            ];
            if(cardDetails.pymt.Administration_Fee__c > 0){
                cardDetails.Amount -= cardDetails.pymt.Administration_Fee__c;
                //For multiple Partial Payments, add to the Admin Fee.
                if(pymt.Administration_Fee__c != Null && pymt.Administration_Fee__c >0)
                {
                	pymt.Administration_Fee__c += cardDetails.pymt.Administration_Fee__c; 
            	}
                else{
                    pymt.Administration_Fee__c = cardDetails.pymt.Administration_Fee__c;
                }
                
            }
            //cardDetails.pymt.Order_Id__c = pymt.Order_Id__c;
            //cardDetails.pymt.pymt__Transaction_Id__c = pymt.pymt__Transaction_Id__c;
            pymt__PaymentX__c pymtRefund = new pymt__PaymentX__c();
                pymtRefund.Name = pymt.Name +'_Refund';
                //pymtRefund.pymt__Transaction_Id__c = response.response.receipt.TransID;
                //pymtRefund.Order_Id__c = response.response.receipt.ReceiptId;
                pymtRefund.pymt__Transaction_Type__c = 'Refund';
                pymtRefund.Payment_Response__c = '\n Payment Refund Success:';
                pymtRefund.pymt__Status__c = 'Reversed';
            	pymtRefund.pymt__Payment_Type__c = pymt.pymt__Payment_Type__c;
                pymtRefund.pymt__Parent_Transaction__c= pymt.Id;
                pymtRefund.pymt__Amount__c = double.valueOf(cardDetails.Amount);
            	pymtRefund.pymt__Date__c = date.today();
                insert pymtRefund;
            	pymt.pymt__Status__c = 'Reversed';
                pymt.pymt__Amount__c = pymt.pymt__Amount__c - double.valueOf(cardDetails.Amount) - double.valueOf(pymt.Administration_Fee__c);
            	update pymt;
            	response = 'Refund Successful!';
        }catch(Exception e){
            response = 'Refund Failure!: '+e.getMessage();
            throw new AuraHandledException('Exception : '+e.getMessage()+ ', at Line '+ e.getLineNumber());           
            
        }
        return response;
    }
}