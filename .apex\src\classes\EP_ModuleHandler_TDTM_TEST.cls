@isTest
public class EP_ModuleHandler_TDTM_TEST {

    @testSetup
    static void testSetup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for EP_ModuleHandler_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('EP_ModuleHandler_TDTM', 'Course_Enrollment__c', 'AfterInsert;AfterUpdate;', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
                                          
        //Create test Account
        Account a = new Account(Name='TestAccount');
        insert a;                                 
        //Create test Contact
        Contact c = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test1', LastName = 'Student', Email = '<EMAIL>'));  
        insert c;
        //Create Term
        hed__Term__c trm = new hed__Term__c(hed__Account__c=a.Id,name='Spring 2020');
        insert trm;                           
        //Create course
        hed__Course__c course = new hed__Course__c(hed__Account__c=a.Id,name='MBA 2020');
        insert course;
        //Create course offering
        hed__Course_Offering__c cOffering = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering',hed__Term__c=trm.Id); 
        insert cOffering;
        hed__Course_Offering__c cOfferingChild1 = new hed__Course_Offering__c(Parent_Course__c =cOffering.Id,hed__Course__c = course.id,Name = 'Test Child1',hed__Term__c=trm.Id);
        insert cOfferingChild1;
        //Create applications
        List<hed__Application__c> applst = new List<hed__Application__c>();
        hed__Application__c app = new hed__Application__c(hed__Application_Status__c = 'Accepted Offer',Course_Offering__c = cOffering.id,hed__Applicant__c = c.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(app);
		insert applst;
        //Create course enrollment
        hed__Course_Enrollment__c ce = new hed__Course_Enrollment__c(hed__Contact__c = c.Id, hed__Course_Offering__c = cOfferingChild1.Id, Application__c = app.Id, hed__Status__c = 'Current');
        insert ce;
        
    }
    
    @isTest
    public static void testModule(){
        //Course enrollment to update
        hed__Course_Enrollment__c ceToUpdate = [SELECT ID, hed__Status__c FROM hed__Course_Enrollment__c WHERE hed__Status__c = 'Current' LIMIT 1];
		//Create new course enrollment
        hed__Course_Offering__c cOffering = [SELECT Id FROM hed__Course_Offering__c WHERE Name = 'Test Course Offering' LIMIT 1];
        hed__Term__c trm = [SELECT ID from hed__Term__c LIMIT 1];
        hed__Course__c course = [SELECT ID FROM hed__Course__c LIMIT 1];
        hed__Course_Offering__c cOfferingChild1 = new hed__Course_Offering__c(Parent_Course__c =cOffering.Id,hed__Course__c = course.id,Name = 'Test Child2',hed__Term__c=trm.Id);
        insert cOfferingChild1;
        Contact c = [Select Id, Name from Contact WHERE lastName = 'Student'];
        hed__Application__c app = new hed__Application__c(hed__Application_Status__c = 'Accepted Offer',Course_Offering__c = cOffering.id,hed__Applicant__c = c.id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        hed__Course_Enrollment__c ce = new hed__Course_Enrollment__c(hed__Contact__c = c.Id, hed__Course_Offering__c = cOfferingChild1.Id, Application__c = app.Id, hed__Status__c = 'Complete');

        test.startTest();
        	ceToUpdate.hed__Status__c = 'Complete';
        	update ceToUpdate;
        	insert ce;
        test.stopTest();
    }
}