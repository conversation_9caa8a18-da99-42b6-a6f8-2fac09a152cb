public with sharing class ImportAttendeesController {
    @AuraEnabled
    public static List<String> getCountriesValues() {
        List<String> industryValues = new List<String>();

        List<Schema.PicklistEntry> entries = evt__Attendee__c.Industry__c.getDescribe().getPickListValues();
        for (Schema.PicklistEntry entry : entries) {
            if (entry.isActive()) {
                industryValues.add(entry.getLabel());
            }
        }
        return industryValues;
    }

    @AuraEnabled
    public static List<evt__Event_Fee__c> getEventTickets(String eventId) {
        List<evt__Event_Fee__c> eventTickets = new List<evt__Event_Fee__c>();

        eventTickets = [SELECT Name, Id, Mailing_Enabled__c, Fields_Recruitment_Admissions__c FROM evt__Event_Fee__c WHERE evt__Event__c = :eventId AND evt__Active__c = true];

        return eventTickets;
    }

    @AuraEnabled
    public static List<Program_Term_Availability__c> getProgramNameAndTermName() {
        List<Program_Term_Availability__c> programNameAndTermName =  [SELECT Program__r.Name , Term__r.Name FROM Program_Term_Availability__c WHERE Active__c = true ORDER BY Program__r.Name];

        return programNameAndTermName;
    }

    @AuraEnabled
    public static String importAttendees(String eventId, String eventFee, List<evt__Attendee__c> attendeesData, String mapData) {
        String res = 'Success';
        System.debug('01 attendeesData===> ' + attendeesData);
        System.debug('02 mapData===> ' + mapData);

        List<Object> deserialisedList = (List<Object>)JSON.deserializeUntyped(mapData);
        Map<String, String> mapSubscription = new Map<String, String>();
        for (Object item : deserialisedList) {
            List<Object> mapList = (List<Object>)item;
            String myEmail = (String)mapList[0];
            myEmail = myEmail.toLowerCase();
            mapSubscription.put(myEmail, (String)mapList[1]);
        }
        //System.debug('03 mapSubscription===> ' + mapSubscription);

        String emailAddress = '';
        String ticketName = '';
        List<evt__Attendee__c> lstAttendeesInsert = new List<evt__Attendee__c>();
        List<evt__Attendee__c> lstAttendeesUpdate = new List<evt__Attendee__c>();
        List<evt__Attendee__c> lstAttendees = new List<evt__Attendee__c>();
        if (eventFee != null) {
            ticketName = [SELECT Name FROM evt__Event_Fee__c WHERE Id = :eventFee].Name;
        }

        Integer i = 0;
        for (evt__Attendee__c attendee : attendeesData) {
            emailAddress = attendee?.evt__Reg_Email__c;
            attendee.evt__Event__c = eventId;
            attendee.evt__Event_Fee__c = eventFee;
            attendee.evt__Registration_Type__c = ticketName + ' Admission';
            attendee.evt__Category__c = 'Attendee';
            attendee.evt__Invitation_Status__c = 'Registered';
            //attendee.evt__Attended__c = false;

            lstAttendees.add(attendee);
        }

        //System.debug('02 lstAttendees===> ' + lstAttendees);
        Set<String> uniqueEmails = new Set<String>();
        Map<String, evt__Attendee__c> emailToAttendeeMap = new Map<String, evt__Attendee__c>();
        for (evt__Attendee__c attendee : lstAttendees) {
            emailAddress = attendee?.evt__Reg_Email__c;
            if (emailAddress != null && !uniqueEmails.contains(emailAddress)) {
                uniqueEmails.add(emailAddress);
                emailToAttendeeMap.put(emailAddress, attendee);
            }
        }

        //System.debug('03 emailToAttendeeMap===> ' + emailToAttendeeMap);

        Map<String, evt__Attendee__c> existingAttendees = new Map<String, evt__Attendee__c>();
        List<evt__Attendee__c> existingAttendeesList = [SELECT Id, evt__Contact__c, evt__Reg_Email__c FROM evt__Attendee__c WHERE evt__Reg_Email__c IN :uniqueEmails AND evt__Event__c = :eventId AND evt__Event_Fee__c = :eventFee];
        for (evt__Attendee__c attendee : existingAttendeesList) {
            existingAttendees.put(attendee.evt__Reg_Email__c, attendee);
        }

        for (evt__Attendee__c attendee : lstAttendees) {
            emailAddress = attendee?.evt__Reg_Email__c;
            if (emailAddress != null && uniqueEmails.contains(emailAddress)) {
                attendee.evt__Event__c = eventId;
                attendee.evt__Event_Fee__c = eventFee;
                if (existingAttendees.containsKey(emailAddress)) {
                    evt__Attendee__c existingAttendee = existingAttendees.get(emailAddress);
                    attendee.Id = existingAttendee.Id;
                    attendee.evt__Contact__c = existingAttendee.evt__Contact__c;
                    lstAttendeesUpdate.add(attendee);
                } else {
                    lstAttendeesInsert.add(attendee);
                }
            }
        }

        System.debug('04-0=0-1 lstAttendeesInsert===> ' + lstAttendeesInsert);
        System.debug('05-0=0-1 lstAttendeesUpdate===> ' + lstAttendeesUpdate);

        ContactCreationBatch batch2 = new ContactCreationBatch(lstAttendeesInsert, mapSubscription);
        Database.executeBatch(batch2, 20);

        //AttendeesImportBatch batch1 = new AttendeesImportBatch(lstAttendeesUpdate, mapSubscription);
        //Database.executeBatch(batch1, 20);

        //SubscriptionCreationBatch batch3 = new SubscriptionCreationBatch(mapSubscription);
        //Database.executeBatch(batch3, 20);

        return res;
    }

    public static String capitalizeFirstLetter(String input) {
        if (input == null || input.length() == 0) {
            return input;
        }

        if (input == 'Canada' || input == 'CA' || input == 'CANADA') {
            input = input.replaceAll('\\s+','');
            String firstChar = input.substring(0, 1).toUpperCase();
            String remainingChars = input.substring(1).toLowerCase();
            System.debug('01 Canada Country===> ' + (firstChar + remainingChars));
            return firstChar + remainingChars;
        }

        System.debug('02 input===> ' + input);
        List<String> words = input.split(' ');

        for (Integer i = 0; i < words.size(); i++) {
            String word = words[i];
            if (!String.isEmpty(word)) {
                words[i] = word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase();
            }
        }

        System.debug('03 words===> ' + String.join(words, ' '));
        return String.join(words, ' ');
    }
}