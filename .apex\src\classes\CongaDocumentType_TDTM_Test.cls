/**
* @description    Test class for CongaDocumentType_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-07-27
* @modified 2020-07-27
*/
@isTest  
public class CongaDocumentType_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for CongaDocumentType_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('CongaDocumentType_TDTM', 'ContentDocumentLink', 'AfterInsert', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);

        //Test Student record: 
        Contact c =  (Contact) TestFactory.createSObject(new Contact()); 
         //Inserting contact will create admin account 
        insert c;
    }

    /**
     * @description insert contentDocumentLink with name containing $DT_
     * Assert that document type was updated: 
     */
    @isTest 
    public static void test_DocumentTypeUpdate(){
        Contact c = [SELECT ID FROM Contact LIMIT 1];
        
        hed__Application__c app = new hed__Application__c();
        app.hed__Applicant__c = c.Id;
        insert app;
        
        ContentVersion cv1 = new ContentVersion(
                Title = '$DT_Application.pdf',
                PathOnClient = 'Test1.jpg',
                VersionData = Blob.valueOf('Test Content Data'),
                IsMajorVersion = true
        );

        insert cv1; 

        ContentDocument document = [SELECT Id, Title, LatestPublishedVersionId FROM ContentDocument LIMIT 1];
    

        ContentDocumentLink cdl1 = New ContentDocumentLink();
        cdl1.LinkedEntityId = app.Id;
        cdl1.ContentDocumentId = document.Id;
        cdl1.shareType = 'V';
        cdl1.Visibility = 'AllUsers';

        insert cdl1; 

        ContentVersion cvAfter = [SELECT ID, DOCUMENT_TYPE__C FROM ContentVersion WHERE Id = :cv1.Id]; 

        System.assert(cvAfter.Document_Type__c == 'Application'); 
    }
}