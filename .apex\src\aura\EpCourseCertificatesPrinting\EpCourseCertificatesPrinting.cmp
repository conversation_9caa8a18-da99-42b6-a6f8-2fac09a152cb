<aura:component controller="EpCourseCertificatesPrintingController" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global" >
    <aura:attribute name="showSuccessMessage" type="String"/>
    <aura:attribute name="showSuccess" type="Boolean"/>
    <aura:if isTrue="{!!v.showSuccess}">
        <lightning:layout horizontalAlign="space" verticalAlign="center" multipleRows="true">
            <div class="slds-notify__content">
                <p class="slds-align_absolute-center">
                    <lightning:icon iconName="action:info" alternativeText="Info" title="Info" size="small" />
                    <div class="slds-text-heading_medium slds-p-left_x-small">
                     	Click continue to print certificates
                    </div>
                </p>
            </div>
            <lightning:button label="Continue" onclick="{! c.printCertificates }"/>
        </lightning:layout>
    </aura:if>
    <aura:if isTrue="{!v.showSuccess}">
        <!-- Show the message to ui -->
        <div class="slds-notify slds-notify_toast slds-theme_success">
            <span class="slds-assistive-text">success</span>
            <div class="slds-notify__content">
                <h5 class="slds-text-heading_small slds-align_absolute-center">Success Message </h5>
                <br/>
                <p class="slds-align_absolute-center">
                    {!v.showSuccessMessage}
                </p> 
            </div>
        </div>
     </aura:if>
</aura:component>