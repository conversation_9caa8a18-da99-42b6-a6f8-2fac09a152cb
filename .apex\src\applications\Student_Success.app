<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Event_Session_Record_Page_old</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>evt__Session__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Event_Session_Record_Page_old</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>evt__Session__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Special_Event_Record_Page11</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Special_Event_Record_Page11</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Student_Appointment_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>sfal__Appointment__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Student_Appointment_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>sfal__Appointment__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Discount_old</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Discount__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Discount_old</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Discount__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Rotman_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Case</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Rotman_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Case</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Student_Success_Contact_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Contact</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Student_Success_Contact_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Contact</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <content>Student_Success_Home_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#41C3DC</headerColor>
        <logo>Rotman_R_Icon_White_w_Blue_BG_128x2</logo>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>App for managing Student Records, appointments, and requests.  Used by the Registrar&apos;s Office, Office of Student Engagement, and Program Services.</description>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>true</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Student Success</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Student_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.StudentRecord</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Academic_Accommodation_Request_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Academic_Accommodation</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Applicant Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Applicant Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Applicant Community Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Applicant Community Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Applicant Community - Lightning Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Applicant Community - Lightning Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Pre_Screening_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Application_Pre_Screening</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Active Student Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Active Student Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Advancement</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Advisee</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Career Services</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Executive</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Executive Programs Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Recruitment and Admissions</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Rotman Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Strategic Communications</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Student Life</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Student Success</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Coffee_Chat</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Event_with_Sessions_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Event_with_Sessions</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Conference</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Concert</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Quick_Event</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Seminar</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Standard_Event</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>ITS Service Desk</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Standard_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>evt__Special_Event__c</pageOrSobjectType>
        <recordType>evt__Special_Event__c.evt__Web_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Select Events Profile</profile>
    </profileActionOverrides>
    <setupExperience>all</setupExperience>
    <tabs>standard-home</tabs>
    <tabs>Student_Records</tabs>
    <tabs>Cases</tabs>
    <tabs>Academic_Accommodation_Requests</tabs>
    <tabs>standard-Event</tabs>
    <tabs>standard-report</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>Student_Success_UtilityBar</utilityBar>
</CustomApplication>
