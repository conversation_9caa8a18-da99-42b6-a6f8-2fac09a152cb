/**
* 
* Inbound email handler that is connected to an email service that accepts emails from allowed addresses(s)
* and takes the info from that info and creates an Interaction. Checks if there is a related existing Contact 
* with the same from email Address. If True attaches the interaction to the Contact with the related email information.
* Otherwise is just creates the interaction with a connected Contact and puts Process Status into 'Draft' phase.
*
*
* See <PERSON><PERSON><PERSON> ticket 'UTR-356'
*
*
* Helpful reference link: https://help.salesforce.com/articleView?id=code_inbound_email.htm&type=5
*
* <AUTHOR> 
* @since   2020-03-05 
*/
global class InboundEmailReciever implements Messaging.InboundEmailHandler {
	global Messaging.InboundEmailResult handleInboundEmail(Messaging.InboundEmail email, Messaging.InboundEnvelope envelope) {
        
	// Create an InboundEmailResult object for returning the result of the Apex Email Service
    Messaging.InboundEmailResult result = new Messaging.InboundEmailResult();
    Id messageTypeId = Schema.SObjectType.Staging_Table__c.getRecordTypeInfosByDeveloperName().get('Message').getRecordTypeId();
        
    // Build a Dynamic Query String.
     List<Staging_Table__c> template = Database.query(' SELECT ' + string.join(new List<String>(Schema.SObjectType.Staging_Table__c.fields.getMap().keySet()), ',') + ' FROM Staging_Table__c Where RecordType.DeveloperName = \'Template\' AND Template_Type__c = \'Inbound Email\' AND Inbound_Email_Address__c = \'Example\' LIMIT 1');
        
    Staging_Table__c interaction;
    //If template is found, clone template
    if (template.size() > 0) {
        interaction = template[0].clone(false, false, false, false);
        interaction.Message_Body__c    = email.htmlBody;
        interaction.Message_Subject__c = email.subject;
        interaction.Personal_Email__c  = email.fromAddress;
        interaction.RecordTypeId       = messageTypeId;
        interaction.Type__c			   = 'Email';
        
    //Otherwise, create new
    } else {
        interaction = new Staging_Table__c(
            Message_Body__c    = email.htmlBody,
            Message_Subject__c = email.subject,
            Personal_Email__c  = email.fromAddress,
            RecordTypeId       = messageTypeId,
            Type__c			   = 'Email'
        );  
        
    }
   
	
    List<Contact> matchingCon = [SELECT Id, FirstName, LastName, Email, hed__UniversityEmail__c, hed__WorkEmail__c, hed__AlternateEmail__c
                                  FROM Contact
                                  WHERE hed__UniversityEmail__c = :email.fromAddress OR hed__WorkEmail__c = :email.fromAddress OR hed__AlternateEmail__c = :email.fromAddress
                                  LIMIT 1];
        
		//If the contact is known, fill in the Contact information    
        if ( matchingCon.size() > 0 ) {
            interaction.First_Name__c = matchingCon[0].FirstName;
            interaction.Last_Name__c = matchingCon[0].LastName;
            interaction.Contact__c = matchingCon[0].Id;
            interaction.Processing_Status__c = 'Unprocessed';

        //If the contact is unknown, create email as a draft
        } else {
            interaction.Processing_Status__c = 'Draft';
            
        }

            
   // Insert the new interaction 
   insert interaction;    
     
   // Set the result to true. No need to send an email back to the user 
   result.success = true;
   
   // Return the result for the Apex Email Service
   return result;
  }
    
}