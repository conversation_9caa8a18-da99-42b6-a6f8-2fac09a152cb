/**
 * @description EpCourse Certificates Printing functionality testing
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-17
 * @modified 2020-08-17
 */
@isTest
public class EpCourseCertificatesPrintingTest {

	@testSetup
    static void testSetup (){
        RecordType EPRT = [Select Id, Name from RecordType where SObjectType = 'hed__Course__c' AND Name = 'EP Course'];
		//Create an account test records
		Account a = (Account) TestFactory.createSObject(new Account());
        insert a;
        //Creating course and Term to create Course Offering
		hed__Course__c course = new hed__Course__c(RecordTypeId = EPRT.Id, Name = 'Test Course1', hed__Account__c = a.Id);
        insert course;
        hed__Term__c t = new hed__Term__c(Name = 'Test Term1', hed__Account__c = a.Id);
        insert t;
        
        hed__Course_Offering__c cOffering = new hed__Course_Offering__c(Name = 'Test CO1', hed__Course__c = course.Id, hed__Term__c = t.Id, hed__Start_Date__c = Date.newInstance(2025,1,1), hed__End_Date__c = Date.newInstance(2025,1,15));
        insert cOffering;

        
        //Inserting contact
        Contact c = (Contact) TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student',email='<EMAIL>')); 
        insert c; 

        //create an attachment
        Attachment att = new Attachment(Name='Test Attachment', Body=blob.valueof('This is a long text for testing purpose.'), ContentType='text', ParentId=c.Id);
        insert att;
        
        hed__Course_Enrollment__c cenrollment = new hed__Course_Enrollment__c(hed__Contact__c=c.id,hed__Course_Offering__c=cOffering.id);
        insert cenrollment;
        Test.startTest();
        	Test.setMock(HttpCalloutMock.class, new CongaMergeMock());
		Test.stopTest();

        //Create a ContentDocument and link it to Application
        Map<String, Object> cvMap1 = new Map<String, Object>{
            'title'=>'Humpty Dumpty', 
            'PathOnClient' => '/HumptyDumpty.pdf', 
            'origin'  => 'H', 
            'ContentLocation' => 'S', 
            'versiondata' => blob.valueof('Humpty Dumpty Sat on a Wall') 
        };
        ContentVersion cv = (ContentVersion) TestFactory.createSObject(new ContentVersion(), cvMap1);
        insert cv;
        ContentVersion cv1 = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv.Id];
        ContentDocumentLink cdl = new ContentDocumentLink(ContentDocumentId=cv1.ContentDocumentId, LinkedEntityId=cenrollment.Id);
        insert cdl;
    }
    /**
     * @description Print certificates 
     * @return Void 
     * @param  CourseOffering id
     */
	@isTest
    static void runApexJobTest(){
        hed__Course_Offering__c coff = [Select Id from hed__Course_Offering__c LIMIT 1];
        Test.startTest();
        Test.setMock(HttpCalloutMock.class, new CongaMergeMock());
        EpCourseCertificatesPrintingController.printCourseCertificates(coff.Id);
		Test.stopTest();        
    }
}