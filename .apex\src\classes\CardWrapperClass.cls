public class CardWrapperClass {
    @AuraEnabled
    public string cardNo{get; set;}
    @AuraEnabled
    public string cardHolderFname{get; set;}
    
    @AuraEnabled public string cardHolderLname{get; set;}
    @AuraEnabled public string CVD{get; set;}
    @AuraEnabled public Decimal Amount{get; set;}
    @AuraEnabled public string expireDate{get; set;}
    @AuraEnabled public string expireMonth{get; set;}
    @AuraEnabled public string expireYear{get; set;}
    @AuraEnabled public string tnx{get; set;}
    @AuraEnabled public string orderid{get; set;}
    @AuraEnabled public pymt__PaymentX__c pymt{get; set;}
    @AuraEnabled public LIST<pymt__Shopping_Cart_Item__c> cartItem{get; set;}
}