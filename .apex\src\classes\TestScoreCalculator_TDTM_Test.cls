/**
* @description    Test class for TestScoreCalculator_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-03-10
* @modified 2020-03-10
*/
@isTest
public class TestScoreCalculator_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for TestScoreCalculator_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('TestScoreCalculator_TDTM', 'Test_Score__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Create test contact: 
        Contact c = new Contact(FirstName = 'Test', LastName = 'Student'); 
        insert c; 
        
        //List of test records to insert: 
        List<hed__Test__c> testRecordsToInsert = new List<hed__Test__c>(); 
        //GRE record (unofficial) with related children test score records: 
        hed__Test__c greUnofficalRecord = new hed__Test__c(hed__Contact__c = c.Id, hed__Test_Type__c = 'GRE', hed__Test_Date__c = Date.newInstance(2020, 2, 1)); 
        //Official TOEFL record: 
        hed__Test__c toeflOfficalRecord = new hed__Test__c(hed__Contact__c = c.Id, hed__Test_Type__c = 'TOEFL', hed__Test_Date__c = Date.newInstance(2020, 2, 1), hed__Source__c = 'Official', Composite_Score__c = 110); 
        testRecordsToInsert.add(greUnofficalRecord); 
        testRecordsToInsert.add(toeflOfficalRecord); 
        testRecordsToInsert.add(new hed__Test__c(hed__Contact__c = c.Id, hed__Test_Type__c = 'GRE', hed__Test_Date__c = Date.newInstance(2020, 1, 1))); 
        testRecordsToInsert.add(new hed__Test__c(hed__Contact__c = c.Id, hed__Test_Type__c = 'GMAT', hed__Test_Date__c = Date.newInstance(2020, 1, 1), Composite_Score__c = 700)); 
        testRecordsToInsert.add(new hed__Test__c(hed__Contact__c = c.Id, hed__Test_Type__c = 'TOEFL', hed__Test_Date__c = Date.newInstance(2020, 1, 1))); 
        insert testRecordsToInsert; 

        //Insert Test Score Records 
        List<hed__Test_Score__c> testScoresToInsert = new List<hed__Test_Score__c>(); 
        //GRE test score records related to unofficial GRE test record: 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = greUnofficalRecord.Id, hed__Subject_Area__c = 'Analytical Writing', Source__c = 'Self Reported', hed__Score__c = 5)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = greUnofficalRecord.Id, hed__Subject_Area__c = 'Verbal Reasoning', Source__c = 'Self Reported', hed__Score__c = 164)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = greUnofficalRecord.Id, hed__Subject_Area__c = 'Quantitative Reasoning', Source__c = 'Self Reported', hed__Score__c = 161)); 
        insert testScoresToInsert; 
    }

    /**
     * @description test GRE Calculations
     * Assert that composite score was updated correctly for GRE (GRE Total Score = Verbal Reasoning + Quantitative Reasoning)
     * Assert that the test record's source was not updated 
     */
    @isTest 
    public static void testGREScores(){
        //Query for a GRE test score record: 
        hed__Test_Score__c greScore = [SELECT ID, hed__Test__c FROM hed__Test_Score__c WHERE hed__Test_Type__c = 'GRE' LIMIT 1]; 
        //Query for GRE test record that does not have any related test scores: 
        hed__Test__c gre = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c 
                            WHERE hed__Test_Type__c = 'GRE' 
                            AND ID != :greScore.hed__Test__c LIMIT 1];
                            
        //Score values: 
        Decimal verbalReasoning = 164; 
        Decimal quantitativeReasoning = 161; 
        Decimal analyticalWriting = 4.5; 
        
        List<hed__Test_Score__c> testScoresToInsert = new List<hed__Test_Score__c>(); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gre.Id, hed__Subject_Area__c = 'Analytical Writing', Source__c = 'Self Reported', hed__Score__c = analyticalWriting)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gre.Id, hed__Subject_Area__c = 'Verbal Reasoning', Source__c = 'Self Reported', hed__Score__c = verbalReasoning)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gre.Id, hed__Subject_Area__c = 'Quantitative Reasoning', Source__c = 'Self Reported', hed__Score__c = quantitativeReasoning)); 

        Test.startTest(); 
            insert testScoresToInsert; 
        Test.stopTest(); 

        //Query for updated test record: 
        hed__Test__c greAfter = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c
                                    WHERE Id = :gre.id]; 
        
        //Assert that composite score is updated correctly: 
        System.assertNotEquals(gre.Composite_Score__c, greAfter.Composite_Score__c); 
        Decimal expectedGREScore = verbalReasoning + quantitativeReasoning; 
        System.assertEquals(expectedGREScore, greAfter.Composite_Score__c); 

        //Assert that source on the test record was not updated: 
        System.assertEquals(gre.hed__Source__c, greAfter.hed__Source__c); 
    }

    /**
     * @description test TOEFL Calculations
     * Assert that composite score was updated correctly for TOEFL
     * TOEFL total score = Listening + Reading + Speaking + Writing 
     * Assert that the test record's source was not updated 
     */
    @isTest 
    public static void testTOEFLScores(){
        //Query for Unofficial TOEFL test record: 
        hed__Test__c toefl = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c 
                            WHERE hed__Test_Type__c = 'TOEFL' 
                            AND hed__Source__c != 'Official' 
                            LIMIT 1];
                            
        //Score values: 
        Decimal reading = 22; 
        Decimal listening = 23; 
        Decimal writing = 20;
        Decimal speaking = 21;  
        
        List<hed__Test_Score__c> testScoresToInsert = new List<hed__Test_Score__c>(); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Reading', Source__c = 'Self Reported', hed__Score__c = reading)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Listening', Source__c = 'Self Reported', hed__Score__c = listening)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Writing', Source__c = 'Self Reported', hed__Score__c = writing)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Speaking', Source__c = 'Self Reported', hed__Score__c = speaking)); 

        Test.startTest(); 
            insert testScoresToInsert; 
        Test.stopTest(); 

        //Query for updated test record: 
        hed__Test__c toeflAfter = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c
                                    WHERE Id = :toefl.id]; 
        
        //Assert that composite score is updated correctly: 
        System.assertNotEquals(toefl.Composite_Score__c, toeflAfter.Composite_Score__c); 
        Decimal expectedTOEFLScore = reading + listening + writing + speaking; 
        System.assertEquals(expectedTOEFLScore, toeflAfter.Composite_Score__c); 

        //Assert that source on the test record was not updated: 
        System.assertEquals(toefl.hed__Source__c, toeflAfter.hed__Source__c); 
    }

    /**
     * @description test GMAT Calculations (GMAT test scores will be manually inputted)
     * Assert that composite score was not updated after test score records are inserted 
     * Assert that the test record's source was not updated 
     */
    @isTest 
    public static void testGMATScores(){
        //Query for GMAT test record: 
        hed__Test__c gmat = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c 
                            WHERE hed__Test_Type__c = 'GMAT' LIMIT 1];
                            
        //Score values: 
        Decimal verbal = 41; 
        Decimal quantitative = 43; 
        Decimal ir = 7;
        Decimal analyticalWriting = 5;  
        
        List<hed__Test_Score__c> testScoresToInsert = new List<hed__Test_Score__c>(); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Quantitative Reasoning', Source__c = 'Self Reported', hed__Score__c = quantitative)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Verbal Reasoning', Source__c = 'Self Reported', hed__Score__c = verbal)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Integrated Reasoning', Source__c = 'Self Reported', hed__Score__c = ir)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Analytical Writing Assessment', Source__c = 'Self Reported', hed__Score__c = analyticalWriting)); 

        Test.startTest(); 
            insert testScoresToInsert; 
        Test.stopTest(); 

        //Query for updated test record: 
        hed__Test__c gmatAfter = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c
                                    WHERE Id = :gmat.id]; 
        
        //Assert that composite score did not update, as GMAT composite scores will be inputted by user or integrations: 
        System.assertEquals(gmat.Composite_Score__c, gmat.Composite_Score__c);  

        //Assert that source on the test record was not updated: 
        System.assertEquals(gmat.hed__Source__c, gmatAfter.hed__Source__c); 
    }
    /**
     * @description test update source to "Official" 
     * Assert that when all test score records are inserted as "Official" scores, 
     * the parent test score record's source updates to "Official" 
     */
    @isTest
    public static void testInsertOfficialScores(){
        //Query for GMAT test record: 
        hed__Test__c gmat = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c 
                                WHERE hed__Test_Type__c = 'GMAT'
                                AND hed__Source__c != 'Official' 
                                LIMIT 1];
         
        //Score values: 
        Decimal verbal = 41; 
        Decimal quantitative = 43; 
        Decimal ir = 7;
        Decimal analyticalWriting = 5;  

        //Insert official test scores 
        List<hed__Test_Score__c> testScoresToInsert = new List<hed__Test_Score__c>(); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Quantitative Reasoning', Source__c = 'Official', hed__Score__c = quantitative)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Verbal Reasoning', Source__c = 'Official', hed__Score__c = verbal)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Integrated Reasoning', Source__c = 'Official', hed__Score__c = ir)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = gmat.Id, hed__Subject_Area__c = 'Analytical Writing Assessment', Source__c = 'Official', hed__Score__c = analyticalWriting)); 

        Test.startTest(); 
            insert testScoresToInsert; 
        Test.stopTest(); 

        //Query for updated test record: 
        hed__Test__c gmatAfter = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c
                        WHERE Id = :gmat.id]; 

        //Assert that composite score did not update, as GMAT composite scores will be inputted by user or integrations: 
        System.assertEquals(gmat.Composite_Score__c, gmat.Composite_Score__c);  

        //Assert that source on the test record was updated to Official: 
        System.assertNotEquals(gmat.hed__Source__c, gmatAfter.hed__Source__c); 
        System.assertEquals('Official', gmatAfter.hed__Source__c); 
    }
     /**
     * @description test update source to "Official" 
     * Assert that when all test score records are updated to "Official" scores, 
     * the parent test score record's source updates to "Official" 
     */
    @isTest
    public static void testUpdateOfficialScores(){
        //Query for GRE test score records: 
        List<hed__Test_Score__c> greTestScores = [SELECT Id, Source__c, hed__Test__c, hed__Test__r.Composite_Score__c, hed__Test__r.hed__Source__c 
                                            FROM hed__Test_Score__c 
                                            WHERE hed__Test_Type__c = 'GRE']; 
        
        //Update GRE Test Scores to "Official": 
        for(hed__Test_Score__c ts :greTestScores){
            ts.Source__c = 'Official'; 
        }
        
        Test.startTest(); 
            update greTestScores; 
        Test.stopTest(); 

        //Query for updated test record: 
        hed__Test__c greAfter = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c
                        WHERE Id = :greTestScores[0].hed__Test__c]; 


        //Assert that composite score did not update: 
        System.assertEquals(greTestScores[0].hed__Test__r.Composite_Score__c, greAfter.Composite_Score__c);  

        //Assert that source on the test record was updated to Official: 
        System.assertNotEquals(greTestScores[0].hed__Test__r.hed__Source__c, greAfter.hed__Source__c); 
        System.assertEquals('Official', greAfter.hed__Source__c); 
    }
    /**
     * @description test that scores are not recalculated when source = "Official"
     * Assert that composite score is not updated when new test score records
     * related to an Official test record are inserted 
     * Assert that source was not updated
     */
    @isTest 
    public static void testOfficialTOEFLScores(){
        //Query for Official TOEFL test record: 
        hed__Test__c toefl = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c 
                            WHERE hed__Test_Type__c = 'TOEFL' 
                            AND hed__Source__c = 'Official' 
                            LIMIT 1];
                            
        //Score values: 
        Decimal reading = 22; 
        Decimal listening = 23; 
        Decimal writing = 20;
        Decimal speaking = 21;  
        
        //new test score records to insert: 
        List<hed__Test_Score__c> testScoresToInsert = new List<hed__Test_Score__c>(); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Reading', Source__c = 'Self Reported', hed__Score__c = reading)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Listening', Source__c = 'Self Reported', hed__Score__c = listening)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Writing', Source__c = 'Self Reported', hed__Score__c = writing)); 
        testScoresToInsert.add(new hed__Test_Score__c(hed__Test__c = toefl.Id, hed__Subject_Area__c = 'Speaking', Source__c = 'Self Reported', hed__Score__c = speaking)); 

        Test.startTest(); 
            insert testScoresToInsert; 
        Test.stopTest(); 

        //Query for updated test record: 
        hed__Test__c toeflAfter = [SELECT ID, hed__Source__c, Composite_Score__c FROM hed__Test__c
                                    WHERE Id = :toefl.id]; 
        
        //Assert that composite score was not updated: 
        System.assertEquals(toefl.Composite_Score__c, toeflAfter.Composite_Score__c);  

        //Assert that source on the test record was not updated: 
        System.assertEquals(toefl.hed__Source__c, toeflAfter.hed__Source__c); 
    }
}