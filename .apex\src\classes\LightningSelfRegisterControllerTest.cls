/**
* @description    Test class for LightningSelfRegisterController class
* <AUTHOR>
* @version        1.0 
* @created 2020-06-10
* @modified 2020-06-10
*/
@IsTest
public class LightningSelfRegisterControllerTest {
    private static final String DUPLICATE_PHONE = '**********';
    private static final String DUPLICATE_EMAIL = '<EMAIL>'; 
	private static final String contactAddnlFields = '[{\"type\":\"picklist\",\"label\":\"Primary Program of Interest\",\"fieldName\":\"Primary_Program_of_Interest__c\",\"required\":true,\"options\":[{\"label\":\"Graduate Diploma in Professional Accounting\",\"value\":\"Graduate Diploma in Professional Accounting\"},{\"label\":\"Master of Management Analytics\",\"value\":\"Master of Management Analytics\"},{\"label\":\"Global Executive MBA\",\"value\":\"Global Executive MBA\"},{\"label\":\"Morning/Evening MBA (AM)\",\"value\":\"Morning/Evening MBA (AM)\"},{\"label\":\"Morning/Evening MBA (PM)\",\"value\":\"Morning/Evening MBA (PM)\"},{\"label\":\"Full-Time MBA\",\"value\":\"Full-Time MBA\"},{\"label\":\"Master of Financial risk Management\",\"value\":\"Master of Financial risk Management\"},{\"label\":\"Global Executive MBA for Healthcare and Life Sciences\",\"value\":\"Global Executive MBA for Healthcare and Life Sciences\"}],\"isPicklist\":true,\"isText\":false,\"isNumber\":false,\"isBoolean\":false,\"selectedVal\":\"Master of Management Analytics\"},{\"type\":\"checkbox\",\"required\":false,\"label\":\"Sign Up For News Letter\",\"fieldName\":\"Sign_Up_For_News_Letter__c\",\"isPicklist\":false,\"isText\":false,\"isNumber\":false,\"isBoolean\":true}]';
        
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        
        //Create System Admin user
        Profile sysAdminP = [SELECT Id, Name FROM Profile WHERE Name = 'System Administrator' LIMIT 1];
        UserRole userrole = [Select Id, DeveloperName From UserRole Where DeveloperName = 'Site_Administrators' Limit 1];
        //User sysAdminU = new User(alias = 'ttest', email = '<EMAIL>', emailencodingkey = 'UTF-8', firstName = 'System', lastName = 'Admin', userName = '<EMAIL>', 
		//                         profileId = sysAdminP.Id, UserRoleId=userrole.Id, timeZoneSidKey = 'America/Los_Angeles', LocaleSidKey = 'en_US', LanguageLocaleKey = 'en_US', isActive = true);
        //insert sysAdminU;
        User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		adminUser.UserRoleId = userRole.Id;
        update adminUser;
        
        System.runAs(adminUser){
        List<Contact> contactsToInsert = new List<Contact>(); 
        contactsToInsert.add(new Contact(firstName = 'Rose', lastName = 'Tessa', email='<EMAIL>', phone = '1231231231')); 
        contactsToInsert.add(new Contact(firstName = 'Bob', lastName = 'Jone', email = '<EMAIL>', phone = '3453453453')); 
        contactsToInsert.add(new Contact(firstName = 'Nina', lastName = 'Jazz', email = '<EMAIL>', phone = '6786786786')); 
        contactsToInsert.add(new Contact(firstName = 'Aiden', lastName = 'Penn', email = '<EMAIL>', phone = '8768768768')); 
        contactsToInsert.add(new Contact(firstName = 'Mildred', lastName = 'Storm', email = '<EMAIL>', phone = DUPLICATE_PHONE)); 
        contactsToInsert.add(new Contact(firstName = 'Mildre', lastName = 'Storm', email = '<EMAIL>', phone = DUPLICATE_PHONE)); 
        insert contactsToInsert; 
        
        //Query for community profiles 
        List<Profile> communityProfiles = [SELECT Id, Name FROM Profile WHERE Name = 'Applicant Community User' OR Name = 'Student Community User'];
        List<User> usersToInsert = new List<User>(); 
        usersToInsert.add(new User( alias = 'rtess', 
                                    email = '<EMAIL>', 
                                    emailencodingkey = 'UTF-8', 
                                    firstName = 'Rose',
                                    lastName = 'Tessa', 
                                    userName = '<EMAIL>', 
                                    profileId = communityProfiles[0].Id, 
                                    timeZoneSidKey = 'America/Los_Angeles', 
                                    LocaleSidKey = 'en_US', 
                                    LanguageLocaleKey = 'en_US', 
                                    isActive = true, 
                                    contactId = contactsToInsert[0].Id)); 

        usersToInsert.add(new User( alias = 'bJone', 
                                    email = '<EMAIL>', 
                                    emailencodingkey = 'UTF-8', 
                                    firstName = 'Bob',
                                    lastName = 'Jone', 
                                    userName = '<EMAIL>', 
                                    profileId = communityProfiles[1].Id, 
                                    timeZoneSidKey = 'America/Los_Angeles', 
                                    LocaleSidKey = 'en_US', 
                                    FederationIdentifier ='fedid123',
                                    LanguageLocaleKey = 'en_US', 
                                    isActive = true, 
                                    contactId = contactsToInsert[1].Id));
        
        usersToInsert.add(new User( alias = 'nJazz', 
                                    email = '<EMAIL>', 
                                    emailencodingkey = 'UTF-8', 
                                    firstName = 'Nina',
                                    lastName = 'Jazz', 
                                    userName = '<EMAIL>', 
                                    profileId = communityProfiles[1].Id, 
                                    timeZoneSidKey = 'America/Los_Angeles', 
                                    LocaleSidKey = 'en_US', 
                                    FederationIdentifier ='fedid123',
                                    LanguageLocaleKey = 'en_US', 
                                    isActive = false, 
                                    contactId = contactsToInsert[2].Id));
        
        insert usersToInsert; 
        Store_Configuration__c sc = new Store_Configuration__c (
            Name = 'EP Courses'
        );
		insert sc;
		String cookieId = '2398410734';
        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c(
            Cart_Key__c = cookieId + '.' + sc.Id,
            Store_Configuration__c = sc.Id
        );
        insert cart;
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c(
            pymt__Shopping_Cart__c = cart.Id
        );
        insert cartItem;
        }
        
    }

    /**
     * @description Assert that when a new user who does not have an existing contact record or user record
     * registers, the page will redirect to the 'Check Password Reset Email' page 
     */
    @IsTest  
    public static void testNewUser(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
        Test.startTest(); 
            String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 
            LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
        Test.stopTest(); 
        
        //Assert that the Controller redirects to the correct page: 
        system.debug(response); 
        System.assert(response.redirectURL == '');   
    }

    /**
     * @description Assert that when a new user who has an active user record (with no federation ID) registers, 
     * the page will redirect to the 'Forgot Password' page 
     */
    @isTest
    public static void testActiveUser_noFedId(){
        List<Contact> testConts = [SELECT ID FROM Contact]; 
        User testUser = [SELECT ID, firstName, lastName, email, Contact.phone FROM User WHERE isActive = true AND federationIdentifier = null AND ContactId IN :testConts LIMIT 1];
        System.debug(testUser);  

        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
                String uwJSON = '{"firstName": "' + testUser.FirstName + '", "lastName": "' + testUser.lastName + '", "email": "' + testUser.email 
                                    +'", "phone": "'+ testUser.Contact.phone + '", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 

                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            //Assert that the controller redirects to the correct page: 
            System.assertEquals(false, response.hasError); 
            System.debug(response); 
            System.assert(response.redirectURL.contains(LightningSelfRegisterController.FORGOTPASSWORD)); 
    }

    /**
     * @description Assert that when a new user who has an active user record (with federation ID) registers, 
     * the page will redirect to the SSO page
     */
    @IsTest
    public static void testActiveUser_wFedId(){
        List<Contact> testConts = [SELECT ID FROM Contact]; 
        User testUser = [SELECT ID, firstName, lastName, email, Contact.phone FROM User WHERE isActive = true AND federationIdentifier != null AND ContactId IN :testConts LIMIT 1]; 
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
            String uwJSON = '{"firstName": "' + testUser.FirstName + '", "lastName": "' + testUser.lastName + '", "email": "' + testUser.email 
                                    +'", "phone": "'+ testUser.Contact.phone + '", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}';  

                LightningSelfRegisterController.Response  response = LightningSelfRegisterController.selfRegister(uwjSON);
            Test.stopTest(); 

            //Assert that the controller redirects to the correct page: 
            System.assertEquals(false, response.hasError); 
            System.assert(response.redirectURL == (Label.Community_SSO)); 
    }

    /**
     * @description Assert that when a new user who has an inactive user record registers, 
     * the page will redirect to the Forgot Password page
     */
    @isTest
    public static void testInactiveUser(){
        List<Contact> testConts = [SELECT ID FROM Contact]; 
        User testUser = [SELECT ID, firstName, lastName, email, Contact.phone FROM User WHERE isActive = false AND ContactId IN :testConts LIMIT 1];

        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
            String uwJSON = '{"firstName": "' + testUser.FirstName + '", "lastName": "' + testUser.lastName + '", "email": "' + testUser.email 
                                    +'", "phone": "'+ testUser.Contact.phone + '", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 
                LightningSelfRegisterController.Response  response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            User testUserAfter = [SELECT ID, isActive FROM User WHERE Id = :testUser.Id]; 
    
            //Assert that the controller redirects to the correct page: 
            System.assertEquals(true, response.hasError);
            //System.assert(response.redirectURL.contains(LightningSelfRegisterController.FORGOTPASSWORD)); 
    
            //Assert that the user was activated: 
            //System.assert(testUserAfter.isActive == true, 'User was not activated'); 
    }

    /**
     * @description Assert that when a new user who has an existing contact record but no user record registers, 
     * the page will redirect to the "Check Password" page
     */
    @isTest  
    public static void testExistingContact_NoUserRec(){
        Contact testContact = [SELECT ID, FirstName, LastName, Email, Phone FROM Contact WHERE email = '<EMAIL>' LIMIT 1]; 

        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
            String uwJSON = '{"firstName": "' + testContact.FirstName + '", "lastName": "' + testContact.lastName + '", "email": "' + testContact.email 
                                    +'", "phone": "'+ testContact.phone + '", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 
    
            	LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
        	Test.stopTest(); 

	        //Assert that the controller redirects to the correct page: 
    	    //System.assertEquals(false, response.hasError, response);
    	    //system.assert(response.validationMessage.contains('Invalid id'), response); 
        	System.assert(response.redirectURL == '');
    }

    // /**
    //  * @description Assert that when a new user registers with similar name and same phone number (but different email)
    //  * a list of emails of potential duplicate contacts show up 
    //  */
    // @isTest
    // public static void testDupContacts(){
    //     List<Contact> potentialDups = [SELECT ID, phone, email FROM Contact WHERE Phone = :DUPLICATE_PHONE]; 
		
    //     User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
    //     System.runAs(u){
    //         Test.startTest(); 
    //             LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister('Mildred', 'Storm', '<EMAIL>', DUPLICATE_PHONE, 'testPassword1', 'testPassword1', 'CheckPasswordResetEmail', true, false,contactAddnlFields, '1234'); 
    //         Test.stopTest(); 
     
    //         //Assert duplicate contacts are detected: 
    //         System.assertEquals(false, response.hasError, potentialDups + ' ' + response); 
    //         System.assert(response.relatedDupEmailList.size() == potentialDups.size(), potentialDups + ' ' + response); 
    //         System.assert(response.relatedDupEmailList.size() == response.obsDupEmailList.size()); 
    //         System.assert(String.isBlank(response.redirectURL)); 
    //     }
    // }

    /**
     * @description Assert that when a new user clicks on email options, the page redirects to 
     * "Check Password Reset Email" page 
     */
    //  @isTest
    //  public static void testSelectEmail(){
    //     User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
    //     System.runAs(u){
	//         Test.startTest(); 
    // 	        LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister('Mildred', 'Storm', DUPLICATE_EMAIL, '1231231231', 'testPassword1', 'testPassword1', 'CheckPasswordResetEmail', false, true,contactAddnlFields, '1234');
    //     	Test.stopTest(); 
	//         System.debug(response); 
	
    // 	    //System.assertEquals(false, response.hasError, response); 
    // 	    System.assert(response.validationMessage.contains('Invalid id'), response); 
    //     	System.assert(response.redirectURL.contains('CheckPasswordResetEmail')); 
    //     }
    // }

    /**
    * @description Assert that when a new user clicks on 'None of these emails are mind' options, the page redirects to 
    * "Forgot Password" page
    */
    // @isTest
    // public static void testNoneEmailSelection(){
    //     User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
    //     System.runAs(u){
    //         Test.startTest(); 
    //             LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister('Mildred', 'Storm', '<EMAIL>', DUPLICATE_PHONE, 'testPassword1', 'testPassword1', 'CheckPasswordResetEmail', false, false,contactAddnlFields, '1234'); 
    //         Test.stopTest(); 
    
    //         //System.assertEquals(false, response.hasError); 
    //         //System.assert(response.redirectURL.contains('ForgotPassword'), response); 
    //     }
    // }

    /**
    * @description Assert that when a new user registers without a firstname, 
    an error will be logged in the response
    */
    @isTest  
    public static void validateFirstName(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            String uwJSON = '{"firstName": "", "lastName": "Joe", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 

            Test.startTest(); 
                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 

            System.assert(response.hasError); 
            System.assert(response.validationMessage == Label.Site_FirstName_is_Required); 
    }

    /**
    * @description Assert that when a new user registers without a lastname, 
    an error will be logged in the response
    */
    @isTest  
    public static void validateLastName(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
            String uwJSON = '{"firstName": "Billy", "lastName": "", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 

                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            System.assert(response.hasError); 
            System.assert(response.validationMessage == Label.Site.lastname_is_required); 
    }

    /**
    * @description Assert that when a new user registers without an email, 
    an error will be logged in the response
    */
    @isTest  
    public static void validateEmail(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
                String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 

                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            System.assert(response.hasError); 
            System.assert(response.validationMessage == Label.Site.email_is_required); 
    }

    /**
    * @description Assert that when a new user registers without a phone number, 
    an error will be logged in the response
    */
    @isTest  
    public static void validatePhone(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
                String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "<EMAIL>", "phone": "", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 
 
                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            System.assert(response.hasError); 
            System.assert(response.validationMessage == Label.Site_Phone_is_Required); 
    }

    /**
    * @description Assert that when a new user registers without a password, 
    an error will be logged in the response
    */
    @isTest  
    public static void validatePassword_Null(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
                String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "", "confirmPassword":"testPassword1", "contactAddnlFields":"","cookieId": "1234"}'; 

                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            System.assert(response.hasError); 
            System.assert(response.validationMessage == Label.Site_Password_is_Required);
    }

    /**
    * @description Assert that when a new user registers without a password confirmation, 
    an error will be logged in the response
    */
    @isTest  
    public static void validateConfirmPassword_Null(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
                String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"", "contactAddnlFields":"","cookieId": "1234"}'; 

                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            System.assert(response.hasError); 
            System.assert(response.validationMessage == Label.Site_ConfirmPassword_is_Required); 
    }

    /**
    * @description Assert that when a new user registers with mis-matching passwords, 
    an error will be logged in the response
    */
    @isTest  
    public static void validateMatchingPasswords(){
        //User u = [SELECT Id, Name from User where Profile.Name = 'System Administrator' LIMIT 1];
        //System.runAs(u){
            Test.startTest(); 
                String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword2", "contactAddnlFields":"","cookieId": "1234"}'; 

                LightningSelfRegisterController.Response response = LightningSelfRegisterController.selfRegister(uwJSON);
            Test.stopTest(); 
    
            System.assert(response.hasError); 
            System.assert(response.validationMessage == Label.site.passwords_dont_match); 
    }
    
    @isTest
    public static void testReassignSC(){
        
        String cookieId = '2398410734';
        User u = [SELECT ID, ContactId FROM User WHERE ContactId != null AND isActive = true LIMIT 1];
		test.startTest();
        	LightningSelfRegisterController.reassignShoppingCart(u,cookieId);
        test.stopTest();
    }
    
    @isTest  
    public static void validateRegisterUser(){
        User u = [SELECT ID, ContactId FROM User WHERE ContactId != null AND isActive = true LIMIT 1];
        Test.startTest(); 
        	String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword2", "contactAddnlFields":"","cookieId": "1234"}'; 

        	LightningSelfRegisterController.Response response = LightningSelfRegisterController.registerUser(uwJSON,'test','test',u.ContactId,'test','mbaapp');
        Test.stopTest(); 

    }
    @isTest
    public static void sendVerificationCodetest(){
        Test.startTest();
        String uwJSON = '{"firstName": "Billy", "lastName": "Joe", "email": "<EMAIL>", "phone": "1234567890", "timeZone": "America/New_York", "password": "testPassword1", "confirmPassword":"testPassword2", "contactAddnlFields":"","cookieId": "1234"}';
        LightningSelfRegisterController.sendVerificationCode(uwJSON, '');
        Test.stopTest();        

    }
    
    
   
}