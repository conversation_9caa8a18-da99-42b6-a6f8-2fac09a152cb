//This is necessary to provide a list of available forms to a user when using the Lightning App Builder to an app
global class CartController_ObjList extends VisualEditor.DynamicPickList{
    
    global override VisualEditor.DataRow getDefaultValue(){
        VisualEditor.DataRow defaultValue = new VisualEditor.DataRow('', '--NONE--');
        return defaultValue;
    }
    global override VisualEditor.DynamicPickListRows getValues() {

    	List <Store_SObject_Configuration__c> configs = [SELECT Id, Store_Configuration__r.Name, Name FROM Store_SObject_Configuration__c];
        VisualEditor.DynamicPickListRows  pickVals = new VisualEditor.DynamicPickListRows();

    	for (Store_SObject_Configuration__c f : configs)
	        pickVals.addRow( new VisualEditor.DataRow( f.Store_Configuration__r.Name + ' - ' + f.Name, String.valueOf(f.Id) ) );

        return pickVals;
    }
}