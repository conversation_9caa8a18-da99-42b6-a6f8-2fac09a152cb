({
    doInit : function(component) {
        var action = component.get("c.getPickListValuesIntoList");
        action.setCallback(this, function(response) {
            var list = response.getReturnValue();
            component.set("v.allStatuses", list);
        })
        $A.enqueueAction(action);
    },
    selectoptionvalue: function(component, event, helper) {
        var selected = [], checkboxes = component.find("checkbox");
        if(!checkboxes) {   // Find returns zero values when there's no items
            checkboxes = [];
        } else if(!checkboxes.length) { // Find returns a normal object with one item
            checkboxes = [checkboxes];
        }
        checkboxes
        .filter(checkbox => checkbox.get("v.value"))    // Get only checked boxes
        .forEach(checkbox => selected.push(checkbox.get("v.label")));   // And get the labels
        component.set("v.selectedStatuses", selected);    // Set to display
    },
    callApexJob : function(component, event, helper) {
        var selectedStatuses = component.get("v.selectedStatuses");
        if(selectedStatuses ==''){
            component.set("v.showWarning",true);
            component.set("v.showWarningMessage",'Please select at least one status to go forward.');
        }
        if(selectedStatuses!=''){
            var action = component.get("c.runApexJob");
            action.setParams({ "selectedStatuses" : selectedStatuses,"recordId": component.get("v.recordId")});
            action.setCallback(this, function(response) {
                var state = response.getState();
                if (state == 'SUCCESS') {
                    component.set("v.showWarning",false);
                    component.set("v.showSuccess",true);
                    component.set("v.showSuccessMessage",'Job is in progress and will be ready soon.');
                } 
                else {
                    console.log(state);
                }
            });
            $A.enqueueAction(action);
        }
    }
})