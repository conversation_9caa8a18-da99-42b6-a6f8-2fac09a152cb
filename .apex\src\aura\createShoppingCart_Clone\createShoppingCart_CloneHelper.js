({
    getCartInfo : function(component,callTo) {
        return new Promise(function(resolve, reject) { 
            callTo.setCallback(this, 
                               function(response) {
                                   var state = response.getState();
                                   console.log(state);
                                   console.log(response);
                                   console.log('res=> '+response.getReturnValue());
                                   if (state === "SUCCESS") {
                                       resolve(response.getReturnValue());
                                       
                                       var UserDetail = component.get("c.getUserDetails");
                                       UserDetail.setCallback(this, function(response) {
                                           var state = response.getState();
                                           console.log(state);
                                           if (state === "SUCCESS") {
                                               console.log('UserName: '+response.getReturnValue());
                                               if(response.getReturnValue() =="Events"){
                                                   component.set("v.userName",'Guest\'s');
                                               }
                                               else{
                                                   component.set("v.userName",response.getReturnValue()+'\'s');
                                               }
                                               
                                               
                                           }
                                       });
                                       $A.enqueueAction(UserDetail);
                                   } else {
                                       console.log(response.getError());
                                       reject(new Error(response.getError()));
                                   }
                               }); 
            $A.enqueueAction(callTo);
        });
    },
    setCookie : function(cname, cvalue, exdays) {
        console.log("exdays="+ exdays);
        var d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        console.log("d="+ d);
        var expires = "expires="+d.toUTCString();
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
        console.log("Cookie set-> "+document.cookie); 
    },
    getCookie : function(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(';');
        console.log(ca);
        for(var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                console.log(c.substring(name.length, c.length));
                return c.substring(name.length, c.length);
            }
        }
        return "";
    },
    getUrlParams : function (url,val){
        console.log('helper URL: '+url);
        console.log('helper val: '+val);
        var urlstring = url.split('&');
        console.log('url: '+url);
        for(var item in urlstring){
            
            if(urlstring[item].includes(val)){
                
                var cartId = urlstring[item].substring(7);
                component.set("v.cartId",cartId);
                console.log('cart: '+cartId);
                //return cartId;
                
            }
            if(urlstring[item].includes(val)){
                var recId = urlstring[item].substring(4);
                component.set("v.recordId",recId);
                console.log(recId);
                //return recId;
                
            }
        }
    },
    showToastFun : function(type,title,msg){
        
       if($A.get("e.force:showToast") == undefined )
        {
          alert(msg);
         /*  sforce.one.showToast({
                'type' : type,
                'title' : title,//'Cart Updated!', 
                'message' : msg//"Success" 
            });*/ 
        }
        else {
            
            showToast.setParams({ 
                'type' : type,//"success",  // info, success, warning, error
                'title' : title,//'Cart Updated!', 
                'message' : msg//"Success" 
            }); 
            showToast.fire(); 
        }
    },
})