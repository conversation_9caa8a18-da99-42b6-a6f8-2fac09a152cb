@isTest
public class ShoppingCartFeeService_TDTM_Test {
    
    public static final Map<String, String> expectedFeeBySci = new Map<String, String>{
        'Student Cart Item' =>      'Student Fee',
        'Non-Student Cart Item' =>  'Other Fee'
    };

    @TestSetup
    static void setupTest() {
        
        evt__Special_Event__c evt = new evt__Special_Event__c (
            Name    = 'Special Test Event'
        );

        insert evt;

        insert new List<evt__Event_Fee__c>{ 
            new evt__Event_Fee__c(
                evt__Active__c  = true,
                evt__Event__c   = evt.Id,
                evt__Amount__c  = 2000,
                Name            = 'Student Fee',
                Type__c         = 'Student' 
            ),
            new evt__Event_Fee__c(
                evt__Active__c  = true,
                evt__Event__c   = evt.Id,
                evt__Amount__c  = 3000,
                Name            = 'Other Fee',
                Type__c         = 'Standard' 
            )
        };

        insert new List<Contact>{ 
            new Contact(
                FirstName   = 'Ted',
                LastName    = 'Logan',
                Email       = '<EMAIL>',
                Type__c     = 'Student'
            ),
            new Contact(
                FirstName   = 'Bill',
                LastName    = 'S. Preston',
                Email       = '<EMAIL>'
            )
        };

        insert new List<SObject>{
            new hed__Trigger_Handler__c(
                hed__Active__c              = true,
                hed__Class__c               = 'ShoppingCartFeeService_TDTM', 
                hed__Load_Order__c          = 1, 
                hed__Object__c              = 'pymt__Shopping_Cart_Item__c',
                hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate'
            )
        };

    }

    @isTest
    static void testInsertFeeCarts() {

        List<Contact> cons =                [ SELECT Id FROM Contact ORDER BY FirstName ASC ];
        List<evt__Special_Event__c> evts =  [ SELECT Id FROM evt__Special_Event__c ];

        Test.startTest();
            insert new List<pymt__Shopping_Cart_Item__c>{
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'Student Cart Item',
                    Special_Event__c            = evts[0].Id,
                    pymt__Contact__c            = cons[1].Id,
                    Type__c                     = 'Event Registration'
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'Non-Student Cart Item',
                    Special_Event__c            = evts[0].Id,
                    pymt__Contact__c            = cons[0].Id,
                    Type__c                     = 'Event Registration'
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'Non-Event Cart Item'
                )
            };
        Test.stopTest();

        for ( pymt__Shopping_Cart_Item__c sci : [ SELECT Id, Name, Event_Fee__c, Event_Fee__r.Name FROM pymt__Shopping_Cart_Item__c ] )
            if ( sci.Name == 'Non-Event Cart Item' )
                system.assert( sci.Event_Fee__c == null, 'Non-Event cart item erroneously assigned event fee');
            else {
                system.assert( sci.Event_Fee__c != null, 'Event cart item erroneously not assigned fee');
                system.assert( ShoppingCartFeeService_TDTM_Test.expectedFeeBySci.get(sci.Name) == sci.Event_Fee__r.Name, 'Event cart item erroneously assigned fee wrong fee, got ' + sci.Event_Fee__r.Name + ', expected ' + expectedFeeBySci.get(sci.Name) );
            }


    }


    @isTest
    static void testInsertNoFeesAvail() {

        List<Contact> cons =                [ SELECT Id FROM Contact ORDER BY FirstName ASC ];
        List<evt__Special_Event__c> evts =  [ SELECT Id FROM evt__Special_Event__c ];
        
        delete [ SELECT Id FROM evt__Event_Fee__c WHERE Type__c = 'Standard' ];

        Test.startTest();
            insert new List<pymt__Shopping_Cart_Item__c>{
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'Non-Student Cart Item',
                    Special_Event__c            = evts[0].Id,
                    pymt__Contact__c            = cons[0].Id,
                    Type__c                     = 'Event Registration'
                )
            };
        Test.stopTest();

        system.assert( new List<pymt__Shopping_Cart_Item__c>([ SELECT Id, Name, Event_Fee__c, Event_Fee__r.Name FROM pymt__Shopping_Cart_Item__c ])[0].Event_Fee__c == null, 'Erroneously assigned student fee to non-student' );

    }


    @isTest
    static void testInsertNoConFee() {

        List<Contact> cons =                [ SELECT Id FROM Contact ORDER BY FirstName ASC ];
        List<evt__Special_Event__c> evts =  [ SELECT Id FROM evt__Special_Event__c ];
        
        Test.startTest();
            insert new List<pymt__Shopping_Cart_Item__c>{
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'Contactless Cart Item',
                    Special_Event__c            = evts[0].Id,
                    Type__c                     = 'Event Registration'
                )
            };
        Test.stopTest();

        List<pymt__Shopping_Cart_Item__c> scis = [ SELECT Id, Name, Event_Fee__c, Event_Fee__r.Name FROM pymt__Shopping_Cart_Item__c ];
        system.assert( scis[0].Event_Fee__c != null, 'Erroneously did not assign any fee to contactless cart item' );
        system.assert( scis[0].Event_Fee__r.Name == 'Other Fee', 'Erroneously did not assign other fee to contactless cart item' );
    }


    @isTest
    static void testUpdateFeeCarts() {


        List<Contact> cons =                [ SELECT Id FROM Contact ORDER BY FirstName ASC ];
        List<evt__Special_Event__c> evts =  [ SELECT Id FROM evt__Special_Event__c ];

        List<pymt__Shopping_Cart_Item__c> scis = new List<pymt__Shopping_Cart_Item__c>{
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'Student Cart Item',
                pymt__Contact__c            = cons[1].Id,
                Type__c                     = 'Event Registration'
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'Non-Student Cart Item',
                pymt__Contact__c            = cons[0].Id,
                Type__c                     = 'Event Registration'
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'Non-Event Cart Item'
            )
        };

        insert scis;

        scis[0].Special_Event__c = evts[0].Id;
        scis[1].Special_Event__c = evts[0].Id;

        Test.startTest();
            update scis;
        Test.stopTest();

        for ( pymt__Shopping_Cart_Item__c sci : [ SELECT Id, Name, Event_Fee__c, Event_Fee__r.Name FROM pymt__Shopping_Cart_Item__c ] )
            if ( sci.Name == 'Non-Event Cart Item' )
                system.assert( sci.Event_Fee__c == null, 'Non-Event cart item erroneously assigned event fee');
            else {
                system.assert( sci.Event_Fee__c != null, 'Event cart item erroneously not assigned fee');
                system.assert( ShoppingCartFeeService_TDTM_Test.expectedFeeBySci.get(sci.Name) == sci.Event_Fee__r.Name, 'Event cart item erroneously assigned fee wrong fee, got ' + sci.Event_Fee__r.Name + ', expected ' + expectedFeeBySci.get(sci.Name) );
            }

    }

}