/**
 * @description EpCourseCertificatesPrintingController to print certificates
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-14
 * @modified 2020-08-14
 */
public class EpCourseCertificatesPrintingController {
    /**
     * @description Print certificates 
     * @return Void 
     * @param  CourseOffering id from page
     */
    @AuraEnabled
    public static void printCourseCertificates(string cOfferingRecordId) {
        if(cOfferingRecordId!=null && cOfferingRecordId!=''){
           Database.executeBatch(new EpCoursePDFLightningBatch(cOfferingRecordId),1); 
        }
    }
}