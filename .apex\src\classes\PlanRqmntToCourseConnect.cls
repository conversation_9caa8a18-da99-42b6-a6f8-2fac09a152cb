public class PlanRqmntToCourseConnect {
	public static List<Plan_Requirement_Fulfillment__c> connectPlanRequirementToCourseConnection(Map<Id, List<Plan_Requirement_Fulfillment__c>> contactIdToFullfillmentMap){
    	System.Debug('<<<<<contactIdToFullfillmentMap>>>>>'+contactIdToFullfillmentMap);
        List<hed__Course_Enrollment__c> crsEnrlmntLst = new List<hed__Course_Enrollment__c>();
        List<Plan_Requirement_Fulfillment__c> prqfulflmntLst = new List<Plan_Requirement_Fulfillment__c>();
        crsEnrlmntLst = [SELECT Id,hed__Contact__c,hed__Course_Offering__r.hed__Course__c,hed__Status__c from hed__Course_Enrollment__c where hed__Contact__c =:contactIdToFullfillmentMap.keySet()];
        System.Debug('<<<<<crsEnrlmntLst>>>>>'+crsEnrlmntLst);
        if(crsEnrlmntLst!=null && crsEnrlmntLst.size()>0){
            for(hed__Course_Enrollment__c crsEnrlmnt : crsEnrlmntLst){
                if(contactIdToFullfillmentMap.containsKey(crsEnrlmnt.hed__Contact__c)){
                    for(Plan_Requirement_Fulfillment__c prqfulflmn : contactIdToFullfillmentMap.get(crsEnrlmnt.hed__Contact__c)){
                        if(crsEnrlmnt.hed__Course_Offering__r.hed__Course__c ==prqfulflmn.Course__c){
                            prqfulflmn.Course_Connection__c = crsEnrlmnt.Id;
                            if(crsEnrlmnt.hed__Status__c=='Former'){
                                prqfulflmn.Status__c = 'Completed';
                            } else if(crsEnrlmnt.hed__Status__c=='Current'){
                                prqfulflmn.Status__c = 'In Progress';
                            }
                        }
                        prqfulflmntLst.add(prqfulflmn);
                    }
                }
            }
        } else {
            for(List<Plan_Requirement_Fulfillment__c> prqfulflmn : contactIdToFullfillmentMap.values()){
                prqfulflmntLst.addAll(prqfulflmn);
            }
        }
        System.Debug('<<<<<prqfulflmntLst>>>>>'+prqfulflmntLst);
        return prqfulflmntLst;
    }
    public static List<Plan_Requirement_Fulfillment__c> courseConnectionToConnectPlanRequirement(List<hed__Course_Enrollment__c> courseEnrollmentLst){
        System.Debug('<<<<<courseEnrollmentLst>>>>>'+courseEnrollmentLst);
        Set<Id> crseEnrolmentIds = new Set<Id>();
        for(hed__Course_Enrollment__c cenrlmnt : courseEnrollmentLst){
            crseEnrolmentIds.add(cenrlmnt.Id);
        }
        courseEnrollmentLst = [Select Id,hed__Contact__c,hed__Course_Offering__r.hed__Course__c,hed__Status__c from hed__Course_Enrollment__c where Id=:crseEnrolmentIds];
        Map<Id, List<Plan_Requirement_Fulfillment__c>> contactIdToFullfillmentMap = new Map<Id, List<Plan_Requirement_Fulfillment__c>>();
        List<Plan_Requirement_Fulfillment__c> prqfulflmntLstToreturn = new List<Plan_Requirement_Fulfillment__c>();
        List<Plan_Requirement_Fulfillment__c> prqfulflmntLst = new List<Plan_Requirement_Fulfillment__c>();
        Set<Id> contactIds = new Set<Id>();
        for(hed__Course_Enrollment__c crsenrlmnt : courseEnrollmentLst){
            contactIds.add(crsenrlmnt.hed__Contact__c);
        }
        System.Debug('<<<<<contactIds>>>>>'+contactIds);
        prqfulflmntLst = [Select Id,Program_Enrollment__r.hed__Contact__c,Course__c from Plan_Requirement_Fulfillment__c where Program_Enrollment__r.hed__Contact__c =:contactIds];
        System.Debug('<<<<<prqfulflmntLst>>>>>'+prqfulflmntLst);
        for(Plan_Requirement_Fulfillment__c prqflmnt : prqfulflmntLst){
            if(contactIdToFullfillmentMap.containsKey(prqflmnt.Program_Enrollment__r.hed__Contact__c)){
                contactIdToFullfillmentMap.get(prqflmnt.Program_Enrollment__r.hed__Contact__c).add(prqflmnt);
            } else {
                contactIdToFullfillmentMap.put(prqflmnt.Program_Enrollment__r.hed__Contact__c, new List<Plan_Requirement_Fulfillment__c> { prqflmnt });
            }
            
        }
        System.Debug('<<<<<contactIdToFullfillmentMap>>>>>'+contactIdToFullfillmentMap);
        for(hed__Course_Enrollment__c crsEnrlmnt : courseEnrollmentLst){
            if(contactIdToFullfillmentMap.containsKey(crsEnrlmnt.hed__Contact__c)){
                for(Plan_Requirement_Fulfillment__c prqfulflmn : contactIdToFullfillmentMap.get(crsEnrlmnt.hed__Contact__c)){
                    System.Debug('<<<<<crsEnrlmnt.hed__Course_Offering__r.hed__Course__c>>>>>'+crsEnrlmnt.hed__Course_Offering__r.hed__Course__c);
                    if(crsEnrlmnt.hed__Course_Offering__r.hed__Course__c ==prqfulflmn.Course__c){
                        prqfulflmn.Course_Connection__c = crsEnrlmnt.Id;
                        if(crsEnrlmnt.hed__Status__c=='Former'){
                            prqfulflmn.Status__c = 'Completed';
                        } else if(crsEnrlmnt.hed__Status__c=='Current'){
                            prqfulflmn.Status__c = 'In Progress';
                        }
                        prqfulflmntLst.add(prqfulflmn);
                    }
                }
            }
        }
        
        System.Debug('<<<<<prqfulflmntLst>>>>>'+prqfulflmntLst);
        return prqfulflmntLst;
    }
}