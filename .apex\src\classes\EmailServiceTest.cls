/**
 * @description Test Class for EmailService
 * <AUTHOR>
 * @version 1.0
 * @created 5-JUNE-2020
 * @modified 5-JUNE-2020
 */
@isTest
public class EmailServiceTest {
    
    @testSetup
    static void testSetup (){
		Map<String, Object> conMap = new Map<String, Object>{
            		'email' => '<EMAIL>' 
                    };
        Contact c =  (Contact) TestFactory.createSObject(new Contact(), conMap);
        insert c;
        
        Map<String, Object> cvMap1 = new Map<String, Object>{
            'title'=>'Humpty Dumpty', 
            'PathOnClient' => '/HumptyDumpty.txt', 
            'origin'  => 'H', 
            'ContentLocation' => 'S', 
            'versiondata' => blob.valueof('Humpty Dumpty Sat on a Wall'), 
            'Document_Type__c'=>'reference'
        };
        ContentVersion cv1 = (ContentVersion) TestFactory.createSObject(new ContentVersion(), cvMap1);
        insert cv1;
        Map<String, Object> cvMap2 = new Map<String, Object>{
            'title' => 'Cow Jumped Over the Moon', 
            'PathOnClient' => '/CowJumpedOverTheMoon.txt', 
            'origin'  => 'H', 
            'ContentLocation' => 'S', 
            'versiondata' => blob.valueof('Hey Diddle Diddle, The Cat and the Fiddle !!'), 
            'Document_Type__c' => 'reference'
        };        
        ContentVersion cv2 = (ContentVersion) TestFactory.createSObject(new ContentVersion(), cvMap2);
        insert cv2;
    }
    
    @isTest
    static void sendEmailsExternalEmailTest(){
        Contact c = [SELECT ID FROM Contact LIMIT 1]; 
        
        EmailService.SendEmailRequest reqWrapper = new EmailService.SendEmailRequest();
        reqWrapper.emailTemplateName = [Select Id, Name, DeveloperName from EmailTemplate LIMIT 1][0].DeveloperName;
        reqWrapper.emailId = '<EMAIL>,  <EMAIL>';
        reqWrapper.subject = 'DEMO';
        reqWrapper.eventStartDate = system.now();
        reqWrapper.eventEndDate = system.now();
        reqWrapper.eventSubject = 'How are you!!';
        reqWrapper.eventTitle = 'Sprint 4 DEMO';
        reqWrapper.eventLocation = 'Chicago';
        reqWrapper.targetRecordId = c.Id;
        //reqWrapper.DocumentType = '';
        reqwrapper.ccAddresses = '<EMAIL>, <EMAIL>';
        
        Test.startTest();
        EmailService.sendEmails(new EmailService.SendEmailRequest[]{reqWrapper});
        Test.stopTest();
    }    

    @isTest
    static void sendEmailsContactIdTest(){
        Contact c = [SELECT ID FROM Contact LIMIT 1]; 
        Account acc = [SELECT ID, NAME FROM ACCOUNT LIMIT 1];
        
        List<ContentVersion> cv = [SELECT ID FROM ContentVersion LIMIT 2];
        ContentVersion testContent1 = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv[0].Id];
        ContentDocumentLink cdl = new ContentDocumentLink(ContentDocumentId=testContent1.ContentDocumentId, LinkedEntityId=acc.Id);
        insert cdl;
        ContentVersion testContent2 = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv[1].Id];
        ContentDocumentLink cdl2 = new ContentDocumentLink(ContentDocumentId=testContent2.ContentDocumentId, LinkedEntityId=acc.Id);
        insert cdl2;

        EmailService.SendEmailRequest reqWrapper = new EmailService.SendEmailRequest();
        reqWrapper.emailTemplateName = [Select Id, Name, DeveloperName from EmailTemplate LIMIT 1][0].DeveloperName;
        reqWrapper.emailId = c.Id;
        reqWrapper.subject = 'DEMO';
        reqWrapper.eventStartDate = null;
        reqWrapper.eventEndDate = null;
        reqWrapper.eventSubject = '';
        reqWrapper.eventTitle = 'Sprint 4 DEMO';
        reqWrapper.eventLocation = 'Chicago';
        reqWrapper.DocumentType = 'reference';
        reqwrapper.targetRecordId = acc.Id;
        //reqwrapper.ccAddresses = '<EMAIL>, <EMAIL>';
        
        Test.startTest();
        EmailService.sendEmails(new EmailService.SendEmailRequest[]{reqWrapper});
        Test.stopTest();
    }

    @isTest
    static void sendEmailsContactIdWCCAddrTest(){
        Contact c = [SELECT ID FROM Contact LIMIT 1]; 
        Account acc = [SELECT ID, NAME FROM ACCOUNT LIMIT 1];
        
        List<ContentVersion> cv = [SELECT ID FROM ContentVersion LIMIT 2];
        ContentVersion testContent1 = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv[0].Id];
        ContentDocumentLink cdl = new ContentDocumentLink(ContentDocumentId=testContent1.ContentDocumentId, LinkedEntityId=acc.Id);
        insert cdl;
        ContentVersion testContent2 = [SELECT id, ContentDocumentId FROM ContentVersion where Id = :cv[1].Id];
        ContentDocumentLink cdl2 = new ContentDocumentLink(ContentDocumentId=testContent2.ContentDocumentId, LinkedEntityId=acc.Id);
        insert cdl2;

        EmailService.SendEmailRequest reqWrapper = new EmailService.SendEmailRequest();
        reqWrapper.emailTemplateName = [Select Id, Name, DeveloperName from EmailTemplate LIMIT 1][0].DeveloperName;
        reqWrapper.emailId = c.Id;
        reqWrapper.subject = 'DEMO';
        reqWrapper.eventStartDate = null;
        reqWrapper.eventEndDate = null;
        reqWrapper.eventSubject = '';
        reqWrapper.eventTitle = 'Sprint 4 DEMO';
        reqWrapper.eventLocation = 'Chicago';
        reqWrapper.DocumentType = 'reference';
        reqwrapper.targetRecordId = acc.Id;
        reqwrapper.ccAddresses = '<EMAIL>, <EMAIL>';
        
        Test.startTest();
        EmailService.sendEmails(new EmailService.SendEmailRequest[]{reqWrapper});
        Test.stopTest();
    }    
}