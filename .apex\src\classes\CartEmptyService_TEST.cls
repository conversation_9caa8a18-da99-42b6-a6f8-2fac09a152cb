@isTest
public class CartEmptyService_TEST {

    @TestSetup
    static void testSetup () {

        evt__Special_Event__c evt = new evt__Special_Event__c (
            Name            = 'TEST Event',
            evt__Start__c   = System.now().addDays(-365),
            evt__End__c     = System.now().addDays(365)
        );

        Account a = new Account(
            Name = 'TEST Term Account'
        );

        insert evt;
        insert a;

        hed__Term__c term = new hed__Term__c (
            Name                 = 'TEST Term',
            hed__Account__C      = a.Id,
            hed__Start_Date__c   = Date.today().addDays(-365),
            hed__End_Date__c     = Date.today().addDays(365)
        );

        Contact con = new Contact ( 
            FirstName = 'TEST',
            LastName  = 'Applicant'
        );

        insert term;
        insert con;

        pymt__PaymentX__c pymt = new pymt__PaymentX__c (
            pymt__Contact__c    = con.Id,
            Name                = 'TEST Payment',
            pymt__Status__c     = 'Online Checkout'
        );

        hed__Application__c app = new hed__Application__c (
            hed__Applicant__c           = con.Id,
            hed__Term__c                = term.Id,
            hed__Application_Status__c  = 'In Progress'
        );

        evt__Attendee__c ea = new evt__Attendee__c (
            evt__Event__c               = evt.Id,
            evt__Invitation_Status__c   = 'Pending Payment',
            evt__Contact__c             = con.Id

        );

        insert app;
        insert ea;
        insert pymt;

        Test.setCreatedDate( pymt.Id, Date.today().addDays(-365) );

        insert new List<pymt__Shopping_Cart_Item__c>{
            new pymt__Shopping_Cart_Item__c (
                pymt__Payment__c        = pymt.Id,
                Type__c                 = 'Event Registration',
                Attendee__c             = ea.Id,  
                Name                    = 'Event Registration'         
            ),
            new pymt__Shopping_Cart_Item__c (
                pymt__Payment__c        = pymt.Id,
                Type__c                 = 'EP Program Balance',
                Application__c          = app.Id,  
                Name                    = 'EP Program Balance'         
            ),
            new pymt__Shopping_Cart_Item__c (
                pymt__Payment__c        = pymt.Id,
                Type__c                 = 'Donation',
                Name                    = 'Donation'         
            ),
            new pymt__Shopping_Cart_Item__c (
                pymt__Payment__c        = pymt.Id,
                Type__c                 = 'Application Fee',
                Name                    = 'Application Fee'         
            ),
            new pymt__Shopping_Cart_Item__c (
                pymt__Payment__c        = pymt.Id,
                Type__c                 = 'EP Program Deposit',
                Name                    = 'EP Program Deposit'         
            ),
            new pymt__Shopping_Cart_Item__c (
                pymt__Payment__c        = pymt.Id,
                Type__c                 = 'Program Deposit',
                Name                    = 'Program Deposit'         
            )
        };
        
    }

    @isTest
    static void testEmptyCart () {

        Test.startTest();
            String jobId = System.schedule('ScheduledApexTest', '0 0 0 1 1 ? 2030', new CartEmptyService_Scheduler());
        Test.stopTest();

        List<pymt__Shopping_Cart_Item__c> scis = [ SELECT Id, Name, Application__c, Attendee__c FROM pymt__Shopping_Cart_Item__c ];
        List<pymt__PaymentX__c> pymts = [ SELECT Id, Name, Application__c, pymt__Contact__c FROM pymt__PaymentX__c ];

    }
}