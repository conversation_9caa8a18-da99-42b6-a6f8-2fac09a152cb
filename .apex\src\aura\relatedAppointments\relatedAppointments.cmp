<!--
 - Created by <PERSON><PERSON> on 2023-06-05.
 -->

<aura:component description="relatedAppointments"
                implements="flexipage:availableForAllPageTypes,force:hasRecordId,force:hasSObjectName"
                access="global" controller="caseController">
    <aura:attribute name="recordId" type="Id"/>

    <aura:attribute name="contactName" type="String"/>
    <aura:attribute name="relatedAppointments" type="List"/>
    <aura:attribute name="columns" type="List"/>
    <aura:attribute name="showModal" type="boolean" default="false"/>
    <aura:attribute name="modal" type="Aura.Component[]"/>
    <aura:attribute name="activeTab" type="String" default="tab1"/>

    <aura:handler name="init" value="{!this}" action="{!c.doInit}"/>

    <lightning:card title="Appointments" iconName="standard:service_appointment">
        <div class="autoHeight slds-card slds-card_boundary">
            <div class="autoHeight slds-card slds-card_boundary">
                <h2 style="margin-top: 10px; margin-bottom: 10px; padding-left: 10px;  font-weight: 500;">All related
                    appointments</h2>
                <div class="slds-grid" style="max-width: 100%;">
                    <lightning:datatable data="{!v.relatedAppointments}" columns="{!v.columns}" keyField="Id"
                                         onrowaction="{! c.handleRowAction }" class="slds-scrollable_y"/>
                </div>
            </div>
        </div>
    </lightning:card>
</aura:component>
