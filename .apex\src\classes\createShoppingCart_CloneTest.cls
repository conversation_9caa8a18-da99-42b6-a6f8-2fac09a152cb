@isTest
public class createShoppingCart_CloneTest {
 @testSetup
    static void setupMethod(){
        Account acc = new Account();
        acc.Name = 'Dept Account';
        insert acc;
        Contact con = new contact();
        con.FirstName = '';
        con.lastName = System.Label.Guest_User_Contact_Name;
        insert con;
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        pymt.Name = 'payment_'+string.valueOf(datetime.now());
        pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
        pymt.pymt__Transaction_Type__c = 'Payment';
        pymt.pymt__Status__c = 'Scheduled';
        pymt.pymt__Amount__c = 10.12;
        pymt.pymt__Billing_Country__c = 'CA';
        pymt.pymt__Billing_State__c = 'ON';
        pymt.pymt__Tax_Method__c = 'State/Province Lookup';
        //pymt.pymt__Contact__c = con.Id;
        insert pymt;
        hed__Term__c term = new hed__Term__c();
        term.Name='Term';
        term.hed__Type__c = 'Quarter';
        term.hed__Account__c=acc.Id;
        term.hed__Start_Date__c= date.today().addDays(5);
        term.hed__End_Date__c = date.today().addDays(10);
        insert term;
        
        hed__Course__c course= new hed__Course__c();
        course.hed__Account__c = acc.Id;
        course.Name = 'Negotiation Foundations';
        course.hed__Course_ID__c = 'Course1';
        insert course;
        
        hed__Course_Offering__c courseOff = new hed__Course_Offering__c();
        courseOff.Name = 'Course Offering';
        courseOff.hed__Course__c = course.Id;
        courseOff.hed__Term__c = term.Id;
        courseOff.hed__Start_Date__c = date.today().addDays(5);
        courseOff.hed__End_Date__c = date.today().addDays(10);
        courseOff.Registration_Fee__c = 50;
        insert courseOff;
        
        RecordType eventType = [select id,Name from RecordType where SobjectType='evt__Special_Event__c' AND Name = 'Standard Event' LIMIT 1];
        evt__Special_Event__c event = new evt__Special_Event__c();
        event.RecordTypeId = eventType.Id;
        event.Name = 'Special event';
        insert event;
        
        evt__Special_Event__c event2 = new evt__Special_Event__c();
        event2.RecordTypeId = eventType.Id;
        event2.Name = 'Special event 2';
        insert event2;
        
        evt__Event_Fee__c fee = new evt__Event_Fee__c();
        fee.Name = 'special event fee';
        fee.evt__Event__c = event.Id;
        fee.evt__Amount__c = 5.00;
        fee.evt__Active__c = true;
        fee.evt__Category__c = 'Attendee';
        fee.evt__Order__c = 1;
        insert fee;
        evt__Event_Fee__c fee2 = new evt__Event_Fee__c();
        fee2.Name = 'special event fee2';
        fee2.evt__Event__c = event2.Id;
        fee2.evt__Amount__c = 5.00;
        fee2.evt__Active__c = true;
        fee2.evt__Category__c = 'Attendee';
        fee2.evt__Order__c = 1;
        insert fee2;
        string cookieName = string.valueOf(dateTime.now());
        Cookie cook = new Cookie('cartID', cookieName, null, -1, false);
        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
        cart.pymt__Cart_UID__c = cookieName;
        insert cart;
    } 
    static testMethod void addToCart() {
        Contact con = [SELECT Id, Name From Contact WHERE Name=:System.Label.Guest_User_Contact_Name];
        evt__Special_Event__c event = [select Id from evt__Special_Event__c where Name = 'Special event'];
        evt__Special_Event__c event2 = [select Id,Name from evt__Special_Event__c where Name = 'Special event 2'];
        hed__Course_Offering__c courseOff = [select Id from hed__Course_Offering__c where Name = 'Course Offering'];
        pymt__Shopping_Cart__c cart = [Select Id,pymt__Cart_UID__c from pymt__Shopping_Cart__c Limit 1];
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event2.Name;
        cartItem.Special_Event__c = event2.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = con.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        cartItem.pymt__Shopping_Cart__c = cart.Id;
        
        insert cartItem;
        
        Test.setCurrentPageReference(new PageReference('Page.ShoppingCartPage_clone')); 
        System.currentPageReference().getParameters().put('id', event.Id);  
        System.currentPageReference().getParameters().put('cartId', cart.Id);
        string cookieName = string.valueOf(dateTime.now());
        Cookie cookie = new Cookie('cartID', cookieName, null, -1, false);
		ApexPages.currentPage().setCookies(new Cookie[]{cookie});
        ApexPages.StandardController sc = new ApexPages.StandardController(event);
		createShoppingCartController_clone controller = new createShoppingCartController_clone();
        test.startTest();
        createShoppingCartController_clone.addToCart(event.Id,cart.Id,cart.pymt__Cart_UID__c);
        createShoppingCartController_clone.addToCart(event2.Id,cart.id,cart.pymt__Cart_UID__c);
        createShoppingCartController_clone.addToCart(courseOff.Id,cart.id,cart.pymt__Cart_UID__c);
        test.stopTest();
    }
    static testMethod void testPaymentId() {
        evt__Special_Event__c event = [select Id from evt__Special_Event__c where Name = 'Special event'];
        pymt__Shopping_Cart__c cart = [Select Id,pymt__Cart_UID__c from pymt__Shopping_Cart__c Limit 1];
        Test.setCurrentPageReference(new PageReference('Page.ShoppingCartPage_clone')); 
        System.currentPageReference().getParameters().put('id', event.Id);  
        System.currentPageReference().getParameters().put('cartId', cart.Id);
        string cookieName = string.valueOf(dateTime.now());
        Cookie cookie = new Cookie('cartID', cookieName, null, -1, false);
		ApexPages.currentPage().setCookies(new Cookie[]{cookie});
        createShoppingCartController_clone con = new createShoppingCartController_clone();
        test.startTest();
        createShoppingCartController_clone.addToCart(event.Id,cart.id,cart.pymt__Cart_UID__c);
        //string cookieName = string.valueOf(dateTime.now());
        createShoppingCartController_clone.PaymentId(event.Id,cookieName);// (event.Id);
        createShoppingCartController_clone.noCart(cookieName);
        test.stopTest();
    }//removeFromCart
    static testMethod void testRemove() {
        evt__Special_Event__c event = [select ID,Name from evt__Special_Event__c where Name = 'Special event'];
        evt__Special_Event__c event2 = [select Id,Name from evt__Special_Event__c where Name = 'Special event 2'];
        hed__Course_Offering__c courseOff = [select Id from hed__Course_Offering__c where Name = 'Course Offering'];
        
        Contact con = [SELECT Id, Name From Contact WHERE Name=:System.Label.Guest_User_Contact_Name];
        test.startTest();
        createShoppingCartController_clone.addToCart(event.Id,null,string.valueOf(datetime.now()));
        pymt__Shopping_Cart__c cart = [Select Id,pymt__Cart_UID__c from pymt__Shopping_Cart__c Limit 1];
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
        cartItem.Name = event.Name;
        cartItem.Special_Event__c = event.Id;
        cartItem.pymt__Quantity__c =1;
        cartItem.pymt__Contact__c = con.id;
        cartItem.pymt__Unit_Price__c = 10.00;
        insert cartItem;
        createShoppingCartController_clone.addToCart(event.Id,cart.id,cart.pymt__Cart_UID__c);
        createShoppingCartController_clone.addToCart(event2.Id,cart.id,cart.pymt__Cart_UID__c);
        createShoppingCartController_clone.addToCart(courseOff.Id,cart.id,cart.pymt__Cart_UID__c);
        createShoppingCartController_clone.removeFromCart(event.Id,cart.id);// (event.Id);
        
        //createShoppingCartController.addToCart(courseOff.Id);
        test.stopTest();
    }
    static testMethod void testContinue() {
        evt__Special_Event__c event = [select Id from evt__Special_Event__c where Name = 'Special event'];
        evt__Special_Event__c event2 = [select Id from evt__Special_Event__c where Name = 'Special event 2'];
        hed__Course_Offering__c courseOff = [select Id from hed__Course_Offering__c where Name = 'Course Offering'];
        pymt__Shopping_Cart__c cart = [Select Id,pymt__Cart_UID__c from pymt__Shopping_Cart__c Limit 1];
        test.startTest();
        createShoppingCartController_clone.addToCart(event.Id,cart.id,cart.pymt__Cart_UID__c);// (event.Id);
        createShoppingCartController_clone.addToCart(event2.Id,cart.id,cart.pymt__Cart_UID__c);
        list<createShoppingCartController_clone.cartWrapper> wrap = createShoppingCartController_clone.addToCart(courseOff.Id,cart.id,cart.pymt__Cart_UID__c);
        list<createShoppingCartController_clone.cartWrapper> wrap2 = createShoppingCartController_clone.addToCart(courseOff.Id,cart.id,cart.pymt__Cart_UID__c);
        system.debug( 'size--> '+wrap.size() );
        createShoppingCartController_clone.continueCartSave(wrap,cart.id,cart.pymt__Cart_UID__c);
        createShoppingCartController_clone.noRecord(cart.id);
        test.stopTest();
    }
    static testMethod void testLoggedInUser(){
        test.startTest();
        createShoppingCartController_clone.getUserDetails();
        createShoppingCartController_clone.createCookie();
        test.stopTest();
    }
}