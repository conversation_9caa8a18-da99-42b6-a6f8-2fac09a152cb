({
    removeItem : function(component, event, helper) {
        //addToCart
        var source = event.getSource().get("v.name");//event.getSource().getLocalId();
        console.log(source);
        var action = component.get("c.removeFromCart");
        action.setParams({
            EventCourseId : source//component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log('delete Resp: '+response.getReturnValue());
                component.set("v.CartMap",response.getReturnValue());
                component.set("v.showCartInfo",'true');
                var subTotal = 0.00;
                var resp = response.getReturnValue();
                for(var item in resp ){
                    console.log('item= '+resp[item].price);
                    console.log('item= '+resp[item].qty);
                    subTotal += resp[item].price * resp[item].qty;
                    console.log('subTotal= '+subTotal);
                }
                component.set("v.Total",subTotal);
                //             $A.get('e.force:refreshView').fire();
            }
        });
        $A.enqueueAction(action);
    },
    onLoad : function(component, event, helper){
        var recId = component.get("v.recordId");
        var url = window.location.search.substr(1);
        console.log('In onload - '+url);
        
        if(recId){
        var callTo = component.get("c.addToCart");
        callTo.setParams({
            EventCourseId : component.get("v.recordId")
        });
        helper.getCartInfo(component,callTo).then(
            function(response) {
                //console.log("response on js-> "+response.cartWrapper.item);
                console.log("response on js-> "+response.item);
                component.set("v.showCartInfo",'true');
                var subTotal = 0.00;
                for(var item in response){
                    console.log('item= '+response[item].price);
                    console.log('item= '+response[item].qty);
                    subTotal += response[item].price * response[item].qty;
                    console.log('subTotal= '+subTotal);
                }
                component.set("v.Total",subTotal);
                component.set("v.CartMap",response);
                
            }).catch(
            function(error) {
                //component.set("v.status" ,error ) ; 
                console.log(error);
            }
        );
    }
        else{
            var action = component.get("c.noRecord");
        action.setParams({
            //EventCourseId : source//component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log(' Resp: '+response.getReturnValue());
                component.set("v.CartMap",response.getReturnValue());
                component.set("v.showCartInfo",'true');
                var subTotal = 0.00;
                var resp = response.getReturnValue();
                for(var item in resp ){
                    console.log('item= '+resp[item].price);
                    console.log('item= '+resp[item].qty);
                    subTotal += resp[item].price * resp[item].qty;
                    console.log('subTotal= '+subTotal);
                }
                component.set("v.Total",subTotal);
                //             $A.get('e.force:refreshView').fire();
                var UserDetail = component.get("c.getUserDetails");
                                       UserDetail.setCallback(this, function(response) {
                                           var state = response.getState();
                                           console.log(state);
                                           if (state === "SUCCESS") {
                                               console.log('UserName: '+response.getReturnValue());
                                               component.set("v.userName",response.getReturnValue()+'\'s');
                                               
                                           }
                                       });
                                       $A.enqueueAction(UserDetail);
            }
        });
        $A.enqueueAction(action);
        }
    },
    continueShopping : function(component, event, helper){
        var dismissActionPanel = $A.get("e.force:closeQuickAction");
        console.log(dismissActionPanel);
        if(dismissActionPanel != undefined){
            dismissActionPanel.fire();
        }
        else{
            console.log('inside close');
            //window.history.go(-2);
            window.close();
        }
        //window.location.reload();
        //window.opener.location.reload();
        //close();
       //
        //window.history.go(-1);
    }, 
    checkout : function(component, event, helper){
        //getPaymentId
        var baseUrl = $A.get("$Label.c.base_url");
        var checkoutURL = $A.get("$Label.c.Events_Checkout_URL");
        //console.log('base= '+baseUrl);
        var source = event.getSource().get("v.name");//event.getSource().getLocalId();
        //alert(source);
        //console.log('checkoutURL= '+checkoutURL);
        console.log('recordId='+component.get("v.recordId"));
        var action = component.get("c.PaymentId");
        action.setParams({
            event : component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log('payment ID: '+response.getReturnValue());
                alert('Payment URL='+baseUrl+checkoutURL+'?pid='+response.getReturnValue());
                window.location.href = baseUrl+checkoutURL+'?pid='+response.getReturnValue();
                ///component.set("v.CartMap",response.getReturnValue());
                //             $A.get('e.force:refreshView').fire();
            }
        });
        $A.enqueueAction(action);
        
        //window.location.href = baseUrl+checkoutURL;
    },
    UpdateCart : function(component, event, helper){
        console.log('cart: '+component.get("v.CartMap"));
        var action = component.get("c.continueCartSave");
        action.setParams({
            cartItems : component.get("v.CartMap")
        });
        action.setCallback(this, function(response) {
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log(' Resp: '+response.getReturnValue());
                component.set("v.CartMap",response.getReturnValue());
                var subTotal = 0.00;
                var resp = response.getReturnValue();
                for(var item in resp){
                    console.log('item= '+resp[item].price);
                    console.log('item= '+resp[item].qty);
                    subTotal += resp[item].price * resp[item].qty;
                    console.log('subTotal= '+subTotal);
                }
                component.set("v.Total",subTotal);
                alert('Cart Updated!');
            }
        });
        $A.enqueueAction(action);
    }
    
})