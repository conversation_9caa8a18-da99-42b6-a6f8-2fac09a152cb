<!-- add implements="forceCommunity:availableForAllPageTypes" to surface the component in community builder -->
<aura:component controller="LightningSelfRegisterController" implements="forceCommunity:availableForAllPageTypes">
    <aura:attribute name="accountId" type="String" required="false" description="accountId for creating the user. If not specified, it will create a PersonAccount if possible for B2C scenario. Or otherwise if it's in a community, the community's self-registration accountId will be used."/>
    <aura:attribute name="regConfirmUrl" type="String" required="true"/>
    <aura:attribute name="startUrl" type="String" required="false" description="The url you go to after a successful login" />
    <aura:attribute name="showError" type="Boolean" required="true" description="" default="false" access="private"/>
    <aura:attribute name="errorMessage" type="String" required="false" description="" access="private"/>
    <aura:attribute name="firstnameLabel" type="String" required="true" default="Legal First Name"/>
    <aura:attribute name="lastnameLabel" type="String" required="true" default="Legal Last Name"/>
    <aura:attribute name="formerlastnameLabel" type="String" required="false" default="Former Last Name"/>
    <aura:attribute name="preffirstnameLabel" type="String" required="false" default="Preferred First Name"/>
    <aura:attribute name="emailLabel" type="String" required="true" default="Personal Email"/>
    <aura:attribute name="phoneLabel" type="String" required="true" default="Mobile Phone"/>
    <aura:attribute name="utoridLabel" type="String" required="false" default="UTor ID"/>
    <aura:attribute name="passwordLabel" type="String" required="false" default="Create Password"/>
    <aura:attribute name="confirmPasswordLabel" type="String" required="false" default="Confirm Password"/>    
    <aura:attribute name="submitButtonLabel" type="String" required="false" default="Sign Up"/>
    <aura:attribute name="includePasswordField" type="Boolean" required="false" default="false" description="Whether to include password"/>    
    <aura:attribute name="extraFieldsFieldSet" type="String" required="false" default="Self_Registration_FieldSet" description="A field set name whose fields are desired for user registration"/>
    <aura:attribute name="extraFields" type="list" required="false" description="A field set name whose fields are desired for user registration"/>
    <aura:handler name="init" value="{!this}" action="{!c.initialize}"/>
    <aura:attribute name="expid" type="String" required="false" description="The branding experience ID" />    
    
    <aura:registerevent name="sitePropagatedStartUrl" type="c:setStartUrl"/>
    <aura:handler name="init" value="{!this}" action="{!c.initialize}"/>
    <aura:dependency resource="c:setStartUrl" type="EVENT"/>
    <!-- Please uncomment -->
    <aura:dependency resource="siteforce:registerQueryEventMap" type="EVENT"/>
    <!--><-->
    <aura:handler event="c:setStartUrl" action="{!c.setStartUrl}"/> 
    <aura:handler event="c:setExpId" action="{!c.setExpId}"/>    
    <aura:dependency resource="c:setExpId" type="EVENT"/>   
    
    <div>
            <aura:renderIf isTrue="{!v.showError}">
                <div id="error">
                    <ui:outputRichText value="{!v.errorMessage}"/>
                </div>
            </aura:renderIf>
            <div id="sfdc_username_container" class="sfdc_username_container">
                <span id="sfdc_user" class="login-icon" data-icon="a"></span>
                <ui:inputText value="" aura:id="firstname" required="true" placeholder="{!v.firstnameLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>

            <div id="sfdc_nickname_container" class="sfdc">
                <span id="sfdc_user" class="login-icon" data-icon="a"></span>
                <ui:inputText value="" aura:id="lastname" required="true" placeholder="{!v.lastnameLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>
<!--
            <div id="sfdc_formerlastname_container" class="sfdc">
                <span id="sfdc_user" class="login-icon" data-icon="a"></span>
                <ui:inputText value="" aura:id="formerlastname" placeholder="{!v.formerlastnameLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>

            <div id="sfdc_preffirstname_container" class="sfdc">
                <span id="sfdc_user" class="login-icon" data-icon="a"></span>
                <ui:inputText value="" aura:id="preffirstname" placeholder="{!v.preffirstnameLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>
-->    
            <div id="sfdc_email_container" class="sfdc">
                <span id="sfdc_user" class="login-icon" data-icon="k"></span>
                <ui:inputText value="" aura:id="email" required="true" placeholder="{!v.emailLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>

            <div id="sfdc_phone_container" class="sfdc">
                <span id="sfdc_user" class="login-icon" data-icon="k"></span>
                <ui:inputText value="" aura:id="phone" required="true" placeholder="{!v.phoneLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>
<!--
            <div id="sfdc_utorid_container" class="sfdc">
                <span id="sfdc_user" class="login-icon" data-icon="k"></span>
                <ui:inputText value="" aura:id="utorid" placeholder="{!v.utoridLabel}" keyup="{!c.onKeyUp}" class="input sfdc_usernameinput sfdc"/>
            </div>
-->    
            <aura:iteration aura:id="extraFields" items="{!v.extraFields}" var="curField" indexVar="index">
                <div id="sfdc_extrafield_container" class="sfdc">
                    <span id="sfdc_user" class="login-icon" data-icon="a"></span>
                    <ui:inputText value="{!curField.value}" aura:id="{!curField.fieldPath}" placeholder="{!curField.label}" keyup="{!c.onKeyUp}" class="input sfdc_extrafieldinput sfdc"/>
                </div>
            </aura:iteration>

            <aura:renderIf isTrue="{!v.includePasswordField}">
                <div id="sfdc_password_container" class="sfdc">
                    <span id="sfdc_lock" class="login-icon sfdc" data-icon="c"></span>
                    <ui:inputSecret value="" aura:id="password" placeholder="{!v.passwordLabel}" keyup="{!c.onKeyUp}" class="input sfdc_passwordinput sfdc"/>
                </div>
    
                <div id="sfdc_confirm_password_container" class="sfdc">
                    <span id="sfdc_lock" class="login-icon sfdc" data-icon="c"></span>
                    <ui:inputSecret value="" aura:id="confirmPassword" placeholder="{!v.confirmPasswordLabel}" keyup="{!c.onKeyUp}" class="input sfdc_passwordinput sfdc"/>
                </div>
            </aura:renderIf>

            <div class="sfdc">
                <ui:button aura:id="submitButton" label="{!v.submitButtonLabel}" press="{!c.handleSelfRegister}" class="sfdc_button"/>
            </div>
        
        <!--a:if isTrue="{!v.showError}">
            <div class="alert alert-warning">
                <ui:outputText value="{!v.errorMessage}" class="showError" />
            </div>
         </aura:if>-->
    </div>
</aura:component>