/**
* @description    Test class for TestDateStampDupRulesApex_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-11-05
* @modified 2020-11-05
*/
@isTest
public class CourseCnctnCreatefulfillment_TDTMTest { 
	@testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for WorkHistoryCalculator__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('CourseCnctnCreateReqmntfulfillment_TDTM', 'Course_Enrollment__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        //Create test Account:
        Account a = (Account)TestFactory.createSObject(new Account(Name='TestAccount'));
        insert a;

        //Create test contact: 
        Contact c = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student'));
        insert c;

        //Create Term
        hed__Term__c trm = new hed__Term__c(hed__Account__c=a.Id,name='Spring 2020');
        insert trm;
        //Create course
        hed__Course__c course = new hed__Course__c(hed__Account__c=a.Id,name='MBA 2020');
        insert course;
        //Create Course Offering
        hed__Course_Offering__c cOffering = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering',hed__Term__c=trm.Id);
        insert cOffering;
        //Create a Program Plan
        hed__Program_Plan__c ppln = new hed__Program_Plan__c(name='Test Program Plan',hed__Account__c=a.Id,hed__Start_Date__c=Date.Today(),hed__End_Date__c = Date.Today().addDays(30));
        insert ppln;
        //Create Course Connection
        hed__Course_Enrollment__c cenrollment = new hed__Course_Enrollment__c(hed__Contact__c = c.Id,hed__Course_Offering__c=cOffering.Id,hed__Status__c='Current');
        insert cenrollment;
        //Create Plan Requirements
        hed__Plan_Requirement__c planRequirementParent = new hed__Plan_Requirement__c(name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id,hed__Category__c = 'Required');
        insert planRequirementParent;
        hed__Plan_Requirement__c planRequirementParentwithCourse = new hed__Plan_Requirement__c(hed__Course__c	 = course.id,hed__Category__c = 'Required',name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        insert planRequirementParentwithCourse;
        List<hed__Plan_Requirement__c> plnRqrmntLst = new List<hed__Plan_Requirement__c>();
        hed__Plan_Requirement__c planRequirement1 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement1);
        hed__Plan_Requirement__c planRequirement2 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement2);
        hed__Plan_Requirement__c planRequirement3 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement3);
        hed__Plan_Requirement__c planRequirement4 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement4);
        hed__Plan_Requirement__c planRequirement5 = new hed__Plan_Requirement__c(hed__Plan_Requirement__c = planRequirementParent.Id,name='Test Plan Requirement 1',hed__Program_Plan__c = ppln.Id);
        plnRqrmntLst.add(planRequirement5);
        insert plnRqrmntLst;
        hed__Program_Enrollment__c penrollment = new hed__Program_Enrollment__c(hed__Contact__c=c.Id,hed__Account__c = a.Id,hed__Program_Plan__c = ppln.Id);
        insert penrollment;
        List<Plan_Requirement_Fulfillment__c> plrqmnfllmntLst = new List<Plan_Requirement_Fulfillment__c>();
        Plan_Requirement_Fulfillment__c plrqmnfllmnt1 = new Plan_Requirement_Fulfillment__c(Course__c= course.Id,Course_Connection__c = cenrollment.Id,Plan_Requirement__c =planRequirementParent.Id,Program_Enrollment__c =penrollment.Id);
        plrqmnfllmntLst.add(plrqmnfllmnt1);
        Plan_Requirement_Fulfillment__c plrqmnfllmnt2 = new Plan_Requirement_Fulfillment__c(Course__c= course.Id,Course_Connection__c = cenrollment.Id,Plan_Requirement__c =planRequirementParentwithCourse.Id,Program_Enrollment__c =penrollment.Id);
        plrqmnfllmntLst.add(plrqmnfllmnt2);
        insert plrqmnfllmntLst;
    } 
    @isTest 
    static void CourseCnctnCreatefulfillment_TDTMTestMethod() {
        Contact c = [Select id from Contact limit 1];
        hed__Course_Offering__c cOffering = [Select Id from hed__Course_Offering__c limit 1];
        Test.StartTest();
        hed__Course_Enrollment__c cenrollment = new hed__Course_Enrollment__c(hed__Course_Offering__c=cOffering.Id,hed__Contact__c=c.Id,hed__Status__c='Current');
        insert cenrollment;
        System.AssertEquals('In Progress',[Select Id,Course_Connection__c,Status__c from Plan_Requirement_Fulfillment__c Limit 1].Status__c);
        cenrollment.hed__Status__c='Former';
        update cenrollment;
        System.AssertEquals(cenrollment.Id,[Select Id,Course_Connection__c,Status__c from Plan_Requirement_Fulfillment__c  Limit 1].Course_Connection__c);
        System.AssertEquals('Completed',[Select Id,Course_Connection__c,Status__c from Plan_Requirement_Fulfillment__c  Limit 1].Status__c);
        Test.StopTest();
    }
}