<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" 
                access="global" controller="ShoppingCartController">
    <aura:method name="initData" action="{!c.initData}">
        <aura:attribute Name="showCart" type="boolean" default="false"/>
    </aura:method>
	<aura:attribute Name="ItemMap" type="MAP"/>
    <aura:attribute Name="CartMap" type="MAP" default="{}"/>
    <aura:attribute Name="showCart" type="boolean" default="false"/>
    <aura:attribute Name="PaymentId" type="string"/>
    <aura:attribute Name="quantity" type="Integer" default="1"/>
    <aura:attribute Name="TotalPrice" type="Double" />
    <aura:handler name="init" value="{!this}" action="{!c.initData}"/>
    
    <aura:if isTrue="{!v.showCart}">
      <div id="CartId" layout="block"  class="popupBackground">
                        <!--      <apex:outputpanel id="CartId" layout="block" style="Display:none;"  styleclass="customPopup"> -->
                        <div layout="block" id="CartDiv"  class="customPopup">
                            
                            
                            
                            <div style="padding-left:40%;">
                            <h4>
                                Cart Information
                            </h4>
                            </div>
                            <div>
                                <table class="cartTable" id="CartTable">
                                    <thead>
                                        <tr>
                                            <th> Item Name </th>
                                            <th> Description </th>
                                            <th> Price </th>
                                            <th> Quantity </th>
                                            <th> Total Price </th>
                                        </tr>
                                    </thead>
                                    <tbody id="tbodyCart">
                                        <aura:iteration items="{!v.CartMap}" var="cus" indexVar="key">
                                            <aura:if isTrue="{!not(empty(cus.value.Name))}">
                                                <tr>
                                                    <td> {!cus.value.Name}</td>
                                                    <td> {!cus.value.evt__Short_Description__c}</td>
                                                    <td> 
                                                        ${!cus.value.evt__Event_Fees__r[0].evt__Amount__c}
                                                    </td>
                                                    <td>
                                                        <lightning:input type="number" name="qty" label="Quantity" value="{!v.quantity}" />
                                                    </td>
                                                    <td>${!cus.value.evt__Event_Fees__r[0].evt__Amount__c * v.quantity}</td>
                                                </tr>
                                                <tr><lightning:button class="slds-m-top_small" variant="brand" 
                                                  type="submit" name="removeFromCart" onclick="{!c.closeCart}" 
                                                  label="Remove From Cart" /></tr>
                                            </aura:if>
                                        </aura:iteration>
                                    </tbody>
                                </table>
                            </div>
                            <div style="padding-left:30%;">
                                <lightning:button class="slds-m-top_small" variant="brand" 
                                                  type="submit" name="AddToCart" onclick="{!c.closeCart}" 
                                                  label="Continue Shopping" />
                                <lightning:button class="slds-m-top_small" variant="brand" 
                                                  type="submit" name="Checkout" onclick="{!c.onSubmit}"
                                                  label="Checkout" />
                            </div>
                            
                            
                            
                        </div> 
                        <!--       </apex:outputpanel>-->
                    </div> 
    </aura:if>
</aura:component>