@IsTest
public with sharing class CartControllerTest {

    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
		
        UserRole userrole = [Select Id, DeveloperName From UserRole Where DeveloperName = 'Site_Administrators' Limit 1];
        User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		adminUser.UserRoleId = userRole.Id;
        update adminUser;
        System.debug('---admin ' + adminUser);
        
        System.runAs(adminUser){
        //Insert store configs
        Store_Configuration__c sc1 = new Store_Configuration__c (
            Name = 'Events'
        );

        Store_Configuration__c sc2 = new Store_Configuration__c (
            Name = 'EP Courses'
        );

        insert new List<Store_Configuration__c>{ sc1, sc2 };


        //Insert store object configs
        Store_SObject_Configuration__c soc1 = new Store_SObject_Configuration__c (
            Name = 'Events',
            Amount_Field_API_Name__c = 'Registration_Fee__c',
            Enforce_Cart_Item_Uniqueness__c = true,
            Include_Quantity_Field__c = true,
            Enable_One_Click_Checkout__c = true,
            SObject_API_Name__c = 'hed__Course_Offering__c',
            Store_Configuration__c = sc1.Id
        );

        Store_SObject_Configuration__c soc2 = new Store_SObject_Configuration__c (
            Name = 'Course',
            Amount_Field_API_Name__c = 'Registration_Fee__c',
            Enforce_Cart_Item_Uniqueness__c = true,
            Include_Quantity_Field__c = true,
            Enable_One_Click_Checkout__c = true,
            Shopping_Cart_Lookup_to_Object__c = 'Course_OFfering__c',
            SObject_API_Name__c = 'hed__Course_Offering__c',
            Store_Configuration__c = sc2.Id
        );

        insert new List<Store_SObject_Configuration__c>{ soc1, soc2 };


        //Prep community user
        List<Account> accList = new List<Account>();
        Account acc1 = (Account)TestFactory.createSObject(new Account(Name = 'Some Account'));
        Account acc2 = (Account)TestFactory.createSObject(new Account(Name = 'Owner Account'));
        accList.add(acc1);
        accList.add(acc2);
        insert accList;
        
        Contact con = (Contact)TestFactory.createSObject(new Contact(AccountId = accList[1].Id, Email = '<EMAIL>', FirstName = 'Joe', LastName  = 'Curve'));
        insert con;

        //Insert course
        hed__Course__c course = new hed__Course__c (
            Name = 'Course 1',
            hed__Account__c = accList[0].Id
        );

        insert course;

        //Insert course
        hed__term__c term = new hed__term__c (
            Name = 'term 1',
            hed__Account__c = accList[0].Id
        );

        insert term;

        //Insert course offerings
        hed__Course_Offering__c course1 = new hed__Course_Offering__c (
            Name = 'Course 1',
            hed__course__c = course.Id,
            hed__Term__c = term.Id,                    
            Registration_Fee__c = 3000
        );

        hed__Course_Offering__c course2 = new hed__Course_Offering__c (
            Name = 'Course 2',
            hed__course__c = course.Id,
            hed__Term__c = term.Id            
        );

        insert new List<hed__Course_Offering__c>{ course1, course2 };
        
        insert new pymt__PaymentX__c (
            pymt__Contact__c    = con.Id,
            Name                = 'TEST Payment',
            pymt__Status__c     = 'Online Checkout',
            pymt__Billing_First_Name__c = 'Test',
            pymt__Billing_Last_Name__c = 'Student',
            pymt__Billing_Street__c = 'Test St',
            pymt__Billing_City__c = 'Testing',
            pymt__Billing_State__c = 'Tester',
            pymt__Billing_Country__c = 'Canada',
            pymt__Billing_Postal_Code__c = '12345',
            pymt__Amount__c = 500
        );
            
        insert new User(
                alias = 'test2',
                communityNickname = 'test123',
                contactId = con.Id, 
                email = '<EMAIL>', 
                emailencodingkey = 'UTF-8', 
                firstName = 'test2',
                lastName = 'User', 
                userName = '<EMAIL>', 
                profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Applicant Community User' LIMIT 1].Id, 
                timeZoneSidKey = 'America/Los_Angeles', 
                LocaleSidKey = 'en_US', 
                LanguageLocaleKey = 'en_US'
            ); 
        }

    }


 
    /**
     * @description future method to add new users, 
     * territory, and user assignments in the setup method 
     */
    @future
    private static void addSetupObjs(){
/*
        UserRole userrole = [Select Id, DeveloperName From UserRole Where DeveloperName = 'Site_Administrators' Limit 1];
        User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		adminUser.UserRoleId = userRole.Id;
        update adminUser;
        System.debug('---admin ' + adminUser);
        
        System.runAs(adminUser){
            
            UserRole stdRole = [Select Id From UserRole Where PortalType = 'None' Limit 1];
            UserRole portalRole = [Select Id From UserRole Where PortalType = 'CustomerPortal' Limit 1];
            Contact con = [Select Id, Name from Contact Limit 1];
    
            User u =  new User(
                alias = 'test',
                email = '<EMAIL>', 
                emailencodingkey = 'UTF-8', 
                firstName = 'test1',
                lastName = 'User', 
                userName = '<EMAIL>', 
                profileId = [SELECT ID FROM PROFILE WHERE NAME = 'System Administrator' LIMIT 1].Id, 
                timeZoneSidKey = 'America/Los_Angeles', 
                LocaleSidKey = 'en_US', 
                LanguageLocaleKey = 'en_US',
                UserRoleId = stdRole.Id
            ); 
    
            insert new User(
                alias = 'test2',
                communityNickname = 'test123',
                contactId = con.Id, 
                email = '<EMAIL>', 
                emailencodingkey = 'UTF-8', 
                firstName = 'test2',
                lastName = 'User', 
                userName = '<EMAIL>', 
                profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Applicant Community User' LIMIT 1].Id, 
                timeZoneSidKey = 'America/Los_Angeles', 
                LocaleSidKey = 'en_US', 
                LanguageLocaleKey = 'en_US'
            ); 
        }
*/
    }


    @IsTest  
    public static void getCartConfigs(){
        
        String cookieId = '2398410734';
        Id scId = [SELECT Id FROM Store_Configuration__c WHERE Name = 'EP Courses' ].Id;
        User commUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' ];

        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c(
            Cart_Key__c = cookieId + '.' + scId,
            Store_Configuration__c = scId
        );
        insert cart;
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c(
            pymt__Shopping_Cart__c = cart.Id
        );
        insert cartItem;

        Map<String, String> responseLoggedInNoCookie;
        Map<String, String> responseLoggedInWithCookie;

        System.runAs(commUser) {
            responseLoggedInNoCookie = CartController.getCart( scId, '' );
            responseLoggedInWithCookie = CartController.getCart( scId, cookieId );
        }
        Map<String, String> responseGuestNoCookie = CartController.getCart( scId, '' );
        Map<String, String> responseGuestWithCookie = CartController.getCart( scId, cookieId );
    
    } 


    @IsTest  
    public static void getObjectConfigs(){
        
        String cookieId = '2398410734';
        Store_SObject_Configuration__c soc = [SELECT Id, Store_Configuration__c FROM Store_Sobject_Configuration__c WHERE Name = 'Course' ];
        User commUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' ];
        hed__Course_Offering__c co = [SELECT Id FROM hed__Course_Offering__c WHERE Name = 'Course 1' ];

        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c(
            Cart_Key__c = cookieId + '.' + soc.Store_Configuration__c,
            Store_Configuration__c = soc.Store_Configuration__c
        );
        insert cart;
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c(
            Course_Offering__c = co.Id,
            pymt__Shopping_Cart__c = cart.Id
        );
        insert cartItem;


        Map<String, String> responseLoggedInNoCookie;
        Map<String, String> responseLoggedInWithCookie;

        System.runAs(commUser) {
            responseLoggedInNoCookie = CartController.getCartItemConfig( co.Id, soc.Id, '' );
            responseLoggedInWithCookie = CartController.getCartItemConfig( co.Id, soc.Id, cookieId );
        }
        Map<String, String> responseGuestNoCookie = CartController.getCartItemConfig( co.Id, soc.Id, '' );
        Map<String, String> responseGuestWithCookie = CartController.getCartItemConfig( co.Id, soc.Id, cookieId );
    
    } 


    /*@IsTest  
    public static void testAddToCart(){
        
        String cookieId = '2398410734';
        Store_SObject_Configuration__c soc = [SELECT Id, Store_Configuration__c FROM Store_Sobject_Configuration__c WHERE Name = 'Course' ];
        User commUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' ];
        hed__Course_Offering__c otherCo = [SELECT Id FROM hed__Course_Offering__c WHERE Name = 'Course 2' ];
        hed__Course_Offering__c co = [SELECT Id FROM hed__Course_Offering__c WHERE Name = 'Course 1' ];

        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c(
            Cart_Key__c = cookieId + '.' + soc.Store_Configuration__c,
            Store_Configuration__c = soc.Store_Configuration__c
        );
        insert cart;
        pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c(
            Course_Offering__c = otherCo.Id,
            pymt__Shopping_Cart__c = cart.Id
        );
        insert cartItem;

        Map<String, String> responseLoggedInNoCookie;
        Map<String, String> responseLoggedInWithCookie;

        System.runAs(commUser) {
            responseLoggedInNoCookie = CartController.addToCart( co.Id, soc.Id, cookieId );
        }
        Map<String, String> responseGuestWithCookie = CartController.addToCart( co.Id, soc.Id, cookieId );
        Map<String, String> responseGuestWithNewCookie = CartController.addToCart( otherCo.Id, soc.Id, null );
    
    } */
	
    @isTest
    public static void testProcessPayment(){
        pymt__PaymentX__c clientPymt = [SELECT Id, pymt__Billing_First_Name__c, pymt__Billing_Last_Name__c, pymt__Billing_Street__c, pymt__Billing_City__c,
                                                pymt__Billing_State__c, pymt__Billing_Country__c,pymt__Billing_Postal_Code__c, pymt__Amount__c 
                                        FROM pymt__PaymentX__c LIMIT 1];
        String cardNo = '****************';
        String cvd = '123';
        String expYr = '2026';
        String expMo = '04';
        String accountName = 'transaction_processing';
        
        test.startTest();
        	Map<String,String> responseMap = CartController.processPayment(clientPymt, cardNo, cvd, expYr, expMo, accountName);
        	//MonerisResponseWrapper mrw = CartController.processPayment1(clientPymt, cardNo, cvd, expYr, expMo);
        test.stopTest();
    }
    
    @isTest
    public static void testGetCardType(){
        List<String> cardTypes = new List<String>{'AMEX','VISA','MASTERCARD','DINERS','DISCOVER','OTHER'};
        test.startTest();
            for(String cardType : cardTypes){
                String response = cartController.getCardType(cardType);
            }
        test.stopTest();
    }
    
    @isTest
    public static void testRedirect(){
        pymt__PaymentX__c payment = [SELECT Id FROM pymt__PaymentX__c LIMIT 1];
        
        test.startTest();
        	CartController.redirectToField(payment.Id, 'Name');
        test.stopTest();
    }
    
    @isTest
    public static void testGetInvoiceId() {
        Test.startTest();
        List<Contact> listCon = [Select Id,Name from Contact LIMIT 1];
        Invoice__c invoice =  new Invoice__c(Invoice_Status__c = 'Open-Personal', Type__c = 'Events', Contact__c = listCon[0].Id); 
        insert invoice;
        Id invoiceId = CartController.getInvoiceId(listCon[0].Id, listCon[0].Id);
        System.assertEquals(invoice.Id, invoiceId);
        Test.stopTest();
    }
    
    @isTest
    public static void testCreateInvoiceId() {
        Test.startTest();
        List<Contact> listCon = [Select Id,Name from Contact LIMIT 1];        
        Id invoiceId = CartController.getInvoiceId(listCon[0].Id, listCon[0].Id);
        System.assertNotEquals(null, invoiceId);
        Test.stopTest();
    }
    
    @isTest
    public static void testGetEventOptOutURL() {
        Test.startTest();
        Map<String, String> mapEvents = CartController.getEventOptOutURL(null);
        System.assertEquals(2, mapEvents.size());
        Test.stopTest();
    }
    
    @isTest
    public static void testPaymentId() {
        Test.startTest();
        pymt__PaymentX__c payment = [Select Id from pymt__PaymentX__c LIMIT 1];
        CartController.PaymentWrapper paymentWrap = CartController.getPayment(payment.Id);
        System.assertNotEquals(null, paymentWrap);
        Test.stopTest();
    }
}