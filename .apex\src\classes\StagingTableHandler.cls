public class StagingTableHandler {

	public static list<Staging_Table__c> records;

	public static list<Staging_Object_Mapping__c> objs;
	public static map<string, Staging_Object_Mapping__c> objMap;
	public static map<string, list<Staging_Field_Mapping__c>> flds;
	public static map<string, Boolean> disabledUpdateflds;
	public static map<string, map<string, Staging_Relationship_Mapping__c>> rels;

	//CHRIS ADDED
	public static map<string, map<string, Schema.RecordTypeInfo>> recTypesByObj;

	public static map<integer, map<string, sObject>> itemsMap;
	public static map<integer, list<sObject>> recsToInsert;
	public static map<integer, list<sObject>> recsToUpdate;
	public static list<string> objectsHandled;

	public static void processBatch(list<Staging_Table__c> stagings) {
		records = stagings;
		System.debug('001records:== ' + records);

		// get order of the objects to load
		objs = [SELECT Id, Lookup_Populated_Behavior__c, Staging_Table_Lookup_Field_Name__c, Target_Object_API_Name__c, Load_Order__c, Update_Existing_Record__c FROM Staging_Object_Mapping__c ORDER BY Load_Order__c ASC];
		map<integer, string> loadOrder = new map<integer, string>();
		//CHRIS ADDED
		StagingTableHandler.objMap = new map<string, Staging_Object_Mapping__c>();
		StagingTableHandler.recTypesByObj = new map<string, map<string, Schema.RecordTypeInfo>>();

		for (Staging_Object_Mapping__c som : objs) {
			loadOrder.put(integer.valueOf(som.Load_Order__c), som.Target_Object_API_Name__c);
			objMap.put(som.Target_Object_API_Name__c, som);
		}

		// get fields to map
		flds = new map<string, list<Staging_Field_Mapping__c>>();
		disabledUpdateflds = new map<string, Boolean>();
		for (
				Staging_Field_Mapping__c sfm
				:
		[SELECT Id, Staging_Table_Field_API_Name__c, Target_Field_API_Name__c, Object_Mapping__r.Target_Object_API_Name__c, Default_Value__c, Active__c, Write_Nulls__c, Disable_Update__c FROM Staging_Field_Mapping__c WHERE Active__c = TRUE]) {

			//CHRIS ADDED
			if (sfm.Target_Field_API_Name__c.toLowerCase() == 'recordtype' &&
					!recTypesByObj.containsKey(sfm.Object_Mapping__r.Target_Object_API_Name__c))
				recTypesByObj.put(sfm.Object_Mapping__r.Target_Object_API_Name__c, Schema.getGlobalDescribe().get(
						sfm.Object_Mapping__r.Target_Object_API_Name__c).getDescribe().getRecordTypeInfosByDeveloperName());

			if (flds.containsKey(sfm.Object_Mapping__r.Target_Object_API_Name__c)) {
				flds.get(sfm.Object_Mapping__r.Target_Object_API_Name__c).add(sfm);
			} else {
				flds.put(sfm.Object_Mapping__r.Target_Object_API_Name__c, new list<Staging_Field_Mapping__c>{sfm});
			}
			String targetField = sfm.Target_Field_API_Name__c;
			disabledUpdateflds.put(targetField.toLowerCase(), sfm.Disable_Update__c);
		}
		System.debug('001flds:== ' + flds);
		System.debug('001disabledUpdateflds:== ' + disabledUpdateflds);

		// get relationships to map
		rels = new map<string, map<string, Staging_Relationship_Mapping__c>>();
		map<string, list<Staging_Relationship_Mapping__c>> srmsByExternalID =
				new map<string, list<Staging_Relationship_Mapping__c>>();
		for (
				Staging_Relationship_Mapping__c srm
				:
		[SELECT Id, Child_Object_Mapping__r.Target_Object_API_Name__c, Parent_Object_Mapping__r.Target_Object_API_Name__c, Parent_Object_API_Name__c, Related_To_Existing_Record__c, Relationship_Field_API_Name__c, External_ID_Field_API_Name__c, Staging_Table_Field_API_Name__c FROM Staging_Relationship_Mapping__c]) {
			if (srm.Related_To_Existing_Record__c) {
				if (srmsByExternalID.containsKey(srm.Child_Object_Mapping__r.Target_Object_API_Name__c)) {
					srmsByExternalID.get(srm.Child_Object_Mapping__r.Target_Object_API_Name__c).add(srm);
				} else {
					srmsByExternalID.put(srm.Child_Object_Mapping__r.Target_Object_API_Name__c,
							new list<Staging_Relationship_Mapping__c>{srm});
				}
			} else {
				string relatedObject = srm.Parent_Object_Mapping__r.Target_Object_API_Name__c;
				if (rels.containsKey(srm.Child_Object_Mapping__r.Target_Object_API_Name__c)) {
					rels.get(srm.Child_Object_Mapping__r.Target_Object_API_Name__c).put(relatedObject, srm);
				} else {
					rels.put(srm.Child_Object_Mapping__r.Target_Object_API_Name__c,
							new map<string, Staging_Relationship_Mapping__c>{
							relatedObject=>srm
							});
				}
			}
		}

		recsToInsert = new map<integer, list<sObject>>();
		//CHRIS ADDED
		recsToUpdate = new map<integer, list<sObject>>();
		itemsMap = new map<integer, map<string, sObject>>();

		integer i = 0;
		integer maxLoadNum = 0;
		for (Staging_Table__c staging : records) {
			itemsMap.put(i, new map<string, sObject>{
			'Staging_Table__c'=>staging
			});
			try {
				for (Staging_Object_Mapping__c som : objs) {
					// str = object API Name
					string str = som.Target_Object_API_Name__c;
					//CHRIS ADDED
					if (staging.Object_API_Names__c.contains(str) && (
							String.isBlank(som.Staging_Table_Lookup_Field_Name__c) ||
									staging.get(som.Staging_Table_Lookup_Field_Name__c) == null ||
									som.Lookup_Populated_Behavior__c == 'Re-evaluate' ||
									som.Lookup_Populated_Behavior__c == 'Use Record and Update')) {
						system.debug('--- object: ' + str);
						Schema.SObjectType recType = Schema.getGlobalDescribe().get(str);

						// get all fields for this object
						map<String, Schema.SObjectField> fieldMap = recType.getDescribe().fields.getMap();
						// create instance
						sObject rec = recType.newSObject();

						//CHRIS ADDED
						if (!String.isBlank(som.Staging_Table_Lookup_Field_Name__c) &&
										staging.get(som.Staging_Table_Lookup_Field_Name__c) != null &&
										som.Lookup_Populated_Behavior__c == 'Use Record and Update')
							rec.Id = (Id)staging.get(som.Staging_Table_Lookup_Field_Name__c);

						// populate any field values, if applicable
						System.debug('flds002: ' + flds);
						System.debug('str002: ' + str);
						if (flds.containsKey(str)) {
							for (Staging_Field_Mapping__c sfm : flds.get(str)) {
								if (string.isBlank(sfm.Staging_Table_Field_API_Name__c)) {
									rec = setDefaultValue(rec, sfm, fieldMap);
								} else {

									//CHRIS ADDED
									if (sfm.Target_Field_API_Name__c.toLowerCase() == 'recordtype')
										rec.put('RecordTypeId',
												recTypesByObj.get(sfm.Object_Mapping__r.Target_Object_API_Name__c).get(
														(String)staging.get(
																sfm.Staging_Table_Field_API_Name__c)).getRecordTypeId());
									else if (staging.get(sfm.Staging_Table_Field_API_Name__c) != null)
										rec.put(sfm.Target_Field_API_Name__c,
												staging.get(sfm.Staging_Table_Field_API_Name__c));

									// populate with the default value if the staging table field value is blank
									if (staging.get(sfm.Staging_Table_Field_API_Name__c) == null) {
										rec = setDefaultValue(rec, sfm, fieldMap);
									}
								}
							}
						}

						// populate any relationship values by external ID
						if (srmsByExternalID.containsKey(str)) {
							for (Staging_Relationship_Mapping__c srm : srmsByExternalID.get(str)) {
								if (!string.isBlank((string)staging.get(srm.Staging_Table_Field_API_Name__c))) {
									//system.debug('--- external id relationship: ' + srm.Relationship_Field_API_Name__c);

									Schema.SObjectType relRecType =
											Schema.getGlobalDescribe().get(srm.Parent_Object_API_Name__c);
									sObject relatedRecord = relRecType.newSObject();
									relatedRecord.put(srm.External_ID_Field_API_Name__c,
											staging.get(srm.Staging_Table_Field_API_Name__c));

									rec.putSObject(srm.Relationship_Field_API_Name__c, relatedRecord);
								}
							}
						}

						// add record to be created
						integer j = integer.valueOf(som.Load_Order__c);
						//CHRIS ADDED
						if (!String.isBlank(som.Staging_Table_Lookup_Field_Name__c) &&
										staging.get(som.Staging_Table_Lookup_Field_Name__c) != null &&
										som.Lookup_Populated_Behavior__c == 'Use Record and Update') {
							if (recsToUpdate.containsKey(j)) {
								recsToUpdate.get(j).add(rec);
							} else {
								recsToUpdate.put(j, new list<sObject>{rec});
							}
						} else {
							if (recsToInsert.containsKey(j)) {
								recsToInsert.get(j).add(rec);
							} else {
								recsToInsert.put(j, new list<sObject>{rec});
							}
						}

						// collect the record for reference later
						itemsMap.get(i).put(str, rec);
						// set maxLoadNum
						if (j > maxLoadNum) maxLoadNum = j;
						//CHRIS ADDED
					} else if (!String.isBlank(som.Staging_Table_Lookup_Field_Name__c) &&
									staging.get(som.Staging_Table_Lookup_Field_Name__c) != null &&
									som.Lookup_Populated_Behavior__c == 'Use Record and Don\'t Update') {
						Schema.SObjectType recType = Schema.getGlobalDescribe().get(str);
						SObject newSObj = recType.newSObject();
						newSObj.Id = (Id)staging.get(som.Staging_Table_Lookup_Field_Name__c);
						itemsMap.get(i).put(str, newSObj);
					}
				}
				staging.Audit_Reason__c = '';
				staging.Processing_Outcome__c = 'Instantiated';
			} catch (exception ex) {
				system.debug('--- error message (processBatch): ' + ex.getMessage());
				staging.Processing_Outcome__c = 'Error';
				staging.Audit_Reason__c =
						getAuditReason(staging.Audit_Reason__c, ex.getMessage()); //ex.getMessage() + '; ';
			}
			staging.Processing_Status__c = 'Processed';
			staging.Last_Processed_Date__c = system.now();
			i++;
		}

		system.debug('--- recsToInsert: ' + recsToInsert);
		System.debug('--- recsToUpdate: ' + recsToUpdate);
		System.debug('--- maxLoadNum: ' + maxLoadNum);
		objectsHandled = new list<string>();
		for (integer j = 0; j <= maxLoadNum; j++) {
			if (recsToInsert.containsKey(j)) {
				if (loadOrder.containsKey(j)) {
					list<sObject> sobjs = recsToInsert.get(j);
					system.debug('--- j: ' + j + ', Insert-recs: ' + sobjs);

					string thisObject = loadOrder.get(j); // api name of this object
/*					if (thisObject == 'Opportunity' && sobjs[0].get('Staging_Source__c') =='Executive Programs RFI'){
						continue;
					}*/
					boolean doUpdate = objMap.get(thisObject).Update_Existing_Record__c;

					// create these records
					saveRecords(sobjs, true, 1, thisObject, doUpdate);

					// set the relationships on this object
					setRelationships(thisObject);

					// add this object to the list of objects that have already been handled - todo: remove?
					objectsHandled.add(thisObject);
				}
			}
			//CHRIS ADDED
			if (recsToUpdate.containsKey(j)) {
				if (loadOrder.containsKey(j)) {
					list<sObject> sobjs = recsToUpdate.get(j);
					system.debug('--- j: ' + j + ', Update-recs: ' + sobjs);

					string thisObject = loadOrder.get(j); // api name of this object
					boolean doUpdate = objMap.get(thisObject).Update_Existing_Record__c;

					// create these records
					saveRecords(sobjs, false, 1, thisObject, doUpdate);

					// set the relationships on this object
					setRelationships(thisObject);

					// add this object to the list of objects that have already been handled - todo: remove?
					objectsHandled.add(thisObject);
				}
			}
		}

		// update staging table records
		Database.SaveResult[] stagingSave = Database.update(records, false);
		System.debug('Update001== ' + stagingSave);

		for (Database.SaveResult sr : stagingSave) {
			if (!sr.isSuccess()) {
				for (Database.Error err : sr.getErrors()) {
					system.debug('--- error: ' + err.getMessage());
				}
			}
		}
	}


	public static void saveRecords(list<sObject> itemsToHandle,
								   boolean isInsert,
								   integer iteration,
								   string sobjType,
								   boolean doUpdate) {
		System.debug('01--- saveRecords: ' + itemsToHandle);
		System.debug('02--- saveRecords: ' + isInsert);
		System.debug('03--- saveRecords: ' + iteration);
		System.debug('04--- saveRecords: ' + sobjType);
		System.debug('05--- saveRecords: ' + doUpdate);
		if (iteration <= 3) {
			doSaveRecords(itemsToHandle, isInsert, iteration, sobjType, doUpdate);
		}
	}

	public static void saveRecords(map<Id, sObject> itemsToHandleMap,
								   boolean isInsert,
								   integer iteration,
								   string sobjType,
								   boolean doUpdate) {
		System.debug('001--- saveRecords: ' + itemsToHandleMap);
		System.debug('002--- saveRecords: ' + isInsert);
		System.debug('003--- saveRecords: ' + iteration);
		System.debug('004--- saveRecords: ' + sobjType);
		System.debug('005--- saveRecords: ' + doUpdate);
		if (iteration <= 3) {
			list<sObject> itemsToHandle = itemsToHandleMap.values();
			System.debug('006--- saveRecords:== ' + itemsToHandle);
			doSaveRecords(itemsToHandle, isInsert, iteration, sobjType, doUpdate);
		}
	}

	public static void doSaveRecords(list<sObject> itemsToHandle,
									 boolean isInsert,
									 integer iteration,
									 string sobjType,
									 boolean doUpdate) {
		Database.SaveResult[] saveResults;
		list<sObject> itemsToHandleCopy = new list<sObject>();

		//Set case assignment rules
		Database.DMLOptions dmlOpts = new Database.DMLOptions();
		dmlOpts.optAllOrNone = false;
		dmlOpts.assignmentRuleHeader.useDefaultRule = true;
		dmlOpts.EmailHeader.triggerUserEmail = true;
		if (isInsert) {
			saveResults = Database.insert(itemsToHandle, dmlOpts);
			System.debug('==saveResultsInsert== ' + saveResults);
		} else {
			System.debug('==@@itemsToHandleUpdate001== ' + itemsToHandle);
			Map<String,Schema.SObjectType> gd = Schema.getGlobalDescribe();
			List<String> currentObjectFields = new List<String>();
			for (SObject s : itemsToHandle) {
				System.debug('==@@itemsToHandleUpdate002s== ' + s);
				System.debug('==@@disableUpdateflds== ' + disabledUpdateflds);
				Schema.SObjectType sot = gd.get(s.getSObjectType().getDescribe().getName());
				Schema.DescribeSObjectResult r = sot.getDescribe();
				Map<String, Schema.SObjectField> MapofField = r.fields.getMap();
				for(String fN : MapofField.keySet()) {
					System.debug('Field Name: '+ fN);
					System.debug('disabledUpdateflds.get(fN) ==: '+ disabledUpdateflds.get(fN.toLowerCase()));
					if (disabledUpdateflds.keySet().contains(fN.toLowerCase()) && disabledUpdateflds.get(fN) != null) {
						if (disabledUpdateflds.get(fN.toLowerCase()) == false) {
							currentObjectFields.add(fN);
						}
					}
				}
				System.debug('==@@currentObjectFields002f== ' + currentObjectFields);
				sObject t = sot.newSObject();
				t.Id = s.Id;
				System.debug('==@@itemsToHandleUpdate003== ' + t);
				System.debug('==@@currentObjectFields003== ' + currentObjectFields);
				for(String fieldName : currentObjectFields) {
						System.debug('Field Name: '+ fieldName);
						System.debug('disabledUpdateflds.get(fieldName) ==: '+ disabledUpdateflds.get(fieldName));
					    if (disabledUpdateflds.get(fieldName) == false && s.get(fieldName) != null) {
						System.debug('==@@fieldName002== ' + fieldName);
						System.debug('==@@s.get(fieldName)002== ' + s.get(fieldName));
						System.debug('==@@t.put(fieldName, s.get(fieldName))002== ' + t.put(fieldName, s.get(fieldName)));
							t.put(fieldName, s.get(fieldName));
						}
					System.debug('==@@itemsToHandleUpdate005== ' + t);
				}
				itemsToHandleCopy.add(t);
			}
			System.debug('==@@itemsToHandleCopy002== ' + itemsToHandleCopy);
			saveResults = Database.update(itemsToHandleCopy, false);
			System.debug('==@@saveResultsUpdate003== ' + saveResults);
		}

		map<Id, sObject> itemsToUpdate = new map<Id, sObject>();
		for (Integer i = 0; i < saveResults.size(); i++) {
			// iterate through all records
			Database.SaveResult sr = saveResults[i];
			sObject itemToUpdate = itemsToHandle[i];
			string recordId = '';
			if (!sr.isSuccess()) {
				// if there was an error, determine if it was caused by a duplicate record
				SYSTEM.DEBUG('dup01== '+itemToUpdate);
				SYSTEM.DEBUG('dupErr== '+sr.getErrors());
				for (Database.Error error : sr.getErrors()) {
					system.debug('--- error (database): ' + error.getMessage());
					SYSTEM.DEBUG('Err:= '+error.getMessage());
					SYSTEM.DEBUG('ErrField:= '+error.getFields());

					// process only duplicates and not errors (e.g. validation errors)
					if (error instanceof database.DuplicateError) {
						Database.DuplicateError duplicateError = (Database.DuplicateError)error;
						SYSTEM.DEBUG('duplicateError:== '+duplicateError.getDuplicateResult().getDuplicateRule());
						Datacloud.MatchResult matchResult = duplicateError.getDuplicateResult().getMatchResults()[0];
						Datacloud.MatchRecord[] matchRecords = matchResult.getMatchRecords();
						SYSTEM.DEBUG('matchResult:== '+matchResult);
						SYSTEM.DEBUG('matchRecords:== '+matchRecords);
						// capture id of record that duplicate error is thrown on. If there are more than one, match on the first duplicate found.
						if (matchRecords.size() > 0) {
							recordId = matchRecords[0].getRecord().Id;
						}
						System.debug('---@@@001 recordId: ' + recordId);
						// set id of record to allow update instead
						if (!string.isBlank(recordId)) {
							if (doUpdate) {
								itemToUpdate.Id = recordId;
								itemsToUpdate.put(recordId, itemToUpdate);
							} else {
								// create new instance to avoid updating any field values
								Schema.SObjectType recType = Schema.getGlobalDescribe().get(sobjType);
								sObject existingItemToUpdate = recType.newSObject();
								existingItemToUpdate.Id = recordId;
								itemsToUpdate.put(recordId, existingItemToUpdate);
							}
							System.debug('---@@@001 itemsToUpdate: ' + itemsToUpdate);
						}
					} else {
						string errorMessage = error.getMessage();
						System.debug('--- error (database): ' + errorMessage);
						// if the duplicate error is thrown on a field, then proceed using the duplicate record id
						if (errorMessage.contains('duplicate') && errorMessage.contains('id: ')) {
							recordId = errorMessage.substringAfter('id: ').split(' ')[0];
							// set id of record to allow update instead
							if (!string.isBlank(recordId)) {
								//itemToUpdate.Id = recordId;
								//itemsToUpdate.put(recordId, itemToUpdate);
								if (doUpdate) {
									itemToUpdate.Id = recordId;
									itemsToUpdate.put(recordId, itemToUpdate);
								} else {
									// create new instance to avoid updating any field values
									Schema.SObjectType recType = Schema.getGlobalDescribe().get(sobjType);
									sObject existingItemToUpdate = recType.newSObject();
									existingItemToUpdate.Id = recordId;
									itemsToUpdate.put(recordId, existingItemToUpdate);
								}
							}
							System.debug('---@@@002 itemsToUpdate: ' + itemsToUpdate);
						} else {
							// update audit reason on the staging table record
							records[i].Audit_Reason__c = getAuditReason(records[i].Audit_Reason__c,
									errorMessage); //error.getMessage() + '; ';
							if (records[i].Processing_Outcome__c == 'Success') {
								records[i].Processing_Outcome__c = 'Partial Insert';
							} else {
								records[i].Processing_Outcome__c = 'Error';
							}
						}
					}
				}
			} else {
				//CHRIS ADDED
				recordId = sr.getId();
				if (records[i].Processing_Outcome__c == 'Instantiated') {
					records[i].Processing_Outcome__c = 'Success';
				}
			}

			// set contact or lead ID on the staging table record
			if (isInsert && !string.isBlank(recordId)) {
				system.debug('02== ' + objMap);
				system.debug('03== ' + objMap.keyset());
				system.debug('04== ' + sobjType);
				if (sobjType == 'Contact') {
					records[i].Contact__c = recordId;
					//Stamping the subscription membership matching key:
					//if(records[i].Subscription_Key__c != null) records[i].Subscription_Membership_Matching_Key__c = String.valueOf(recordId) + String.valueOf(records[i].Subscription_Key__c);
				} else if (sobjType == 'Lead') {
					records[i].Lead__c = recordId;
					//CHRIS ADDED
				} else if (!String.isBlank(objMap.get(sobjType).Staging_Table_Lookup_Field_Name__c)) {
					records[i].put(objMap.get(sobjType).Staging_Table_Lookup_Field_Name__c, recordId);
				}
			}
			recordId = '';
		}

		// if any duplicates were detected, update records instead
		System.debug('Duplicate001 = ' + itemsToUpdate);
		System.debug('Duplicate002 = ' + sobjType);
		System.debug('Duplicate003 = ' + doUpdate);
		if (itemsToUpdate.size() > 0) saveRecords(itemsToUpdate, false, iteration + 1, sobjType, doUpdate);
	}

	public static void setRelationships(string relationshipObject) {
		// relationshipObject = object api name of records being updated
		system.debug('--- relationshipObject: ' + relationshipObject);
		for (Integer i : itemsMap.keySet()) {
			//sObject staging = itemsMap.get(i).get('Staging_Table__c');
			Staging_Table__c staging = records[i];
			for (Staging_Object_Mapping__c som : objs) {
				// str = object API Name
				try {
					string str = som.Target_Object_API_Name__c;
					if (((string)staging.get('Object_API_Names__c')).contains(str) && !objectsHandled.contains(str)) {
						system.debug('--- object: ' + str);
						sObject rec = itemsMap.get(i).get(str);
						if (rels.containsKey(str)) {
							// populate any relationship values, if applicable
							if (rels.get(str).containsKey(relationshipObject)) {
								Staging_Relationship_Mapping__c srm = rels.get(str).get(relationshipObject);
								system.debug('--- relationship: ' + srm.Relationship_Field_API_Name__c);
								if (!srm.Related_To_Existing_Record__c) {
									system.debug('--- relationship field name: ' + srm.Relationship_Field_API_Name__c +
											', relationship field value: ' + itemsMap.get(i).get(
											srm.Parent_Object_Mapping__r.Target_Object_API_Name__c).get('Id'));
									rec.put(srm.Relationship_Field_API_Name__c, itemsMap.get(i).get(
											srm.Parent_Object_Mapping__r.Target_Object_API_Name__c).get('Id'));
								}
							}
						}
					}
				} catch (exception ex) {
					system.debug('--- error message (setRelationships): ' + ex.getMessage());
					staging.put('Audit_Reason__c', getAuditReason((string)staging.get('Audit_Reason__c'),
							ex.getMessage())); //staging.get('Audit_Reason__c') + '; ' + ex.getMessage());
					staging.put('Processing_Outcome__c', 'Error');
				}
			}
		}
	}

	public static sObject setDefaultValue(sObject rec,
										  Staging_Field_Mapping__c sfm,
										  map<String, Schema.SObjectField> fieldMap) {
		if (String.isBlank(sfm.Default_Value__c) && sfm.Write_Nulls__c != true){
            return rec;
        }
		String fieldType = string.valueOf(fieldMap.get(sfm.Target_Field_API_Name__c).getDescribe().getType());
		if (fieldType == 'Date') {
			if (!string.isEmpty(sfm.Default_Value__c)) {
				rec.put(sfm.Target_Field_API_Name__c, date.parse(sfm.Default_Value__c));
			}
			//CHRIS ADDED
		} else if (sfm.Target_Field_API_Name__c.toLowerCase() == 'recordtype') {
			rec.put('RecordTypeId', recTypesByObj.get(sfm.Object_Mapping__r.Target_Object_API_Name__c).get(
					sfm.Default_Value__c).getRecordTypeId());
		} else {
			rec.put(sfm.Target_Field_API_Name__c, sfm.Default_Value__c);
		}
		return rec;
	}

	public static string getAuditReason(string auditReason, string errorMsg) {
		auditReason += errorMsg + '; ';
		if (auditReason.length() <= 210) {
			return auditReason;
		} else {
			return auditReason.substring(0, 200);
		}
	}
}