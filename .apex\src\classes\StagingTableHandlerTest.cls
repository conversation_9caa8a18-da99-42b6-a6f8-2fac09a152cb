@isTest
public class StagingTableHandlerTest {

    static testMethod void validateLoad() {
        test.startTest();
        
        addOtherData();
        addStagingSetup();
        addStagingRecords();
        
        Id batchJobId = Database.executeBatch(new StagingTableBatchScheduler(), 10);

        List<Staging_Table__c> st1 = [SELECT Id, Last_Name__c FROM Staging_Table__c WHERE Last_Name__c='TestLast4'];
        System.debug(st1);
        st1[0].Last_Name__c = 'TestLast5';
        Update st1;

        List<Contact> c1 = [SELECT Id, FirstName, LastName FROM Contact];
        System.debug(c1);
        
    }

    @isTest(SeeAllData = True)
    private static void testStaging() {
        Date newBirthday = Date.newInstance(1990, 5, 15);
        //Contact c1 = [SELECT Id, FirstName, LastName FROM Contact WHERE hed__AlternateEmail__c='<EMAIL>'];
        Contact c1 = [SELECT Id, FirstName, LastName FROM Contact WHERE hed__AlternateEmail__c='<EMAIL>'];
        test.startTest();
        Staging_Table__c st7 = new Staging_Table__c(
                Access__c            = 'Public',
                Objects_To_Upload__c = 'Contact; Opportunity',
                Processing_Status__c = 'Unprocessed',
                First_Name__c = 'test',
                Last_Name__c = 'terst',
                Work_Email__c = '<EMAIL>',
                Mailing_Country__c = 'Canada',
                Opportunity_Name__c = 'Test Opty 2',
                Opportunity_Stage__c = 'Lead'
        );
        insert st7;

        test.stopTest();
        //Contact c2 = [SELECT Id, FirstName, LastName FROM Contact WHERE hed__AlternateEmail__c='<EMAIL>'];
        Contact c2 = [SELECT Id, FirstName, LastName FROM Contact WHERE hed__AlternateEmail__c='<EMAIL>'];
        System.debug('Contact 01= '+c2);
    }

    
    public static void addStagingRecords() {
        list<Staging_Table__c> stsToInsert = new list<Staging_Table__c>();
        
        Staging_Table__c st1 = new Staging_Table__c(
            Access__c            = 'Public',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            First_Name__c = 'Joe',
            Last_Name__c = 'TestLast1',
            Personal_Email__c = '<EMAIL>'
        );
        //stsToInsert.add(st1);
        
        Staging_Table__c st2 = new Staging_Table__c(
            Access__c            = 'Public',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            First_Name__c = 'Joe',
            Last_Name__c = 'TestLast1',
            Personal_Email__c = '<EMAIL>'
        );
        //stsToInsert.add(st2);
        
        Staging_Table__c st3 = new Staging_Table__c(
            Access__c            = 'Public',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            Last_Name__c = 'TestLast2',
            Email__c = '<EMAIL>',
            Opportunity_Name__c = 'Case Subject'
        );
        //stsToInsert.add(st3);
        
        Staging_Table__c st4 = new Staging_Table__c(
            Access__c            = 'Public',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            Last_Name__c = 'TestLast3',
            Email__c = '<EMAIL>',
            Mailing_Country__c = 'Canada',
            Birthdate__c = Date.newInstance(1990, 1, 1)
        );
        //stsToInsert.add(st4);

        Staging_Table__c st5 = new Staging_Table__c(
            Access__c            = 'Public',
            Objects_To_Upload__c = 'Contact; Case',
            Processing_Status__c = 'Unprocessed',
            Last_Name__c = 'TestLast3',
            Email__c = '<EMAIL>',
            Mailing_Country__c = 'Canada'
        );
        //stsToInsert.add(st5);
        
        Staging_Table__c st6 = new Staging_Table__c(
            Access__c            = 'Public',
            Objects_To_Upload__c = 'Contact; Opportunity',
            Processing_Status__c = 'Unprocessed',
            Last_Name__c = 'TestLast3',
            Email__c = '<EMAIL>',
            Mailing_Country__c = 'Canada',
            Opportunity_Name__c = 'Test Opty 1',
            Opportunity_Stage__c = 'Lead'
        );
        //stsToInsert.add(st6);

        Staging_Table__c st7 = new Staging_Table__c(
                Access__c            = 'Public',
                Objects_To_Upload__c = 'Contact; Opportunity',
                Processing_Status__c = 'Unprocessed',
                First_Name__c = 'TestFirst4',
                Last_Name__c = 'TestLast4',
                Personal_Email__c = '<EMAIL>',
                Mailing_Country__c = 'Canada',
                Opportunity_Name__c = 'Test Opty 2',
                Opportunity_Stage__c = 'Lead'
        );

        stsToInsert.add(st7);
        database.insert(stsToInsert);
    }
    
    public static void addOtherData() {
        Contact con = new Contact(
            FirstName = 'Joe',
            LastName = 'Smith',
            hed__alternateemail__c = '<EMAIL>'
        );
        insert con;
    }
    
    public static void addStagingSetup() {
        Staging_Object_Mapping__c som_contact = new Staging_Object_Mapping__c(
            Name = 'Contact',
            Target_Object_API_Name__c = 'Contact',
            Staging_Table_Lookup_Field_Name__c = 'Contact__c',
            Lookup_Populated_Behavior__c = 'Use Record and Don\'t Update',
            Load_Order__c = 1
        );
        insert som_contact;
        
        Staging_Object_Mapping__c som_case = new Staging_Object_Mapping__c(
            Name = 'Case',
            Target_Object_API_Name__c = 'Case',
            Load_Order__c = 2
        );
        insert som_case;
        
        Staging_Object_Mapping__c som_Opty = new Staging_Object_Mapping__c(
            Name = 'Opportunity',
            Target_Object_API_Name__c = 'Opportunity',
            Staging_Table_Lookup_Field_Name__c = 'Use Record and Update',
            Load_Order__c = 3
        );
        insert som_Opty;
        
        // insert relationships
        list<Staging_Relationship_Mapping__c> relationships = new list<Staging_Relationship_Mapping__c>();
        relationships.add(
            new Staging_Relationship_Mapping__c(
                Parent_Object_Mapping__c = som_contact.Id,
                Child_Object_Mapping__c = som_case.Id,
                Relationship_Field_API_Name__c = 'ContactId'
            )
        );
        relationships.add(
            new Staging_Relationship_Mapping__c(
                Parent_Object_Mapping__c = som_contact.Id,
                Child_Object_Mapping__c = som_case.Id,
                Relationship_Field_API_Name__c = 'ContactId',
                Related_To_Existing_Record__c = true
            )
        );
        relationships.add(
            new Staging_Relationship_Mapping__c(
                Parent_Object_Mapping__c = som_contact.Id,
                Child_Object_Mapping__c = som_Opty.Id,
                Relationship_Field_API_Name__c = 'ContactId',
                Related_To_Existing_Record__c = true
            )
        );
        insert relationships;
        
        // insert field mappings
        list<Staging_Field_Mapping__c> fields = new list<Staging_Field_Mapping__c>();
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_contact.Id, Staging_Table_Field_API_Name__c = 'Last_Name__c', Target_Field_API_Name__c = 'LastName', Disable_Update__c = true, Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_contact.Id, Staging_Table_Field_API_Name__c = 'First_Name__c', Target_Field_API_Name__c = 'FirstName', Disable_Update__c = false, Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_contact.Id, Staging_Table_Field_API_Name__c = 'Personal_Email__c', Target_Field_API_Name__c = 'hed__AlternateEmail__c', Disable_Update__c = false, Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_contact.Id, Staging_Table_Field_API_Name__c = 'Mailing_Country__c', Target_Field_API_Name__c = 'MailingCountry', Disable_Update__c = false, Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_contact.Id, Staging_Table_Field_API_Name__c = 'Birthdate__c', Target_Field_API_Name__c = 'Birthdate', Disable_Update__c = true, Write_Nulls__c=true, Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_case.Id, Target_Field_API_Name__c = 'Origin', Default_Value__c = 'Web', Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_case.Id, Target_Field_API_Name__c = 'Status', Default_Value__c = 'New', Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_case.Id, Staging_Table_Field_API_Name__c = 'Opportunity_Name__c', Target_Field_API_Name__c = 'Subject', Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_Opty.Id, Staging_Table_Field_API_Name__c = 'Opportunity_Name__c', Target_Field_API_Name__c = 'Name', Active__c = TRUE));
        fields.add(new Staging_Field_Mapping__c(Object_Mapping__c = som_Opty.Id, Staging_Table_Field_API_Name__c = 'Opportunity_Stage__c', Target_Field_API_Name__c = 'StageName', Active__c = TRUE));
        
        insert fields; 
    }
}