public class MonerisMockResponse implements HttpCalloutMock {

    public HTTPResponse respond(HTTPRequest req) {
        system.debug('req'+ req.getMethod());
        string resp = '<?xml version="1.0" standalone="yes"?><response><receipt><ReceiptId>Payment:2020-06-02 07:48:33</ReceiptId><ReferenceNum>660109300014588340</ReferenceNum><ResponseCode>027</ResponseCode><ISO>01</ISO><AuthCode>775244</AuthCode><TransTime>03:48:33</TransTime><TransDate>2020-06-02</TransDate><TransType>00</TransType><Complete>true</Complete><Message>APPROVED *=</Message><TransAmount>10.11</TransAmount><CardType>V</CardType><TransID>337281-0_15</TransID><TimedOut>false</TimedOut><BankTotals>null</BankTotals><Ticket>null</Ticket><CorporateCard>false</CorporateCard><AvsResultCode>B</AvsResultCode><ITDResponse>null</ITDResponse><CvdResultCode>1M</CvdResultCode><IsVisaDebit>false</IsVisaDebit></receipt></response>';
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        res.setBody(resp);
        res.setStatusCode(200);
        return res;
    }
}