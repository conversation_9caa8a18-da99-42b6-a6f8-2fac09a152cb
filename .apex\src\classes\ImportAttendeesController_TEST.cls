@IsTest
private class ImportAttendeesController_TEST {
	@TestSetup
	private static void testDataSetup() {
		Id fulltimeId = ApplicationService.FTandSpecializedRTId;
		Account testAccount = new Account(Name = 'Test Account');
		insert testAccount;

		Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>',
				AccountId = testAccount.Id);
		insert testContact;

		hed__Term__c trm = new hed__Term__c(hed__Account__c = testAccount.Id, name = 'Spring 2025');
		insert trm;

		Cohort__c cohort = new Cohort__c(
				Name = 'FTMBA - 2025',
				Start_Term__c = trm.Id,
				Program__c = testAccount.Id,
				Orientation_Date__c = Date.valueOf('2024-04-09 09:30:40'),
				Key__c = 'FTMBA - 2025'
		);
		insert cohort;

		Program_Term_Availability__c pta = new Program_Term_Availability__c(
				Active__c = true,
				Program__c = testAccount.Id,
				Cohort__c = cohort.Id,
				Program_Start_Date__c = Date.valueOf('2022-12-09 10:15:30'),
				Program_End_Date__c = Date.valueOf('2024-04-09 09:30:40'),
				Term__c = trm.Id
		);
		insert pta;

		hed__Application__c app = new hed__Application__c(
				Program_Term_Availability__c = pta.Id,
				hed__Application_Status__c = 'In Progress',
				RecordTypeId = fulltimeId,
				hed__Applying_To__c = testAccount.Id,
				hed__Term__c = trm.Id
		);
		insert app;

		Subscription__c sc = new Subscription__c();
		sc.Name = 'Full time MBA information';
		insert sc;

		Subscription_Membership__c sm1 = new Subscription_Membership__c(Subscription_Status__c = 'Subscribed', Contact__c = testContact.Id, Subscription__c = sc.Id);
		insert sm1;

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c =
				'$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.Start_Date__c = Date.today().addDays(30);
		event.End_Date__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.Venue_Type__c = 'In-Person';
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.evt__Max_Attendees__c = 100;
		event.Tags__c = 'Strategic Communications';
		event.Thumbnail_Image__c =
				'<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';

		List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
		listOfEventToInsert.add(event);

		insert listOfEventToInsert;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = listOfEventToInsert[0].Id;
		fee.evt__Amount__c = 0.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = testContact.Id;
		at.evt__Invitation_Status__c = 'Registered';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.evt__Event__c = listOfEventToInsert[0].Id;
		at.evt__Reg_Email__c = '<EMAIL>';
		at.evt__Reg_First_Name__c = 'John';
		at.evt__Reg_Last_Name__c = 'Doe';
		at.Industry__c = 'Technology';

		insert at;

	}

	@isTest
	static void testGetEventTickets() {
		Test.startTest();
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];

		List<evt__Event_Fee__c> eventTickets = ImportAttendeesController.getEventTickets(testEvent.Id);
		System.assertEquals(1, eventTickets.size());
		Test.stopTest();
	}

	@isTest
	static void testImportAttendees1() {
		Test.startTest();
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Event_Fee__c testEventFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		Program_Term_Availability__c pta = [SELECT Id, Term__c FROM Program_Term_Availability__c LIMIT 1];

		String eventId = testEvent.Id;
		String eventFee = testEventFee.Id;

		List<evt__Attendee__c> attendeesData = new List<evt__Attendee__c>();
		evt__Attendee__c attendee1 = new evt__Attendee__c();
		attendee1.evt__Reg_Email__c = '<EMAIL>';
		attendee1.evt__Reg_First_Name__c = 'John';
		attendee1.evt__Reg_Last_Name__c = 'Doe';
		attendee1.Industry__c = 'Technology';
		attendeesData.add(attendee1);

		String mapData = '[["<EMAIL>","Full time MBA information"]]';

		String result = ImportAttendeesController.importAttendees(eventId, eventFee, attendeesData, mapData);
		Test.stopTest();
		System.assertEquals('Success', result);
	}

	@isTest
	static void testImportAttendees2() {
		Test.startTest();
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Event_Fee__c testEventFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		Program_Term_Availability__c pta = [SELECT Id, Term__c FROM Program_Term_Availability__c LIMIT 1];

		String eventId = testEvent.Id;
		String eventFee = testEventFee.Id;

		List<evt__Attendee__c> attendeesData = new List<evt__Attendee__c>();
		evt__Attendee__c attendee1 = new evt__Attendee__c();
		attendee1.evt__Reg_Email__c = '<EMAIL>';
		attendee1.evt__Reg_First_Name__c = 'John';
		attendee1.evt__Reg_Last_Name__c = 'Doe';
		attendee1.Industry__c = 'Technology';
		attendeesData.add(attendee1);

		String mapData = '[["<EMAIL>","Full time MBA information"]]';

		String result = ImportAttendeesController.importAttendees(eventId, eventFee, attendeesData, mapData);
		Test.stopTest();
		System.assertEquals('Success', result);
	}

	@isTest
	static void testImportAttendees3() {
		Test.startTest();
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Event_Fee__c testEventFee = [SELECT Id FROM evt__Event_Fee__c LIMIT 1];
		Program_Term_Availability__c pta = [SELECT Id, Term__c FROM Program_Term_Availability__c LIMIT 1];

		String eventId = testEvent.Id;
		String eventFee = testEventFee.Id;

		evt__Attendee__c attendee2 = new evt__Attendee__c();
		attendee2.evt__Reg_Email__c = '<EMAIL>';
		attendee2.evt__Reg_First_Name__c = 'John';
		attendee2.evt__Reg_Last_Name__c = 'Doe';
		attendee2.Industry__c = 'Technology';
		attendee2.evt__Event_Fee__c = testEventFee.Id;
		attendee2.evt__Event__c = testEvent.Id;
		attendee2.evt__Contact__c = testContact.Id;
		insert attendee2;

		List<evt__Attendee__c> attendeesData = new List<evt__Attendee__c>();
		evt__Attendee__c attendee1 = new evt__Attendee__c();
		attendee1.evt__Reg_Email__c = '<EMAIL>';
		attendee1.evt__Reg_First_Name__c = 'John';
		attendee1.evt__Reg_Last_Name__c = 'Doe';
		attendee1.Industry__c = 'Technology';
		attendeesData.add(attendee1);

		String mapData = '[["<EMAIL>","Full time MBA information"]]';

		String result = ImportAttendeesController.importAttendees(eventId, eventFee, attendeesData, mapData);
		Test.stopTest();
		System.assertEquals('Success', result);
	}

	public void testCapitalizeFirstLetter_ValidInput() {
		// Positive test case
		System.assertEquals('Hello', ImportAttendeesController.capitalizeFirstLetter('hello'));
		System.assertEquals('World', ImportAttendeesController.capitalizeFirstLetter('world'));
		System.assertEquals('Java', ImportAttendeesController.capitalizeFirstLetter('java'));
	}

	@isTest
	static void testGetCountriesValues() {
		// Execute method
		Test.startTest();
		List<String> result = ImportAttendeesController.getCountriesValues();
		Test.stopTest();
	}
}