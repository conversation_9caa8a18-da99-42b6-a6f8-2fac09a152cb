public class GetLoggedInContact {
  @InvocableMethod(label='Get Logged In Contact' description='Checks for a contact Id on the current logged in user.')
  public static List<Id> getLoggedInContactId() {
      List<Id> results = new List<Id>{};
      Id loggedInContactId = pymt.Util.getLoggedInContactId();
      if (loggedInContactId <> null) {
          results.add(loggedInContactId);
      }
     return results;
      
  }
}