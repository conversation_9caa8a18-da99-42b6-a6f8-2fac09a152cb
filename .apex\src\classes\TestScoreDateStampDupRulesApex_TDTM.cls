/** 
* @description    Trigger helper class for date stamping to for duplicate/matching rule.
* <AUTHOR> 
* @version        1.0 
*/
global class TestScoreDateStampDupRulesApex_TDTM extends hed.TDTM_Runnable{
	/**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Interaction records from trigger new 
     * @param oldList the list of Interaction records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Interactions 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
    	hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        List<hed__Test__c> tstLst = new List<hed__Test__c>();
        Map<Id,hed__Test__c> IdtoTestMap = new Map<Id,hed__Test__c>();
        Set<ID> testIds = new Set<ID>();
        //Stamp Date
        if ( triggerAction == hed.TDTM_Runnable.Action.BeforeInsert || triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate) {
        	System.Debug('<<<<<TRIGGERFIRED>>>>>>'+newlist);
            for(hed__Test_Score__c tstScore : (List<hed__Test_Score__c>) newList) {
                if(tstScore.hed__Test__c!=null)
                	testIds.add(tstScore.hed__Test__c);
            }
            tstLst = [SELECT Id,hed__Test_Date__c,hed__Test_Type__c,hed__Contact__c from hed__Test__c where Id=:testIds];
            for(hed__Test__c tst : tstLst){
                IdtoTestMap.put(tst.Id,tst);
            }
            for(hed__Test_Score__c affltn : (List<hed__Test_Score__c>) newList) {
                if(IdtoTestMap.ContainsKey(affltn.hed__Test__c)){
                    if(affltn.hed__Test__c!=null && IdtoTestMap.get(affltn.hed__Test__c).hed__Test_Date__c!=null)
                        affltn.Test_Score_Matching_Key__c = String.ValueOf(IdtoTestMap.get(affltn.hed__Test__c).hed__Test_Date__c);
                    if(affltn.hed__Test__c!=null && IdtoTestMap.get(affltn.hed__Test__c).hed__Test_Type__c!=null)
                        affltn.Test_Type_Text__c = String.ValueOf(IdtoTestMap.get(affltn.hed__Test__c).hed__Test_Type__c);
                    if(affltn.hed__Test__c!=null && IdtoTestMap.get(affltn.hed__Test__c).hed__Contact__c!=null)
                        affltn.Contact__c = IdtoTestMap.get(affltn.hed__Test__c).hed__Contact__c;
                }
            }
        } 
        return dmlWrapper;
    }
}