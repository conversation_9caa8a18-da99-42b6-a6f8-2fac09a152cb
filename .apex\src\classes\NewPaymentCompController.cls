public class NewPaymentCompController {
    
    @AuraEnabled
    public static Id  getPaymentDone(pymt__PaymentX__c pymt,string CVD,string expD,string cardNo){
        try{
            
            CardWrapperClass cardDetails = new CardWrapperClass();
            cardDetails.CVD = CVD;     
            cardDetails.expireDate=expD;
            cardDetails.Amount = Double.valueOf(pymt.pymt__Amount__c);
            cardDetails.cardNo = cardNo;
            pymt.pymt__Status__c = 'Scheduled';
            cardDetails.pymt = pymt;
            system.debug('card= '+pymt);
            system.debug('Type__c= '+cardDetails.pymt.pymt__Payment_Type__c);
            String res = NUll;
            
            if(cardDetails.pymt.pymt__Payment_Type__c =='Credit Card'){
                res =  MonerisConnectClass.monerisPurchase(cardDetails);
                
            }
            if(cardDetails.pymt.pymt__Payment_Type__c =='Credit Card' && cardNo != null){
                pymt.pymt__Last_4_Digits__c = cardNo.right(4); 
            }   
            //pymt__PaymentX__c pymtIns =  cardDetails.pymt;
            insert pymt;
            id pid = pymt.Id;
            system.debug('pid='+pid);
            if(res != Null){
                system.debug('payment res==> '+ res);
                String jsonContent = XmlParser.xmlToJson(res);
                system.debug('jsonContent=> '+jsonContent);
                MonerisResponseWrapper response = (MonerisResponseWrapper) json.deserialize(jsonContent, MonerisResponseWrapper.class);
                pymt__PaymentX__c pymtC = [
                    SELECT 
                    Name, pymt__Transaction_Id__c, Order_Id__c,pymt__Date__c,
                    Payment_Response__c, pymt__Check_Number__c,pymt__Status__c
                    FROM pymt__PaymentX__c
                    WHERE Id=:pid LIMIT 1
                ];
                if(pymtC.Payment_Response__c == null){
                    pymtC.Payment_Response__c = '';
                }
                if(response.response.receipt.ReceiptId != null && response.response.receipt.ReceiptId !='Null' && response.response.receipt.Message.contains('APPROVED')){
                    pymtC.pymt__Transaction_Id__c = response.response.receipt.TransID;
                    pymtC.Order_Id__c = response.response.receipt.ReceiptId;
                    //pymt.pymt__Check_Number__c = response.response.receipt.ReferenceNum;
                    pymtC.Payment_Response__c += '\n Payment Success:'+string.valueOf(response.response)+'\n------------------\n';
                    pymtC.pymt__Status__c = 'Completed';
                    pymtC.pymt__Date__c = date.today(); //Updated on 31/07
                }
                else if(response.response.receipt.ReceiptId != null && response.response.receipt.Message.contains('DECLINED')){
                    pymtC.pymt__Transaction_Id__c = response.response.receipt.TransID;
                    pymtC.Order_Id__c = response.response.receipt.ReceiptId;
                    pymtC.Payment_Response__c += '\n Payment Declined:'+string.valueOf(response.response)+'\n------------------\n';
                    pymtC.pymt__Status__c = 'Declined';
                }
                else{
                    pymtC.Payment_Response__c += '\n Payment Error:'+string.valueOf(response.response)+'\n------------------\n';
                }
                system.debug('pymt==> '+pymtC);
                update pymtC;
                //Update Payment Record/
                system.debug('response= '+response);
            }
            
            return pymt.Id;
        }catch(Exception e){
            
            throw new AuraHandledException('Exception :'+e.getMessage());           
            
        }
        // return null;
        
    }
    
    @AuraEnabled
    public static CardWrapperClass getContactDetails(string ContactId){//Id Con //string ContactId
        system.debug('con--' +ContactId);
        List<String> conId = (List<String>)System.JSON.deserialize(ContactId, List<String>.class);
        system.debug('con--' +conId[0] ) ;
        ContactId = conId[0];//'0034c000001xfyeAAA';
        system.debug('con--' +ContactId);
        Contact selContact = [SELECT firstName,lastname, MailingCity, MailingCountry, MailingCountryCode,
                              MailingPostalCode, MailingState, MailingStreet,Email
                              FROM Contact where Id=: ContactId ];
        CardWrapperClass cardDetails = new CardWrapperClass();
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        pymt.pymt__Contact__c = ContactId;
        pymt.pymt__Billing_City__c = selContact.MailingCity;
        pymt.pymt__Billing_Country__c = selContact.MailingCountry;
        pymt.pymt__Billing_Street__c = selContact.MailingStreet;
        pymt.pymt__Billing_Postal_Code__c = selContact.MailingPostalCode;
        pymt.pymt__Billing_Company__c = '';
        pymt.pymt__Billing_State__c = selContact.MailingState;
        pymt.pymt__Billing_First_Name__c = selContact.FirstName;
        pymt.pymt__Billing_Last_Name__c = selContact.LastName;
        pymt.pymt__Billing_Email__c = selContact.Email;
        cardDetails.pymt = pymt;
        return cardDetails;
    }
    
}