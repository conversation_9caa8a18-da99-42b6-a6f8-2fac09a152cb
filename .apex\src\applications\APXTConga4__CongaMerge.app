<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <defaultLandingTab>APXTConga4__CreateStep</defaultLandingTab>
    <description>Conga Composer makes it easy for salesforce.com customers to create sophisticated documents, presentations and reports using data from any standard or custom object.</description>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Conga Composer</label>
    <logo>APXTConga4__Conga/APXTConga4__CongaLogo.png</logo>
    <tabs>APXTConga4__CreateStep</tabs>
    <tabs>APXTConga4__Conga_Composer_Setup</tabs>
    <tabs>APXTConga4__About_Conga_Composer</tabs>
    <tabs>APXTConga4__Conga_Solution__c</tabs>
    <tabs>APXTConga4__Conga_Template__c</tabs>
    <tabs>APXTConga4__Conga_Email_Template__c</tabs>
    <tabs>APXTConga4__Conga_Merge_Query__c</tabs>
    <tabs>APXTConga4__CongaMerge</tabs>
    <tabs>APXTConga4__Composer_QuickMerge__c</tabs>
    <tabs>APXTConga4__Conga_Solution_Collection</tabs>
    <tabs>APXTConga4__Document_History__c</tabs>
    <tabs>standard-report</tabs>
    <tabs>APXTConga4__Conga_Solution_Query__c</tabs>
    <tabs>APXTConga4__Conga_Solution_Template__c</tabs>
    <tabs>APXTConga4__Conga_Solution_Parameter__c</tabs>
</CustomApplication>
