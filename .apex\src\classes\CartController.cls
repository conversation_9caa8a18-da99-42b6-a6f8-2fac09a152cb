/**
* @description    Controller for shopping cart functionality exposed in the community
* <AUTHOR>
* @version        1.0 
* @created 2020-09-07
* @modified 2020-09-07
*/
public without Sharing class CartController {
    
    /**
     * @description Gets the ID of the cart associated to the user
     * @param storeId - Unique ID of the store
     * @param cookieId - Unique ID of the cookie dropped into the user's browser, used to get a shopping cart for an anonymous user
     * @return Map of cart ID, link to the payemnt page, number of items in the cart, and whether a the status of whether they're logged in, associated to a cart, or must have a cookie set
     */
    @AuraEnabled
    public static Map<String, String> getCart ( Id storeId, String cookieId ) {

        Store_Configuration__c sc = [ SELECT Id, Name, Link_to_Cart__c 
                                      FROM Store_Configuration__c 
                                      WHERE Id = :storeId ];
        Map<String, String> cartInfo = new Map<String, String>{ 
            'cartId' => '',
            'count'  => '0',
            'link'   => sc.Link_to_Cart__c,
            'type'   => 'guest'
        };

        String cartKey = '';

        //If the user is logged in, check if they currently have a cart
        //First, check if the user is logged-in, if they are, use their shopping cart, otherwise, 
        List<User> lstUser = [ Select u.ContactId from User u where u.Id = :UserInfo.getUserId() ];
        if ( lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId) ) {
            cartKey = lstUser[0].ContactId + '.' + sc.Id;
            cartInfo.put( 'type', 'auth' );
        
        } else  if ( !String.isBlank(cookieId) ){
            cartKey = cookieId + '.' + sc.Id;

        } 

        if ( !String.isBlank(cartKey) ) {
            List<AggregateResult> ars = [ SELECT pymt__Shopping_Cart__c cartId, Count(Id) 
                                          FROM pymt__Shopping_Cart_Item__c 
                                          WHERE pymt__Shopping_Cart__r.Cart_Key__c = :cartKey AND pymt__Payment_Completed__c = false 
                                          AND Item_Available__c = true AND pymt__Payment__r.pymt__Status__c != 'Invoice'
                                          GROUP BY pymt__Shopping_Cart__c ];

            if ( ars.size() > 0 ) {
                cartInfo.put( 'cartId', String.valueOF(ars[0].get('cartId')) );
                cartInfo.put( 'count', String.valueOF(ars[0].get('expr0')) );
            }

        }

        return cartInfo;

    }

    /**
     * @description Gets the ID of the cart associated to the user
     * @param objConfigId - Unique ID of the object configuration
     * @param cookieId - Unique ID of the cookie dropped into the user's browser, used to get a shopping cart for an anonymous user
     * @return Map of cart ID, link to the payemnt page, number of items in the cart, and whether a the status of whether they're logged in, associated to a cart, or must have a cookie set
     */
    @AuraEnabled
    public static Map<String, String> getCartItemConfig (  Id itemId, Id objConfigId, String cookieId ) {

        //Get the cart configuration 
        Store_SObject_Configuration__c soc = [ SELECT Id, Amount_Field_API_Name__c, Enable_One_Click_Checkout__c, Enforce_Cart_Item_Uniqueness__c, Field_Indicating_Availability_for_Cart__c, Include_Quantity_Field__c, Shopping_Cart_Lookup_to_Object__c, SObject_API_Name__c, Store_Configuration__c, Store_Configuration__r.Name,
                                               ( SELECT Id, Default_Value__c, Target_Object_Field_API_Name__c, Cart_Field_API_Name__c FROM Store_SObject_Mappings__r)
                                               FROM Store_SObject_Configuration__c
                                               WHERE Id = :objConfigId ];

        Map<String, String> cartInfo = new Map<String, String>{
            'addDisabled'   => 'false',
            'oneClick'      => String.valueOf(soc.Enable_One_Click_Checkout__c),
            'quantField'    => String.valueOf(soc.Include_Quantity_Field__c),
            'unique'        => String.valueOf(soc.Enforce_Cart_Item_Uniqueness__c)
        };

        //Check if this item already exists in the cart, and if it does, prevent the user from adding another one
        if ( soc.Enforce_Cart_Item_Uniqueness__c && !String.isBlank(soc.Shopping_Cart_Lookup_to_Object__c) ) {

            String cartKey;
            //First, check if the user is logged-in, if they are, use their shopping cart, otherwise, 
            List<User> lstUser = [ Select u.ContactId from User u where u.Id = :UserInfo.getUserId() ];
            if ( lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId) )
                cartKey = lstUser[0].ContactId + '.' + soc.Store_Configuration__c;
            
            else if ( !String.isBlank(cookieId) )
                cartKey = cookieId + '.' + soc.Store_Configuration__c;

            if ( !String.isBlank(cartKey) )
                cartInfo.put('addDisabled', String.valueOf( database.query( 'SELECT Id FROM pymt__Shopping_Cart_Item__c WHERE ' + soc.Shopping_Cart_Lookup_to_Object__c + ' = :itemId AND pymt__Shopping_Cart__r.Cart_Key__c = :cartKey ' ).size() > 0 ) );

            if ( !String.isBlank(soc.Field_Indicating_Availability_for_Cart__c) && cartInfo.get('addDisabled') == 'false' )
                cartInfo.put('addDisabled', String.valueOf( database.query( 'SELECT Id FROM '  + soc.SObject_API_Name__c + ' WHERE ' + soc.Field_Indicating_Availability_for_Cart__c + ' = true AND Id =:itemId ' ).size() == 0 ) );

        }

        return cartInfo;

    }

    /**
     * @description Retrieves the opt out URL field from the special event object
     * @param recordId - special event record Id
     * @return opt out url link
     */
    @AuraEnabled
    public static Map<String, String> getEventOptOutURL(Id recordId){
        String optOutURL = '';
        String siteURL = URL.getCurrentRequestUrl().toExternalForm();
        //System.debug('*** Site URL: *** ' + siteURL);
        string[] urlParse = siteURL.split('/');
        Map<String, String> eventsInfo = new Map<String, String>{
            'eventsWithOptOutURL' => 'false',
            'eventsOptOutURLLink' => ''
        };
        if(urlParse[3].equalsIgnoreCase('events')){
            List<evt__Special_Event__c> specialEventList = [SELECT Id, OptOutURL__c FROM evt__Special_Event__c WHERE Id = :recordId];
            if(specialEventList.size() > 0)
            {
                if(specialEventList[0].OptOutURL__c != null){
                    eventsInfo.put('eventsWithOptOutURL', 'true');
                    eventsInfo.put('eventsOptOutURLLink', String.valueOf(specialEventList[0].OptOutURL__c)); 
                }               
            }
        }    
        return  eventsInfo; 
    }
	@TestVisible
    private static Id getInvoiceId(Id contactId, Id recordId) {
        Id invoiceId = null;
        String invoiceType = recordId.getSObjectType() == Schema.hed__Course_Offering__c.SObjectType ? 'Executive Programs' : 'Events'; 
        List<Invoice__c> inv = new List<Invoice__c>(); 
        if(contactId != null) {
             inv = [SELECT Id, Contact__c, Invoice_Status__c
                    FROM Invoice__c
                    WHERE Contact__c = :contactId 
                    AND Invoice_Status__c = 'Open-Personal'
                    AND Type__c = :invoiceType
                    ORDER BY CreatedDate DESC LIMIT 1];
        }

        invoiceId = inv.size() > 0 ? inv[0].Id : createInvoice(contactId, invoiceType).Id; 
        return invoiceId;
    }

    private static Invoice__c createInvoice(Id contactId, String invoiceType){
        Invoice__c invoice =  new Invoice__c(Invoice_Status__c = 'Open-Personal', 
                                            Type__c = invoiceType, 
                                            Contact__c = ContactId); 
        insert invoice; 
        return invoice; 
    }


    /**
     * @description Adds an itemn into the specified shopping cart
     * @param scId - Id indicating the shopping cart to add the item to
     * @param objToAdd - Id of the object to add to the shopping cart
     * @return Status of add to cart action
     */
    @AuraEnabled
    public static Map<String, String> addToCart ( Id itemId, Id objConfigId, String cookieId, evt__Event_Fee__c eventFeeRecord ) {

        //Get the cart configuration 
        Store_SObject_Configuration__c soc = [ SELECT Id, Name, Amount_Field_API_Name__c, Enable_One_Click_Checkout__c, Enforce_Cart_Item_Uniqueness__c, Include_Quantity_Field__c, Shopping_Cart_Lookup_to_Object__c, SObject_API_Name__c, Store_Configuration__c, Store_Configuration__r.Name, Store_Configuration__r.Link_to_Cart__c,
                                               ( SELECT Id, Default_Value__c, Target_Object_Field_API_Name__c, Cart_Field_API_Name__c FROM Store_SObject_Mappings__r)
                                               FROM Store_SObject_Configuration__c
                                               WHERE Id = :objConfigId ];

        Map<String, String> cartInfo = new Map<String, String>{
            'link'          => soc.Store_Configuration__r.Link_to_Cart__c,
            'oneClick'      => String.valueOf(soc.Enable_One_Click_Checkout__c),
            'quantField'    => String.valueOf(soc.Include_Quantity_Field__c),
            'unique'        => String.valueOf(soc.Enforce_Cart_Item_Uniqueness__c)
        };

        pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c( Store_Configuration__c = soc.Store_Configuration__c );
        String cartItemName = '';

        //Construct query and get item in cart
        Set<String> fieldsToQuery = new Set<String>{ 'name', 'id' };
        if ( !String.isBlank(soc.Amount_Field_API_Name__c) ) 
            fieldsToQuery.add( soc.Amount_Field_API_Name__c.toLowerCase() );

        for ( Store_SObject_Mapping__c ssm : soc.Store_SObject_Mappings__r )
            if ( !String.isBlank(ssm.Target_Object_Field_API_Name__c) )
                fieldsToQuery.add( ssm.Target_Object_Field_API_Name__c.toLowerCase() );

        List<SObject> sObjs = database.query( 'SELECT ' + String.join( new List<String>(fieldsToQuery), ',' ) + ' FROM ' + soc.SObject_API_Name__c + ' WHERE ID = \'' + itemId + '\'' );


        //First, check if the user is logged-in, if they are, use their shopping cart, otherwise, 
        List<User> lstUser = [ SELECT Id, ContactId, Contact.Name, Username FROM User WHERE Id = :UserInfo.getUserId() ];
        Id invoiceId = null;
        if ( lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId) ) {
            cart.Cart_Key__c            = lstUser[0].ContactId + '.' + soc.Store_Configuration__c;
            cart.pymt__Contact__c       = lstUser[0].ContactId;
            cart.Guest_Cart__c          = false;
            cart.Store_Configuration__c = soc.Store_Configuration__c;
            cartItemName                = lstUser[0].ContactId != null ? lstUser[0].Contact.Name : lstUser[0].Username;
            cartInfo.put( 'type', 'auth' );
            System.debug('***********In here************');
            //UTR-3856: Create Invoice at Oneform submission instead
            //invoiceId = getInvoiceId(lstUser[0].ContactId, itemId);
        
        } else if ( !String.isBlank(cookieId) ){
            cart.Cart_Key__c            = cookieId + '.' + soc.Store_Configuration__c;
            cart.Guest_Cart__c          = true;
            cart.Store_Configuration__c = soc.Store_Configuration__c;
            cartItemName                = 'Anonymous';
            cartInfo.put( 'type', 'guest' );

        } else {
            cookieId = EncodingUtil.ConvertToHex( Crypto.GenerateAESKey(128) ).Substring(0,18);
            cart.Cart_Key__c            = cookieId + '.' + soc.Store_Configuration__c;
            cart.Guest_Cart__c          = true;
            cart.Store_Configuration__c = soc.Store_Configuration__c;
            cartItemName                = 'Anonymous';
            cartInfo.put( 'type', 'guest' );
            cartInfo.put( 'newCookie', cookieId );

        }

        database.upsert( cart, pymt__Shopping_Cart__c.FIELDS.Cart_Key__c );
        cartInfo.put( 'cartId', cart.Id );


        //Construct item to be added to cart and insert it
        pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c(
            Name                   = (cartItemName + ' - ' + sobjs[0].get('Name')).left(80),
            pymt__Shopping_Cart__c = cart.Id,
            pymt__Quantity__c      = 1
        );

        if ( invoiceId != null ) {
            item.put( 'Invoice__c', invoiceId);
        }
        
        if(eventFeeRecord != null) {
            item.put( 'pymt__Unit_Price__c', eventFeeRecord.evt__Amount__c );
            item.Event_Fee__c = eventFeeRecord.Id;
        } else if(eventFeeRecord == null) {
            if ( !String.isBlank(soc.Amount_Field_API_Name__c) && sobjs[0].get(soc.Amount_Field_API_Name__c) != null ) {
                item.put( 'pymt__Unit_Price__c', sobjs[0].get(soc.Amount_Field_API_Name__c) );
            }
            else {
                item.put( 'pymt__Unit_Price__c', 0 );
            }
        }

        for ( Store_SObject_Mapping__c ssm : soc.Store_SObject_Mappings__r )
            if ( !String.isBlank(ssm.Target_Object_Field_API_Name__c) && sobjs[0].get(ssm.Target_Object_Field_API_Name__c) != null && sobjs[0].get(ssm.Target_Object_Field_API_Name__c) != '' )
                item.put( ssm.Cart_Field_API_Name__c, sobjs[0].get(ssm.Target_Object_Field_API_Name__c) );
            else if ( !String.isBlank(ssm.Default_Value__c) )
                item.put( ssm.Cart_Field_API_Name__c, ssm.Default_Value__c );

        if ( !String.isBlank(soc.Shopping_Cart_Lookup_to_Object__c) ) 
            item.put( soc.Shopping_Cart_Lookup_to_Object__c, sobjs[0].Id );

        insert item;

        return cartInfo; 

    }

    

    /**
     * @description Gets the ID of the cart associated to the user
     * @param objConfigId - Unique ID of the object configuration
     * @param cookieId - Unique ID of the cookie dropped into the user's browser, used to get a shopping cart for an anonymous user
     * @return Map of cart ID, link to the payemnt page, number of items in the cart, and whether a the status of whether they're logged in, associated to a cart, or must have a cookie set
     */
    @AuraEnabled
    public static PaymentWrapper getPayment ( Id paymentId ) {

        return new PaymentWrapper( paymentId );

    }

    /**
     * @description Gets the list of countries used for the shipping portion
     * @param objConfigId - Unique ID of the object configuration
     * @param cookieId - Unique ID of the cookie dropped into the user's browser, used to get a shopping cart for an anonymous user
     * @return Map of cart ID, link to the payemnt page, number of items in the cart, and whether a the status of whether they're logged in, associated to a cart, or must have a cookie set
     */
    @AuraEnabled
    public static ShippingAddressWrapper getCountries ( Id UserId ) {

        return new ShippingAddressWrapper( UserId );

    }


    /**
     * @description Processes Payment
     * @param payment - Payment record being processed
     * @param cardNo - Credit Card number being processed
     * @param cvv - Security code of the credit card being processed
     * @return MonerisResponseWrapper with details about payment
     */
    @AuraEnabled
    public static Map<String, String> processPayment ( pymt__PaymentX__c clientPymt, String cardNo, 
                                                      String cvd, String expYr, String expMo, String accountName ) 
    {
        Map<String, String> responseMap = new Map<String, String>();
        String returnResponse = null;
        System.debug('clientPymt '+clientPymt);
        System.debug('cardNo '+cardNo);
        System.debug('cvd '+cvd);
        System.debug('expYr '+expYr);
        System.debug('expMo '+expMo);
        System.debug('accountName '+accountName);
        system.debug( clientPymt.Id );

        Organization[] orgData = [SELECT Id, InstanceName, IsSandbox, Name, OrganizationType FROM Organization];
        if (orgData.size() > 0) {
            if (orgData[0].IsSandbox == true)
                accountName = 'transaction_processing';
        }
        
        //Compare the amount being acknowledged by the user and record to ensure that the payment is valid
        pymt__PaymentX__c payment;
        try {
            
            payment = [ SELECT Id, pymt__Amount__c, Payment_Response__c, Amount_Paid__c 
                       FROM pymt__PaymentX__c 
                       WHERE ID = :clientPymt.Id AND pymt__Status__c != 'Completed'];// AND pymt__Amount__c = :clientPymt.pymt__Amount__c
            //String sourceIp = Auth.SessionManagement.getCurrentSession().get('SourceIp');
            String sourceIp='';
            payment.pymt__IP_Address__c = sourceIp;
            
            System.debug('imhere 1 ');
            System.debug('pmt billing --- ' + clientPymt.pymt__Billing_First_Name__c);
            System.debug('pmt lastname --- ' + clientPymt.pymt__Billing_Last_Name__c);
            //Update address information from the payment component
            payment.pymt__Billing_First_Name__c     = clientPymt.pymt__Billing_First_Name__c;
            payment.pymt__Billing_Last_Name__c      = clientPymt.pymt__Billing_Last_Name__c;
            payment.pymt__Billing_Street__c         = clientPymt.pymt__Billing_Street__c;
            payment.pymt__Billing_City__c           = clientPymt.pymt__Billing_City__c;
            payment.pymt__Billing_State__c          = clientPymt.pymt__Billing_State__c;
            payment.pymt__Billing_Country__c        = clientPymt.pymt__Billing_Country__c;
            payment.pymt__Billing_Postal_Code__c    = clientPymt.pymt__Billing_Postal_Code__c;
            
            if(payment.Payment_Response__c == NULL)
                payment.Payment_Response__c = '';
            
            HTTPResponse response = GlobalPayConnect.processPayment(clientPymt, cardNo, cvd, expYr, expMo, accountName);
            
            System.debug('response.getStatusCode '+response.getStatusCode());
            GP_PaymentResponseWrapper wrapper = (GP_PaymentResponseWrapper) JSON.deserialize(response.getBody(), GP_PaymentResponseWrapper.class);
            System.debug('wrapper '+wrapper);
            //System.debug('wrapper.action.result_code '+wrapper.action.result_code);
            System.debug('wrapper error_code '+wrapper.detailed_error_description);
            System.debug('wrapper status '+wrapper.status);
            
            //Error in response
            if(String.isNotBlank(wrapper.detailed_error_description)){
                returnResponse = wrapper.detailed_error_description;
                payment.pymt__Status__c = 'Error';
                responseMap.put('Status', 'ERROR');
                responseMap.put('ErrMessage', wrapper.detailed_error_description);
            }
            //CAPTURED status
            else if(response.getStatusCode() == 200 && wrapper.status == 'CAPTURED'){
                System.debug('Payment_methodbrand '+wrapper.Payment_method.Card.brand);
                returnResponse = wrapper.status;
                payment.pymt__Status__c = 'Completed';
                payment.pymt__Date__c = Date.today();
                payment.pymt__Batch_Id__c = wrapper.batch_id;
                payment.pymt__Reference_Id__c = wrapper.reference;
                payment.pymt__Last_4_Digits__c = cardNo.right(4);
                if(payment.Amount_Paid__c == null)
                    payment.Amount_Paid__c = 0;
                payment.Amount_Paid__c += clientPymt.pymt__Amount__c;
                payment.pymt__Card_Type__c = getCardType(wrapper.Payment_method.Card.brand);
                responseMap.put('Status', 'CAPTURED');
            }
            //DECLINED status
            else if(response.getStatusCode() == 200 && wrapper.status == 'DECLINED'){
                returnResponse = wrapper.status;
                payment.pymt__Status__c = 'Declined';
                payment.pymt__Batch_Id__c = wrapper.batch_id;
                payment.pymt__Reference_Id__c = wrapper.reference;
                payment.pymt__Last_4_Digits__c = cardNo.right(4);
                payment.pymt__Card_Type__c = getCardType(wrapper.Payment_method.Card.brand);
                responseMap.put('Status', 'DECLINED');
            }
            //Any other kind of error
            else{                  
                payment.pymt__Status__c = 'Error';
                String errMsg = 'We\'re sorry, but we cannot complete your payment. Please refresh this page and try again.';
                responseMap.put('Status', 'ERROR');
                responseMap.put('ErrMessage', errMsg);
            }
            if(String.isNotBlank(wrapper.id))
                payment.pymt__Transaction_Id__c = wrapper.id;
            
            payment.Payment_Response__c += '\n\n'+ DateTime.now()+' ->'+ String.valueOf(response.getBody());
            System.debug('imhere 2');
            update payment;
        } 
        catch (Exception e) {
            System.debug('imhere 3 ');
            System.debug('e.getMessage '+e.getMessage());
            payment.pymt__Status__c = 'Error';
            payment.Payment_Response__c += '\n\n'+ DateTime.now()+' ->'+ e.getMessage();
            update payment;
            responseMap.put('Status', 'ERROR');
            responseMap.put('ErrMessage', e.getMessage() + ':' + e.getStackTraceString());
            
            //throw new AuraHandledException(e.getMessage());
        } 
        return responseMap;
    }
    public static String getCardType(String cardTypeInResponse){
        if(cardTypeInResponse == 'AMEX')
            return 'Amex';
        else if(cardTypeInResponse == 'VISA')
            return 'Visa';
        else if(cardTypeInResponse == 'MASTERCARD')
            return 'Mastercard';
        else if(cardTypeInResponse == 'DINERS')
            return 'Diners Club';
        else if(cardTypeInResponse == 'DISCOVER')
            return 'Discover';
        else
            return cardTypeInResponse;
    }  
    /*
    @AuraEnabled
    public static MonerisResponseWrapper processPayment1 ( pymt__PaymentX__c clientPymt, String cardNo, String cvd, String expYr, String expMo ) {


        system.debug( clientPymt.Id );

        //Compare the amount being acknowledged by the user and record to ensure that the payment is valid
        pymt__PaymentX__c payment;
        try {
            payment = [ SELECT Id, pymt__Amount__c, Payment_Response__c 
                        FROM pymt__PaymentX__c 
                        WHERE ID = :clientPymt.Id AND pymt__Status__c != 'Completed' AND pymt__Amount__c = :clientPymt.pymt__Amount__c];
       
        } catch (Exception e) {
            throw new AuraHandledException('We\'re sorry, but we cannot complete your payment. Please refresh this page and try again.' + clientPymt.pymt__Amount__c + ' ' + clientPymt.pymt__Status__c + ' ' + clientPymt.Id );

        } 

        //Update address information from the payment component
        payment.pymt__Billing_First_Name__c     = clientPymt.pymt__Billing_First_Name__c;
        payment.pymt__Billing_Last_Name__c      = clientPymt.pymt__Billing_Last_Name__c;
        payment.pymt__Billing_Street__c         = clientPymt.pymt__Billing_Street__c;
        payment.pymt__Billing_City__c           = clientPymt.pymt__Billing_City__c;
        payment.pymt__Billing_State__c          = clientPymt.pymt__Billing_State__c;
        payment.pymt__Billing_Country__c        = clientPymt.pymt__Billing_Country__c;
        payment.pymt__Billing_Postal_Code__c    = clientPymt.pymt__Billing_Postal_Code__c;

        //Construct the card
        CardWrapperClass card = new CardWrapperClass();
        card.cardNo             = cardNo;
        card.CVD                = cvd;
        card.Amount             = payment.pymt__Amount__c;
        card.pymt               = clientPymt;
        card.expireDate         = expYr + expMo;

        //Make purchase
        MonerisResponseWrapper response = (MonerisResponseWrapper) json.deserialize( XmlParser.xmlToJson( MonerisConnectClass.monerisPurchase(card) ), MonerisResponseWrapper.class );
        
        //if( response.response.receipt.ReceiptId != null && response.response.receipt.Message.contains('APPROVED') true ){ //Good

            //payment.pymt__Transaction_Id__c = response.response.receipt.TransID;
            //payment.Order_Id__c = response.response.receipt.ReceiptId;
            //payment.Payment_Response__c += '\n Payment Success:'+string.valueOf(response.response)+'\n------------------\n';
            payment.pymt__Status__c = 'Completed';
            //payment.pymt__Last_4_Digits__c = card.cardNo.right(4);
            //ayment.pymt__Date__c = date.today();

        } else if (response.response.receipt.ReceiptId != null && response.response.receipt.Message.contains('DECLINED')){ //Bad

            payment.pymt__Transaction_Id__c = response.response.receipt.TransID;
            payment.Order_Id__c = response.response.receipt.ReceiptId;
            payment.Payment_Response__c += '\n Payment Declined:'+string.valueOf(response.response)+'\n------------------\n';
            payment.pymt__Status__c = 'Declined';
            
        } else { //Ugly

            payment.Payment_Response__c += '\n Payment Error:'+string.valueOf(response.response)+'\n------------------\n';

        }

        update payment;
        return response;

    }
*/

    public class PaymentWrapper {

        @AuraEnabled public pymt__PaymentX__c payment;
        @AuraEnabled public List<Map<String,String>> countries;

        public PaymentWrapper ( ID paymentId ) {

            this.payment           = [ SELECT Id, pymt__Status__c, pymt__Contact__c, pymt__contact__r.FirstName, pymt__Contact__r.LastName,pymt__Transaction_Type__c,  
                                      pymt__Contact__r.Email, pymt__AMount__c, pymt__Contact__r.Community_User__c, pymt__Transaction_Id__c,Amount_Paid__c, Type__c
                                      FROM pymt__PaymentX__c 
                                      WHERE Id = :paymentId ];

            //Add all counties to combobox list format, if default, add to beginning of list    
            this.countries = new List<Map<String, String>>{
                new Map<String, String>{ 'label' => 'Canada', 'value' => 'CA' },
                new Map<String, String>{ 'label' => 'United States', 'value' => 'US' }
            };
            for ( PicklistEntry pe : SObjectType.Account.fields.BillingCountryCode.getPicklistValues() )
                if ( pe.getValue() != 'US' && pe.getValue() != 'CA' ) 
                    this.countries.add( new Map<String, String>{ 'label' => pe.getLabel(), 'value' => pe.getValue() } );

        }

    }

    /* Similar to the Wrapper above. This makes/changes the picklist for the shipping portion of the checkout*/

    public class ShippingAddressWrapper {

        @AuraEnabled public contact user;
        User loggedInUser = [SELECT Id, ContactId FROM User where Id = : UserInfo.getUserId()];
        @AuraEnabled public List<Map<String,String>> countries;

        public ShippingAddressWrapper ( ID countriesId ) {

            //Add all counties to combobox list format, if default, add to beginning of list    
            this.countries = new List<Map<String, String>>{
                new Map<String, String>{ 'label' => 'Canada', 'value' => 'Canada' },
                new Map<String, String>{ 'label' => 'United States', 'value' => 'U.S.A.' }
            };
            for ( PicklistEntry pe : SObjectType.Account.fields.BillingCountryCode.getPicklistValues() )
                if ( pe.getValue() != 'US' && pe.getValue() != 'CA' ) 
                    this.countries.add( new Map<String, String>{ 'label' => pe.getLabel(), 'value' => pe.getValue() } );

        }

    }


    /**
     * @description Redirects user to URL in specified field on specified record, if populated
     * @param recId - The ID of the record which is the 'target' of the redirect
     * @param fieldName - Field on the object containing URL being redirected to
     */
    @AuraEnabled
    public static void redirectToField( String recId, String fieldName ){
        if(recId != null) {
            for( Schema.SObjectType obj : Schema.getGlobalDescribe().Values() )
                if( obj.getDescribe().getKeyPrefix() == recId.substring(0,3) ) { //If the object prefix matches the first 3 ID chars, query the db for that record, find the field, if not null, redirect to the URL in that field
                    List<SObject> recs = Database.query( 'Select Id, ' + fieldName + ' FROM ' + obj.getDescribe().getName() + ' WHERE Id = :recId ' );
                    if ( recs.size() > 0 && recs[0].get(fieldName) != null )
                        aura.redirect( new PageReference( (String)recs[0].get(fieldName) ) );
                    
                    break;
                }


        }
        
    }

    @AuraEnabled
    public static List<pymt__PaymentX__c> getPendingPaymentsRecord(){
        try {
            User loggedInUser = [SELECT Id, ContactId FROM User where Id = : UserInfo.getUserId()];
            List<pymt__PaymentX__c> listOfPaymentRecords = [SELECT Id, Name FROM pymt__PaymentX__c WHERE pymt__Contact__c=: loggedInUser.ContactId
                    AND pymt__Status__c = 'Online Checkout'
                    AND Type__c = 'EP Program Payment'
                    AND pymt__Transaction_Type__c = 'Payment'];
            return listOfPaymentRecords;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static List<evt__Event_Fee__c> getEventFeeList(Id eventId){
        
        try {
            //If the user is logged in, get contact Id
            //First, check if the user is logged-in, if they are, display all event fees 
            List<User> lstUser = [ Select u.ContactId from User u where u.Id = :UserInfo.getUserId() ];
            List<evt__Event_Fee__c> listOfEventFee;
            if (lstUser.size() > 0 && !String.isBlank(lstUser[0].ContactId) ) {
                Contact loggedInContact = [SELECT ID, Type__c FROM Contact WHERE id = :lstUser[0].ContactId];
                if( loggedInContact.Type__c == null ){listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c WHERE evt__Event__c =: eventId
                    AND Type__c = 'Standard'];}    
                else if ( loggedInContact.Type__c.contains('Faculty') && loggedInContact.Type__c.contains('Student') ){
                        listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c WHERE evt__Event__c =: eventId
                                                                                                            AND (Type__c = 'Standard'
                                                                                                            OR Type__c = 'Faculty'
                                                                                                            OR Type__c = 'Student')];
                    }
                else if ( loggedInContact.Type__c.contains('Faculty') ){
                        listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c WHERE evt__Event__c =: eventId
                                                                                                            AND (Type__c = 'Standard'
                                                                                                            OR Type__c = 'Faculty')];
                    }
                else if ( loggedInContact.Type__c.contains('Student') ){
                        listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c WHERE evt__Event__c =: eventId
                                                                                                            AND (Type__c = 'Standard'
                                                                                                            OR Type__c = 'Student')];
                    }
                else if ( loggedInContact.Type__c.contains('Alumni') ){
                        listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c WHERE evt__Event__c =: eventId
                                                                                                            AND (Type__c = 'Standard'
                                                                                                            OR Type__c = 'Alumni')];
                    }
                else if ( loggedInContact.Type__c != null && !loggedInContact.Type__c.contains('Faculty') && !loggedInContact.Type__c.contains('Student') ){
                    listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c WHERE evt__Event__c =: eventId
                    AND Type__c = 'Standard'];}
            }
            else {listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c WHERE evt__Event__c =: eventId
                                                                                                AND Type__c = 'Standard'];}
            System.debug('imhere 4 ');
            return listOfEventFee;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}