/**
 * @description Calls ConflictCheckService class methods to check for conflicts in new Course Enrollment records
 * WHERE the course offering’s parent course is an EP course record type, and the Course Enrollment is not in a cancelled or completed status.
 * <AUTHOR>
 * @version 1.0
 * @created 07-AUG-2020
 * @modified 14-DEC-2020 
 */
global class EPCourseConflict<PERSON><PERSON><PERSON>_TDTM extends hed.TDTM_Runnable{

    public static final String ERROR_MESSAGE = 'You are registering for course you have already registered for, or you are registering for an overlapping course. Please contact an administrator for assistance';
    public static final Set<String> excludedStatuses = new Set<String>{'Cancelled', 'Completed'}; 
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Course Enrollment records from trigger new 
     * @param oldList the list of Course Enrollment records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, Before Update)
     * @param objResult the describe for Course Enrollment
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        Set<Id> contIds = new Set<Id>(); 
        Map<Id, hed__Course_Enrollment__c> ceToConflictCheckMap = new Map<Id, hed__Course_Enrollment__c>(); 
        Map<Id, List<hed__Course_Enrollment__c>> contIdToCourseEnrollMap = new Map<Id, List<hed__Course_Enrollment__c>>(); 


        //AFTER INSERT AND AFTER UPDATE CONTEXT:
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert || triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            for(hed__Course_Enrollment__c ce : (List<hed__Course_Enrollment__c>)newList){
                if(ce.hed__Contact__c != null && ce.hed__Course_Offering__c != null && !ce.Disable_Conflict_Checking__c && !excludedStatuses.contains(ce.hed__Status__c)){
                    contIds.add(ce.hed__Contact__c); 
                    //Map Id to Course Enrollment: 
                    ceToConflictCheckMap.put(ce.Id, ce); 

                }
            }
        

            if(ceToConflictCheckMap.size() > 0){
 
                //Query for all existing course enrollment records of main parent course offering that are related to to contacts: 
                List<hed__Course_Enrollment__c> allRelatedCEList = [SELECT Id, hed__Contact__c, hed_Start_Date__c, hed_End_Date__c 
                                                                    FROM hed__Course_Enrollment__c
                                                                    WHERE hed__Contact__c IN :contIds 
                                                                    AND Disable_Conflict_Checking__c = false]; 
                
                //Map existing course enrollment records to contacts: 
                if(allRelatedCEList.size() > 0){
                    for(hed__Course_Enrollment__c ce :allRelatedCEList){
                        if(contIdToCourseEnrollMap.containsKey(ce.hed__Contact__c)){
                            contIdToCourseEnrollMap.get(ce.hed__Contact__c).add(ce); 
                        }else{
                            contIdToCourseEnrollMap.put(ce.hed__Contact__c, new List<hed__Course_Enrollment__c>{ce}); 
                        }
                    }
                }

                //Call ConflictCheckService for each contact's Course Enrollment List: 
                for(Id contId : contIdToCourseEnrollMap.keySet()){
                    if(ConflictCheckService.hasConflicts(contIdToCourseEnrollMap.get(contId), 'hed_Start_Date__c', 'hed_End_Date__c')){
                        //if there is a conflict, add error to course enrollment record: 
                        for(hed__Course_Enrollment__c rec : contIdToCourseEnrollMap.get(contId)){
                            if(ceToConflictCheckMap.containsKey(rec.Id)){
                                ceToConflictCheckMap.get(rec.Id).addError(ERROR_MESSAGE); 
                                
                            }
                        }
                    }
                }
            }
        }
        return null;
    }
}