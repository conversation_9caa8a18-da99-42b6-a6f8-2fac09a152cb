({
    MakePayment: function(component,wrapper) {
        console.log(wrapper);
        
        var pay = component.get("c.getPaymentDone");
        pay.setParams({
            pymt: wrapper.pymt,
            CVD : wrapper.CVD,
            expD: wrapper.expireDate,
            //amt: wrapper.pymt,
            cardNo: wrapper.cardNo
        });
        pay.setCallback(this, function(response) {
            //store state of response
            
            var state = response.getState();
            console.log(state);
            if (state === "SUCCESS") {
                console.log("resp--> "+response.getReturnValue());
                this.showToastFun('success','Payment Successful','Payment Complete');
                window.open('/lightning/r/pymt__PaymentX__c/'+response.getReturnValue()+'/view','_top');
                //set response value in wrapperList attribute on component.
                //component.set('v.paymentCardWrapper', response.getReturnValue());
                //component.set("v.TotalAmount",response.getReturnValue().pymt.pymt__Amount__c);
            }
            else if (state === "ERROR") {
                console.log('Error Occurred'+response.getError());
                
                
                var errors = response.getError();
                console.log(errors[0].message);
                console.log('errors= '+errors);
                var errorMsg = 'Unknown error.';
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        errorMsg = errors[0].message;
                        console.log("Error message: " + errorMsg);
                       // alert("Error occured: "+ errorMsg);
                    }
                } else {
                    console.log("Unknown error");
                   // alert("Unknown error");
                }
                var showToast = $A.get("e.force:showToast");
                showToast.setParams({ 
                    'type' : "Error",  // info, success, warning, error
                    'title' : errorMsg, 
                    'message' : "error" 
                }); 
                showToast.fire(); 
                console.log('showToast--> '+showToast);
                console.log('--> '+component.get("v.Msg"));
                
            } 
        });
        $A.enqueueAction(pay);
        
        
    },
    ContactDetails: function(component){
        var con = component.get("v.contactId");
        console.log(con);
        var wrapper = component.get("v.paymentCardWrapper");
        
        var action  = component.get("c.getContactDetails");//component.get("c.getContactDetails");
        action.setParams({ "ContactId" : JSON.stringify(con) });//JSON.stringify(con)
        
        
        action.setCallback(this, function(response) {
            //store state of response
            
            var state = response.getState();
            console.log(state);
            
            if (state === "SUCCESS") {
                console.log("resp--> "+response.getReturnValue().pymt.pymt__Billing_First_Name__c);
                
                //set response value in wrapperList attribute on component.
                //component.set(wrapper.pymt, response.getReturnValue());
                console.log('paymt-> '+response.getReturnValue().pymt);
                console.log('paymt-> '+response.getReturnValue().pymt.pymt__Billing_First_Name__c);
                component.set('v.paymentCardWrapper',response.getReturnValue());
                //component.set("v.TotalAmount",response.getReturnValue().pymt.pymt__Amount__c);
            }
        });
        $A.enqueueAction(action);
    },
    showToastFun : function(type,title,msg){
        console.log('Inside show toast:'+ msg);
        var showToast = $A.get("e.force:showToast");
                showToast.setParams({ 
                    'type' : type,//"success",  // info, success, warning, error
                    'title' : title,//'Cart Updated!', 
                    'message' : msg//"Success" 
                }); 
                showToast.fire(); 
    },
    
})