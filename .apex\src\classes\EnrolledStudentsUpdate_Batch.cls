public class EnrolledStudentsUpdate_Batch implements Database.Batchable<SObject> {  

	Integer errorCount;
	String errors;
	@TestVisible Static Integer testErrorCount;

	//Get new Course Enrollments that have not started
    public Database.QueryLocator start (Database.BatchableContext BC){ 
		return Database.getQueryLocator ('SELECT Id, Enrolled_Students__c, (SELECT ID From hed__Course_Enrollment__r WHERE hed__Status__c IN (\'Current\', \'Complete\' , \'Incomplete\', \'Former\') AND Recordtype.DeveloperName = \'Student\') FROM hed__Course_Offering__c WHERE hed__End_Date__c >= Today');
	}

    //Update Course Offering Enrolled Students field with count of Course Enrollments
    public void execute (Database.BatchableContext BC, List<sObject> scope){ 
		List<hed__Course_Offering__c> courseOffToUpdate = new List<hed__Course_Offering__c>();
		
		for (hed__Course_Offering__c co: (List<hed__Course_Offering__c>) scope){
            Integer count = 0;
            //Fix INVALID_QUERY_LOCATOR exception - <PERSON> Zhang
            hed__Course_Enrollment__c[] courseEnrollments = co.getSObjects('hed__Course_Enrollment__r');
            if (courseEnrollments != null) {
                for (hed__Course_Enrollment__c ce: co.hed__Course_Enrollment__r){
                    count += 1;
                }
            }
			co.Enrolled_Students__c = count;
			courseOffToUpdate.add(co);
		}
        //Fix Aggregate query has too many rows for direct asignment - Evan Zhang
        //if (courseOffToUpdate.size() > 0){
			List<Database.SaveResult> saveResults = Database.update(courseOffToUpdate,false);
            
			for (Integer i = 0; i < saveResults.size(); i++){
                Database.SaveResult saveResult = saveResults[i];
                
                if (!saveResult.isSuccess()){
                    errorCount++;
                    errors += courseOffToUpdate.get(i) + '--';
                    for ( Database.Error err : saveResult.getErrors()){
                        errors += 'failed to create: ' + err.getStatusCode() + ': ' + err.getMessage() + ': ' + err.getFields();
                    }
                    errors += '\n';
                }
            }
		//}
    }
	
    public void finish (Database.BatchableContext BC){
		testErrorCount = errorCount;
        String sender = '0054g000000d8QlAAI';
        if (errorCount > 0 || Test.isRunningTest()){
			List<String> sendToAddresses = new List<String>();
			for (User u : [SELECT Id,Email FROM User WHERE Id =:sender]){
				sendToAddresses.add(u.Email);
			}
			sendToAddresses.add(UserInfo.getUserEmail());
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            mail.setToAddresses(sendToAddresses);
            mail.setSenderDisplayName('Enrolled Students Batch Processing Error');
            mail.setSubject('Exception occurred during Enrolled Students Batch');
            mail.setPlainTextBody(errorCount + ' records processed failed.' + '\n\n' + errors);
            if( !Test.isRunningTest() ) Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
		}
	}

}