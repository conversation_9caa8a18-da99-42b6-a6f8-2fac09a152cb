public with sharing class CustomPanelController {
    @AuraEnabled
    public static ReturnWP fetchFields(String recordId, String objectName, String fieldSetName) {
        ReturnWP res = new ReturnWP();
        try {
            // Querying Name Field
            if(String.isBlank(recordId)) {
                res.message = 'Invalid Id';
                return res;
            }
            List<sObject> objectList = Database.query('SELECT Id FROM ' + objectName + ' WHERE Id =: recordId');
            res.nameField = String.valueof(objectList[0].get('Id'));

            // Fetching Highlight Panel Field Set
            if(String.isBlank(fieldSetName)) {
                res.message = 'Please provide a Field Set Name';
                return res;
            }
            Schema.FieldSet fieldSet = Schema.getGlobalDescribe().get(objectName).getDescribe().fieldSets.getMap().get(fieldSetName);
            if(fieldSet == null) {
                res.message = 'Field Set provided is not Valid';
                return res;
            }
            res.fieldsAPI = new List<String>();
            for(Schema.FieldSetMember fieldSetMem:fieldSet.getFields()){
                res.fieldsAPI.add(fieldSetMem.getFieldPath());
            }
            if (!res.fieldsAPI.isEmpty()) {
                String fieldName = res.fieldsAPI[0];
                List<SObject> sObjList = Database.query('SELECT ' + fieldName + ' FROM ' + objectName + ' WHERE Id =: recordId');
                res.nameField = String.valueof(sObjList[0].get(fieldName));
            }
            return res;
        } catch(Exception ex) {
            throw new AuraHandledException(ex.getMessage());
        }
    }

    public class ReturnWP {
        @AuraEnabled public String message;
        @AuraEnabled public String nameField;
        @AuraEnabled public List<String> fieldsAPI;
    }


}