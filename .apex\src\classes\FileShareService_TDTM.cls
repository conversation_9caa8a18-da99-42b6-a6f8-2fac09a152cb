/**
* Marks Salesforce content as shared when it is initially uploaded
*
* <AUTHOR> 
* @since   2020-07-21 
*/
global without sharing class FileShareService_TDTM extends hed.TDTM_Runnable {

    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();

        if ( triggerAction == hed.TDTM_Runnable.Action.AfterInsert || triggerAction == hed.TDTM_Runnable.Action.AfterUpdate ) {
            Set<Id> cdIds = new Set<Id>();

            for ( SObject sObj : newList ) {

                ContentVersion cv = (ContentVersion)sObj;
                if ( cv.Community_User_Visible__c == true )
                    cdIds.add( cv.ContentDocumentId );
                
            }

            if ( cdIds.size() > 0 ) {
                List<ContentDocumentLink> cdls = [SELECT Id FROM ContentDocumentLink WHERE ContentDocumentId IN :cdIds];
                for ( ContentDocumentLink cdl : cdls ) {
                    cdl.Visibility = 'AllUsers';
                    cdl.ShareType  = 'I';
                }
                update cdls;
            }
        }

        return dmlWrapper;

    }
}