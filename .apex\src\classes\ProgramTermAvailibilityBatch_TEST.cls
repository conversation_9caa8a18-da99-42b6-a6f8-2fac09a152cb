/**
 * Created by <PERSON><PERSON> on 2023-02-23.
 */

@IsTest
private class ProgramTermAvailibilityBatch_TEST {
    @TestSetup
    static void testSetup(){
        Id fulltimeId = ApplicationService.FTandSpecializedRTId;
        //Create test Account
        Account a1 = new Account(Name='TestAccount1');
        insert a1;

        //Create Term
        hed__Term__c trm1 = new hed__Term__c(hed__Account__c=a1.Id,Name='Spring 2023');
        insert trm1;

        //Create Cohort
        Cohort__c cohort1 = new Cohort__c(
                Name = 'FTMBA - 2025',
                Start_Term__c = trm1.Id,
                Program__c = a1.Id,
                Orientation_Date__c = Date.valueOf('2024-04-09 09:30:40'),
                Key__c = 'FTMBA - 2025'
        );
        insert cohort1;

        //Create test recor'd
        Program_Term_Availability__c pta1 = new Program_Term_Availability__c(
                Active__c = true,
                Program__c = a1.Id,
                Cohort__c = cohort1.Id,
                Program_Start_Date__c = Date.valueOf('2022-12-09 10:15:30'),
                Program_End_Date__c = Date.valueOf('2024-04-09 09:30:40'),
                Term__c = trm1.Id
        );
        insert pta1;

        hed__Application__c app1 = new hed__Application__c(
                Program_Term_Availability__c = pta1.Id,
                hed__Application_Status__c = 'In Progress',
                //hed__Application_Status__c = 'Accepted Offer',
                RecordTypeId = fulltimeId,
                hed__Applying_To__c = a1.Id,
                hed__Term__c = trm1.Id
        );
        insert app1;

    }

    @IsTest
    public static void testProgramTerm(){
        Program_Term_Availability__c pta = [SELECT Id, Active__c FROM Program_Term_Availability__c LIMIT 1];

        Test.startTest();
        pta.Active__c = false;
        update pta;
        Test.stopTest();
        List<hed__Application__c> res = [SELECT Id, Program_Term_Availability__c, hed__Application_Status__c FROM hed__Application__c];
        System.debug('Applications '+res);
    }

}