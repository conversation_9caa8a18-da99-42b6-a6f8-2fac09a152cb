/**
 * @description Calculates current usage on shopping cart item 
 * <AUTHOR>
 * @version 1.0
 * @created 2020-10-16
 * @modified 2020-10-16
 */
global class DiscountUsage_TDTM extends hed.TDTM_Runnable{
      final List<String> sciTypesToInclude = new List<String>{'EP Program Balance', 'Event Registration', 'Application Fee'};
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for the object 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        Set<Id> discountIds = new Set<Id>(); 

        //BEFORE INSERT CONTEXT: 
        if(triggerAction ==  hed.TDTM_Runnable.Action.AfterInsert){
            //Increment discount current usage if discount is linked to a shopping cart item & payment != null: 
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                if(sci.Discount__c != null && sci.pymt__Payment__c != null && sciTypesToInclude.contains(sci.type__c)){
                    discountIds.add(sci.Discount__c);  
                }
            }

            if(discountIds.size() > 0){ 
                //Update current usage: 
                dmlWrapper.objectsToUpdate.addAll(updateCurrentUsage(discountIds)); 

            } //BEFORE UPDATE CONTEXT: 
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList);
            //Filter out records that have updated Discount__c field and/or payment fields
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                if(((sci.Discount__c != oldMap.get(sci.Id).Discount__c) || (sci.pymt__Payment__c != oldMap.get(sci.id).pymt__Payment__c))  && sciTypesToInclude.contains(sci.type__c)){
                    if(sci.Discount__c != null) discountIds.add(sci.Discount__c); 
                    if(oldMap.get(sci.Id).Discount__c != null) discountIds.add(oldMap.get(sci.Id).Discount__c); 
                }
            }

            if(discountIds.size() > 0){
                //Update current usage: 
                dmlWrapper.objectsToUpdate.addAll(updateCurrentUsage(discountIds)); 

            }//AFTER DELETE and AFTER UNDELETE CONTEXT: if shopping cart item is not paid for and discount is linked, decrement current usage of the discount
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterDelete){
            for(pymt__Shopping_Cart_Item__c sci :(List<pymt__Shopping_Cart_Item__c>)oldList){
                if(!sci.pymt__Payment_Completed__c && sci.Discount__c != null && sci.pymt__Payment__c != null && sciTypesToInclude.contains(sci.type__c)) discountIds.add(sci.Discount__c); 
            }
            if(discountIds.size() > 0){
                 //Update current usage: 
                dmlWrapper.objectsToUpdate.addAll(updateCurrentUsage(discountIds)); 
            } 
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterUndelete){
            for(pymt__Shopping_Cart_Item__c sci :(List<pymt__Shopping_Cart_Item__c>)newList){
                if(!sci.pymt__Payment_Completed__c && sci.Discount__c != null && sci.pymt__Payment__c != null && sciTypesToInclude.contains(sci.type__c)) discountIds.add(sci.Discount__c); 
            }
            if(discountIds.size() > 0){
                 //Update current usage: 
                dmlWrapper.objectsToUpdate.addAll(updateCurrentUsage(discountIds)); 
            } 
        }

        return dmlWrapper; 
    }

    /**
     * @description update discount current usage
     * @param discountIds set of discount Ids
     * @return List<Discount__c> list of discounts with updated current usage
     */
    private List<discount__c> updateCurrentUsage(Set<Id> discountIds){
        
        List<Discount__c> discounts = [SELECT Id, Current_Usage__c, (SELECT Id FROM Shopping_Cart_Items__r WHERE type__c IN :sciTypesToInclude AND pymt__Payment__c != null) FROM Discount__c WHERE Id IN: discountIds];
        
        for(Discount__c discount :discounts){
        System.debug(discount); 
            discount.current_usage__c = discount.Shopping_Cart_Items__r.size(); 
        } 
        return discounts; 
    }
}