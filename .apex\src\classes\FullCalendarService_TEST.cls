@isTest
public class FullCalendarService_TEST {
	@TestSetup
	private static void testDataSetup() {
		Id fulltimeId = ApplicationService.FTandSpecializedRTId;
		Account testAccount = new Account(Name = 'Test Account');
		insert testAccount;

		Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>', AccountId = testAccount.Id);
		insert testContact;

		String cookieName = String.valueOf(dateTime.now());

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c = '$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.Start_Date__c = Date.today().addDays(30);
		event.End_Date__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.Tags__c = 'Strategic Communications';
		event.Thumbnail_Image__c =
				'<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';

		List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
		listOfEventToInsert.add(event);

		insert listOfEventToInsert;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = listOfEventToInsert[0].Id;
		fee.evt__Amount__c = 0.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;
	}


	@isTest
	static void testGetEventsNearbyDynamic2_Negative() {
		// Negative test case with empty additionalFilter
		List<Object> resultsEmptyFilter = FullCalendarService.getEventsNearbyDynamic2('');
		System.assertEquals(1, resultsEmptyFilter.size(), 'Results with empty filter should be empty');
	}

	@isTest
	static void testGetEventImgSrcUrls() {
		Test.startTest();
		List<String> imgSrcUrls = FullCalendarService.getEventImgSrcUrls();
		System.assertEquals(1, imgSrcUrls.size());
		Test.stopTest();
	}
}