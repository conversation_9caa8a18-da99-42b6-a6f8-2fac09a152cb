@isTest
public class CongaCalloutNewTest {
	/**
	 * @description setup method used to load initial data for the test
	 */
	@TestSetup
	static void setup(){
		List<Contact> contList =  (List<Contact>) TestFactory.createSObjectList(new Contact(), 25);
		insert contList;
	}

	/**
	 * @description test Conga callout
	 */
	@isTest
	static void validateCongaCallout () {
		Test.setMock(HttpCalloutMock.class, new VidyardCreationMock());

		List<CongaCalloutAction.Request> requests = new List<CongaCalloutAction.Request>();
		List<Contact> cons = [ SELECT Id, Name FROM Contact ];

		for(Contact c : cons){
			CongaCalloutAction.Request request =  new CongaCalloutAction.Request();
			request.recordId = c.Id;
			request.congaParams = '&Id=' + c.Id + '&OFN=' + c.Name;
			requests.add(request);
		}
		Test.startTest();
		CongaCalloutAction.callCongaApi( requests );
		Test.stopTest();
	}
}