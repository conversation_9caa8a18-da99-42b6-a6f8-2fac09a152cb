/* Handles Global Pay Refunds */
public class RefundController {
    @auraEnabled
    public static pymt__PaymentX__c getPaymentDetails(Id pymtId){
        try{
            pymt__PaymentX__c pymt = [SELECT Id, pymt__Status__c, pymt__Transaction_Type__c, pymt__AMount__c, 
                                      Amount_Paid__c, Amount_Refunded__c, pymt__Transaction_Id__c 
                                      FROM pymt__PaymentX__c WHERE Id = :pymtId];
            return pymt;
        }
        catch(Exception e){
            throw new AuraHandledException(e.getMessage());
        }
    }
    //Process Refunds
    @AuraEnabled
    public static Map<String, String> processRefund(Id pymtId, Decimal amountToRefund, Decimal adminFee, String refundReason) 
    {
        Map<String, String> responseMap = new Map<String, String>();
        Boolean isSuccess = false;
        
        pymt__PaymentX__c payment, refundPayment;
        try {
            Decimal totalAmtToRefund = amountToRefund + adminFee;
            //totalAmtToRefund = totalAmtToRefund.setScale(2);
            payment = [SELECT Id, OwnerId, Name, pymt__Transaction_Id__c, pymt__Amount__c, pymt__Card_Type__c, pymt__Billing_Email__c,
                       pymt__Billing_First_Name__c, pymt__Billing_Last_Name__c, pymt__Billing_Street__c, Application__c,
                       pymt__Billing_City__c, pymt__Billing_State__c, pymt__Billing_Country__c, pymt__Contact__c,
                       pymt__Billing_Postal_Code__c, pymt__Last_4_Digits__c, pymt__Reference_Id__c, Amount_Paid__c, Amount_Refunded__c
                       FROM pymt__PaymentX__c 
                       WHERE ID = :pymtId];
            
            refundPayment = new pymt__PaymentX__c(pymt__Parent_Transaction__c = pymtId,
                                                  pymt__Transaction_Type__c = 'Refund',
                                                  pymt__Amount__c = amountToRefund,
                                                  Refund_Reason__c = refundReason,
                                                  Administration_Fee__c = adminFee,
                                                  pymt__Payment_Processor__c = 'Global Pay');
            
            refundPayment.pymt__Billing_First_Name__c     = payment.pymt__Billing_First_Name__c;
            refundPayment.pymt__Billing_Last_Name__c      = payment.pymt__Billing_Last_Name__c;
            refundPayment.pymt__Billing_Street__c         = payment.pymt__Billing_Street__c;
            refundPayment.pymt__Billing_City__c           = payment.pymt__Billing_City__c;
            refundPayment.pymt__Billing_State__c          = payment.pymt__Billing_State__c;
            refundPayment.pymt__Billing_Country__c        = payment.pymt__Billing_Country__c;
            refundPayment.pymt__Billing_Postal_Code__c    = payment.pymt__Billing_Postal_Code__c;
            refundPayment.pymt__Last_4_Digits__c    	  = payment.pymt__Last_4_Digits__c;
            refundPayment.pymt__Card_Type__c    	  	  = payment.pymt__Card_Type__c;
            refundPayment.pymt__Reference_Id__c    	  	  = payment.pymt__Reference_Id__c;
            refundPayment.pymt__Billing_Email__c    	  = payment.pymt__Billing_Email__c;
            refundPayment.pymt__Contact__c    	  		  = payment.pymt__Contact__c;
            refundPayment.Application__c    	  		  = payment.Application__c;
            refundPayment.OwnerId    	  		  		  = payment.OwnerId;
            refundPayment.Name    	  		  			  = payment.Name + ' Refund';
            
            System.debug('processRefund pymt__Transaction_Id__c '+payment.pymt__Transaction_Id__c);
            if (test.isRunningTest()) {
                refundPayment.pymt__IP_Address__c = '127.0.0.1';
            } else {
                String sourceIp = Auth.SessionManagement.getCurrentSession().get('SourceIp');
                refundPayment.pymt__IP_Address__c = sourceIp;
            }

            HTTPResponse response = GlobalPayConnect.processRefund(payment.pymt__Transaction_Id__c, amountToRefund);
           
            System.debug('response.getStatusCode '+response.getStatusCode());
            GP_PaymentResponseWrapper wrapper = (GP_PaymentResponseWrapper) JSON.deserialize(response.getBody(), GP_PaymentResponseWrapper.class);
            System.debug('wrapper '+wrapper);
            //System.debug('wrapper.action.result_code '+wrapper.action.result_code);
            System.debug('wrapper error_code '+wrapper.detailed_error_description);
            System.debug('wrapper status '+wrapper.status);
            
            //Error in response
            if(String.isNotBlank(wrapper.detailed_error_description)){
                refundPayment.pymt__Status__c = 'Error';
                responseMap.put('Status', 'ERROR');
                responseMap.put('ErrMessage', wrapper.detailed_error_description);
            }
            //CAPTURED status
            else if(response.getStatusCode() == 200 && 
                    wrapper.action.result_code == 'SUCCESS' &&
                    wrapper.status == 'CAPTURED')
            {
                refundPayment.pymt__Status__c = 'Reversed';
                refundPayment.pymt__Date__c = Date.today();
                refundPayment.pymt__Batch_Id__c = wrapper.batch_id;
                refundPayment.Amount_Refunded__c = amountToRefund;
                payment.Amount_Refunded__c += amountToRefund;
                payment.Refund_Administration_Fee__c = adminFee;
                responseMap.put('Status', 'SUCCESS');
                isSuccess = true;
            }
            else{                     
                String errMsg = 'We\'re sorry, but we cannot complete your refund. Please refresh this page and try again.';
                refundPayment.pymt__Status__c = 'Error';
                responseMap.put('Status', 'ERROR');
                responseMap.put('ErrMessage', errMsg);
            }
            if(String.isNotBlank(wrapper.id))
                refundPayment.pymt__Transaction_Id__c = wrapper.id;
         
            refundPayment.Payment_Response__c = DateTime.now()+ ' ->' + String.valueOf(response.getBody());
            System.debug('imhere 2');
            insert refundPayment;
            if(isSuccess)
                update payment;
        } 
        catch (Exception e) {
            refundPayment.pymt__Status__c = 'Error';
            refundPayment.Payment_Response__c = DateTime.now()+' ->'+ e.getMessage();
            insert refundPayment;
            responseMap.put('Status', 'ERROR');
            responseMap.put('ErrMessage', e.getMessage());
            
        } 
        return responseMap;
    }
}