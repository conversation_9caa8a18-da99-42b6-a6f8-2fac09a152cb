/**
* @description    Test class for InteractionEnggmntScoreAuto_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-13-04
* @modified 2020-13-04
*/
@isTest
public class CourseCatalogControllerTest {
    @TestSetup
    static void setup(){
        //Create test Account:
        Account a = new Account(Name='TestAccount');
		insert a;
        hed__Course__c crs = new hed__Course__c(Name = 'Test',hed__Account__c = a.Id);
        insert crs;
        List<hed__Term__c> trmLst = new List<hed__Term__c>();
    	hed__Term__c trm1 = new hed__Term__c(Name='Test1',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
        trmLst.add(trm1);
        hed__Term__c trm2 = new hed__Term__c(Name='Test2',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
        trmLst.add(trm2);
        hed__Term__c trm3 = new hed__Term__c(Name='Test3',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
        trmLst.add(trm3);
        hed__Term__c trm4 = new hed__Term__c(Name='Test4',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
        trmLst.add(trm4);
        hed__Term__c trm5 = new hed__Term__c(Name='Test5',hed__Account__c = a.id,hed__End_Date__c = Date.today().addDays(3));
        trmLst.add(trm5);
        insert trmLst;
        
        hed__Course_Offering__c coffrng1 = new hed__Course_Offering__c(Name = '150',hed__Term__c=trm1.id,hed__Course__c = crs.Id);
        insert coffrng1;
    }
    /**
     * @description Test to query all the term names to show in the landing page to filter 
     *  
     */
	@isTest 
    static void queryAllTerms() {
        Test.StartTest();
    	CourseCatalogController.allTermNames();
        Test.StopTest();
    }
    /**
     * @description Test to query all the courses to different filter values
     *  
     */
    @isTest 
    static void queryAllCourses() {
    	CourseCatalogController.allCourses(null,null,null);
        CourseCatalogController.allCourses('100',null,null);
        Test.StartTest();
        CourseCatalogController.allCourses(null,'Test1',null);
        CourseCatalogController.allCourses(null,null,'150');
        CourseCatalogController.allCourses('All',null,'150');
        CourseCatalogController.allCourses(null,'None','150');
        CourseCatalogController.allCourses(null,'Test1','150');
        Test.StopTest();
    }
}