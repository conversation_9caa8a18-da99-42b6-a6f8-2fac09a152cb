/**
 * Created by <PERSON><PERSON> on 2022-06-09.
 */

({
    runTerritoryAssignment : function(component) {
        let action = component.get("c.reRunTerritoryAssignment");
        action.setParams({"accId": component.get("v.recordId")});
        action.setCallback(this, function(response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    title : 'Success',
                    message: 'Re-run territory assingment has been processed successfully!',
                    duration:' 5000',
                    key: 'info_alt',
                    type: 'success',
                    mode: 'pester'
                });
                toastEvent.fire();
                $A.get('e.force:refreshView').fire();
                $A.get("e.force:closeQuickAction").fire();
                /*
                $A.get("e.force:navigateToSObject").setParams({
                    "recordId": component.get("v.recordId"),
                    "slideDevName": "related"
                }).fire();
                 */
            }
            else if (state === "INCOMPLETE") {
                console.error('No response from server or client is offline.');
            } else if (state === "ERROR") {
                const errors = response.getError();
                if (errors) {
                    if (errors[0] && errors[0].message) {
                        console.log("Error message: " +
                            errors[0].message);
                    }
                } else {
                    console.log("Unknown error");
                }
            }
        });
        $A.enqueueAction(action);
    },
});