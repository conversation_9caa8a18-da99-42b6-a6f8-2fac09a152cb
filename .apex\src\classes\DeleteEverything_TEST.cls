@isTest
public class DeleteEverything_TEST {
    @TestSetup
    static void testSetup () {
        //Create test Account
        Account a = (Account)TestFactory.createSObject(new Account(Name='TestAccount'));
        insert a;
        //Create test Contact
        Contact c = (Contact)TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student'));
        insert c;
    }
    
    @isTest
    public static void testBatch(){
        test.startTest();
    	//Test account and contact delete
            Id batchprocessid = Database.executeBatch(new DeleteEverything(new List<String>{'Account', 'Contact'}, false));
		test.stopTest();
        System.assertEquals(database.countQuery('SELECT COUNT()'
                                                + ' FROM Account'),
                            0);
        System.assertEquals(database.countQuery('SELECT COUNT()'
                                                + ' FROM Contact'),
                            0);
    }
}