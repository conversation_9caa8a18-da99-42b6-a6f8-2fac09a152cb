/** 
* @description    When shopping cart is saved this checks to see if the contact is in the useage group
* <AUTHOR> 
* @version        1.0 
* @created        3-JAN-2022
*/
global class Discount_group_restriction_TDTM extends hed.TDTM_Runnable{

    public static final String ERROR_MESSAGE = 'Contact is not part of Discount Group';
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Course Enrollment records from trigger new 
     * @param oldList the list of Course Enrollment records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, Before Update)
     * @param objResult the describe for Course Enrollment
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        set<string> DiscountGroupSet = new set<string>(); 
        set<string> SecondaryDiscountGroupSet = new set<string>(); 
                
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert || triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){

        	 //for each database record create dbDiscountUsage to compare (discount + contact) into single string
            for(pymt__Shopping_Cart_Item__c newDiscountUsage : (List<pymt__Shopping_Cart_Item__c>)newlist){

                String newDiscountInput = (newDiscountUsage.Discount__c);//Get DiscountID (String)
                String shoppingCartContact = (newDiscountUsage.pymt__Contact__c);//Get ContatID (String)
                String shoppingCart = (newDiscountUsage.ID);//Get ContatID (String)
                list<String> discountGroups;
                list<String> GroupPicklist;
                
                
                if(newDiscountUsage.discount__c != null && shoppingCartContact != null){
                for(Discount__c dbDiscountGroups : [SELECT Discount_Group__c
                                                                 FROM Discount__c 
                                                                WHERE ID = :newDiscountInput
                                                                AND Discount_Group__c != '']){
        
                GroupPicklist = dbDiscountGroups.Discount_Group__c.split(';'); //Get db contact (string)

                }
                if(GroupPicklist != null){
                Set<String> GroupPicklistSET = new Set<String>(GroupPicklist);
                for(pymt__Shopping_Cart_Item__c contactDiscountGroups : [SELECT pymt__Contact__r.Discount_Group__c
                                                                 FROM pymt__Shopping_Cart_Item__c 
                                                                WHERE pymt__Contact__c = :shoppingCartContact]){
        
                if(contactDiscountGroups.pymt__Contact__r.Discount_Group__c != null){                                                   
                discountGroups = contactDiscountGroups.pymt__Contact__r.Discount_Group__c.split(';');}
                }
                    Set<String> discountGroupsSET;
                    Boolean result1 = false;
                    if(discountGroupsSET != null){ discountGroupsSET = new Set<String>(discountGroups);
                                                  result1 = GroupPicklistSET.containsALL(discountGroupsSET);}//Boolean result if strings match
                	system.debug('Nick' + result1 + discountGroupsSET + GroupPicklistSET);
                	if(!result1){
                    	newDiscountUsage.addError(ERROR_MESSAGE);
                    	newDiscountUsage.Discount__c = null;}

                }
            }
        }    
    }
    return null;
}}