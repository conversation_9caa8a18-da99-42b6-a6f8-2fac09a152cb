/**
 * @description Custom Calendar functionality testing
 * <AUTHOR>
 * @version 1.0
 * @created 2020-09-04
 * @modified 2020-09-04
 */
@isTest
public class CustomCalendarLWCControllerTest {

	@testSetup
    static void testSetup (){
        //RecordType EPRT = [Select Id, Name from RecordType where SObjectType = 'hed__Course__c' AND Name = 'EP Course'];
		//Create an account test records
		Account a = (Account) TestFactory.createSObject(new Account());
        insert a;
        //Creating course and Term to create Course Offering
		hed__Course__c course = new hed__Course__c(Name = 'Test Course1', hed__Account__c = a.Id);
        insert course;
        hed__Term__c t = new hed__Term__c(Name = 'Test Term1', hed__Account__c = a.Id);
        insert t;
        
        hed__Course_Offering__c cOffering = new hed__Course_Offering__c(Name = 'Test CO1', hed__Course__c = course.Id, hed__Term__c = t.Id, hed__Start_Date__c = Date.newInstance(2025,1,1), hed__End_Date__c = Date.newInstance(2025,1,15));
        insert cOffering;
        
        //Inserting contact
        Contact c = (Contact) TestFactory.createSObject(new Contact(FirstName = 'Test', LastName = 'Student',email='<EMAIL>')); 
        insert c; 

        //create an attachment
        Attachment att = new Attachment(Name='Test Attachment', Body=blob.valueof('This is a long text for testing purpose.'), ContentType='text', ParentId=c.Id);
        insert att;
        
        hed__Course_Enrollment__c cenrollment = new hed__Course_Enrollment__c(hed__Contact__c=c.id,hed__Course_Offering__c=cOffering.id);
        insert cenrollment;
        
        //RecordType stdEvntRT = [Select Id, Name from RecordType Where SObjectType = 'Account' and Name = 'Standard Event'];
        /*List<evt__Special_Event__c> eList = new List<evt__Special_Event__c>{ 
            new evt__Special_Event__c (
                RecordTypeId = stdEvntRT.Id, 
                Name = 'Test Event1', 
                evt__Disable_Conflict_Checking__c = false, 
                evt__Start__c = Datetime.now(),
                evt__End__c = DateTime.now()
            ),
            new evt__Special_Event__c (
                RecordTypeId = stdEvntRT.Id, 
                Name = 'Test Event1', 
                evt__Disable_Conflict_Checking__c = False, 
                evt__Start__c = Datetime.newInstanceGmt(2025,12,12,1,0,0), 
                evt__End__c = DateTime.NewInstance(2025,12,12,3,0,0)
            ),
            new evt__Special_Event__c (
                RecordTypeId = stdEvntRT.Id, 
                Name = 'Test Event1', 
                evt__Disable_Conflict_Checking__c = False, 
                evt__Start__c = Datetime.newInstanceGmt(2025,12,12,1,0,0), 
                evt__End__c = DateTime.NewInstance(2025,12,12,3,0,0)
            )
        };
        insert eList;*/

        insert new List<Calendar_View__c>{
            new Calendar_View__c(
                Name                    = 'Special Event Calendar View',
                Name_Field__c           = 'Name',
                SObject_API_Name__c     = 'Account',
                Date_Field_Name__c      = 'LastActivityDate',
                Detail_Fields__c        = 'Name',
                Filter_Field__c         = 'Name',
                Thumbnail_Image_Field__c= 'Name',
                SOQL_Where_Clause__c    = 'LastActivityDate >= TODAY'
            ),
            new Calendar_View__c(
                Name                    = 'Course Offering Calendar View',
                Name_Field__c           = 'Name',
                Date_Field_Name__c      = 'hed__Start_Date__c',
                SObject_API_Name__c     = 'hed__Course_Offering__c',
                Detail_Fields__c        = 'Name,hed__Start_Date__c,hed__End_Date__c,Venue__c'
            )
        };

    }
    /**
     * @description Display Events 
     * @return Void 
     * @param  
     */
	@isTest
    static void runDisplayEvents(){
        Test.startTest();
        Calendar_View__c cv = [ SELECT Id, Name, Date_Field_Name__c, Detail_Fields__c, Filter_Field__c, Link_to_Calendar_Item__c, Name_Field__c, Search_Fields__c, SObject_API_Name__c, SOQL_Where_Clause__c, Thumbnail_Image_Field__c
                               FROM Calendar_View__c limit 1];
        Set<String> queryFields = new Set<String>{ 'id' };
            queryFields.addAll( cv.Detail_Fields__c.toLowerCase().split(',') );
        queryFields.add( cv.Name_Field__c.toLowerCase().trim() );
        queryFields.add( cv.Date_Field_Name__c.toLowerCase().trim() );
        if ( !String.isBlank(cv.Thumbnail_Image_Field__c) )
            queryFields.add( cv.Thumbnail_Image_Field__c.toLowerCase().trim() );
        String queryString = 'SELECT '+String.join( new List<String>(queryFields), ',' ) + ' FROM ' + cv.SObject_API_Name__c + ' limit 1';
        SObject calObjs = Database.query( queryString );    
        List<CustomCalendarLWCController.FieldWrapper> fws = new List<CustomCalendarLWCController.FieldWrapper>();
        Schema.SObjectType sDescribe                        = Schema.getGlobalDescribe().get( cv.SObject_API_Name__c );
        Map<String,Schema.DescribeFieldResult> dFields      = new Map<String, Schema.DescribeFieldResult>();

        //If there are detail fields in the calendar, add them to the list of field wrappers
        if ( !String.isBlank(cv.Detail_Fields__c) ) {
            Map<String, Schema.SObjectField> allFields = sDescribe.getDescribe().fields.getMap();
            for ( String s : allFields.keySet() )
                if ( cv.Detail_Fields__c.toLowerCase().contains(s.toLowerCase()) )
                    dFields.put( s.toLowerCase(), allFields.get(s).getDescribe() );
			for ( String s : cv.Detail_Fields__c.toLowerCase().split(',') )
                fws.add(new CustomCalendarLWCController.FieldWrapper( dFields.get(s.trim()) ) );
        }
        CustomCalendarLWCController.getCalendarConfig( 'Special Event Calendar View', 10 );
        CustomCalendarLWCController.getCalendarItems( 'Special Event Calendar View', 10, 0, new List<String>{ 'Science and Technology' }, 'Bu', Date.today() );
        CustomCalendarLWCController.CalendarItem ci = new CustomCalendarLWCController.CalendarItem(calObjs,cv,fws);
        Test.stopTest();        
    }
}