public without sharing class GroupEventCartCheckoutController {
    @AuraEnabled(cacheable=true)
    public static List<evt__Special_Event__c> getAllEvents(){
        try {
            List<evt__Special_Event__c> listOfEvents = [SELECT Id, Name, Event_Date_Time__c, evt__Free_Event__c, State_Province__c
                                                        FROM evt__Special_Event__c 
                                                        WHERE Start_Date__c != null 
                                                        AND evt__By_Invitation__c != true 
                                                        AND Start_Date__c >= today 
                                                        AND evt__Publish_To__c EXCLUDES ('Internal Student Events') 
                                                        AND evt__Status__c = 'Published'];
            return listOfEvents;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static List<evt__Event_Fee__c> getEventFee(Id eventId) {
        List<evt__Event_Fee__c> listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c FROM evt__Event_Fee__c 
                                                    WHERE evt__Event__c =: eventId
                                                    AND Type__c= 'Standard' 
                                                    AND evt__Limit_Per_Purchase__c = null 
                                                    AND Available_for_Checkout__c = true
                                                    ORDER BY evt__Amount__c ASC];   
        return listOfEventFee;
    }

    @AuraEnabled
    public static Discount__c getDiscountFromCouponCode(String couponCode){
        try {
            Discount__c discount = [SELECT Id, Percent_Discount__c, Dollar_Discount__c, Event__c
                                    FROM Discount__c 
                                    WHERE Code__c =: couponCode];
            return discount;
        } catch (Exception e) {
            return null;
            //throw new AuraHandledException(e.getMessage());
        }
    }
}