/**
 * @description Nulls out Payment lookup when void payment is checked
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-31
 * @created 2020-08-31
 */
global without sharing class VoidPayment_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for the object 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        List<pymt__Shopping_Cart_Item__c> scisToUpdate = new List<pymt__Shopping_Cart_Item__c>(); 
        //BEFORE INSERT CONTEXT:
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert){
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList){
                if(sci.void_payment__c && sci.pymt__payment__c != null){
                    scisToUpdate.add(sci); 
                }
            }
            if(scisToUpdate.size() > 0) nullPayments(scisToUpdate); 
        //BEFORE UPDATE CONTEXT:
        }if(triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList); 
            for(pymt__Shopping_Cart_Item__c sci :(List<pymt__Shopping_Cart_Item__c>)newList){
                System.debug(sci); 
                if((sci.void_payment__c != oldMap.get(sci.Id).void_Payment__c || sci.pymt__payment__c != oldMap.get(sci.Id).pymt__Payment__c)  && sci.void_payment__c && sci.pymt__payment__c != null && !sci.pymt__Payment_Completed__c)
                {
                    scisToUpdate.add(sci); 
                }
            }
            if(scisToUpdate.size() > 0) nullPayments(scisToUpdate); 
        }
        return dmlWrapper; 
    }

    /**
     * @description null out payment lookup value
     * @param items list of shopping cart items to void payments 
     */
    private void nullPayments(List<pymt__Shopping_Cart_Item__c> items){
        for(pymt__Shopping_Cart_Item__c item: items){
            item.pymt__Payment__c = null; 
        }
    }

}