@isTest
global class CongaMergeMock implements HttpCalloutMock {
    // Implement this interface method
    global HTTPResponse respond(HTTPRequest req) {

        List<Attachment> att = [SELECT Id, Name from Attachment LIMIT 1];
        // Create a fake response
        HttpResponse res = new HttpResponse();
        res.setHeader('Content-Type', 'application/json');
        if(att.size()>0)
        	res.setBody(att[0].Id);
        res.setStatusCode(200);
        return res;
    }

}