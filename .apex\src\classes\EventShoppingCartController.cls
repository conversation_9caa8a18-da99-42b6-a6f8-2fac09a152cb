public without sharing class EventShoppingCartController {

    @AuraEnabled
    public static List<EventCartWrapperNew> getShoppingCartItems(Id cartId){
        try{
            List<pymt__Shopping_Cart_Item__c> listOfShoppingCartItem = [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c,
            Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c,Discount_Amount__c,
            Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c,
            Special_Event__r.evt__Max_Additional_Attendees__c, Discount__r.Percent_Discount__c,
            Discount__r.Taxable__c, Discount__r.Dollar_Discount__c, Special_Event__r.evt__Accept_Donations__c
            FROM pymt__Shopping_Cart_Item__c
            WHERE pymt__Shopping_Cart__c =: cartId AND Type__c != 'Donation'];
            Map<Id, List<evt__Session__c>> mapOfEventInfToListOfSessions = new Map<Id, List<evt__Session__c>>();
            Map<Id, List<evt__Event_Fee__c>> mapOfEventIdToListOfFees = new Map<Id, List<evt__Event_Fee__c>>();
            for(pymt__Shopping_Cart_Item__c cartItem : listOfShoppingCartItem) {
                mapOfEventInfToListOfSessions.put(cartItem.Special_Event__c, new List<evt__Session__c>());
                mapOfEventIdToListOfFees.put(cartItem.Special_Event__c, new List<evt__Event_Fee__c>());
            }
            List<evt__Session__c> listOfSessions = [SELECT Id, Name, evt__Session_Fee__c, evt__Short_Description__c, Session_Date_Time__c, evt__Event__c
            FROM evt__Session__c
            WHERE evt__Event__c IN : mapOfEventInfToListOfSessions.keySet()];
            List<evt__Event_Fee__c> listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c, evt__Event__c FROM evt__Event_Fee__c
            WHERE evt__Event__c IN: mapOfEventIdToListOfFees.keySet()
            AND Type__c= 'Standard'
            AND evt__Limit_Per_Purchase__c = null
            AND Available_for_Checkout__c = true
            ORDER BY evt__Amount__c ASC];
            for(evt__Session__c sessionItem : listOfSessions) {
                if(mapOfEventInfToListOfSessions.containsKey(sessionItem.evt__Event__c)) {
                    mapOfEventInfToListOfSessions.get(sessionItem.evt__Event__c).add(sessionItem);
                }
            }
            for(evt__Event_Fee__c feeItem : listOfEventFee) {
                if(mapOfEventIdToListOfFees.containsKey(feeItem.evt__Event__c)) {
                    mapOfEventIdToListOfFees.get(feeItem.evt__Event__c).add(feeItem);
                }
            }
            List<EventCartWrapperNew> listOfEventCartWrapper = new List<EventCartWrapperNew>();
            for(pymt__Shopping_Cart_Item__c cartItem : listOfShoppingCartItem) {
                EventCartWrapperNew wrapperObj = new EventCartWrapperNew();
                wrapperObj.showSession = (mapOfEventInfToListOfSessions.get(cartItem.Special_Event__c) != null && mapOfEventInfToListOfSessions.get(cartItem.Special_Event__c).size() > 0) ? true : false;
                wrapperObj.listOfEventSession = mapOfEventInfToListOfSessions.get(cartItem.Special_Event__c);
                wrapperObj.shoppingcartItem = cartItem;
                wrapperObj.showDiscount = cartItem.Discount__c == null ? true : false;
                wrapperObj.showAddGuests = ((cartItem.Special_Event__r.evt__Show_Available_Seats__c) && (cartItem.Special_Event__r.evt__Max_Additional_Attendees__c != null && Integer.valueOf(cartItem.Special_Event__r.evt__Max_Additional_Attendees__c) > 0)) ? true : false;
                if(!wrapperObj.showDiscount) {
                    wrapperObj.couponCode = cartItem.Discount__r.Code__c;
                    wrapperObj.discountCodeMessage = cartItem.Discount__r.Code__c + ' discount code is applied.Please click here to add a new code';
                }
                wrapperObj.showZeroDiscount = (!wrapperObj.showDiscount) ? false : true;
                wrapperObj.listOfGuests = new List<evt__Attendee__c>();
                //hsing => to support dynamic price calulaton in case we have session price. only for front-end
                for(evt__Event_Fee__c eventfee : mapOfEventIdToListOfFees.get(cartItem.Special_Event__c)) {
                    wrapperObj.listofEventFee = new List<String>();
                    wrapperObj.listofEventFee.add(JSON.serialize('{ label : ' + eventfee.Name + ',' + 'value:' + eventfee.Id + '}'));
                }
                // wrapperObj.listofEventFee = mapOfEventIdToListOfFees.containsKey(cartItem.Special_Event__c) ? mapOfEventIdToListOfFees.get(cartItem.Special_Event__c) : null;     //hsing => to support dynamic price calulaton in case we have session price. only for front-end
                if(wrapperObj.listofEventFee != null) {
                    wrapperObj.showPicklist = true;
                } else {
                    wrapperObj.showPicklist = false;
                }
                wrapperObj.totalAmount = cartItem.Total_Amount__c;
                wrapperObj.subTotal = cartItem.Gross_Amount__c;
                wrapperObj.estimatedTax = cartItem.Tax_Amount__c;
                wrapperObj.discountAmount = cartItem.Discount_Amount__c;
                wrapperObj.originalSubTotal = cartItem.Gross_Amount__c;
                wrapperObj.taxPercentage = 0;
                wrapperObj.taxable = true;
                wrapperObj.showDonationCheckBox = cartItem.Special_Event__r.evt__Accept_Donations__c;
                if(cartItem.Tax_Amount__c != 0 && cartItem.Gross_Amount__c !=0){
                    wrapperObj.taxPercentage = cartItem.Tax_Amount__c/ cartItem.Gross_Amount__c;
                }
                if(cartItem.Discount__c != null) {
                    if(cartItem.Discount__r.Percent_Discount__c != null) {
                        wrapperObj.discountPercent = cartItem.Discount__r.Percent_Discount__c/100;
                    }
                    wrapperObj.taxable = cartItem.Discount__r.Taxable__c;
                }
                //end of hsing code
                listOfEventCartWrapper.add(wrapperObj);
            }
            return listOfEventCartWrapper;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled(Cacheable = true)
    public static Contact getUserDetails() {
        Id userId = UserInfo.getUserId();
        System.debug('userId ' + userId);
        if (userId != null) {
            Contact loggedContact;
            try {
                User loggedInUser = [SELECT Id, ContactId FROM User WHERE Id =: userId];
                if (loggedInUser.ContactId != null) {
                    loggedContact = [SELECT Id, FirstName, LastName, Type__c, Email, MobilePhone, hed__Current_Address__c, Current_Employer__c, MailingStreet, MailingCity, MailingPostalCode, MailingState, MailingCountry, Accessibility_Requirements__c, Dietary_Restrictions__c FROM Contact WHERE Id =: loggedInUser.ContactId];
                }
                return loggedContact;
            } catch (Exception e) {
                throw new AuraHandledException(e.getStackTraceString());
            }
        } else {
            return null;
        }
    }

    @AuraEnabled
    public static pymt__Shopping_Cart_Item__c getTheUpdatedShoppingCartItem(Id shoppingCartItemId) {
        try {
            pymt__Shopping_Cart_Item__c udpatedShoppingcartItem = [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c,
            Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c, Discount_Amount__c,
            Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c, Special_Event__r.evt__Max_Additional_Attendees__c,
            Discount__r.Percent_Discount__c, Discount__r.Taxable__c, Discount__r.Dollar_Discount__c
            FROM pymt__Shopping_Cart_Item__c
            WHERE Id =: shoppingCartItemId];
            return udpatedShoppingcartItem;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    @AuraEnabled
    public static pymt__PaymentX__c getRegisteredAttendeeforUser(Id cartId) {
        try {
            Set<Id> eventIdlist = new Set<Id>();
            List<pymt__Shopping_Cart_Item__c> listOfShoppingCartItem = [select id, name, Gross_Amount__c, Total_Amount__c, Tax_Amount__c,
            Discount_Amount__c, Event_Fee__c, Special_Event__c from
            pymt__Shopping_Cart_Item__c where pymt__Shopping_Cart__c =: cartId];
            for (Integer i = 0;i < listOfShoppingCartItem.size();i++) {
                eventIdlist.add(listOfShoppingCartItem[i].Special_Event__c);
            }
            system.debug('eventIdlist: ' + eventIdlist);
            Id userId = UserInfo.getUserId();
            User loggedInUser = [SELECT Id, ContactId FROM User WHERE Id =: userId];
            Contact loggedContact;
            evt__Attendee__c registeredAttendy;
            pymt__PaymentX__c pymntCreated;
            if (loggedInUser.ContactId != null) {
                loggedContact = [select id, name, (select id, evt__Payment__c from evt__attendees__r where evt__Event__c in :eventIdlist) from Contact WHERE Id =: loggedInUser.ContactId];
                if (loggedContact.evt__attendees__r != null) {
                    registeredAttendy = loggedContact.evt__attendees__r;
                    system.debug('registeredAttendy: ' + registeredAttendy);
                    pymntCreated = [select id, name from pymt__PaymentX__c where id =: registeredAttendy.evt__Payment__c];
                    system.debug('pymntCreated: ' + pymntCreated);
                    return pymntCreated;
                } else {
                    return null;
                }
            }
            return null;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }

    public class EventFeeWrapper {
        public String eventFeeId {
            get; set;
        }
        public String Name {
            get; set;
        }
        public Boolean mailingEnabled {
            get; set;
        }
        public Decimal price {
            get; set;
        }
        public Decimal quantity {
            get; set;
        }
        public Boolean taxable {
            get; set;
        }
        public String eventId {
            get; set;
        }
        public List<String> tickets {
            get; set;
        }
    }

    /*
	 * @AuraEnabled public static List<EventCartWrapper> getShoppingCartItemsProd(Id cartId){
	 * try{
	 * List<pymt__Shopping_Cart_Item__c> listOfShoppingCartItem = [SELECT Id, Special_Event__r.Name, Total_Amount__c, Tax_Amount__c, Gross_Amount__c,
	 * Special_Event__r.Event_Date_Time__c, Discount__c, Special_Event__c, Discount__r.Code__c,Discount_Amount__c,
	 * Special_Event__r.Supplemental_Event_Items__c, Special_Event__r.evt__Show_Available_Seats__c,
	 * Special_Event__r.evt__Max_Additional_Attendees__c, Discount__r.Percent_Discount__c,
	 * Discount__r.Taxable__c, Discount__r.Dollar_Discount__c, Special_Event__r.evt__Accept_Donations__c
	 * FROM pymt__Shopping_Cart_Item__c
	 * WHERE pymt__Shopping_Cart__c =: cartId AND Type__c != 'Donation'];
	 * Map<Id, List<evt__Session__c>> mapOfEventInfToListOfSessions = new Map<Id, List<evt__Session__c>>();
	 * Map<Id, List<evt__Event_Fee__c>> mapOfEventIdToListOfFees = new Map<Id, List<evt__Event_Fee__c>>();
	 * for(pymt__Shopping_Cart_Item__c cartItem : listOfShoppingCartItem) {
	 * mapOfEventInfToListOfSessions.put(cartItem.Special_Event__c, new List<evt__Session__c>());
	 * mapOfEventIdToListOfFees.put(cartItem.Special_Event__c, new List<evt__Event_Fee__c>());
	 * }
	 * List<evt__Session__c> listOfSessions = [SELECT Id, Name, evt__Session_Fee__c, Available_for_Registration__c, evt__Short_Description__c, Session_Date_Time__c, evt__Event__c
	 * FROM evt__Session__c
	 * WHERE evt__Event__c IN : mapOfEventInfToListOfSessions.keySet()];
	 * List<evt__Event_Fee__c> listOfEventFee = [SELECT Id, Name, evt__Amount__c, Type__c, evt__Event__c FROM evt__Event_Fee__c
	 * WHERE evt__Event__c IN: mapOfEventIdToListOfFees.keySet()
	 * AND Type__c= 'Standard'
	 * AND evt__Limit_Per_Purchase__c = null
	 * AND Available_for_Checkout__c = true
	 * ORDER BY evt__Amount__c ASC];
	 * for(evt__Session__c sessionItem : listOfSessions) {
	 * if(mapOfEventInfToListOfSessions.containsKey(sessionItem.evt__Event__c)) {
	 * mapOfEventInfToListOfSessions.get(sessionItem.evt__Event__c).add(sessionItem);
	 * }
	 * }
	 * for(evt__Event_Fee__c feeItem : listOfEventFee) {
	 * if(mapOfEventIdToListOfFees.containsKey(feeItem.evt__Event__c)) {
	 * mapOfEventIdToListOfFees.get(feeItem.evt__Event__c).add(feeItem);
	 * }
	 * }
	 * List<EventCartWrapper> listOfEventCartWrapper = new List<EventCartWrapper>();
	 * for(pymt__Shopping_Cart_Item__c cartItem : listOfShoppingCartItem) {
	 * EventCartWrapper wrapperObj = new EventCartWrapper();
	 * wrapperObj.showSession = (mapOfEventInfToListOfSessions.get(cartItem.Special_Event__c) != null && mapOfEventInfToListOfSessions.get(cartItem.Special_Event__c).size() > 0) ? true : false;
	 * wrapperObj.listOfEventSession = mapOfEventInfToListOfSessions.get(cartItem.Special_Event__c);
	 * wrapperObj.shoppingcartItem = cartItem;
	 * wrapperObj.showDiscount = cartItem.Discount__c == null ? true : false;
	 * wrapperObj.showAddGuests = ((cartItem.Special_Event__r.evt__Show_Available_Seats__c) && (cartItem.Special_Event__r.evt__Max_Additional_Attendees__c != null && Integer.valueOf(cartItem.Special_Event__r.evt__Max_Additional_Attendees__c) > 0)) ? true : false;
	 * if(!wrapperObj.showDiscount) {
	 * wrapperObj.couponCode = cartItem.Discount__r.Code__c;
	 * wrapperObj.discountCodeMessage = cartItem.Discount__r.Code__c + ' discount code is applied.Please click here to add a new code';
	 * }
	 * wrapperObj.showZeroDiscount = (!wrapperObj.showDiscount) ? false : true;
	 * wrapperObj.listOfGuests = new List<evt__Attendee__c>();
	 * //hsing => to support dynamic price calulaton in case we have session price. only for front-end
	 * for(evt__Event_Fee__c eventfee : mapOfEventIdToListOfFees.get(cartItem.Special_Event__c)) {
	 * wrapperObj.listofEventFee = new List<String>();
	 * wrapperObj.listofEventFee.add(JSON.serialize('{ label : ' + eventfee.Name + ',' + 'value:' + eventfee.Id + '}'));
	 * }
	 * // wrapperObj.listofEventFee = mapOfEventIdToListOfFees.containsKey(cartItem.Special_Event__c) ? mapOfEventIdToListOfFees.get(cartItem.Special_Event__c) : null; //hsing => to support dynamic price calulaton in case we have session price. only for front-end
	 * if(wrapperObj.listofEventFee != null) {
	 * wrapperObj.showPicklist = true;
	 * } else {
	 * wrapperObj.showPicklist = false;
	 * }
	 * wrapperObj.totalAmount = cartItem.Total_Amount__c;
	 * wrapperObj.subTotal = cartItem.Gross_Amount__c;
	 * wrapperObj.estimatedTax = cartItem.Tax_Amount__c;
	 * wrapperObj.discountAmount = cartItem.Discount_Amount__c;
	 * wrapperObj.originalSubTotal = cartItem.Gross_Amount__c;
	 * wrapperObj.taxPercentage = 0;
	 * wrapperObj.taxable = true;
	 * wrapperObj.showDonationCheckBox = cartItem.Special_Event__r.evt__Accept_Donations__c;
	 * if(cartItem.Tax_Amount__c != 0 && cartItem.Gross_Amount__c !=0){
	 * wrapperObj.taxPercentage = cartItem.Tax_Amount__c/ cartItem.Gross_Amount__c;
	 * }
	 * if(cartItem.Discount__c != null) {
	 * if(cartItem.Discount__r.Percent_Discount__c != null) {
	 * wrapperObj.discountPercent = cartItem.Discount__r.Percent_Discount__c/100;
	 * }
	 * wrapperObj.taxable = cartItem.Discount__r.Taxable__c;
	 * }
	 * //end of hsing code
	 * listOfEventCartWrapper.add(wrapperObj);
	 * }
	 * return listOfEventCartWrapper;
	 * } catch (Exception e) {
	 * throw new AuraHandledException(e.getMessage());
	 * }
	 * }
	 */


}