/**
* UTR-356:
* Inbound Reference Handler is connected to an email service 'InboundHandlerService' that accepts emails sent to the email addresses
* listed under the EmailService. It takes the info from that email, queries for corresponding application, with the same from Application ID 
* and matches FromAddress with Referrer_1_Email__c/Referrer_2_Email__c/Referrer_3_Email__c. 
* If True, it updates the corresponding  Reference_1__c/Reference_2__c/Reference_3__c field with the email response.
*
* <AUTHOR> 
* @date     17-June-2020
*/
global class InboundReferenceHandler implements Messaging.InboundEmailHandler {
	global Messaging.InboundEmailResult handleInboundEmail(Messaging.InboundEmail email, Messaging.InboundEnvelope envelope) {

    // Create an InboundEmailResult object for returning the result of the Apex Email Service
    Messaging.InboundEmailResult result = new Messaging.InboundEmailResult();

	try{
        //Extract the App Id from Subject
        String subject = email.subject;
        integer i = subject.lastIndexOf('Application');
        String appId = subject.substring(i + 13);   //Subject: "RE: Application: <ID HERE>". Use this format, to get APP ID (starting from 0 base)
        system.debug('app Id: '+ appId);
        
        String fromAddress = email.fromAddress;
        system.debug('from emailAddress: ' + fromAddress);
        
        String body = email.HTMLBody;
        system.debug('Response: ' + body);

        List<hed__Application__c> appList = [SELECT Id, Name, Referrer_1_Email__c, Referrer_2_Email__c, Referrer_3_Email__c, Reference_1__c, Reference_2__c, Reference_3__c
                                                FROM hed__Application__c
                                                WHERE Name=:appId AND (Referrer_1_Email__c=:fromAddress OR 
                                                                    Referrer_2_Email__c=:fromAddress OR
                                                                    Referrer_3_Email__c=:fromAddress) 
                                                LIMIT 1];
        

        // Match the "from address" in the inbound email with the referrer email fields 1,2 and 3 on the application. 
        // Update the corresponding Reference textarea, only if it is not already filled.
        if(appList.size() >0){
            system.debug('appList[0]: '+appList[0]);
            if(appList[0].Referrer_1_Email__c == fromAddress && String.isBlank(appList[0].Reference_1__c)) 
                appList[0].Reference_1__c = body;
            if(appList[0].Referrer_2_Email__c == fromAddress && String.isBlank(appList[0].Reference_2__c)) 
                appList[0].Reference_2__c = body;
            if(appList[0].Referrer_3_Email__c == fromAddress && String.isBlank(appList[0].Reference_3__c)) 
                appList[0].Reference_3__c = body;

            update applist[0];

            // Set the result to true. No need to send an email back to the user 
            result.success = true;
            result.message = '';
            
            // Return the result for the Apex Email Service
            return result;
        }
        result.success = false;
        result.message = '';	//No Application records found'; removed this line as for it was generating un-necessary emails for other kind of inbound emails too.
        return result;


    } catch(Exception e){
        hed__Error__c error = new hed__Error__c();  //create TDTM Error record
        error.hed__Context_Type__c = 'Error caused by InboundReferenceHandler apex class';
        error.hed__Stack_Trace__c = e.getMessage();
        insert error;

        result.success = false;
        result.message = e.getMessage();
        return result;
    }
  }
    
}