<aura:component controller="ApplicationPDFBatchLightningController" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global" >
	<aura:attribute name="allStatuses" type="List"/>
    <aura:attribute name="selectedStatuses" type="List" default="[]" />
    <aura:attribute name="showSuccess" type="Boolean"/>
    <aura:attribute name="showSuccessMessage" type="String"/>
    <aura:attribute name="showWarningMessage" type="String"/>
    <aura:attribute name="showWarning" type="String"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:if isTrue="{!v.showWarning}">
        <!-- Show the message to ui -->
        <div class="slds-notify slds-notify_toast slds-theme_warning">
            <span class="slds-assistive-text">warning</span>
            <div class="slds-notify__content">
                <h5 class="slds-text-heading_small slds-align_absolute-center">Warning Message </h5>
                <br/>
                <p class="slds-align_absolute-center">
                    {!v.showWarningMessage}
                </p> 
            </div>
        </div>
     </aura:if>
    <aura:if isTrue="{!!v.showSuccess}">
        <lightning:layout horizontalAlign="space" verticalAlign="center" multipleRows="true">
            <aura:iteration var="a" items="{!v.allStatuses}" indexVar="indx">
                <lightning:layoutItem padding="around-small" size="6">
                    <ui:inputCheckbox aura:id="checkbox" 
                                      text="{!a}"
                                      name="{!indx}"
                                      label="{!a}" 
                                      change="{!c.selectoptionvalue}"/>
                </lightning:layoutItem>
            </aura:iteration>
            <lightning:button label="Submit" onclick="{! c.callApexJob }"/>
        </lightning:layout>
    </aura:if>
    <aura:if isTrue="{!v.showSuccess}">
        <!-- Show the message to ui -->
        <div class="slds-notify slds-notify_toast slds-theme_success">
            <span class="slds-assistive-text">success</span>
            <div class="slds-notify__content">
                <h5 class="slds-text-heading_small slds-align_absolute-center">Success Message </h5>
                <br/>
                <p class="slds-align_absolute-center">
                    {!v.showSuccessMessage}
                </p> 
            </div>
        </div>
     </aura:if>
</aura:component>