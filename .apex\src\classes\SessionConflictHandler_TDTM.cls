/**
 * @description Calls ConflictCheckService class methods to check for time conflicts with other session attendee records associated to the same contact
 * If the parent event is not marked ‘conflict check disabled’ AND the attendee is not in a status of ‘Attended’ or ‘Cancelled’ AND the contact is populated
 * <AUTHOR>
 * @version 1.0
 * @created 03-AUG-2020
 */
global class SessionConflict<PERSON><PERSON><PERSON>_TDTM extends hed.TDTM_Runnable{

    public static final String ERROR_MESSAGE = 'You are registering for a session you have already registered for, or an event which is scheduled at the same time of an session you have registered for. Please contact an administrator for assistance';
    
	/**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Session Attendee records from trigger new 
     * @param oldList the list of Session Attendee records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, Before Update)
     * @param objResult the describe for Session Attendee 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();

        List<evt__Session_Assignment__c> newSesAsgmtList  = new List<evt__Session_Assignment__c>();	//insert/update records of Session Assignment with Attendee Id populated
        List<Id> parAtnIdList = new List<Id>();		//this will contain all the parent Attendee Ids of incoming records
        Map<Id, evt__Session_Assignment__c> updSesAsgmtMap = new Map<Id, evt__Session_Assignment__c>();		//to be used in Before Update Trigger context
        List<Id> atnIdList = new List<Id>();
        for(evt__Session_Assignment__c rec: (List<evt__Session_Assignment__c>)newlist)
            if(rec.evt__attendee__c != null){
            	newSesAsgmtList.add(rec);
                parAtnIdList.add(rec.evt__Attendee__c);
            }
        
        Map<Id, evt__Attendee__c> parAttendeeMap = new Map<Id, evt__Attendee__c>([Select Id, evt__Event__r.evt__Disable_Conflict_Checking__c, evt__Attended__c, evt__Invitation_Status__c, 
                                                                                  evt__Contact__c from evt__Attendee__c Where Id IN : parAtnIdList]);
        
        //Prepare a Map (AttendeeId => List of insert/update SeesionAsgmt records)
        Map<Id, List<evt__Session_Assignment__c>> AtnIdSesAsgmtMap = new Map<Id, List<evt__Session_Assignment__c>>();
        for(evt__Session_Assignment__c rec : newSesAsgmtList){
            
            evt__Attendee__c parAtn = parAttendeeMap.get(rec.evt__Attendee__c);
            //evt__Special_Event__c parEvent = parEventMap.get(parAtn.evt__Event__c);
            
            //Check if the parent event is not marked ‘conflict check disabled’ AND the attendee is not in a status of ‘Attended’ or ‘Cancelled’ AND the contact is populated, then add it to map
            if(parAtn != null && parAtn.evt__Event__r.evt__Disable_Conflict_Checking__c == False && parAtn.evt__Attended__c == False && parAtn.evt__Invitation_Status__c != 'Cancelled' && parAtn.evt__Contact__c != null){
                
                if(AtnIdSesAsgmtMap.containsKey(rec.evt__Attendee__c)){
                    List<evt__Session_Assignment__c> sesAsgmtList = AtnIdSesAsgmtMap.get(rec.evt__Attendee__c);
                    sesAsgmtList.add(rec);
                }
                else{
                    List<evt__Session_Assignment__c> sesAsgmtList = new List<evt__Session_Assignment__c>();
                    sesAsgmtList.add(rec);
                    AtnIdSesAsgmtMap.put(rec.evt__Attendee__c, sesAsgmtList);
                }
                
                atnIdList.add(rec.evt__attendee__c);
                updSesAsgmtMap.put(rec.Id, rec);
            }
        }
        
        //Query All Existing Sessio Assignment records that are related to all Attendees of incoming record
        List<evt__Session_Assignment__c> allExistingSesAsgmtList = [SELECT Id, evt__Attendee__c, evt_Start__c, evt_End__c 
                                                                    FROM evt__Session_Assignment__c 
                                                                    WHERE evt__Attendee__c IN : parAttendeeMap.keySet() AND
                                                                    evt__Status__c = 'Confirmed'];

        //Create Map for all AtendeeId => AttendeeList (Existing + New) records
        Map<Id, List<evt__Session_Assignment__c>> checkForConflictMap = new Map<Id, List<evt__Session_Assignment__c>>();
        
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert){
            allExistingSesAsgmtList.addAll(newSesAsgmtList);
            
            //populate Map fow All Existng + New records
            for(evt__Session_Assignment__c rec : allExistingSesAsgmtList){
                if(checkForConflictMap.containsKey(rec.evt__Attendee__c)){
                    List<evt__Session_Assignment__c> recList = checkForConflictMap.get(rec.evt__Attendee__c);
                    recList.add(rec);
                }
                else{
                    List<evt__Session_Assignment__c> recList = new List<evt__Session_Assignment__c>();
                    recList.add(rec);
                    checkForConflictMap.put(rec.evt__Attendee__c, recList);
                }
            }
            
        }
        
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){

            //Populate Map with Existing records and New Trigger records (and making sure New trigger records are not counted again in Existing records list)
            for(evt__Session_Assignment__c rec : allExistingSesAsgmtList){

                if(checkForConflictMap.containsKey(rec.evt__Attendee__c)){
                    List<evt__Session_Assignment__c> recList = checkForConflictMap.get(rec.evt__Attendee__c);
                    if(updSesAsgmtMap.containsKey(rec.Id))
                    	recList.add(updSesAsgmtMap.get(rec.Id));		// if record exist in Update Trigger context then select it from Update Trigger
                    else
                        recList.add(rec);
                }
                else{
                    List<evt__Session_Assignment__c> recList = new List<evt__Session_Assignment__c>();
                    if(updSesAsgmtMap.containsKey(rec.Id))
                    	recList.add(updSesAsgmtMap.get(rec.Id));
                    else
                        recList.add(rec);
                    checkForConflictMap.put(rec.evt__Attendee__c, recList);
                }
                    
            }
        }

        
        //Call ConflictCheckService for each Contact's AttendeeList
        for(Id atnId : atnIdList){
            List<evt__Session_Assignment__c> sesAsgmtList = checkForConflictMap.get(atnId);
            Boolean conflictFound = false;
            if(sesAsgmtList != null && sesAsgmtList.size() >0)
                conflictFound = ConflictCheckService.hasConflicts(sesAsgmtList, 'evt_Start__c', 'evt_End__c');
            if(conflictFound){
                //In case of conflict, add error to all the insert attendee records of this contact
                for(evt__Session_Assignment__c rec : AtnIdSesAsgmtMap.get(atnId))
                    rec.addError(ERROR_MESSAGE);
            }
        }

        return null;
    }
}