/** 
* @description    Update Counter value on Certificate Achievement record
* <AUTHOR> 
* @version        1.0 
*/
global class CCON_UpdateCounter_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
    * @param newList the list of course connection records from trigger new 
     * @param oldList the list of course connection records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc...)
     * @param objResult the describe for course connection 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) 
    {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        System.debug('DEBUGCCON_UpdateCounter_TDTM '+triggerAction);
        Set<Id> conIds = new Set<Id>();
        
        Set<Id> relatedApps = new Set<Id>(); 
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert)
        {
            for(hed__Course_Enrollment__c ce : (List<hed__Course_Enrollment__c>)newList)
            {
                if((ce.Suite__c == 'Leadership Suite' || ce.Suite__c == 'Focused Suite') &&
                   ce.hed__Status__c == 'Complete' &&
                   ce.hed__Contact__c != null){
                       conIds.add(ce.hed__Contact__c); 
                   }
            }    
        }
        else if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            Map<Id, hed__Course_Enrollment__c> oldMap = new Map<Id, hed__Course_Enrollment__c>(((List<hed__Course_Enrollment__c>) oldList)); 
            hed__Course_Enrollment__c oldCE;
            for(hed__Course_Enrollment__c ce: (List<hed__Course_Enrollment__c>)newList){
                oldCE = oldMap.get(ce.Id);
                if(oldCE.hed__Status__c != ce.hed__Status__c && 
                   (ce.hed__Status__c == 'Complete' || oldCE.hed__Status__c == 'Complete') &&
                   (ce.Suite__c == 'Leadership Suite' || ce.Suite__c == 'Focused Suite') && 
                   ce.hed__Contact__c != null)
                {
                    conIds.add(ce.hed__Contact__c);  
                }
            }
        }
        else if(triggerAction == hed.TDTM_Runnable.Action.AfterDelete)
        {
            for(hed__Course_Enrollment__c ce : (List<hed__Course_Enrollment__c>)oldList)
            {
                if((ce.Suite__c == 'Leadership Suite' || ce.Suite__c == 'Focused Suite') &&
                   ce.hed__Status__c == 'Complete' && 
                   ce.hed__Contact__c != null){
                       conIds.add(ce.hed__Contact__c); 
                   }
            }    
        }
        System.debug('DEBUGCCON_UpdateCounter_TDTM '+conIds);
        
        if (conIds.size() > 0){
            Map<Id, Certificate_Achievement__c> conIdToCertificateMap = getConIdToCertificateMap(conIds);
            List<hed__Course_Enrollment__c> ccList = getAllCourseConnForContacts(conIds);
            List<Certificate_Achievement__c> caListToUpsert = recalculateCounterValue(conIdToCertificateMap, ccList);
            System.debug('DEBUGCCON_UpdateCounter_TDTM caListToUpsert '+caListToUpsert);
            if(caListToUpsert.size() > 0) 
                upsert caListToUpsert; 
        }
        
        return dmlWrapper;
    }
    //Get ContactID to Certificate Achievement record map
    public Map<Id, Certificate_Achievement__c> getConIdToCertificateMap(Set<Id> conIds){
        Map<Id, Certificate_Achievement__c> conIdToCertificateMap = new Map<Id, Certificate_Achievement__c>();
        List<Certificate_Achievement__c> caList = [SELECT Id, Contact__c 
                                                   FROM Certificate_Achievement__c WHERE Contact__c IN :conIds 
                                                   ORDER BY CreatedDate DESC];
        for(Certificate_Achievement__c ca : caList){
            if(conIdToCertificateMap.get(ca.Contact__c) == null){
                conIdToCertificateMap.put(ca.Contact__c, new Certificate_Achievement__c(Course_Counter_Optional__c = 0, 
                                                                                        Course_Counter_Required__c = 0,
                                                                                        Id = ca.Id));
            }
        }
        System.debug('DEBUGCCON_UpdateCounter_TDTM conIdToCertificateMap '+conIdToCertificateMap);
        return conIdToCertificateMap;
    }
    //Get all Course Enrollments for ContactIDs 
    public List<hed__Course_Enrollment__c> getAllCourseConnForContacts(Set<Id> conIds){
        List<hed__Course_Enrollment__c> ccList = [SELECT Id, Suite__c, hed__Contact__c 
                                                  FROM hed__Course_Enrollment__c WHERE hed__Contact__c IN :conIds AND
                                                  Suite__c IN ('Leadership Suite', 'Focused Suite') AND
                                                  hed__Status__c = 'Complete'
                                                  ORDER BY hed__Contact__c, CreatedDate DESC];
        System.debug('DEBUGCCON_UpdateCounter_TDTM ccList '+ccList);
        return ccList;
    }
    //Recalculate Counter Value for contacts
    public List<Certificate_Achievement__c> recalculateCounterValue(Map<Id, Certificate_Achievement__c> conIdToCertificateMap, List<hed__Course_Enrollment__c> ccList){
        System.debug('DEBUGCCON_UpdateCounter_TDTM recalculateCounterValue');
        Id conId;
        Certificate_Achievement__c ca;
        for(hed__Course_Enrollment__c eachCC : ccList){
            conId = eachCC.hed__Contact__c;
            ca = conIdToCertificateMap.get(conId);
            if(ca == null){
                ca = new Certificate_Achievement__c(Course_Counter_Optional__c = 0, 
                                                    Course_Counter_Required__c = 0,
                                                    Contact__c = conId);
            }
            if(eachCC.Suite__c == 'Leadership Suite')
                ca.Course_Counter_Required__c += 1;
            else if(eachCC.Suite__c == 'Focused Suite')
                ca.Course_Counter_Optional__c += 1;
            conIdToCertificateMap.put(conId, ca);
        }
        return conIdToCertificateMap.values();
    }
}