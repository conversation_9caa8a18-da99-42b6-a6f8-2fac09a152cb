/**
* Helper Class that does contact name matching
* and staging table field swapping as needed. 
* See <PERSON><PERSON><PERSON> ticket 'UTR-319'
*
*Related TDTM Class: Contact_Trigger_TDTM
*
* <AUTHOR> 
* @since   2020-03-05 
*/

public class StagingTransformService {
    
/**
 * <AUTHOR> 
 * @since   2020-03-05 
 *
 *
 * This method takes the Trigger.old and Trigger.New lists of records in beforeUpdate context
 * and checks given contact fields and matches/updates accordingly. Finnaly we update the needed fields
 * and set those values to the new list "newCon"
 *  
 * 
 * @param oldCon This is the list of Trigger.old
 * @param newCon This is the list of Trigger.new
 * @param i This is the given index for whatever loop the function is called in
 *  
 * 
 */
    public static void contactNameMatching(Contact newCon){
        contactNameMatching( null, newCon );
    }

    public static void contactNameMatching(Contact oldCon, Contact newCon){

              //first name logic
        if((oldCon == null || newCon.FirstName != oldCon.FirstName) && newCon.Preferred_First_Name__c == null){
            newCon.Matching_Preferred_First_Name__c = newCon.FirstName;
            
        } else if ( (oldCon == null && newCon.Preferred_First_Name__c != null) || ((oldCon.Preferred_First_Name__c != null) && (newCon.Preferred_First_Name__c != oldCon.Preferred_First_Name__c)) ){
            newCon.Matching_Preferred_First_Name__c = newCon.Preferred_First_Name__c;
        
        }
        
        //last name logic
        if((oldCon == null || oldCon.LastName != newCon.LastName) && newCon.Former_Last_Name__c == null){
            newCon.Matching_Former_Last_Name__c = newCon.LastName;
        
        } else if( (oldCon == null && newCon.Former_Last_Name__c != null) || ((oldCon.Former_Last_Name__c != null) && (newCon.Former_Last_Name__c != oldCon.Former_Last_Name__c)) ){
            newCon.Matching_Former_Last_Name__c = newCon.Former_Last_Name__c;
        
        }
        
    }

    /**
     * <AUTHOR> Boulden
     * @since   2020-04-02 
     *
     *
     * Takes a multiselect picklist, and a list of values to exclude should one be found, and eliminates incompatiable values
     * 
     * @return The new value of the multiselect list
     * @param initVal Current value of the multiselectlist
     * @param stagingTableTypes This the Map representation of the staging table and values. See associated TDTM class for this Map
     *  
     * 
     */
    public static String removeExludedFromMultiselect(String initVal, Map<String, List<String>> stagingTableTypes ){
    
        //Check for case that no value is provided
        if ( String.isBlank(initVal) )
            return null;

        //List of all types currently on 
        Set<String> types = new Set<String>( initVal.split(';') );

        //Loop through values in table, if types includes table value, remove all of the 'excluded values' assigned to that type
        for ( String val : stagingTableTypes.keySet() )
            if ( types.contains(val) )
                for ( String excludeVal : stagingTableTypes.get(val) )
                    types.remove(excludeVal);

        
        return String.join( new List<String>(types), ';' );	
        
    }
}