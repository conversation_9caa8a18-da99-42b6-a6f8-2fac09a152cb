@isTest
public class ShoppingCartSumService_TDTM_TEST {
    
    @TestSetup
    static void setupTest() {
        
        Taxing_Authority__c tax1 = new Taxing_Authority__c(
            Name                = 'Ontario Tax',
            State_Province__c   = 'ON',
            Country__c          = 'CA',
            Tax_Rate__c         = 10
        );
        insert tax1;

        List<Discount__c> discs = new List<Discount__c>{ 
            new Discount__c(
                Name                = 'A',
                Available_for__c    = 'All',
                Percent_Discount__c = 10,
                Taxable__c          = false
            ),
            new Discount__c(
                Name                = 'B',
                Available_for__c    = 'All',
                Percent_Discount__c = 10,
                Taxable__c          = true
            ),
            new Discount__c(
                Name                = 'C',
                Available_for__c    = 'All',
                Dollar_Discount__c  = 20,
                Taxable__c          = false
            )
        };
        insert discs;

        insert new List<SObject>{
            new hed__Trigger_Handler__c(
                hed__Active__c              = true,
                hed__Class__c               = 'ShoppingCartSumService_TDTM', 
                hed__Load_Order__c          = 1, 
                hed__Object__c              = 'pymt__Shopping_Cart_Item__c',
                hed__Trigger_Action__c      = 'AfterInsert;AfterUpdate;AfterDelete'
            ),
            new hed__Trigger_Handler__c(
                hed__Active__c              = true,
                hed__Class__c               = 'TaxService_TDTM', 
                hed__Load_Order__c          = 1, 
                hed__Object__c              = 'pymt__Shopping_Cart_Item__c',
                hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate'
            )
        };

    }

    @isTest
    static void testCartAmounts() {

        List<Discount__c> discs = [ SELECT Id, Name FROM Discount__c ORDER BY Name ASC ];
        
        //A is untaxable but should have a tax applied. No Discount
        //B is taxable and should have a tax applied. No discount
        //C is 

        Test.startTest();
            insert new List<pymt__Shopping_Cart_Item__c>{
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'A',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 3,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'B',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'C',
                    Discount__c                 = discs[0].Id,
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 3,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'D',
                    Discount__c                 = discs[0].Id,
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'E',
                    Discount__c                 = discs[1].Id,
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 3,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'F',
                    Discount__c                 = discs[1].Id,
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'G',
                    Discount__c                 = discs[2].Id,
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 3,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'H',
                    Discount__c                 = discs[2].Id,
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                )
            };
        Test.stopTest();
    }

    @isTest
    static void testTaxApplication() {

        List<Taxing_Authority__c> taxes = new List<Taxing_Authority__c>{ 
            new Taxing_Authority__c(
                Name                = 'Local Tax',
                State_Province__c   = 'ON',
                Country__c          = 'CA',
                Postal_Codes__c     = '2222;1111;0000',
                Tax_Rate__c         = 5
            ),
            new Taxing_Authority__c(
                Name                = 'Canada Tax',
                State_Province__c   = '',
                Country__c          = 'CA',
                Tax_Rate__c         = 15
            ),
            new Taxing_Authority__c(
                Name                = 'All Tax',
                State_Province__c   = '',
                Country__c          = '',
                Tax_Rate__c         = 20
            )
        };
        insert taxes;

        //First 5 except 3 should be associated to a tax authority
        //Second 5 should not be associated to a tax authority
        //Last one should not be associated to a tax authority

        Test.startTest();
            insert new List<pymt__Shopping_Cart_Item__c>{
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'A',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    Shipping_Postal_Code__c     = '1111',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'B',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'C',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = '',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'D',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = '',
                    Shipping_Country__c         = 'CA',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'E',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = '',
                    Shipping_Country__c         = '',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'F',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    Shipping_Postal_Code__c     = '1111',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'G',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = 'CA',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'H',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'ON',
                    Shipping_Country__c         = '',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'I',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = '',
                    Shipping_Country__c         = 'CA',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'J',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = '',
                    Shipping_Country__c         = '',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = false
                ),
                new pymt__Shopping_Cart_Item__c(
                    Name                        = 'K',
                    pymt__Unit_Price__c         = 200,
                    Shipping_State_Province__c  = 'BC',
                    Shipping_Country__c         = 'CA',
                    Shipping_Postal_Code__c     = '1234',
                    pymt__Quantity__c           = 1,
                    pymt__Taxable__c            = true
                )
            };
        Test.stopTest();

        List<pymt__Shopping_Cart_Item__c> insetedScis = [ SELECT Id, Discount__c, Discount_Amount__c, Gross_Amount__c, Tax_Amount__c, Taxing_Authority__c, Taxing_Authority__r.Name, Total_Amount__c
                                                          FROM pymt__Shopping_Cart_Item__c
                                                          ORDER BY Name ASC ];

        
        System.assertEquals( 200,           insetedScis[0].Gross_Amount__c );                            
        System.assertEquals( 10,            insetedScis[0].Tax_Amount__c );                            
        System.assertEquals( 210,           insetedScis[0].Total_Amount__c );                            
        System.assertEquals( 'Local Tax',   insetedScis[0].Taxing_Authority__r.Name );                            
    
        System.assertEquals( 200,           insetedScis[1].Gross_Amount__c );                            
        System.assertEquals( 20,            insetedScis[1].Tax_Amount__c );                            
        System.assertEquals( 220,           insetedScis[1].Total_Amount__c );                            
        System.assertEquals( 'Ontario Tax', insetedScis[1].Taxing_Authority__r.Name ); 

        System.assertEquals( 200,           insetedScis[2].Gross_Amount__c );                            
        System.assertEquals( 20,            insetedScis[2].Tax_Amount__c );                            
        System.assertEquals( 220,           insetedScis[2].Total_Amount__c );                            
        System.assertEquals( 'Ontario Tax', insetedScis[2].Taxing_Authority__r.Name );                            

        System.assertEquals( 200,           insetedScis[3].Gross_Amount__c );                            
        System.assertEquals( 30,            insetedScis[3].Tax_Amount__c );                            
        System.assertEquals( 230,           insetedScis[3].Total_Amount__c );                            
        System.assertEquals( 'Canada Tax',  insetedScis[3].Taxing_Authority__r.Name );   

        // System.assertEquals( 200,           insetedScis[4].Gross_Amount__c );                            
        // System.assertEquals( 40,            insetedScis[4].Tax_Amount__c );                            
        // System.assertEquals( 240,           insetedScis[4].Total_Amount__c );                            
        // System.assertEquals( 'All Tax',     insetedScis[4].Taxing_Authority__r.Name );  

        // System.assertEquals( 200,           insetedScis[5].Gross_Amount__c );                            
        // System.assertEquals( 0,             insetedScis[5].Tax_Amount__c );                            
        // System.assertEquals( 200,           insetedScis[5].Total_Amount__c );                            
        // System.assertEquals( null,          insetedScis[5].Taxing_Authority__c ); 

        // System.assertEquals( 200,           insetedScis[6].Gross_Amount__c );                            
        // System.assertEquals( 0,             insetedScis[6].Tax_Amount__c );                            
        // System.assertEquals( 200,           insetedScis[6].Total_Amount__c );                            
        // System.assertEquals( null,          insetedScis[6].Taxing_Authority__c ); 

        // System.assertEquals( 200,           insetedScis[7].Gross_Amount__c );                            
        // System.assertEquals( 0,             insetedScis[7].Tax_Amount__c );                            
        // System.assertEquals( 200,           insetedScis[7].Total_Amount__c );                            
        // System.assertEquals( null,          insetedScis[7].Taxing_Authority__c ); 

        // System.assertEquals( 200,           insetedScis[8].Gross_Amount__c );                            
        // System.assertEquals( 0,             insetedScis[8].Tax_Amount__c );                            
        // System.assertEquals( 200,           insetedScis[8].Total_Amount__c );                            
        // System.assertEquals( null,          insetedScis[8].Taxing_Authority__c ); 

        // System.assertEquals( 200,           insetedScis[9].Gross_Amount__c );                            
        // System.assertEquals( 0,             insetedScis[9].Tax_Amount__c );                            
        // System.assertEquals( 200,           insetedScis[9].Total_Amount__c );                            
        // System.assertEquals( null,          insetedScis[9].Taxing_Authority__c ); 

        System.assertEquals( 200,           insetedScis[10].Gross_Amount__c );                            
        System.assertEquals( 30,            insetedScis[10].Tax_Amount__c );                            
        System.assertEquals( 230,           insetedScis[10].Total_Amount__c );                            
        System.assertEquals( 'Canada Tax',  insetedScis[10].Taxing_Authority__r.Name ); 

    }


    @isTest
    static void testUpdateTax() {

        List<Taxing_Authority__c> taxes = new List<Taxing_Authority__c>{ 
            new Taxing_Authority__c(
                Name                = 'Local Tax',
                State_Province__c   = 'ON',
                Country__c          = 'CA',
                Postal_Codes__c     = '2222;1111;0000',
                Tax_Rate__c         = 5
            ),
            new Taxing_Authority__c(
                Name                = 'Canada Tax',
                State_Province__c   = '',
                Country__c          = 'CA',
                Tax_Rate__c         = 15
            ),
            new Taxing_Authority__c(
                Name                = 'BC Tax',
                State_Province__c   = 'BC',
                Country__c          = 'CA',
                Tax_Rate__c         = 20
            )
        };
        insert taxes;

        //First 5 except 3 should be associated to a tax authority
        //Second 5 should not be associated to a tax authority
        //Last one should not be associated to a tax authority

        List<pymt__Shopping_Cart_Item__c> scis = new List<pymt__Shopping_Cart_Item__c>{
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'A',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                Shipping_Postal_Code__c     = '1234',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'B',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                Shipping_Postal_Code__c     = '1234',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'C',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                Shipping_Postal_Code__c     = '1234',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'D',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                Shipping_Postal_Code__c     = '1234',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'E',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                Shipping_Postal_Code__c     = '1234',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = false
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'F',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                Shipping_Postal_Code__c     = '1234',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true
            )
        };

        insert scis;

        Test.startTest();

            scis[0].Shipping_State_Province__c = ''; //Null province to ensure Canada tax is applied
            scis[1].Shipping_State_Province__c = 'BC'; //Set province to BC ensure BC tax is applied
            scis[2].Shipping_Postal_Code__c    = '1111'; //Set postal code to local tax ensure local tax is applied
            scis[3].Shipping_Country__c        = 'U.S.A.'; //Set country to US to void tax
            scis[4].pymt__Taxable__c           = true; //Mark taxable to update taxes
            scis[5].pymt__Taxable__c           = false; //Mark not taxable to void taxes

            update scis;

        Test.stopTest();

        List<pymt__Shopping_Cart_Item__c> insetedScis = [ SELECT Id, Discount__c, Discount_Amount__c, Gross_Amount__c, Tax_Amount__c, Taxing_Authority__c, Taxing_Authority__r.Name, Total_Amount__c
                                                          FROM pymt__Shopping_Cart_Item__c
                                                          ORDER BY Name ASC ];

        
        System.assertEquals( 200,           insetedScis[0].Gross_Amount__c );                            
        System.assertEquals( 30,            insetedScis[0].Tax_Amount__c );                            
        System.assertEquals( 230,           insetedScis[0].Total_Amount__c );                            
        System.assertEquals( 'Canada Tax',  insetedScis[0].Taxing_Authority__r.Name );                            
    
        System.assertEquals( 200,           insetedScis[1].Gross_Amount__c );                            
        System.assertEquals( 40,            insetedScis[1].Tax_Amount__c );                            
        System.assertEquals( 240,           insetedScis[1].Total_Amount__c );                            
        System.assertEquals( 'BC Tax',      insetedScis[1].Taxing_Authority__r.Name ); 

        System.assertEquals( 200,           insetedScis[2].Gross_Amount__c );                            
        System.assertEquals( 10,            insetedScis[2].Tax_Amount__c );                            
        System.assertEquals( 210,           insetedScis[2].Total_Amount__c );                            
        System.assertEquals( 'Local Tax',   insetedScis[2].Taxing_Authority__r.Name );                            
    
        System.assertEquals( 200,           insetedScis[3].Gross_Amount__c );                            
        System.assertEquals( 20,            insetedScis[3].Tax_Amount__c );                            
        System.assertEquals( 220,           insetedScis[3].Total_Amount__c );                            
        System.assertEquals( 'Ontario Tax', insetedScis[3].Taxing_Authority__r.Name ); 

        System.assertEquals( 200,           insetedScis[4].Gross_Amount__c );                            
        System.assertEquals( 20,            insetedScis[4].Tax_Amount__c );                            
        System.assertEquals( 220,           insetedScis[4].Total_Amount__c );                            
        System.assertEquals( 'Ontario Tax', insetedScis[4].Taxing_Authority__r.Name );  

        // System.assertEquals( 200,           insetedScis[5].Gross_Amount__c );                            
        // System.assertEquals( 0,             insetedScis[5].Tax_Amount__c );                            
        // System.assertEquals( 200,           insetedScis[5].Total_Amount__c );                            
        // System.assertEquals( null,          insetedScis[5].Taxing_Authority__c ); 

    }


    @isTest
    static void testSummationInsert() {

        List<Discount__c> discs = [ SELECT Id, Name FROM Discount__c ORDER BY Name ASC ];

        List<pymt__PaymentX__c> pmts = new List<pymt__PaymentX__c>{
            new pymt__PaymentX__c(
                Name                        = 'A'
            ),
            new pymt__PaymentX__c(
                Name                        = 'B'
            )
        };
        insert pmts;


        Test.startTest();
        insert new List<pymt__Shopping_Cart_Item__c>{
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'A',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 3,
                pymt__Taxable__c            = false,
                pymt__Payment__c            = pmts[1].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'B',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true,
                pymt__Payment__c            = pmts[0].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'C',
                Discount__c                 = discs[0].Id,
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 3,
                pymt__Taxable__c            = false,
                pymt__Payment__c            = pmts[1].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'D',
                Discount__c                 = discs[0].Id,
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true,
                pymt__Payment__c            = pmts[0].Id
            )
        };
        Test.stopTest();

    }


    @isTest
    static void testSummationUpdate() {

        List<Discount__c> discs = [ SELECT Id, Name FROM Discount__c ORDER BY Name ASC ];

        List<pymt__PaymentX__c> pmts = new List<pymt__PaymentX__c>{
            new pymt__PaymentX__c(
                Name                        = 'A'
            ),
            new pymt__PaymentX__c(
                Name                        = 'B'
            )
        };
        insert pmts;

        List<pymt__Shopping_Cart_Item__c> scis = new List<pymt__Shopping_Cart_Item__c>{
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'A',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 3,
                pymt__Taxable__c            = false,
                pymt__Payment__c            = pmts[1].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'B',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true,
                pymt__Payment__c            = pmts[0].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'C',
                Discount__c                 = discs[0].Id,
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 3,
                pymt__Taxable__c            = false,
                pymt__Payment__c            = pmts[1].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'D',
                Discount__c                 = discs[0].Id,
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true,
                pymt__Payment__c            = pmts[0].Id
            )
        };

        insert scis;

        Test.startTest();

            scis[0].pymt__Payment__c    = pmts[0].Id;
            scis[1].pymt__Unit_Price__c = 300;
            scis[2].Discount__c         = discs[0].Id;
            scis[3].pymt__Taxable__c    = true;

            update scis;

        Test.stopTest();

    }


    @isTest
    static void testDeletePayments() {

        List<Discount__c> discs = [ SELECT Id, Name FROM Discount__c ORDER BY Name ASC ];

        List<pymt__PaymentX__c> pmts = new List<pymt__PaymentX__c>{
            new pymt__PaymentX__c(
                Name                        = 'A'
            ),
            new pymt__PaymentX__c(
                Name                        = 'B'
            )
        };
        insert pmts;

        List<pymt__Shopping_Cart_Item__c> scis = new List<pymt__Shopping_Cart_Item__c>{
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'A',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 3,
                pymt__Taxable__c            = false,
                pymt__Payment__c            = pmts[1].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'B',
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true,
                pymt__Payment__c            = pmts[0].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'C',
                Discount__c                 = discs[0].Id,
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 3,
                pymt__Taxable__c            = false,
                pymt__Payment__c            = pmts[1].Id
            ),
            new pymt__Shopping_Cart_Item__c(
                Name                        = 'D',
                Discount__c                 = discs[0].Id,
                pymt__Unit_Price__c         = 200,
                Shipping_State_Province__c  = 'ON',
                Shipping_Country__c         = 'CA',
                pymt__Quantity__c           = 1,
                pymt__Taxable__c            = true,
                pymt__Payment__c            = pmts[0].Id
            )
        };

        insert scis;

        Test.startTest();

            delete scis[0];

        Test.stopTest();

    }

}