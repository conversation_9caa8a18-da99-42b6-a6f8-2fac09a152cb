@IsTest
private class CaseController_TEST {
	@testSetup
	static void testSetup () {
		List<Account> accList = new List<Account>();
		Account acc1 = (Account)TestFactory.createSObject(new Account(Name = 'test Account'));
		accList.add(acc1);
		insert accList;

		Contact con = (Contact)TestFactory.createSObject(new Contact(AccountId = accList[0].Id, Email = '<EMAIL>', FirstName = 'Joe', LastName  = 'Curve'));
		insert con;

		// insert task (past activity)
		Task t = new Task();
		t.WhoId = con.Id;
		t.Subject = 'Task';
		t.Status = 'Completed';
		t.ActivityDate = system.today().AddDays(-2);
		insert t;

		// insert event (future activity)
		Event e = new Event();
		e.WhoId = con.Id;
		e.StartDateTime = system.now().AddDays(2);
		e.EndDateTime = system.now().AddDays(3);
		insert e;

		// insert cases
		list<Case> cases = new list<Case>();
		Case cs1 = new Case();
		cs1.ContactId = con.Id;
		cs1.Subject = 'Case Controller Test';
		cs1.Reason = 'Testing';
		cs1.Origin = 'Web';
		cs1.Priority = 'Medium';
		cases.add(cs1);

		insert cases;

		// Create test task
		Task task = new Task();
		task.WhoId = con.Id;
		task.WhatId = cases[0].Id;
		task.Subject = 'Test Task';
		task.Status = 'Completed';
		task.TaskSubtype = 'Call';
		task.ActivityDate = system.today();
		insert task;

		// Create test appointment
		sfal__Appointment__c appointment = new sfal__Appointment__c();
		appointment.Name = 'Test Appointment';
		appointment.sfal__Topic__c = 'Test';
		appointment.sfal__RelatedCase__c = cases[0].Id;
		appointment.sfal__StartDateTime__c = system.now();
		appointment.sfal__EndDateTime__c = system.now().AddHours(1);
		appointment.OwnerId = UserInfo.getUserId();
		insert appointment;
	}

	@isTest static void testGetCaseWithContact() {
		Case myCase = [SELECT Id, ContactId FROM Case LIMIT 1];
		Test.startTest();
		Case result1 = caseController.getCaseWithContact(myCase.Id);
		Test.stopTest();
	}

	@isTest
	static void testGetTasksByCase() {
		Case myCase = [SELECT Id, ContactId FROM Case LIMIT 1];
		Test.startTest();
		List<Task> result2 = caseController.getTasksByCase(myCase.Id);
		Test.stopTest();
	}

	@isTest
	static void testGetAppointmentsByCase() {
		Case myCase = [SELECT Id, ContactId FROM Case LIMIT 1];
		Test.startTest();
		List<sfal__Appointment__c> results = caseController.getAppointmentsByCase(myCase.Id);
		Test.stopTest();
	}
}