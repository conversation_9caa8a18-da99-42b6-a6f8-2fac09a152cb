/**
 * @description Assigns shipping address to payment record to ensure that tax is calculated correctly
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-17
 * @modified 2020-08-17
 */
global without sharing class PaymentService_TDTM extends hed.TDTM_Runnable {
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Object
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();

        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert) {

            for(Integer i= 0; i < newList.size(); i++){
                pymt__PaymentX__c pymt = (pymt__PaymentX__c)newList[i]; 

                //If the payment has no address, assign the address as the Rotman School of Management
                if ( String.isBlank(pymt.pymt__Ship_To_City__c) && String.isBlank(pymt.pymt__Ship_To_Street__c) && String.isBlank(pymt.pymt__Ship_To_Postal_Code__c) && String.isBlank(pymt.pymt__Ship_To_Country__c) && String.isBlank(pymt.pymt__Ship_To_State__c) ) {
                    pymt.pymt__Ship_To_Street__c        = '105 St. George St';
                    pymt.pymt__Ship_To_City__c          = 'Toronto';
                    pymt.pymt__Ship_To_State__c         = 'ON';
                    pymt.pymt__Ship_To_Country__c       = 'Canada';
                    pymt.pymt__Ship_To_Postal_Code__c   = 'M5S 3E6';
                }
            }
        }

        return dmlWrapper; 

    }
}