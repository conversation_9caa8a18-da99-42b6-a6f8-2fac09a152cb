/**
 * Created by <PERSON><PERSON> on 2023-02-07.
 */

@IsTest
private class CloneCheckListItemsTest {

    @testSetup
    static void testSetup () {
        Map<String, Schema.RecordTypeInfo> acctTypesByDevName = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName();
        Map<String, Schema.RecordTypeInfo> citTypesByDevName = Schema.SObjectType.Application_Checklist_Item_Template__c.getRecordTypeInfosByDeveloperName();

        List<Account> progs = new List<Account>{
                new Account(
                        RecordTypeId                            = acctTypesByDevName.get('Academic_Program').getRecordTypeId(),
                        Name                                    = 'FTMBA',
                        Applicant_Opportunity_Record_Type__c    = 'Degree_Program_Prospect'
                ),
                new Account(
                        RecordTypeId                            = acctTypesByDevName.get('Academic_Program').getRecordTypeId(),
                        Name                                    = 'FTMBA',
                        Applicant_Opportunity_Record_Type__c    = 'Full_Time_and_Specialized'
                )
        };

        List<Contact> cons = new List<Contact>{
                new Contact(
                        FirstName   = 'Test Fn 1',
                        LastName    = 'Test LN 1',
                        Email		= '<EMAIL>'
                ),
                new Contact(
                        FirstName   = 'Test Fn 2',
                        LastName    = 'Test Ln 2',
                        Email		= '<EMAIL>'
                )
        };

        insert cons;
        insert progs;

        insert new List<Application_Checklist_Item_Template__c>{
                new Application_Checklist_Item_Template__c(
                        Complete_Status__c                      = 'Complete - Unreviewed',
                        Description__c                          = 'Related Record Description',
                        Incomplete_Status__c                    = 'Incomplete',
                        Lookup_Field_API_Name__c                = 'Test__c',
                        RecordTypeId                            = citTypesByDevName.get('Related_Record').getRecordTypeId(),
                        Related_Object_Key_Field__c             = 'hed__Contact__c',
                        Related_Object_Key_Type__c              = 'Contact',
                        Required__c                             = true,
                        Reviewed_Status__c                      = 'Complete - Reviewed',
                        Object_API_Name__c                      = 'hed__Test__c',
                        Program__c                              = progs[0].Id,
                        Progress_To_Application_Status__c       = 'Submitted',
                        Name                                    = 'Related Record',
                        Score_Field_API_Name__c                 = 'Composite_Score__c',
                        Score_Weighting__c                      = 0.5,
                        SOQL_Query_Filter__c                    = 'hed__Contact__c IN :cons AND hed__Test_Type__c = \'GRE\''    ,
                        Verified_Field_on_Object__c             = 'hed__Source__c',
                        Verified_Lookup_API_Name__c             = 'Test__c'
                ),
                new Application_Checklist_Item_Template__c(
                        Complete_Status__c                      = 'Complete - Unverified',
                        Description__c                          = 'Related Document Description',
                        Incomplete_Status__c                    = 'Incomplete',
                        Program__c                              = progs[0].Id,
                        Progress_To_Application_Status__c       = 'Submitted',
                        RecordTypeId                            = citTypesByDevName.get('Related_Document').getRecordTypeId(),
                        Related_Object_Key_Type__c              = 'Contact',
                        Required__c                             = true,
                        Reviewed_Status__c                      = 'Complete - Verified',
                        Name                                    = 'Related Document',
                        SOQL_Query_Filter__c                    = 'Document_Type__c = \'Official Transcript\'',
                        Verified_Field_on_Object__c             = 'Official__c',
                        Verified_Status__c                      = 'Complete - Verified'
                )
        };

        insert new List<SObject>{
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'Application_OpportunitySync_TDTM',
                        hed__Load_Order__c          = 1,
                        hed__Object__c              = 'Application__c',
                        hed__Owned_by_Namespace__c  = 'hed',
                        hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate'
                ),
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'Opportunity_IdentifyDuplicates_TDTM',
                        hed__Load_Order__c          = .5,
                        hed__Object__c              = 'Opportunity',
                        hed__Trigger_Action__c      = 'BeforeInsert'
                ),
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'Application_DateService_TDTM',
                        hed__Load_Order__c          = 2,
                        hed__Object__c              = 'Application__c',
                        hed__Owned_by_Namespace__c  = 'hed',
                        hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate'
                ),
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'Application_ChecklistManager_TDTM',
                        hed__Load_Order__c          = 3,
                        hed__Object__c              = 'Application__c',
                        hed__Owned_by_Namespace__c  = 'hed',
                        hed__Trigger_Action__c      = 'AfterInsert;AfterUpdate'
                ),
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'ApplicationChecklistItem_Service_TDTM',
                        hed__Load_Order__c          = 1,
                        hed__Object__c              = 'Application_Checklist_Item__c',
                        hed__Trigger_Action__c      = 'BeforeInsert;BeforeUpdate;AfterInsert;AfterUpdate'
                ),
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'Test_ChecklistIndicator_TDTM',
                        hed__Load_Order__c          = 1,
                        hed__Object__c              = 'Test__c',
                        hed__Owned_by_Namespace__c  = 'hed',
                        hed__Trigger_Action__c      = 'AfterInsert'
                ),
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'File_ChecklistIndicator_TDTM',
                        hed__Load_Order__c          = 1,
                        hed__Object__c              = 'ContentVersion',
                        hed__Trigger_Action__c      = 'AfterInsert;AfterUpdate'
                ),
                new hed__Trigger_Handler__c(
                        hed__Active__c              = true,
                        hed__Class__c               = 'File_ChecklistIndicator_TDTM',
                        hed__Load_Order__c          = 1,
                        hed__Object__c              = 'ContentDocumentLink',
                        hed__Trigger_Action__c      = 'AfterInsert;AfterUpdate'
                )

        };
    }

    @IsTest
    static void testCloneCheckListItems() {
        List<Contact> cons              = [ SELECT Id FROM Contact ];
        List<Account> progs             = [ SELECT Id FROM Account WHERE Id IN (SELECT Program__c FROM Application_Checklist_Item_Template__c) ];
        List<hed__Application__c> apps  = new List<hed__Application__c>();

        for ( Integer i=0; i < cons.size(); i++ ) {
            apps.add( new hed__Application__c(
                    hed__Applicant__c = cons[i].Id,
                    hed__Applying_To__c = progs[0].Id
            ));
        }

        insert apps;

        Test.startTest();
            hed__Application__c newApp = [SELECT Id, Name, hed__Application_Status__c FROM hed__Application__c LIMIT 1];
            System.debug('01 '+newApp);
            newApp.hed__Application_Status__c = 'Submitted';
            update newApp;
        Test.stopTest();

        // Verify that the new Checklist Items were inserted
        List<Application_Checklist_Item__c> testCheckListItem = [SELECT Id FROM Application_Checklist_Item__c];
        System.debug('02 '+testCheckListItem);
        System.assert(true, testCheckListItem.size()>0);
    }
}