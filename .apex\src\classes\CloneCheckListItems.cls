/**
 * Created by <PERSON><PERSON> on 2023-02-06.
 */

public with sharing class CloneCheckListItems implements Queueable{
    public List<hed__Application__c> appList;
    public Boolean isNew;

    public CloneCheckListItems(List<hed__Application__c> apps, Boolean isNewFlag) {
        this.appList = apps;
        this.isNew = isNewFlag;
    }

    public void execute(QueueableContext context) {
        ApplicationChecklistService acs = new ApplicationChecklistService ( appList, isNew );
        insert acs.getNewChecklistItems();
        acs.loadCdls();
    }
}