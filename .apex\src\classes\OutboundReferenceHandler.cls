/**
* UTR-1741 : There are three 'reference’ email fields on the application (Referrer_1_Email__c, Referrer_2_Email__c and Referrer_3_Email__c).
* When the applicant fills these reference email fields, emails should be sent to the ‘referrers’ emailIds stored in the 3 fields.
* Condition to send email are:
* 1. The application changes status to ‘Submitted’ or ‘Ready For Review’ after being ‘In Progress’ or created, and the reference fields are populated
* 2. The application is already in the Submitted or Ready to Review stage and the reference fields on the application change
*
* Email Template to be used is: "Application: Reference Email"
* When the referrers' respond to the email, corressponding reference fields (Reference_1__c or Reference_2__c or Reference_3__c) should be populated
* with the reponse body. (Handled in InboundReferenceHandler class).
*
*
* <AUTHOR> 
* @date  17-June-2020 
*/

global without sharing class OutboundReferenceHandler {

    global class OutboundRefEmailWrapper{
        @InvocableVariable(required=true)
        global hed__Application__c application = new hed__Application__c();

        @InvocableVariable(required=true)
        global String emailAddress;
        
        @InvocableVariable(required=true)
        global String emailTemplateAPIName;
        
        global EmailTemplate emailTemplate;
    }

    @InvocableMethod(label='Send Reference Emails')
    global static void sendReferenceEmails(List<OutboundRefEmailWrapper> requests ){
        try{
            List<Messaging.SingleEmailMessage> emailList = new List<Messaging.SingleEmailMessage>();
            List<String> emTemplList = new List<String>();
            for(OutboundRefEmailWrapper req: requests){
                emTemplList.add(req.emailTemplateAPIName);
            }
            List<EmailTemplate> emailTemplate = [select Id, Name, Developername, Subject, Body from EmailTemplate where developerName IN :emTemplList];
            EmailTemplate defEmTemplate = [select Id, Name, Developername, Subject, Body from EmailTemplate where developerName ='ApplicationReferenceEmailTemplate' LIMIT 1];
            OrgWideEmailAddress owa = [select id, Address, DisplayName from OrgWideEmailAddress Where DisplayName = 'Rotman Recruitment & Admissions' limit 1];	//Select Org-Wide Address related to Rotman Applications
            
            Map<String, EmailTemplate> emTemplMap = new Map<String, EmailTemplate>();
            for(EmailTemplate rec: emailTemplate){
                emTemplMap.put(rec.Developername, rec);
            }
            
            for(OutboundRefEmailWrapper req: requests){
                if(emTemplMap.containsKey(req.emailTemplateAPIName))
                    req.emailTemplate = emTemplMap.get(req.emailTemplateAPIName);
                else
                    req.emailTemplate = defEmTemplate;	// Default Email Template
            }
            
            for(OutboundRefEmailWrapper req: requests){
                Messaging.SingleEmailMessage msg = createReferenceEmail(req);
                msg.setOrgWideEmailAddressId(owa.id);   // Sender's Display Name
                emailList.add(msg);
            }

            if(emailList.size() >0)
                Messaging.sendEmail(emailList);

        } catch(Exception e){
            hed__Error__c error = new hed__Error__c();  //create TDTM Error record
            error.hed__Context_Type__c = 'Error caused by OutboundReferenceHandler apex class';
            error.hed__Stack_Trace__c = e.getMessage();
            insert error;
        }

    }

    public static Messaging.SingleEmailMessage createReferenceEmail(OutboundRefEmailWrapper request){

        EmailServicesAddress[] emSerAdd = [SELECT Id,AuthorizedSenders, EmailDomainName, IsActive,LocalPart FROM EmailServicesAddress 
                                            WHERE Function.FunctionName ='InboundReferenceService' and isActive=true LIMIT 1];
        system.debug('emSerAdd:'+emSerAdd);
        
        Messaging.Singleemailmessage email = new Messaging.Singleemailmessage();
		
        String emailAddress = request.emailAddress;
        
        email.setTargetObjectId(request.application.hed__Applicant__c);
        email.setToAddresses(new String[] {emailAddress});  // this is referrer's email address
        if(emSerAdd.size() == 1){
            String sender = emSerAdd[0].LocalPart+'@'+emSerAdd[0].EmailDomainName;
            email.setCcAddresses(new String[]{sender});
        }
        //email.setSenderDisplayName(UserInfo.getOrganizationName());   // Sender's Display Name
        email.setTreatTargetObjectAsRecipient(false);
        email.setWhatId(request.application.Id);
        email.setSaveAsActivity(false);
        email.setTemplateId(request.emailTemplate.Id);
        return email;    
    }

}