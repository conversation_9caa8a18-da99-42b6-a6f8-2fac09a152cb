<aura:component controller="EngagementTimeline" implements="force:hasRecordId,flexipage:availableForRecordHome,force:hasSObjectName,force:appHostable">
    <aura:attribute name="recordId" type="String" />
    <aura:attribute name="sObjectName" type="String" />
    <aura:attribute name="engagementLabel" type="String" />
    
    <aura:attribute name="allowExpandFuture" type="Boolean" />
    <aura:attribute name="allowExpandPast" type="Boolean" />
    
    <aura:attribute name="futureTimelineItems" type="EngagementTimeline.TimelineItemWrp[]" />
    <aura:attribute name="pastTimelineItems" type="EngagementTimeline.TimelineItemWrp[]" />
    
    <aura:attribute name="futureTimelineItems_all" type="EngagementTimeline.TimelineItemWrp[]" />
    <aura:attribute name="pastTimelineItems_all" type="EngagementTimeline.TimelineItemWrp[]" />
    
    <aura:attribute name="futureCollapsed" type="Boolean" />
    <aura:attribute name="pastCollapsed" type="Boolean" />
    
    
    
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    
    <div class="container">
        <div class="slds-page-header">
            <div class="slds-media">
                <div class="slds-media__body">
                    <div style="display:inline-block; width:100%;">
                        <div style="float:left;">
                            <h1 class="slds-page-header__title" title="Future Engagements">
                                Future {!v.engagementLabel}
                            </h1>
                        </div>
                        <div style="float:right;">
                            <aura:if isTrue="{!v.futureTimelineItems.length > 0}" >
                                <p class="slds-text-body_small slds-line-height_reset sectionHeader" style="float:right;">
                                    {!v.futureTimelineItems_all.length} record(s)
                                </p>
                            </aura:if>
                        </div>
                    </div>
                    <aura:if isTrue="{!v.futureTimelineItems.length == 0}" >
                		<p class="slds-text-body_small slds-line-height_reset sectionHeader">
                            No future <span style="text-transform:lowercase">{!v.engagementLabel}</span> at this time
                        </p>
                    </aura:if>
                </div>
            </div>
        </div>
        <div id="futureRepeats" style="padding-top:20px;" >
            <ul class="slds-timeline">
                <aura:if isTrue="{!v.futureCollapsed}" >
                	<aura:iteration var="ti" items="{!v.futureTimelineItems}" >
                        <li>
                            <c:EngagementTimelineItem timelineItem="{!ti}"/>
                        </li>
                    </aura:iteration>
                </aura:if>
                
                <aura:if isTrue="{!!v.futureCollapsed}" >
                	<aura:iteration var="ti" items="{!v.futureTimelineItems_all}" >
                        <li>
                            <c:EngagementTimelineItem timelineItem="{!ti}"/>
                        </li>
                    </aura:iteration>
                </aura:if>
            </ul>
            
            <aura:if isTrue="{!v.allowExpandFuture}">
                <aura:if isTrue="{!v.futureCollapsed}" >
                    <button onclick="{!c.doToggleFuture}" style="margin-left:3em;" class="slds-button slds-button_stateful slds-button_neutral slds-not-selected" >
                        Expand
                    </button>
                </aura:if>
                <aura:if isTrue="{!!v.futureCollapsed}" >
                    <button onclick="{!c.doToggleFuture}" style="margin-left:3em;" class="slds-button slds-button_stateful slds-button_neutral slds-not-selected" >
                        Collapse
                    </button>
                </aura:if>

            </aura:if>
        </div>
        
        <div class="slds-page-header" style="margin-top:1em;">
            <div class="slds-media">
                <div class="slds-media__body">
                    <div style="display:inline-block; width:100%;">
                        <div style="float:left;">
                            <h1 class="slds-page-header__title" title="Past Engagements">
                                Past {!v.engagementLabel}
                            </h1>
                        </div>
                        <div style="float:right;">
                            <aura:if isTrue="{!v.pastTimelineItems.length > 0}" >
                                <p class="slds-text-body_small slds-line-height_reset sectionHeader" >
                                    {!v.pastTimelineItems_all.length} record(s)
                                </p>
                            </aura:if>
                        </div>
                    </div>
                    <aura:if isTrue="{!v.pastTimelineItems.length == 0}" >
                		<p class="slds-text-body_small slds-line-height_reset">
                            No past <span style="text-transform:lowercase">{!v.engagementLabel}</span> at this time
                        </p>
                    </aura:if>
                </div>
            </div>
        </div>
        <div id="pastRepeats">
            <ul class="slds-timeline">
                <aura:if isTrue="{!v.pastCollapsed}" >
                	<aura:iteration var="ti" items="{!v.pastTimelineItems}" >
                        <li style="padding-top:1em;">
                            <c:EngagementTimelineItem timelineItem="{!ti}"/>
                        </li>
                    </aura:iteration>
                </aura:if>
                
                <aura:if isTrue="{!!v.pastCollapsed}" >
                	<aura:iteration var="ti" items="{!v.pastTimelineItems_all}" >
                        <li style="padding-top:1em;">
                            <c:EngagementTimelineItem timelineItem="{!ti}"/>
                        </li>
                    </aura:iteration>
                </aura:if>
            </ul>
            <aura:if isTrue="{!v.allowExpandPast}">
                <aura:if isTrue="{!v.pastCollapsed}" >
                    <button onclick="{!c.doTogglePast}" style="margin-left:3em;" class="slds-button slds-button_stateful slds-button_neutral slds-not-selected" >
                        Expand
                    </button>
                </aura:if>
                <aura:if isTrue="{!!v.pastCollapsed}" >
                    <button onclick="{!c.doTogglePast}" style="margin-left:3em;" class="slds-button slds-button_stateful slds-button_neutral slds-not-selected" >
                        Collapse
                    </button>
                </aura:if>
            </aura:if>
        </div>
    </div>
</aura:component>