@isTest
private class EventEmailService_TEST {
	@TestSetup
	private static void testDataSetup() {
		UserRole userrole = [Select Id, DeveloperName From UserRole Where DeveloperName = 'Site_Administrators' Limit 1];
		User adminUser = [Select Id, UserRoleId From User Where Profile.Name='System Administrator' AND isActive = True Limit 1];
		adminUser.UserRoleId = userRole.Id;
		update adminUser;
		System.debug('---admin ' + adminUser);
		System.runAs(adminUser){

			Id fulltimeId = ApplicationService.FTandSpecializedRTId;
			Account testAccount = new Account(Name = 'Test Account');
			insert testAccount;

			Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>',
					AccountId = testAccount.Id);
			insert testContact;

			String cookieName = String.valueOf(dateTime.now());

			pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
			cart.pymt__Cart_UID__c = cookieName;
			insert cart;

			hed__Term__c trm = new hed__Term__c(hed__Account__c = testAccount.Id, name = 'Spring 2020');
			insert trm;

			Cohort__c cohort = new Cohort__c(
					Name = 'FTMBA - 2025',
					Start_Term__c = trm.Id,
					Program__c = testAccount.Id,
					Orientation_Date__c = Date.valueOf('2024-04-09 09:30:40'),
					Key__c = 'FTMBA - 2025'
			);
			insert cohort;

			Program_Term_Availability__c pta = new Program_Term_Availability__c(
					Active__c = true,
					Program__c = testAccount.Id,
					Cohort__c = cohort.Id,
					Program_Start_Date__c = Date.valueOf('2022-12-09 10:15:30'),
					Program_End_Date__c = Date.valueOf('2024-04-09 09:30:40'),
					Term__c = trm.Id
			);
			insert pta;

			hed__Application__c app = new hed__Application__c(
					Program_Term_Availability__c = pta.Id,
					hed__Application_Status__c = 'In Progress',
					RecordTypeId = fulltimeId,
					hed__Applying_To__c = testAccount.Id,
					hed__Term__c = trm.Id
			);
			insert app;

			evt__Special_Event__c event = new evt__Special_Event__c();
			event.Name = 'Special event';
			event.Price__c =
					'$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
			event.Start_Local__c = Date.today().addDays(30);
			event.End_Local__c = Date.today().addDays(31);
			event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
			event.evt__Registration_Deadline__c = Date.today().addDays(29);
			event.evt__By_Invitation__c = false;
			event.Venue_Type__c = 'In-Person';
			event.evt__Publish_To__c = 'Public Events';
			event.evt__Event_Type__c = 'Session Event';
			event.evt__Status__c = 'Published';
			event.Tags__c = 'Strategic Communications';

			List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
			listOfEventToInsert.add(event);

			insert listOfEventToInsert;

			evt__Event_Fee__c fee = new evt__Event_Fee__c();
			fee.Name = 'special event fee';
			fee.evt__Event__c = listOfEventToInsert[0].Id;
			fee.evt__Amount__c = 0.0;
			fee.evt__Active__c = true;
			fee.Type__c = 'Standard';

			insert fee;

			pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
			item.Name = 'Test Item';
			item.pymt__Contact__c = testContact.Id;
			item.Event_Fee__c = fee.Id;
			item.pymt__Unit_Price__c = 0.0;
			item.Event_Discount_Amount__c = 0.0;
			item.pymt__Quantity__c = 1;
			item.type__c = 'Event Registration';

			insert item;

			evt__Attendee__c at = new evt__Attendee__c();
			at.evt__Event_Fee__c = fee.Id;
			at.evt__Contact__c = testContact.Id;
			at.evt__Invitation_Status__c = 'Registered';
			at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
			at.Shopping_Cart_Item__c = item.Id;
			at.evt__Event__c = listOfEventToInsert[0].Id;
			at.evt__Reg_Email__c = '<EMAIL>';
			at.Industry__c = 'Technology';

			insert at;

			shopping_cart_item_details__c testCartItemDetail = new shopping_cart_item_details__c();
			testCartItemDetail.SC_event__c = listOfEventToInsert[0].Id;
			testCartItemDetail.Event_Fee__c = fee.Id;
			testCartItemDetail.Contact__c = testContact.Id;
			testCartItemDetail.Shopping_Cart_Item__c = item.Id;
			testCartItemDetail.Attendee__c = at.Id;
			testCartItemDetail.Item_Unit_Price__c = 5.0;
			testCartItemDetail.Item_Quantity__c = 1.0;
			testCartItemDetail.Item_Discount_Amount__c = 0.0;
			testCartItemDetail.Item_Gross_Amount__c = 5.0;
			testCartItemDetail.Item_Tax_Amount__c = 0.0;
			testCartItemDetail.Item_Total_Amount__c = 5.0;

			insert testCartItemDetail;
		}
	}

	@IsTest
	static void testSendScheduledEmail() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		// Create test data
		Datetime emailDateTime = Datetime.now();
		emailDateTime = emailDateTime.addMinutes(35);
		List<String> toAddress = new List<String>{'<EMAIL>', '<EMAIL>'};
		String subject = 'Test Subject';
		String htmlbody = '<html><body>Test Email Body</body></html>';
		String senderName = 'Test Sender';
		String replyTo = '<EMAIL>';
		String eventId = testEvent.Id;
		String beforeDateString = '2024-04-20';
		List<String> ticketTypes = new List<String>{'special event fee'};

		// Call the method being tested
		Test.startTest();
		EventEmailService.sendScheduledEmail(emailDateTime, toAddress, ticketTypes, subject, htmlbody, senderName, replyTo, eventId, beforeDateString);
		Test.stopTest();
	}

	@IsTest
	static void testSendMessage() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		List<String> toAddress = new List<String>{'<EMAIL>', '<EMAIL>'};
		String subject = 'Test Subject';
		String message = 'Test Message';
		String senderName = 'Test Sender';
		String replyTo = '<EMAIL>';
		List<String> ticketTypes = new List<String>{'special event fee'};

		// Call the method being tested
		Test.startTest();
		EventEmailService.sendMessage(testEvent.Id, toAddress, ticketTypes, subject, message, senderName, replyTo);
		Test.stopTest();
	}

	@IsTest
	static void testSendEmailToAttendees() {
		Contact testContact = [SELECT Id, Email FROM Contact LIMIT 1];
		List<Contact> attendees = new List<Contact>();
		attendees.add(testContact);

		Test.startTest();
		EventEmailService.sendEmailToAttendees(attendees);
		Test.stopTest();
	}

	@IsTest
	static void testGetAttendees(){
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event' LIMIT 1];
		test.startTest();
		List<evt__Attendee__c> attendees = EventEmailService.getAttendees(testEvent.Id);
		test.stopTest();
	}

	@IsTest
	static void testGetEventName(){
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event' LIMIT 1];
		test.startTest();
		evt__Special_Event__c event = EventEmailService.getEventName(testEvent.Id);
		test.stopTest();
	}

	@IsTest
	static void testGetScheduledEmails() {
		Test.startTest();
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event' LIMIT 1];
		Event_Email_Sent__c myemail = new Event_Email_Sent__c();
		myemail.Subject_Of_Email__c = 'Test Email 1';
		myemail.Special_Event__c = testEvent.Id;

		insert myemail;

		List<Event_Email_Sent__c> scheduledEmails = EventEmailService.getScheduledEmails(testEvent.Id);

		Test.stopTest();
	}

	@IsTest
	static void testGetSentScheduledEmails() {
		Test.startTest();

		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c WHERE Name = 'Special event' LIMIT 1];
		Event_Email_Sent__c myemail = new Event_Email_Sent__c();
		myemail.Subject_Of_Email__c = 'Test Email 1';
		myemail.Special_Event__c = testEvent.Id;

		insert myemail;

		List<Event_Email_Sent__c> scheduledEmails = EventEmailService.getSentScheduledEmails(testEvent.Id);
		Test.stopTest();
	}

	@IsTest
	static void testAbortJob() {
		Test.startTest();

		String jobId = System.schedule('ScheduledApexTest', '0 0 0 1 1 ? 2030', new CartEmptyService_Scheduler());
		Event_Email_Sent__c scheduledJob = new Event_Email_Sent__c(
				Scheduled_Job_Id__c = jobId,
				Subject_Of_Email__c = '<EMAIL>'
		);
		insert scheduledJob;
		String testJobId = [SELECT Scheduled_Job_Id__c FROM Event_Email_Sent__c WHERE Id = :scheduledJob.Id].Scheduled_Job_Id__c;

		EventEmailService.abortJob(testJobId);

		Test.stopTest();
	}

}