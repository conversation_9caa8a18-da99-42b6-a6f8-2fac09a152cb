/**
 * @description Test Class for SessionAssignmentCalEventHandler_TDTM and CalendarService class
 * <AUTHOR>
 * @version 1.0
 * @created 26-AUG-2020
  */
@isTest
public class SessionAssignmentCalEventHandler_Test{

    @testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for AttendeeConflictHandler_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('SessionAssignmentCalEventHandler_TDTM', 'evt__Session_Assignment__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        RecordType stdEvntRT = [Select Id, Name from RecordType Where SObjectType = 'evt__Special_Event__c' and Name = 'Standard Event'];
        List<evt__Special_Event__c> eList = new List<evt__Special_Event__c>();
        eList.add( new evt__Special_Event__c (RecordTypeId = stdEvntRT.Id, Name = 'Test Event', evt__Disable_Conflict_Checking__c = False, evt__Status__c = 'Published', 
                                              Start_Date__c = Date.newInstance(2021, 12, 9), Start_Time__c= '9:30 AM', End_Date__c = Date.newInstance(2021, 12, 9), 
                                              End_Time__c= '9:30 PM',evt__Registration_Deadline__c= Date.newInstance(2021, 12, 9),evt__Max_Attendees__c = 100, 
                                              Price__c= '50', evt__Event_Type__c = 'Conference', Department__c= 'Advancement', evt__Short_Description__c= 'Test Event',
                                             evt__Publish_To__c= 'Admissions Events', Topics__c= 'Science and Technology'));
        insert eList;
        System.debug('eList::'+eList);
        
        List<Contact> cList = new List<Contact>();
        Contact c1 = (Contact)TestFactory.createSObject(new Contact());
        cList.add(c1); 
        Insert cList;
        System.debug('cList::'+cList);
        
        List<evt__Attendee__c> atnList = new List<evt__Attendee__c>();
        atnList.add(new evt__Attendee__c(evt__Event__c = eList[0].Id, evt__Contact__c = cList[0].Id));
        insert atnList;
        System.debug('atnList::'+atnList);
        
        List<evt__Session__c> sList = new List<evt__Session__c>();
        sList.add(new evt__Session__c(evt__Event__c = eList[0].Id, Name = 'Test Session1', Start_Date__c = Date.newInstance(2021, 12, 9), Start_Time__c= Time.newInstance(9,30,0,0), End_Date__c = Date.newInstance(2021, 12, 9), 
                                      End_Time__c= Time.newInstance(21,30,0,0)));
        insert sList;
        System.debug('sList::'+sList);
    }
    

    @isTest
    static void sessionAssignmentEventInsert(){ 

        List<evt__Attendee__c> atnList = [Select Id, Name From evt__Attendee__c];
        List<evt__Session__c> sList = [Select Id, Name From evt__Session__c];
        
        List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id, evt__Status__c = 'Confirmed'));

        Test.startTest();
        try{
            insert saList;

        } catch(Exception e){
            System.debug('Error Message : ' + e.getMessage());
        }
        Test.stopTest();
   
        List<Event> sa = [Select Id from Event where WhatId = :saList[0].Id];
        System.assertEquals(1, sa.size());
    }
    
    @isTest
    static void sessionAssignmentUpdateCancelled(){ 

        List<evt__Attendee__c> atnList = [Select Id, Name From evt__Attendee__c];
        List<evt__Session__c> sList = [Select Id, Name From evt__Session__c];
        
        List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id, evt__Status__c = 'Confirmed'));
        insert saList;

        Test.startTest();
        try{
            saList[0].evt__status__c = 'Cancelled';
            update saList;

        } catch(Exception e){
            System.debug('Error Message : ' + e.getMessage());
        }
        Test.stopTest();

        List<Event> sa = [Select Id from Event where WhatId = :saList[0].Id];
        System.assertEquals(0, sa.size());
        
    }

    @isTest
    static void sessionAssignmentUpdateConfirmed(){ 

        List<evt__Attendee__c> atnList = [Select Id, Name From evt__Attendee__c];
        List<evt__Session__c> sList = [Select Id, Name From evt__Session__c];
        
        List<evt__Session_Assignment__c> saList = new List<evt__Session_Assignment__c>();
        saList.add(new evt__Session_Assignment__c(evt__Event_Session__c = sList[0].Id, evt__Attendee__c = atnList[0].Id, evt__Status__c = 'Waitlisted'));
        insert saList;

        Test.startTest();
        try{
            saList[0].evt__status__c = 'Confirmed';
            update saList;

        } catch(Exception e){
            System.debug('Error Message : ' + e.getMessage());
        }
        Test.stopTest();

        List<Event> sa = [Select Id from Event where WhatId = :saList[0].Id];
        System.assertEquals(1, sa.size());
        
    }
    
}