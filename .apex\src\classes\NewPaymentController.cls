public class NewPaymentController {
	
    transient  public pymt__PaymentX__c payment{get;set;}
    transient  public CardWrapperClass cardDetails{get;set;}
    Public NewPaymentController(ApexPages.StandardController controller){
        pymt__PaymentX__c payment = new pymt__PaymentX__c();
       CardWrapperClass cardDetails = new CardWrapperClass();
    }
    public void newPayment(){
        system.debug('cardDetails=='+cardDetails);
    }
    
        
}