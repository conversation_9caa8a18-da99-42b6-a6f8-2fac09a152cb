/**
* @description   Service class for Test Object 
* <AUTHOR>
* @version        1.0 
* @created 2020-03-10
* @modified 2020-03-10
*/ 
public with sharing class TestService {

    /**
     * @description map test name to a map of subject area to weight of subject 
     * @return Map<String, Map<String, Decimal>> map of test name to a map of subject area to weight of subject
     * @param testNames set of test names to query for 
     */
    public static Map<String,Map<String,Decimal>> mapTestNameToSubjectWeight(Set<String> testNames){
        //Map of test name to a map of subject area to weight of subject 
        Map<String, Map<String, Decimal>> testToWeightMap = new Map<String, Map<String, Decimal>>(); 
        //Query for specific test score setting records 
        List<Test_Score_Setting__mdt> tss = [SELECT Subject_Area__c, Test_Name__c, Weighting__c
                                                FROM Test_Score_Setting__mdt 
                                                WHERE Do_Not_Require_for_Calculation__c != true 
                                                AND Weighting__c > 0
                                                AND Test_Name__c IN :testNames]; 

        for(Test_Score_Setting__mdt setting :tss){
            if(testToWeightMap.containsKey(setting.Test_Name__c)){
                //Map subject area to weight
                testToWeightMap.get((String)setting.Test_Name__c).put((String)setting.Subject_Area__c, (Decimal)setting.Weighting__c); 
            }
            else{
                Map<String, Decimal> subjectToWeight = new Map<String, Decimal>(); 
                subjectToWeight.put((String)setting.Subject_Area__c, (Decimal)setting.Weighting__c); 
                //Map test Name to a map of subject area to weight 
                testToWeightMap.put((String)setting.Test_Name__c, subjectToWeight); 
            }
        }      
        return testToWeightMap; 
    } 

    /**
     * @description map test records to their related test score records 
     * @return Map<Id, List<hed__Test_Score__c>> map of test Id to list of related test score records
     * @param testIds set of test record Ids
     * @param testScoreFieldsToQuery list of test score fields to query 
     */
    public static Map<Id, List<hed__Test_Score__c>> mapTestIdToScores(Set<Id> testIds, Set<String> testScoreFieldsToQuery){
       //Map of test Id to related list of test score records
        Map<Id, List<hed__Test_Score__c>> testIdToScores = new Map<Id, List<hed__Test_Score__c>>(); 

        //Map test Id to list of related test scores: 
        for(hed__Test__c test :Database.query('SELECT ID, (SELECT ' + String.join(new List<String>(testScoreFieldsToQuery), ',') + ' FROM hed__Test_Scores__r)  FROM hed__Test__c WHERE ID IN :testIds')){
            if(test.hed__Test_Scores__r.size() > 0){
                testIdToScores.put(test.Id, (List<hed__Test_Score__c>) test.hed__Test_Scores__r); 
            }
        } 
        return testIdToScores; 
    }
    
    /**
     * @description queries for test records
     * @return List<hed__Test__c> list of test records
     * @param testIds Set of test Ids
     * @param fieldsToQuery list of test fields to query 
     */
    public static List<hed__Test__c> queryFieldsInTests(Set<Id> testIds, Set<String> fieldsToQuery){
        return Database.query('SELECT ' + String.join(new List<String>(fieldsToQuery), ',') + ' FROM hed__test__c WHERE ID IN :testIds ' ); 
    }
}