/**
 * @description       :
 * <AUTHOR> <PERSON>
 * @group             : Rotman Salesforce Team
 * @last modified on  : 03-14-2024
 * @last modified by  : <PERSON>
**/
@RestResource(urlMapping='/api/events')
global without sharing class EventsList_API {
	@HttpGet
	global static void getEvents() {
		try {
			Dom.Document doc = new Dom.Document();
			Dom.XmlNode rootNode = doc.createRootElement('Events', null, null);
			List<evt__Special_Event__c> eventList = [SELECT Id, Name, evt__Short_Description__c, Start_Local__c, End_Local__c, Event_Location__c, evt__Event_Type__c, Venue_Type__c, evt__Topics__c, Thumbnail_Image__c, Event_Registration_URL__c, Price__c, Agenda_RichText__c FROM evt__Special_Event__c WHERE Start_Local__c >= TODAY AND evt__Status__c = 'Published' ORDER BY CreatedDate DESC LIMIT 1000];
			for(evt__Special_Event__c event : eventList) {
				Dom.XmlNode eventNode = rootNode.addChildElement('Event', null, null);
				eventNode.addChildElement('Id', null, null).addTextNode(event.Id);
				String myName = String.valueOf(event.Name) == null ? '' : String.valueOf(event.Name);
				if (myName.length() > 0) {
					myName = myName.replaceAll('<br>', '<br/>');
				}
				eventNode.addChildElement('Name', null, null).addTextNode(myName);
				String myDescription = String.valueOf(event.evt__Short_Description__c) == null ? '' : String.valueOf(event.evt__Short_Description__c);
				if (myDescription.length() > 0) {
					myDescription = myDescription.replaceAll('<br>', '<br/>');
				}
				eventNode.addChildElement('Description', null, null).addTextNode(myDescription);
				String myEventType = String.valueOf(event.evt__Event_Type__c) == null ? '' : String.valueOf(event.evt__Event_Type__c);
				eventNode.addChildElement('EventType', null, null).addTextNode(myEventType);
				//String myDateTime = String.valueOf(event.Display_Event_DateTime_Formatted__c) == null ? '' : String.valueOf(event.Display_Event_DateTime_Formatted__c);
				String myLocation = String.valueOf(event.Event_Location__c) == null ? '' : String.valueOf(event.Event_Location__c);
				if (myLocation.length() > 0) {
					myLocation = myLocation.replaceAll('<br>', '<br/>');
				}
				String myLocationType = String.valueOf(event.Venue_Type__c) == null ? '' : String.valueOf(event.Venue_Type__c);
				String myTopics = String.valueOf(event.evt__Topics__c) == null ? '' : String.valueOf(event.evt__Topics__c);
				String myStartDate = String.valueOf(event.Start_Local__c) == null ? '' : event.Start_Local__c.format('EEE, dd MMM yyyy HH:mm:ss', 'GMT-04:00');
				eventNode.addChildElement('StartDate', null, null).addTextNode(myStartDate);
				String myEndDate = String.valueOf(event.End_Local__c) == null ? '' : event.End_Local__c.format('EEE, dd MMM yyyy HH:mm:ss', 'GMT-04:00');
				eventNode.addChildElement('EndDate', null, null).addTextNode(myEndDate);
				//eventNode.addChildElement('DateTime', null, null).addTextNode(myDateTime);
				eventNode.addChildElement('Location', null, null).addTextNode(myLocation);
				eventNode.addChildElement('LocationType', null, null).addTextNode(myLocationType);
				eventNode.addChildElement('Topics', null, null).addTextNode(myTopics);
				String eventImg = event.Thumbnail_Image__c != null ? event.Thumbnail_Image__c : 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
				if (eventImg == null || eventImg == '') {
					eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
				}else if (eventImg.contains('<img')){
					Pattern imgSrcPattern = Pattern.compile('<img[^>]*src="([^"]*)');
					Matcher matcher = imgSrcPattern.matcher(eventImg);
					while (matcher.find()) {
						eventImg = matcher.group(1);
					}
					eventImg = eventImg.replaceAll('amp;', '');
				}else{
					eventImg = 'https://community.rotman.utoronto.ca/events/sfsites/c/resource/myResources/myResources/images/event-default.png';
				}
				if (eventImg.contains('rotmancrm')) {
					eventImg = eventImg.replace('rotmancrm.file.force.com', 'community.rotman.utoronto.ca/events');
				}
				if (!eventImg.contains('https://')) {
					eventImg = 'https://community.rotman.utoronto.ca'+eventImg;
				}
				System.debug('eventImg: ' + eventImg);
				eventImg = eventImg.replaceAll('amp;', '');
				eventNode.addChildElement('EvdTentImage', null, null).addtextNode(eventImg);
				String myRegistrationURL = String.valueOf(event.Event_Registration_URL__c) == null ? '' : String.valueOf(event.Event_Registration_URL__c);
				String myPrice = String.valueOf(event.Price__c) == null ? '' : String.valueOf(event.Price__c);
				String myAgenda = String.valueOf(event.Price__c) == null ? '' : String.valueOf(event.Agenda_RichText__c);
				if (myAgenda.length() > 0) {
					myAgenda = myAgenda.replaceAll('<br>', '<br/>');
				}
				eventNode.addChildElement('RegistrationURL', null, null).addTextNode(myRegistrationURL);
				eventNode.addChildElement('EventPrice', null, null).addTextNode(myPrice);
				eventNode.addChildElement('EventAgenda', null, null).addTextNode(myAgenda);
			}
			String xmlString = doc.toXmlString();
			RestContext.response.addHeader('Content-Type', 'application/xml');
			RestContext.response.responseBody = Blob.valueOf(xmlString);
		} catch (Exception e) {
			RestContext.response.statusCode = 500;
			RestContext.response.responseBody = Blob.valueOf(e.getMessage());
		}
	}
}