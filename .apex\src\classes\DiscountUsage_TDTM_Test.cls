/**
* @description    Test class for DiscountUsage_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-10-19
* @modified 2020-10-19
*/
@isTest
public class DiscountUsage_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for TestScoreCalculator_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('DiscountUsage_TDTM', 'pymt__Shopping_Cart_Item__c', 'AfterInsert;AfterUpdate;AfterDelete;AfterUndelete', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

         //Test Student record: 
         Contact c =  (Contact) TestFactory.createSObject(new Contact()); 
         //Inserting contact will create admin account 
         insert c; 
    
        //Test discount records 
        List<Discount__c> discountsToInsert = new List<Discount__c>(); 
        discountsToInsert.add(new Discount__c(Name = '10% off discount', Percent_Discount__c = 10, Max_Usage__c = 10, Code__c= 'ROTMAN10')); 
        discountsToInsert.add(new Discount__c(Name = '$100 off discount', Dollar_Discount__c = 100, Max_Usage__c = 20, Code__c='ROTMAN100')); 
        insert discountsToInsert; 

        //Test Payment record: 
        pymt__PaymentX__c payment = new pymt__PaymentX__c(Name='Test Payment', pymt__contact__c = c.Id, Type__c = 'Event Registration', pymt__Status__c = 'Online Checkout'); 
        insert payment; 

        List<pymt__Shopping_Cart_Item__c> items = new List<pymt__Shopping_Cart_Item__c>(); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #1', Discount__c = discountsToInsert[0].Id, pymt__Unit_Price__c = 9000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payment.Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #2', Discount__c= discountsToInsert[1].Id, pymt__Unit_Price__c = 12000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payment.Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Item #3', Discount__c = discountsToInsert[0].Id, pymt__Unit_Price__c = 10000, pymt__Quantity__c = 1, type__c = 'Event Registration'));
        insert items; 
    }

    /**
     * @description test insertion of shopping cart items with no related payment record
     * Assert current usage was not updated
     */
    @isTest
    public static void testInsertSCI_NoPayment(){
       Discount__c discount = [SELECT ID, Name, Percent_Discount__c, Current_Usage__c FROM Discount__c WHERE Percent_Discount__c != null LIMIT 1]; 

        List<pymt__Shopping_Cart_Item__c> items = new List<pymt__Shopping_Cart_Item__c>(); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #1', Discount__c = discount.Id, pymt__Unit_Price__c= 7000, pymt__Quantity__c = 1, type__c = 'Event Registration')); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #2', Discount__c= discount.Id, pymt__Unit_Price__c= 8000, pymt__Quantity__c = 1, type__c = 'Event Registration')); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #3', Discount__c = discount.Id, pymt__Unit_Price__c = 6000, pymt__Quantity__c = 1, type__c = 'Event Registration'));

        Test.startTest(); 
            insert items; 
        Test.stopTest(); 

        //Assert current usage was updated: 
        Discount__c discountAfter = [SELECT Id, Current_Usage__c FROM Discount__c WHERE Id = :discount.Id]; 
        System.Assert(discountAfter.Current_Usage__c == discount.Current_Usage__c, 
                        'Current usage should not be updated: Expected: ' + discount.Current_Usage__c + ' Actual: ' + discountAfter.Current_Usage__c); 
    }

    /**
     * @description test insertion of shopping cart items with related payment record
     * Assert current usage was not updated
     */
    @isTest
    public static void testInsertSCI_Payment(){
       Discount__c discount = [SELECT ID, Name, Percent_Discount__c, Current_Usage__c FROM Discount__c WHERE Percent_Discount__c != null LIMIT 1]; 
       pymt__PaymentX__c payment = [SELECT ID FROM pymt__PaymentX__c LIMIT 1]; 

        List<pymt__Shopping_Cart_Item__c> items = new List<pymt__Shopping_Cart_Item__c>(); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #1', Discount__c = discount.Id, pymt__Unit_Price__c= 7000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payment.Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #2', Discount__c= discount.Id, pymt__Unit_Price__c= 8000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payment.Id)); 
        items.add(new pymt__Shopping_Cart_Item__c(Name='Test Percent Insert Item #3', Discount__c = discount.Id, pymt__Unit_Price__c = 6000, pymt__Quantity__c = 1, type__c = 'Event Registration', pymt__Payment__c = payment.Id));

        Test.startTest(); 
            insert items; 
        Test.stopTest(); 

        //Assert current usage was updated: 
        Discount__c discountAfter = [SELECT Id, Current_Usage__c FROM Discount__c WHERE Id = :discount.Id]; 
        System.Assert(discountAfter.Current_Usage__c == discount.Current_Usage__c + items.size(), 
                        'Current usage did not update correctly: Expected: ' + (discount.Current_Usage__c + items.size()) + ' Actual: ' + discountAfter.Current_Usage__c); 
    }

     /**
     * @description test updating discount on existing shopping cart item 
     * Assert that current usage gets updated properly
     */
    @isTest
    public static void testUpdateSCI_discount(){
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, discount__r.Current_Usage__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Type__c = 'Event Registration' AND pymt__Payment__c != null LIMIT 1]; 
        Discount__c discount = [SELECT ID, Name, Percent_Discount__c, Current_Usage__c FROM Discount__c WHERE Id != :item.Discount__c LIMIT 1]; 

        Test.startTest(); 
            update new pymt__Shopping_Cart_Item__c(Id = item.Id, Discount__c = discount.Id);  
        Test.stopTest(); 

        Set<Id> discountIds = new Set<Id>{item.Discount__c, discount.Id}; 
        Map<Id, Discount__c> discountMap = new Map<Id, Discount__c>([SELECT Id, Current_Usage__c FROM Discount__c WHERE Id IN :discountIds]); 

        //Assert that old discount's current usage has decremented and new discount's current usage has incremented: 
        System.Assert(discountMap.get(item.Discount__c).current_usage__c == (item.Discount__r.Current_Usage__c - 1),'Current usage did not update correctly: Expected: ' + (item.Discount__r.Current_Usage__c - 1) + ' Actual: ' + discountMap.get(item.Discount__c).Current_Usage__c); 
        System.Assert(discountMap.get(discount.Id).current_usage__c == (discount.Current_Usage__c + 1),'Current usage did not update correctly: Expected: ' + (discount.Current_Usage__c + 1) + ' Actual: ' + discountMap.get(discount.Id).Current_Usage__c); 
    }

     /**
     * @description test linking a payment record on an existing shopping cart item 
     * Assert that current usage gets updated properly
     */
    @isTest
    public static void testUpdateSCI_payment(){
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, discount__r.Current_Usage__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Type__c = 'Event Registration' AND pymt__Payment__c = null LIMIT 1]; 
        pymt__PaymentX__c payment = [SELECT ID FROM pymt__PaymentX__c LIMIT 1]; 

        Test.startTest(); 
            update new pymt__Shopping_Cart_Item__c(Id = item.Id, pymt__Payment__c = payment.Id);  
        Test.stopTest(); 

        Discount__c discountAfter = [SELECT Id, Current_Usage__c FROM Discount__c WHERE Id = :item.Discount__c]; 

        //Assert that old discount's current usage has incremented
        System.Assert(discountAfter.current_usage__c == (item.Discount__r.Current_Usage__c + 1),'Current usage did not update correctly: Expected: ' + (item.Discount__r.Current_Usage__c + 1) + ' Actual: ' + discountAfter.Current_Usage__c); 
    }

    /**
     * @description test deletion of shopping cart item that is linked to a discount
     * Assert that current usage is updated on the discount record
     */
    @isTest 
    public static void testDeleteSCI(){
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, discount__r.Current_Usage__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Type__c = 'Event Registration' AND pymt__Payment__c != null AND pymt__Payment_Completed__c != true LIMIT 1]; 

        Test.startTest(); 
            Delete item; 
        Test.stopTest(); 

        Discount__c discountAfter = [SELECT Id, Current_Usage__c FROM Discount__c WHERE Id = :item.Discount__c]; 
        //Assert current usage is updated on discount record: 
        System.Assert(discountAfter.current_usage__c == (item.Discount__r.Current_Usage__c - 1),'Current usage did not update correctly: Expected: ' + (item.Discount__r.Current_Usage__c - 1) + ' Actual: ' + discountAfter.Current_Usage__c); 
    }
    /**
     * @description test deletion of shopping cart item that is linked to a discount
     * Assert that current usage is updated on the discount record
     */
    @isTest 
    public static void TestUndeleteSCI(){
        pymt__Shopping_Cart_Item__c item = [SELECT ID, Discount__c, discount__r.Current_Usage__c FROM pymt__Shopping_Cart_Item__c WHERE Discount__c != null AND Type__c = 'Event Registration' AND pymt__Payment__c != null AND pymt__Payment_Completed__c != true LIMIT 1]; 

        Test.startTest(); 
            Delete item; 
            Undelete item; 
        Test.stopTest(); 

        Discount__c discountAfter = [SELECT Id, Current_Usage__c FROM Discount__c WHERE Id = :item.Discount__c]; 
        //Assert current usage is updated on discount record: 
        System.Assert(discountAfter.current_usage__c == item.Discount__r.current_usage__c ,'Current usage did not update correctly: Expected: ' + item.Discount__r.current_usage__c  + ' Actual: ' + discountAfter.Current_Usage__c); 
    }
}