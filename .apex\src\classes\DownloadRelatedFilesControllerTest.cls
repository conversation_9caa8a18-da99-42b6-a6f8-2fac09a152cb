@isTest
public class DownloadRelatedFilesControllerTest {
	@isTest
    static void getRecordsTestMethod() {
        //Create Document Parent Record
		Account acc = new Account(Name='Test Account');
		Insert acc;
 
		Contact cont = new Contact ();
        cont.FirstName = 'FirstName';
        cont.LastName = 'LastName';
        cont.Email='<EMAIL>';
        cont.phone='********';
        insert cont;  
        
         ContentVersion contentVersion = new ContentVersion(
         	Title = 'Penguins',
         	PathOnClient = 'Penguins.jpg',
            VersionData = Blob.valueOf('Test Content'),
            IsMajorVersion = true
        );
        insert contentVersion;
        
        List<ContentDocument> documents = [SELECT Id, Title, LatestPublishedVersionId FROM ContentDocument];
        
        //create ContentDocumentLink  record 
        ContentDocumentLink cdl = New ContentDocumentLink();
        cdl.LinkedEntityId = acc.id;
        cdl.ContentDocumentId = documents[0].Id;
        cdl.shareType = 'V';
        insert cdl;

        List<ContentDocumentLink> conDocLink = DownloadRelatedFilesController.getRecords(acc.id);  
       
        //System.debug('conDocLink: ' + conDocLink.size());
        System.assertEquals(conDocLink.size(), 1);
    }
}