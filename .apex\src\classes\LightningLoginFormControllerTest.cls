@IsTest(SeeAllData = true)
public with sharing class LightningLoginFormControllerTest {

 @IsTest
 static void testLoginWithInvalidCredentials() {
     //System.assertEquals('Argument 1 cannot be null', LightningLoginFormController.login(null, 'fakepwd', null));
 }

 @IsTest
 static void LightningLoginFormControllerInstantiation() {
  LightningLoginFormController controller = new LightningLoginFormController();
  System.assertNotEquals(controller, null);
 }

 @IsTest
 static void testIsUsernamePasswordEnabled() {
  System.assertEquals(LightningLoginFormController.getIsUsernamePasswordEnabled(), LightningLoginFormController.getIsUsernamePasswordEnabled());
 }

 @IsTest
 static void testIsSelfRegistrationEnabled() {
  System.assertEquals(false, LightningLoginFormController.getIsSelfRegistrationEnabled());
 }

 @IsTest
 static void testGetSelfRegistrationURL() {
  System.assertEquals(null, LightningLoginFormController.getSelfRegistrationUrl());
 }

 @IsTest
 static void testAuthConfig() {
  Auth.AuthConfiguration authConfig = LightningLoginFormController.getAuthConfig();
  System.assertNotEquals(null, authConfig);
 }
    
    @IsTest
    static void testsetExperienceId() {
        System.assertNotEquals(null, LightningLoginFormController.setExperienceId('ApplyNow'));
    }
    
    @IsTest
    static void testsetExperienceId2() {
        System.assertEquals(null, LightningLoginFormController.setExperienceId(null));
    }
    
    @IsTest
    static void testgetForgotPasswordUrl() {
        System.assertEquals(null, LightningLoginFormController.getForgotPasswordUrl());
    }
    
    @IsTest
    static void testLoginCustom() {
        System.assertNotEquals(null, LightningLoginFormController.loginCustom('test User', 'test', null, 'mbaapp'));
        System.assertNotEquals(null, LightningLoginFormController.loginCustom('test User', 'test', null, 'ftmba-request'));
        System.assertNotEquals(null, LightningLoginFormController.loginCustom('test User', 'test', null, 'ftmba-introduce'));
    }

    @IsTest
    static void updateUserPhoto_Test() {
        // Create test data
        Account a = new Account(
                Name='TestAccount');
        insert a;
        ContentVersion cv = new ContentVersion(
                Title = 'Test Image',
                PathOnClient = 'Test1.jpg',
                VersionData = Blob.valueOf('Test Content Data'),
                IsMajorVersion = true
        );

        insert cv;

        List<ContentVersion> cvList = [SELECT Id, Title, ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id];
        System.assertEquals (cvList.size (), 1);
        //create ContentDocumentLink record
        ContentDocumentLink cdl = New ContentDocumentLink ();
        cdl.LinkedEntityId = a.Id;
        cdl.ContentDocumentId = cvList[0].ContentDocumentId;
        cdl.shareType = 'V';
        insert cdl;

        Profile profileRA = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];

        User u = new User(
                alias = 'test1',
                email = '<EMAIL>',
                emailencodingkey = 'UTF-8',
                firstName = 'test1',
                lastName = 'User1',
                userName = '<EMAIL>',
                profileId = profileRA.Id,
                timeZoneSidKey = 'America/Los_Angeles',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US');
        insert u;
        // Call the method
        LightningLoginFormController.updateUserPhoto(u.Id, cv.ContentDocumentId, true);
    }

    @IsTest
    static void updateUserPhoto_NullContentDocumentId_Test() {

        Profile profileRA = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];

        User testUser = new User(
                alias = 'test1',
                email = '<EMAIL>',
                emailencodingkey = 'UTF-8',
                firstName = 'test1',
                lastName = 'User1',
                userName = '<EMAIL>',
                profileId = profileRA.Id,
                timeZoneSidKey = 'America/Los_Angeles',
                LocaleSidKey = 'en_US',
                LanguageLocaleKey = 'en_US');
        insert testUser;

        Test.startTest();
        LightningLoginFormController.updateUserPhoto(testUser.Id, null, true);
        Test.stopTest();
    }


    @isTest
    static void testLoginSuccess() {
        // arrange
        String username = '<EMAIL>';
        String password = 'test1234';
        String startUrl = 'https://rotmancrm--uat.sandbox.my.site.com/events/s/login/';
        // act
        String result = LightningLoginFormController.login(username, password, startUrl);
        // assert
        System.assert(result == null);
    }
}