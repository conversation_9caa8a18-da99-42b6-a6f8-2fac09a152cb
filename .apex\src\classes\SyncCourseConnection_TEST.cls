@isTest
public class SyncCourseConnection_TEST {
    
	@testSetup
    static void testSetup(){
        
        //Create test Account
        Account a = new Account(Name='TestAccount');
        insert a;
        
        //Create test Contacts
        List<Contact> cons = new List<Contact>{
            	(Contact)TestFactory.createSObject(new Contact(FirstName = 'Test1', LastName = 'Student', Email = '<EMAIL>')),
            	(Contact)TestFactory.createSObject(new Contact(FirstName = 'Test2', LastName = 'Student1', Email = '<EMAIL>'))
                };
                    
        //Create Term
        hed__Term__c trm = new hed__Term__c(hed__Account__c=a.Id,name='Spring 2020');
        insert trm;
        
        //Create course
        hed__Course__c course = new hed__Course__c(hed__Account__c=a.Id,name='MBA 2020');
        insert course;
        
        //List to Insert Parent and Child offerings 
        List<hed__Course_Offering__c> parentOfferings = new List<hed__Course_Offering__c>();
        List<hed__Course_Offering__c> childOfferings = new List<hed__Course_Offering__c>();
        
        //Create Course Offerings
        hed__Course_Offering__c cOfferingParent = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering parent Only',hed__Term__c=trm.Id);
        parentOfferings.add(cOfferingParent);
        hed__Course_Offering__c cOfferingParentwithChild = new hed__Course_Offering__c(hed__Course__c = course.id,Name = 'Test Course Offering parent with Childs',hed__Term__c=trm.Id);
        parentOfferings.add(cOfferingParentwithChild);
        insert parentOfferings;
        hed__Course_Offering__c cOfferingChild1 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child1',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild1);
        hed__Course_Offering__c cOfferingChild2 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child2',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild2);
        hed__Course_Offering__c cOfferingChild3 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child3',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild3);
        hed__Course_Offering__c cOfferingChild4 = new hed__Course_Offering__c(Parent_Course__c =cOfferingParentwithChild.Id,hed__Course__c = course.id,Name = 'Test Child4',hed__Term__c=trm.Id);
        childOfferings.add(cOfferingChild4);
        insert childOfferings;
        
        //Create applications
        List<hed__Application__c> applst = new List<hed__Application__c>();
        hed__Application__c app = new hed__Application__c(hed__Application_Status__c = 'Accepted Offer',Course_Offering__c = cOfferingParent.id,hed__Applicant__c = cons[0].id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(app);
        hed__Application__c appWithChildOffrngs = new hed__Application__c(hed__Application_Status__c = 'Offer',Course_Offering__c = cOfferingParentwithChild.id,hed__Applicant__c = cons[0].id,RecordTypeId=Schema.SObjectType.hed__Application__c.getRecordTypeInfosByName().get('Executive Programs').getRecordTypeId());
        applst.add(appWithChildOffrngs);
        insert applst;
    }
    
    @isTest
    public static void testSync(){
        List<hed__Application__c> applst = [SELECT ID,hed__Application_Status__c,Course_Offering__c,hed__Applicant__c FROM hed__Application__c];
        
        test.startTest();
        	SyncCourseConnectionForEPApplication.syncCourseConnectionForEPApplication(applst);
        	SyncCourseConnectionForEPApplication.deleteCourseConnectionForEPApplication(applst);
        test.stopTest();
    }
}