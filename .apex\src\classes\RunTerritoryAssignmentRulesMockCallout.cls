@isTest
global class RunTerritoryAssignmentRulesMockCallout implements HttpCalloutMock {
    String mockResponseBody = '<soapenv:Envelope '+'xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"'+' xmlns:urn="urn:partner.soap.sforce.com"'+' xmlns:urn1="urn:sobject.partner.soap.sforce.com">'+
                                '<soapenv:Body>'+
                                    '<urn:loginResponse>'+
                                        '<urn:result>' +
                                            '<urn:sessionId>{SESSID}</urn:sessionId>'+
        								'</urn:result>' +
                                    '</urn:loginResponse>'+
                                '</soapenv:Body>'+
                            '</soapenv:Envelope>';
    global HttpResponse respond(HTTPRequest req){
        mockResponseBody = mockResponseBody.replace('{SESSID}', 'Test');
        HttpResponse res = new HttpResponse();
        res.setStatus('OK');
        res.setStatusCode(200);
        res.setBody(mockResponseBody);
        return res;
    }
}