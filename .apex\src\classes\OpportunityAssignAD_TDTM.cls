/**
 * @description Assigns the Assistant Director field on the Opportunity if Assign_Assistant_Director_Manual__c is checked
 * <AUTHOR>
 * @version 1.0
 * @created 2020-04-06
 * @modified 2020-04-06
 */
global class OpportunityAssignAD_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Object
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();
        List<Opportunity> oppsToAssign = new List<Opportunity>(); 
        Set<Id> assignedTerritoryIds = new Set<Id>(); 

        /*Assign Assistant Director if the following are true: 
            (1) Assign_Assistant_Director_Manual__c = true && IsExcludedFromTerritory2Filter = false && territory2Id != null && Assistant_Director__c = null
            (2) Assign_Assistant_Director = true && isExcludedFromTerritory2Filter = false && territory2Id != null && Assistant_Director__c = null
        */
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert){
            //(06/02/2020): Recalculate Formulas: 
            List<Opportunity> recalculatedOpps = recalculateOppFormulas((List<Opportunity>)newList); 
            
            for(Integer i= 0; i < newList.size(); i++){
                Opportunity opp = (Opportunity)newList[i]; 
                Opportunity recalculatedOpp = recalculatedOpps[i]; 
                if(opp.Assistant_Director__c == null && ((opp.Assign_Assistant_Director_Manual__c == true && !opp.isExcludedFromTerritory2filter && opp.territory2Id != null) ||
                    (recalculatedOpp.Assign_Assistant_Director__c && !opp.isExcludedFromTerritory2Filter && opp.territory2Id != null))){
                    oppsToAssign.add(opp); 
                    assignedTerritoryIds.add(opp.territory2Id); 
                }
            }
        }

        /*Assign Assistant Director if the following are true: 
            (1) Assign_Assistant_Director_Manual__c is updated to true && IsExcludedFromTerritory2Filter = false && territory2Id != null
            (2) Assign_Assistant_Director is updated to true && isExcludedFromTerritory2Filter = false && territory2Id != null && Assistant_Director == null
            (3) Territory2Id is updated and Assign_Assistant_Director__c = true 
        */
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){
            //(06/02/2020): Recalculate Formulas: 
            List<Opportunity> recalculatedOpps = recalculateOppFormulas((List<Opportunity>)newList); 
            
            Map<Id, Opportunity> oldMap = new Map<id, Opportunity>((List<Opportunity>)oldList);       
            for(Integer i= 0; i < newList.size(); i++){
                Opportunity opp = (Opportunity)newList[i]; 
                Opportunity recalculatedOpp = recalculatedOpps[i];
                if(!opp.isExcludedFromTerritory2Filter && (opp.Assign_Assistant_Director_Manual__c && (opp.Assign_Assistant_Director_Manual__c != oldMap.get(opp.Id).Assign_Assistant_Director_Manual__c) && opp.territory2Id != null) ||
                    (recalculatedOpp.Assign_Assistant_Director__c && (recalculatedOpp.Assign_Assistant_Director__c != oldMap.get(opp.Id).Assign_Assistant_Director__c) && opp.Assistant_Director__c == null && opp.territory2Id != null)||
                    ((opp.Territory2Id != oldMap.get(opp.Id).territory2Id) && recalculatedOpp.Assign_Assistant_Director__c)){
                    oppsToAssign.add(opp); 
                    if(opp.Territory2Id != null){
                        assignedTerritoryIds.add(opp.territory2Id); 
                    }
                } 
            }
        }

        if(oppsToAssign.size() > 0){
            //Query for user assignments: 
            List<UserTerritory2Association> userAssignments = OpportunityTerritoryAssignmentLogic.queryUserAssignments(assignedTerritoryIds, new Set<String>{'Assistant Director'}); 

            //Map users to their level of efforts: 
            Map<Id, Decimal> userToEffortMap = OpportunityTerritoryAssignmentLogic.getUserToLevelOfEffortMap(userAssignments); 

            //Map Territory Ids to a Map of Roles to List of Users in that role: 
            Map<Id, Map<String, List<Id>>> terIdToUsersMap = OpportunityTerritoryAssignmentLogic.mapTerritoryToUsers(userAssignments); 
            
            for(Opportunity opp :oppsToAssign){
                //if territory field is blank, null out assigned Assistant Director: 
                if(opp.territory2Id == null){
                    opp.Assistant_Director__c = null; 
                }
                else{
                    if(terIdToUsersMap != null && terIdToUsersMap.containsKey(opp.territory2Id)){
                        id adUser = null; 
                        //Assign Assistant Director user 
                        if(terIdToUsersMap.get(opp.territory2Id).containsKey('Assistant Director')) adUser = OpportunityTerritoryAssignmentLogic.getUserForLookup(terIdToUsersMap.get(opp.territory2Id).get('Assistant Director'), userToEffortMap); 

                        //Assign Assistant Director and update assigned user's total level of efforts: 
                        if(adUser != null && (opp.Assistant_Director__c == null || opp.Assistant_Director__c != null && opp.Assistant_Director__c != adUser)){ 
                            opp.Assistant_Director__c = adUser; 
                            //update level of effort on assigned user: 
                            userToEffortMap.put(adUser, userToEffortMap.get(adUser) + opp.Level_of_Effort__c);
                        }
                    }
                     //If there are no assigned ADs for the territory, null out Assistant_Director__c: 
                     else if(terIdToUsersMap == null || !terIdToUsersMap.containsKey(opp.territory2Id)){
                        //null out all fields: 
                        if(opp.Assistant_Director__c != null) opp.Assistant_Director__c = null; 
                    }
                }
            }
        }
        return dmlWrapper; 
    }
    /**
    * @description Recalculate formula fields 
    * @return List of opportunities with recalculated formula fields 
    * @param newList List of triggered opportunities
    */
    private static List<Opportunity> recalculateOppFormulas(List<Opportunity> newList){
        List<Opportunity> clonedOpptys = new List<Opportunity>(); 
        for(Opportunity opp: newList){
            clonedOpptys.add(opp.clone(false, false, false, false)); 
        }
        Formula.recalculateFormulas(clonedOpptys); 
        return clonedOpptys; 
    }
}