public without Sharing class EventCartController {
	@AuraEnabled
	public static Map<String, Object> getEventDetailsAndSpeakerInfo(Id eventId) {
		Map<String, Object> eventInfo = new Map<String, Object>{};

		List<evt__Special_Event__c> specialEvent = [SELECT Id, Name, Department__c, Price__c, Event_Location__c, evt__Agenda_HTML__c, Tags__c, evt__Topics__c, Thumbnail_Image__c, Event_Date_Time__c, evt__Street__c, evt__City__c, Country__c, State_Province__c, evt__Postal_Code__c, Confirmation_Email_Custom_Text__c, evt__Short_Description__c FROM evt__Special_Event__c WHERE Id = :eventId];
		List<evt__Speaker__c> speaker = [SELECT Id, Name, evt__Title__c, evt__Company__c, Thumbnail_Image__c, evt__Short_Bio__c FROM evt__Speaker__c WHERE evt__Event__c = :eventId];

		if (specialEvent.size() > 0) {
			eventInfo.put('eventDetails', specialEvent[0]);
		} else {
			eventInfo.put('eventDetails', null);
		}
		if (speaker.size() > 0) {
			eventInfo.put('speakerInfo', speaker);
		} else {
			eventInfo.put('speakerInfo', null);
		}

		return eventInfo;
	}

	@AuraEnabled
	public static List<evt__Session__c> getEventSessions(Id eventId) {
		List<evt__Session__c> sessionList = new List<evt__Session__c>();
		sessionList = [SELECT Id, Name, Display_Session_DateTime_Formatted__c, evt__Short_Description__c, Remaining_Seats__c, Start_Local__c, End_Local__c FROM evt__Session__c WHERE evt__Event__c = :eventId ORDER BY Start_Local__c ASC];

		return sessionList;
	}

	@AuraEnabled
	public static List<EventSessionWrapper> getSessionsSeats(List<EventSessionWrapper> sessionList) {
		List<EventSessionWrapper> lstSeats = new List<EventSessionWrapper>();
		List<String> sessionIds = new List<String>();

		if (sessionList != null && sessionList.size() > 0) {
			for (EventSessionWrapper session : sessionList) {
				sessionIds.add(session.sessionId);
			}

			List<evt__Session__c> sessions = [SELECT Id, Name, Remaining_Seats__c FROM evt__Session__c WHERE Id IN :sessionIds];

			for (evt__Session__c session : sessions) {
				for (EventSessionWrapper sessionWrapper : sessionList) {
					if (session.Id == sessionWrapper.sessionId) {
						EventSessionWrapper se = new EventSessionWrapper();
						se.sessionId = session.Id;
						se.sessionName = session.Name;
						se.selectedSeats = sessionWrapper.selectedSeats;
						se.remainingSeats = session.Remaining_Seats__c;
						lstSeats.add(se);
					}
				}
			}
			System.debug('lstSeats: ' + lstSeats);
		}

		return lstSeats;
	}

	@AuraEnabled
	public static List<String> getAccessibilityValues() {
		List<String> AccessibilityValues = new List<String>();

		// Get all picklist values
		List<Schema.PicklistEntry> entries = evt__Attendee__c.Accessibility_Requirements__c.getDescribe().getPickListValues();
		for (Schema.PicklistEntry entry : entries) {
			if (entry.isActive()) {
				AccessibilityValues.add(entry.getLabel());
			}
		}
		return AccessibilityValues;
	}

	@AuraEnabled
	public static List<String> getDietaryValues() {
		List<String> DietaryValues = new List<String>();

		// Get all picklist values
		List<Schema.PicklistEntry> entries = evt__Attendee__c.Dietary_Restrictions__c.getDescribe().getPickListValues();
		for (Schema.PicklistEntry entry : entries) {
			if (entry.isActive()) {
				DietaryValues.add(entry.getLabel());
			}
		}
		return DietaryValues;
	}

	@AuraEnabled
	public static List<pymt__Shopping_Cart_Item__c> getCurrentCartItemInfoData(String cartId) {
		return [SELECT Id, Name
		FROM pymt__Shopping_Cart_Item__c
		WHERE pymt__Shopping_Cart__c =: cartId AND pymt__Payment_Completed__c = false];
	}

	public class EventSessionWrapper {
		@AuraEnabled
		public String sessionId { get; set; }
		@AuraEnabled
		public String sessionName { get; set; }
		@AuraEnabled
		public Decimal remainingSeats { get; set; }
		@AuraEnabled
		public Integer selectedSeats { get; set; }
	}
}