public class CongaCalloutAction {
	private static final Integer BATCH_SIZE = 20; //Maximum number of calls allowed in future method
	private static Map<Id, String> recordIdToCongaParamsMap = new Map<Id, String>();

	/**
	 * @description Request class holds record Id and conga params
	 */
	public class Request{
		@InvocableVariable(label='Conga Parameters' description='Additional Conga Parameters (in string notation)' required=true)
		public String congaParams;

		@InvocableVariable(label='Additional Conga Parameters' description='Additional Conga Parameters (in string notation)' required=false)
		public String additionalParams;

		@InvocableVariable(label='Record Id' description='Record Id' required=true)
		public Id recordId;
	}

	/**
	 * @description makes callout to Jitterbit to generate conga documents
	 * @param List<Request> list of requests
	 */
	@InvocableMethod(label='Conga Callout' description='Fires REST callout to Conga to generate Conga document')
	public static void callCongaApi(List<Request> requests){

		List<Id> loopRequests = new List<Id>(); //App IDs to be sent to individual async context. Vidyard API does not support batching, necessary to 'batch' callouts to prevent timeouts
		Map<Id, String> idToCongaParamsMap = new Map<Id, String>();

		for ( Integer i=0; i < requests.size(); i++ ) {
			String parameters = requests[i].additionalParams != null ? requests[i].congaParams + requests[i].additionalParams : requests[i].congaParams;
			System.debug(parameters);
			idToCongaParamsMap.put(requests[i].recordId, parameters);

			//If the batch size is equal to the number of Ids in the list, call future method to make callouts
			if ( idToCongaParamsMap.size() == BATCH_SIZE || (i == requests.size()-1 && idToCongaParamsMap.size() > 0) ) {
				performCall(idToCongaParamsMap);
				idToCongaParamsMap = new Map<Id, String>();
			}
		}
	}


	/**
	 * @description constructs HTTP request and makes callout to Jitterbit
	 * @param recordIdToCongaParamsMap maps record time to conga parameters
	 */
	@future(callout=true)
	public static void performCall(Map<Id, String> recordIdToCongaParamsMap) {

		System.debug('++ params: ' + recordIdToCongaParamsMap);

		for ( Id objId : recordIdToCongaParamsMap.keySet() ) {

			String sessionId = UserInfo.getSessionId();
			String serverUrl = Url.getSalesforceBaseUrl().toExternalForm() + '/services/Soap/u/52.0/' +
					UserInfo.getOrganizationId();

			String congaUrl = 'https://composer.congamerge.com/composer8/index.html' +
					'?sessionId=' + sessionId +
					'&Id=' + objId +
					'&apimode=6' +
					'&DefaultPDF=1' +
					'&serverUrl=' + EncodingUtil.urlEncode(serverUrl, 'UTF-8') +
				//Rest of the parameters coming from the COnga Param formula fields (which will typically contain the template ID and Query ID)
					recordIdToCongaParamsMap.get(objId);

			system.debug('++congaURL: ' + congaUrl);
			HttpRequest req = new HttpRequest();
			req.setMethod('GET');
			req.setEndpoint(congaUrl);
			req.setTimeout(120000);

			Http http = new Http();
			HttpResponse res = http.send(req);

			System.debug('++Response: ' + res);
			System.debug('++Response Body: ' + res.getBody());

			//Body contains content version. We can create a document link towards the case
			if (res.getStatusCode() == 200 && res.getBody().left(3) == '068') {
				string contentVersionId = res.getBody();
				ContentVersion cv = [select id, ContentDocumentId from ContentVersion WHERE Id =: contentVersionId];
				ContentDocumentLink cl = new ContentDocumentLink();
				cl.ContentDocumentId = cv.ContentDocumentId;
				cl.LinkedEntityId = objId;
				cl.ShareType = 'V';
				cl.Visibility = 'AllUsers';
				insert cl;
			}
		}
	}
}