@isTest
public class myEmailScheduler_TEST {
	@isTest
	static void testExecute1() {
		// Create test data
		List<String> recipientEmails = new List<String>{'<EMAIL>', '<EMAIL>'};
		String subject = 'Test Subject';
		String body = '<html><body><h1>Test Body</h1></body></html>';
		String senderName = 'Test Sender';
		String replyTo = '<EMAIL>';

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c = '$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.Start_Date__c = Date.today().addDays(30);
		event.End_Date__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.Tags__c = 'Strategic Communications';
		event.Thumbnail_Image__c =
				'<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';
		event.Support_Email__c = '<EMAIL>';
		insert event;

		String eventId = event.Id;

		// Create an instance of myEmailScheduler
		myEmailScheduler scheduler = new myEmailScheduler(recipientEmails, subject, body, senderName, replyTo, eventId);

		// Call the execute method
		Test.startTest();
		scheduler.execute(null);
		Test.stopTest();
	}

	@isTest
	static void testExecute2() {
		// Create test data
		List<String> recipientEmails = new List<String>{'<EMAIL>', '<EMAIL>'};
		String subject = 'Test Subject';
		String body = '<html><body><h1>Test Body</h1></body></html>';
		String senderName = 'Test Sender';
		String replyTo = '<EMAIL>';

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c = '$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.Start_Date__c = Date.today().addDays(30);
		event.End_Date__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.Tags__c = 'Strategic Communications';
		event.Thumbnail_Image__c =
				'<p><img src="https://rotmancrm--uat.sandbox.file.force.com/servlet/rtaImage?eid=a1VG1000000X18b&amp;feoid=00N2B000000Qe9O&amp;refid=0EMG10000004n29" alt="event example 1.png"></img></p>';
		insert event;

		String eventId = event.Id;

		// Create an instance of myEmailScheduler
		myEmailScheduler scheduler = new myEmailScheduler(recipientEmails, subject, body, senderName, replyTo, eventId);

		// Call the execute method
		Test.startTest();
		scheduler.execute(null);
		Test.stopTest();
	}
}