({
    initData: function (component, event, helper){

        var today = new Date();
        var yearList = [];
        console.log('today= '+today);
        var year = today.getFullYear();
        console.log(today.getFullYear());
        for (var i=0; i< 12; i++)
        {
            var newyear = parseInt(year) + parseInt(i);
            yearList.push(newyear);
        }
        console.log(yearList);
        //var ExpYearList = component.get("v.ExpiryYear");
        component.set('v.ExpiryYear',yearList);
        helper.getPaymentWrapper(component, event, helper);
        
    },
    updateTotalFromTaxAmount: function(component, event, helper) {
        var wrapper = component.get('v.paymentCardWrapper');
        var subtotal = component.get('v.subtotal');
        var tax = wrapper.pymt.pymt__Tax__c;
        var ship =wrapper.pymt.pymt__Shipping__c;
        console.log('subtotal--'+subtotal);
        console.log('tax='+tax);
        console.log('ship='+ship);
        var total;
        if(component.get('v.subtotal') != null && component.get('v.subtotal') > 0){
            subtotal = component.get('v.subtotal');
            console.log('subtotal='+subtotal);
        }
        else
        {
            subtotal = "0.00";
        }  
        
        
        if(wrapper.pymt.pymt__Tax__c != null && wrapper.pymt.pymt__Tax__c > 0){
            console.log('tax='+tax);
            tax = wrapper.pymt.pymt__Tax__c;
        }
        else
        {
            tax = "0.00";
        }  
        if(wrapper.pymt.pymt__Shipping__c != null && wrapper.pymt.pymt__Shipping__c > 0){
            ship = wrapper.pymt.pymt__Shipping__c;
            console.log('ship--'+ship);
        }else
        {
            ship = "0.00";
        }
        //total = parseFloat(subtotal) + parseFloat(tax)+ parseFloat(ship);//subtotal + tax+ ship;
        //wrapper.pymt.pymt__Amount__c = parseFloat(total) ;
        //component.set("v.TotalAmount",total);
        
    },
    onSubmit: function(component, event, helper){
        var wrapper = component.get('v.paymentCardWrapper');
        //var total = component.get('v.TotalAmount');
        var checkFields= false;
        console.log(wrapper.pymt.Name+' - '+wrapper.pymt.pymt__Billing_First_Name__c+'  '+wrapper.cardNo);
        console.log(wrapper.expireMonth+' - '+wrapper.pymt.pymt__Billing_Postal_Code__c);
        console.log(wrapper.expireYear+' - '+wrapper.pymt.pymt__Billing_Email__c+' - '+wrapper.CVD);
         console.log(wrapper.pymt.pymt__Billing_Last_Name__c+' - '+wrapper.pymt.pymt__Billing_Street__c+' - '+wrapper.pymt.pymt__Billing_City__c);
        if(wrapper.pymt.Name != undefined  && wrapper.cardNo != undefined && 
           wrapper.expireMonth != undefined && wrapper.expireYear != undefined && wrapper.CVD != undefined &&
           wrapper.pymt.pymt__Billing_First_Name__c != undefined && wrapper.pymt.pymt__Billing_Last_Name__c != undefined &&
           wrapper.pymt.pymt__Billing_Street__c != undefined && wrapper.pymt.pymt__Billing_City__c != undefined &&
           wrapper.pymt.pymt__Billing_Postal_Code__c != undefined && wrapper.pymt.pymt__Billing_Email__c != undefined
          ){
            checkFields = true;
            
        }
        else{
            alert('Please fill all Mandatory(*) Fields');
            return false;
        }
        console.log('outside if else');
       
        var expMon = wrapper.expireMonth;
        var expYear = wrapper.expireYear;
        wrapper.expireDate = expYear.substring(2,4)+expMon;
        component.set('v.paymentCardWrapper',wrapper);
        console.log('Expire Date==> '+component.get('v.paymentCardWrapper').expireDate);
        //helper.MakePayment(component, event, helper);
        
       
        var pay = component.get("c.getPaymentDone");
        pay.setParams({
            cardDetails : component.get("v.paymentCardWrapper"),
            pid : component.get("v.recordId")
        });
        helper.MakePayment(component,pay).then(
            function(response) {
                var ReceiptId = response.response.receipt.ReceiptId;
                console.log("ReceiptId:"+ReceiptId);
                
                if (ReceiptId === null || ReceiptId === "null") {
                    console.log("Msg"+ response.response.receipt.Message);
                    
                    component.set("v.showSuccess" ,true ) ;
                    component.set("v.Msg" , response.response.receipt.Message ) ;
                    window.location.reload();
                    
                    /*component.find('notifLib').showNotice({
                        "variant": "error",
                        "header": "Error",
                        "message": response.response.receipt.Message,
                        closeCallback: function() {
                            var dismissActionPanel = $A.get("e.force:closeQuickAction");
                             dismissActionPanel.fire();
                           $A.get("e.force:refreshView").fire();
                        }
                    });*/
                    }
                    else{
                        console.log("Msg"+ response.response.receipt.Message);
                        component.set("v.showSuccess" ,true ) ;
                        component.set("v.Msg" , " Payment Reference Number: "+response.response.receipt.ReferenceNum ) ;
                        window.location.reload();
                        /*  component.find('notifLib').showNotice({
                        "variant": "Success",
                        "header": "Payment was Successful!",
                        "message": 'Payment Reference Number: '+response.response.receipt.ReferenceNum,
                        closeCallback: function() {
                            var dismissActionPanel = $A.get("e.force:closeQuickAction");
                             dismissActionPanel.fire();
                           $A.get("e.force:refreshView").fire();
                        }
                    }); */
                    } 
                }
            ).catch(
                function(error) {
                    //component.set("v.status" ,error ) ; 
                    console.log(error);
                }
            ); 
        
        
    },
    onContactChange: function(component, event, helper){
        var con = component.get("v.contactId");
        console.log('con= '+con);
        component.set('v.paymentCardWrapper', []);
        
      /*  var action = component.get("c.NewContact");
            action.setParams({
                contactId : con
            });
            action.setCallback(this, function(response) {
                //store state of response
                var state = response.getState();
                console.log('state= '+state);
                if (state === "SUCCESS") {
                    console.log("resp--> "+response.getReturnValue());
                    //set response value in wrapperList attribute on component.
                    component.set('v.paymentCardWrapper', response.getReturnValue());
                    //component.set("v.TotalAmount",response.getReturnValue().pymt.pymt__Amount__c);
                }
            });
            $A.enqueueAction(action); */
        
    }
    
})