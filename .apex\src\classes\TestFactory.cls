/**
 * @description Test Factory
 * <AUTHOR>
 * @version 1.0
 * @created 2020-06-15
 * @modified 2020-06-15 
 */

@isTest
public class TestFactory {

    //Class variables
    public static String TESTFACTORYNAME = 'TestFactory';
    
    /*
    AVAILABLE FACTORY OBJECTS
    DefaultClassName > Object
    
    AccountDefaults        > Account
    OpportunityDefaults    > Opportunity
    ContactDefaults        > Contact
    ContentVersionDefaults > ContentVersion
    UserDefaults           > User
    
    */
    
    /**
     * @description returns the default class name for the sobject
     * @params sObj object to get default class name of 
     * @return String 
     */
    public static String getDefaultClassName(SObject sObj){
        return String.valueOf(sObj.getSObjectType()).replace('__c', '') + 'Defaults';
    }
    
    /**
     * @description adds default fields and field map to object
     * @params sObj object to add fields to
     * @params defaultClassName default class name for the object 
     * @params dynamicValueMap maps of field to value for the object
     * @return sObject 
     */
    public static SObject createSObject(SObject sObj, String defaultClassName, Map<String,Object> dynamicValueMap) {
        if(defaultClassName == null) defaultClassName = getDefaultClassName(sObj);
        FieldDefaults defaults = getFieldDefaults(defaultClassName,dynamicValueMap);
        sObj = addFieldDefaults(sObj.clone(false, true), defaults.getFieldDefaults());
        return sObj;
    } 
    
    /**
     * @description gets all tdtm trigger handlers to pass into test classes to run for test
     * @params none
     * @return List<hed.TDTM_Global_API.TdtmToken> 
     */
    public static List<hed.TDTM_Global_API.TdtmToken> testTriggerRecordList(){
        
        List<hed__Trigger_Handler__c> triggerList = [select id,Name, hed__Class__c, hed__Active__c, hed__Object__c, hed__Trigger_Action__c, hed__Load_Order__c from hed__Trigger_Handler__c];
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        for(hed__Trigger_Handler__c trg:triggerList){
            if(trg.hed__Active__c == TRUE){
                
            
            	tokens.add(new hed.TDTM_Global_API.TdtmToken(trg.hed__Class__c, trg.hed__Object__c, trg.hed__Trigger_Action__c, trg.hed__Load_Order__c));
            }
        }
        
        return tokens;
    }
    
    /**
     * @description creates sObject
     * @params sObj object to create
     * @return sObject
     */
    public static SObject createSObject(SObject sObj) {
        sObj = createSObject(sObj, null, null);
        return sObj;
    }
    
    /**
     * @description creates sObject
     * @params sObj object type to create 
     * @params defaultClassName default class name for object
     * @return sObject
     * 
     */
    public static SObject createSObject(SObject sObj, String defaultClassName) {
        sObj = createSObject(sObj, defaultClassName, null);
        return sObj;
    }
    
    /**
     * @description creates sObject
     * @params sObj object to create
     * @params dynamicValueMap map of fields to field values 
     * @return sObject
     */
    public static SObject createSObject(SObject sObj,Map<String,Object> dynamicValueMap) {
        sObj = createSObject(sObj, null, dynamicValueMap);
        return sObj;
    }
    
    /**
     * @description adds default fields and field map and returns a specified number of test objects
     * @params sObj type of sobject to create
     * @params numberOfObjects number of objects to create
     * @params defaultClassName object default class name 
     * @params dynamicValueMap map of field name to field values 
     * @return list of sobjects to create
     */
    public static SObject[] createSObjectList(Sobject sObj, Integer numberOfObjects, String defaultClassName, Map<String,Object> dynamicValueMap) {
        if(defaultClassName == null) defaultClassName = getDefaultClassName(sObj);
        SObject[] sObjs = new SObject[] {};
        SObject newObj;
        FieldDefaults defaults = getFieldDefaults(defaultClassName,dynamicValueMap);
        for (Integer i = 0; i < numberOfObjects; i++) {
            newObj = addFieldDefaults(sObj.clone(false, true), defaults.getFieldDefaultsMultipleRecords(i));
            sObjs.add(newObj);
        }
        return sObjs;
    }

    /**
     * @description adds default fields and field map and returns a specified number of test objects
     * @params sObj type of sobject to create
     * @params numberOfObjects number of objects to create
     * @return list of sobjects to create
     */
    public static SObject[] createSObjectList( Sobject sObj, Integer numberOfObjects ) 
    {
        return createSObjectList( sObj, numberOfObjects, null, null );
    }
    
    
    /**
     * @description adds default fields and field map and returns a specified number of test objects
     * @params sObj type of sobject to create
     * @params numberOfObjects number of objects to create
     * @params defaultClassName object default class name 
     * @return list of sobjects to create
     */
    public static SObject[] createSObjectList(Sobject sObj, Integer numberOfObjects, String defaultClassName) {
        return createSObjectList(sObj, numberOfObjects, defaultClassName, null);
    }
    /**
     * @description adds default fields and field map and returns a specified number of test objects
     * @params sObj type of sobject to create
     * @params numberOfObjects number of objects to create
     * @params dynamicValueMap map of field name to field values 
     * @return list of sobjects to create
     */
    public static SObject[] createSObjectList(Sobject sObj, Integer numberOfObjects, Map<String,Object> dynamicValueMap) {
        return createSObjectList(sObj, numberOfObjects, null, dynamicValueMap);
    }
    
    /**
     * @description returns the map of field defaults if it exists
     * @params defaultClassName default class name
     * @params dynamicValueMap map of fields to field values
     * @return FieldDefaults
     * 
     */
    private static FieldDefaults getFieldDefaults(String defaultClassName, Map<String,Object> dynamicValueMap) {
        if(!defaultClassName.startsWith(TESTFACTORYNAME + '.')) defaultClassName = TESTFACTORYNAME + '.' + defaultClassName;
        // Create an instance of the defaults class so we can get the Map of field defaults
        Type t = Type.forName(defaultClassName);
        if (t == null) {
            Throw new TestFactoryException('Invalid defaults class:' + defaultClassName);
        }
        FieldDefaults defaults = (FieldDefaults)t.newInstance();
        defaults.setDynamicValueMap(dynamicValueMap);
        return defaults;
    }
    
    /****************************************************************************************
    * <AUTHOR> Consulting Group
    * @Date         March 1, 2016
    * @Description  addFieldDefaults method - returns the map of field defaults if it exists
    * @params       String <default class name>, Map<String,Object> <fieldname,value>
    * @return       FieldDefaults <interface that holds defaults for objects>
    * @revision(s)  
    * **************************************************************************************/
    /**
     * @description returns the map of field defaults if it exists
     * @params sObject sobject type 
     * @params defaults map of default field values
     * @return sObject
     * 
     */
    private static SObject addFieldDefaults(SObject sObj, Map<String, Object> defaults) {
        // Loop through the map of fields and if they are null on the object, fill them.
        for (String field : defaults.keySet()) {
            if (sObj.get(field) == null) {
                sObj.put(field, defaults.get(field));
            }
        }
        return sObj;
    }
    /**
     * @description Custom exception thrown by TestFactory 
     */
    public class TestFactoryException extends Exception {}

    /**
     * @description Interface used to set up values you want to default in for all objects 
     */
    public interface FieldDefaults {
        Map<String,Object> getDynamicValueMap();
        void setDynamicValueMap(Map<String,Object> val);
        Map<String, Object> getFieldDefaults();
        Map<String, Object> getFieldDefaultsMultipleRecords(Integer val);
    }
    
    /*
    DEFAULT METHODS
    To specify defaults for objects, use the naming convention [ObjectName]Defaults.
    For custom objects, omit the __c from the Object Name
    */
    

    /**
     * @description Default field values for ACCOUNT 
     */
    public class AccountDefaults implements FieldDefaults {
        Map<String,Object> dynamicValueMap;
        
        public void setDynamicValueMap(Map<String,Object> val) {
            this.dynamicValueMap = val;
        }
        
        public Map<String,Object> getDynamicValueMap() {
            return this.dynamicValueMap;
        }
        
        public Map<String, Object> getFieldDefaults() {
            Map<String, Object>  m = new Map<String, Object> {
                'Name' => 'Test Account'
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        }
        public Map<String, Object> getFieldDefaultsMultipleRecords(integer counter) {
            Map<String, Object>  m = new Map<String, Object> {
                'Name' => 'Test Account' + counter
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        } 
    }
    /**
     * @description Default field values for TERM 
     */
    public class TermDefaults implements FieldDefaults {
        Map<String,Object> dynamicValueMap;
        
        public void setDynamicValueMap(Map<String,Object> val) {
            this.dynamicValueMap = val;
        }
        
        public Map<String,Object> getDynamicValueMap() {
            return this.dynamicValueMap;
        }
        
        public Map<String, Object> getFieldDefaults() {
            Map<String, Object>  m = new Map<String, Object> {
                'Name' => 'Spring 2020'
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        }
        public Map<String, Object> getFieldDefaultsMultipleRecords(integer counter) {
            Map<String, Object>  m = new Map<String, Object> {
                'Name' => 'Test Account' + counter
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        } 
    }

    /**
     * @description Default field values for OPPORTUNITY 
     */
    public class OpportunityDefaults implements FieldDefaults {
        Map<String,Object> dynamicValueMap;
        
        public void setDynamicValueMap(Map<String,Object> val) {
            this.dynamicValueMap = val;
        }
        
        public Map<String,Object> getDynamicValueMap() {
            return this.dynamicValueMap;
        }
        
        public Map<String, Object> getFieldDefaults() {
            Map<String, Object>  m = new Map<String, Object> {
                'Name' => 'Test Opportunity',
                'StageName' => 'Inquiry',
                'CloseDate' => Date.Today().addMonths(12) 
            }; 
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        }
        public Map<String, Object> getFieldDefaultsMultipleRecords(integer counter) {
            Map<String, Object>  m = new Map<String, Object> {
                'Name' => 'Test Opportunity' + counter,
                'Stage' => 'Inquiry',
                'CloseDate' => Date.Today().addMonths(counter) 
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        } 
    }

    /**
     * @description Default field values for CONTACT 
     */
    public class ContactDefaults implements FieldDefaults {
        Map<String,Object> dynamicValueMap;
        
        public void setDynamicValueMap(Map<String,Object> val) {
            this.dynamicValueMap = val;
        }
        
        public Map<String,Object> getDynamicValueMap() {
            return this.dynamicValueMap;
        }
        
        public Map<String, Object> getFieldDefaults() {
            Map<String, Object>  m = new Map<String, Object> {
                'LastName' => 'Lastname'
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        }
        public Map<String, Object> getFieldDefaultsMultipleRecords(integer counter) {
            Map<String, Object>  m = new Map<String, Object> {
                'LastName' => 'Lastname' + counter
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        } 
    }

    /**
     * @description Default field values for CONTENTVERSION 
     */
    public class ContentVersionDefaults implements FieldDefaults {
        Map<String,Object> dynamicValueMap;
        
        public void setDynamicValueMap(Map<String,Object> val) {
            this.dynamicValueMap = val;
        }
        
        public Map<String,Object> getDynamicValueMap() {
            return this.dynamicValueMap;
        }
        
        public Map<String, Object> getFieldDefaults() {
            Map<String, Object>  m = new Map<String, Object> {
                'VersionData' => Blob.valueOf( 'TestBody' ),
                'Title' => 'test',
                'PathOnClient' => 'test'
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        }
        public Map<String, Object> getFieldDefaultsMultipleRecords(integer counter) {
            Map<String, Object>  m = new Map<String, Object> {
                'VersionData' => Blob.valueOf( 'TestBody' ),
                'Title' => 'test' + counter,
                'PathOnClient' => 'test' + counter
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        } 
    }

    /**
     * @description Default field values for USER 
     */
    public class UserDefaults implements FieldDefaults {
        Profile STANDARDUSER = [SELECT ID FROM Profile WHERE NAME = 'Standard User']; 

        Map<String,Object> dynamicValueMap;
        
        public void setDynamicValueMap(Map<String,Object> val) {
            this.dynamicValueMap = val;
        }
        
        public Map<String,Object> getDynamicValueMap() {
            return this.dynamicValueMap;
        }
        
        public Map<String, Object> getFieldDefaults() {
            Map<String, Object>  m = new Map<String, Object> {
                'Username' => '<EMAIL>', 
                'Email' => '<EMAIL>', 
                'Alias' => 'tuser', 
                'EmailEncodingKey' => 'UTF-8', 
                'FirstName' => 'Test', 
                'LastName' => 'User', 
                'ProfileId' => STANDARDUSER.Id,
                'TimeZoneSidKey' => 'America/Los_Angeles', 
                'LocaleSidKey' => 'en_US', 
                'LanguageLocaleKey' => 'en_US'
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        }
        public Map<String, Object> getFieldDefaultsMultipleRecords(integer counter) {
            Map<String, Object>  m = new Map<String, Object> {
                'Username' => 'test'+counter+'<EMAIL>', 
                'Email' => 'test'+counter+'<EMAIL>', 
                'Alias' => 'tuse' + counter, 
                'EmailEncodingKey' => 'UTF-8', 
                'FirstName' => 'Test', 
                'LastName' => 'User'+counter, 
                'ProfileId' => STANDARDUSER.Id,
                'TimeZoneSidKey' => 'America/Los_Angeles', 
                'LocaleSidKey' => 'en_US', 
                'LanguageLocaleKey' => 'en_US'
            };
            if(dynamicValueMap!=null) m.putAll(dynamicValueMap);
            return m;
        } 
    }
}