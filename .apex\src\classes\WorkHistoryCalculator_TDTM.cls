/**
 * @description work history calculator to calculate Previous_Years_Work_Experience__c amd Current_Work_Effective_Start_Date__c 
 * and Management History Calculator to calculate Previous_Years_Management_Experience__c and Current_Management_Effective_Start_Date__c
 * <AUTHOR>
 * @version 1.0
 * @created 2020-03-03
 * @modified 2020-04-23
 */
global class WorkHistoryCalculator_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Affiliation records from trigger new 
     * @param oldList the list of Affiliation records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Affiliations 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        Set<Id> contactIds = new Set<Id>();  
        Set<Id> unqualifiedMgts = new Set<Id>(); 

        //AFTER INSERT CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
            for(hed__Affiliation__c aff: (List<hed__Affiliation__c>) newList){
                //filter out affiliations of record type 'Work History' where the start dates have values  
                if(aff.hed__Contact__c != null && aff.recordTypeId == AffiliationService.workHistoryRTId && aff.hed__StartDate__c != null){
                    contactIds.add(aff.hed__Contact__c); 
                }
            }
        }

        //AFTER UPDATE CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            Map<Id, hed__Affiliation__c> oldMap = new Map<Id, hed__Affiliation__c>((List<hed__Affiliation__c>)oldList); 

            for(hed__Affiliation__c aff: (List<hed__Affiliation__c>) newList){
                //filter out affiliations or Record type 'Work History' where the start date, end dates, or Qualifies as Management experience have been updated 
                if(aff.hed__Contact__c != null && aff.recordtypeId == AffiliationService.workHistoryRTId && (aff.hed__StartDate__c != oldMap.get(aff.Id).hed__StartDate__c)  ||( aff.hed__EndDate__c != oldMap.get(aff.Id).hed__EndDate__c) || (aff.Qualifies_as_Management_Experience__c != oldMap.get(aff.Id).Qualifies_as_Management_Experience__c)){
                    contactIds.add(aff.hed__Contact__c); 
                    //filter out contacts where affiliation's qualified as managment experience got unchecked: 
                    if(!aff.Qualifies_As_Management_Experience__c && oldMap.get(aff.Id).Qualifies_As_Management_Experience__c){
                        unqualifiedMgts.add(aff.hed__Contact__c); 
                    }
                }
            }
        }

        //AFTER DELETE CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterDelete){
            for(hed__Affiliation__c aff: (List<hed__Affiliation__c>) oldList){
                //filter out affiliations of record type 'Work History' where the start dates have values  
                if(aff.hed__Contact__c != null && aff.recordTypeId == AffiliationService.workHistoryRTId && aff.hed__StartDate__c != null){
                    contactIds.add(aff.hed__Contact__c); 
                }
            }
        }

        if(contactIds.size() > 0){
            //Map of contact records to update: 
            Map<Id, Contact> contsToUpdateMap = new Map<Id, Contact>(); 

            //Query for fields in contacts: 
            List<Contact> contactList = ContactService.queryFieldsInContacts(contactIds, new Set<String>{'Previous_Years_Work_Experience__c', 'Current_Work_Effective_Start_Date__c', 'Previous_Years_Management_Experience__c', 'Current_Management_Effective_Start_Date__c'} ); 
            //Map contact records to their related work history records ordered by start date ASC then end date ASC: 
            Map<Id, List<hed__Affiliation__c>> contToWHMap = AffiliationService.mapContactToWorkHistories(contactIds, 'RecordType.DeveloperName = \'Work_History\' AND hed__Contact__c IN :contactIds ORDER BY hed__StartDate__c ASC, hed__EndDate__c ASC'); 
            //Map contact records to their related management history records 
            Map<Id, List<hed__Affiliation__c>> contToMHMap = mapContactToMgtHistories(contToWHMap); 
  
            // Calculate and update Previous_Years_Work_Experience__c && Current_work_effective_start_date__c field on Contact record 
            if(contToWHMap.size() > 0){
                //Map Contact ID to their earliest current work history: 
                Map<Id, Date> contToCurrentWH = AffiliationService.mapContIDToCurrentWHDate(contToWHMap);

                //Calculate total work history experience: 
                List<Contact> calculatedConsecutiveYears = (List<Contact>) RollupService.calculateConsecutiveYears(contactList, contToWHMap, contToCurrentWH, 'hed__StartDate__c', 'hed__EndDate__c', 'Previous_Years_Work_Experience__c'); 
                if(calculatedConsecutiveYears.size() > 0) contsToUpdateMap.putAll(calculatedConsecutiveYears); 

                //Stamp the current_work_effective_start_date__c on the Contact: 
                List<Contact> updatedCurrentStartDates = stampCurrentStartDate(contactList, contToCurrentWH, 'current_work_effective_start_date__c'); 
                if(updatedCurrentStartDates.size() > 0) contsToUpdateMap.putAll(updatedCurrentStartDates); 

            } 
            
            //Calculate and update Previous_Years_Management_Experience__c && Current_Management_Effective_Start_Date__c field on Contact record
            if(contToMHMap.size() > 0){
                //Map Contact ID to their earliest current work history: 
                Map<Id, Date> contToCurrentMH = AffiliationService.mapContIDToCurrentWHDate(contToMHMap);

                //Calculate total management history experience: 
                List<Contact> calculatedConsecutiveYears = (List<Contact>) RollupService.calculateConsecutiveYears(contactList, contToMHMap, contToCurrentMH, 'hed__StartDate__c', 'hed__EndDate__c', 'Previous_Years_Management_Experience__c'); 
                if(calculatedConsecutiveYears.size() > 0) contsToUpdateMap.putAll(calculatedConsecutiveYears); 

                //Stamp the current_management_effective_start_date__c on the Contact: 
                List<Contact> updatedCurrentStartDates = stampCurrentStartDate(contactList, contToCurrentMH, 'Current_Management_Effective_Start_Date__c'); 
                if(updatedCurrentStartDates.size() > 0) contsToUpdateMap.putAll(updatedCurrentStartDates); 
            }

            //Set Previous_Years_Management_Experience__c && Current_Management_Effective_Start_Date__c field values to null if affiliation record's Qualifies_As_Management_Experience__c checkbox has been unchecked: 
            if(unqualifiedMgts.size() > 0){
                for(Id contId :unqualifiedMgts){
                    if(contToMHMap != null && !contToMHMap.containsKey(contId)){
                        contsToUpdateMap.put(contId, new Contact(Id = contId, Previous_Years_Management_Experience__c = null, Current_Management_Effective_Start_Date__c = null)); 
                    }
                }
            }
            dmlWrapper.objectsToUpdate.addAll(contsToUpdateMap.values());
        }
        return dmlWrapper; 
    }
    /**
     * @description Maps contact Id to a list of management work experiences 
     * @return  Map of contact Id to a list of Management work experience 
     * @param Map<Id, List<hed__Affiliation__c>> map of contact Ids to List of related affiliations of RT work history 
     */
    private Map<Id, List<hed__Affiliation__c>> mapContactToMgtHistories(Map<Id, List<hed__Affiliation__c>> contToWHMap){
        Map<Id, List<hed__Affiliation__c>> contToMHMap = new Map<Id, List<hed__Affiliation__c>>(); 
        for(Id contId :contToWHMap.keySet()){
            for(hed__Affiliation__c aff :contToWHMap.get(contId)){
                if(aff.Qualifies_as_Management_Experience__c){
                    if(!contToMHMap.containsKey(contId)){
                        contToMHMap.put(contId, new List<hed__Affiliation__c>{aff}); 
                    }
                    else{
                        contToMHMap.get(contId).add(aff); 
                    }
                }
            }
        }
        return contToMHMap; 
    }
    
    /**
     * @description updated the current_work_effective_start_date__c field on the contact record  
     * @return  list of contact records that will be updated
     * @param contList list of contact records 
     * @param contToCurrentWHMap map of contact record ID to their current work history start date value  
     * @param startDateField api name of start date field to stamp
     */
    private List<Contact> stampCurrentStartDate(List<Contact> contList, Map<Id, Date> contToCurrentWHMap, String startDateField){
        List<Contact> contsToUpdate = new List<Contact>(); 
        for(Contact cont :contList){
            if(contToCurrentWHMap.containsKey(cont.Id)){
                //only update record if the start date field has been updated: 
                if(contToCurrentWHMap.get(cont.Id) != cont.get(startDateField)){
                    cont.put(startDateField,contToCurrentWHMap.get(cont.Id)); 
                    contsToUpdate.add(cont); 
                }
            }
        }
        return contsToUpdate; 
    }         
}