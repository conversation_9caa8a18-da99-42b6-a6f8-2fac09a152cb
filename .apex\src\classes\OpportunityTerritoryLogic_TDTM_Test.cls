/**
* @description    Test class for OpportunityTerritoryLogic_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-03-13
* @modified 2020-03-13
*/
@isTest
public class OpportunityTerritoryLogic_TDTM_Test {
/**
     * Class Variables 
     */
    private static final String programCode = '999999'; 
    
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for OpportunityTerritoryLogic__TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('OpportunityTerritoryLogic_TDTM', 'Opportunity', 'BeforeInsert;BeforeUpdate', 1.00)); 
        //create trigger handler for OpportunityAssignAD_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('OpportunityAssignAD_TDTM', 'Opportunity', 'BeforeInsert;BeforeUpdate', 2.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
         //Create Program and Administrative Accounts 
         Account paTest = new Account(Name = 'TestPA', Program_Code__c = programCode, RecordTypeId = AccountService.AcademicProgramRTId ); 
         Account paTest2 = new Account(Name = 'TestPA2', RecordTypeId = AccountService.AcademicProgramRTId ); 
         Account adminAcct = new Account(Name = 'Admin1 Account', RecordTypeId = AccountService.AdminRTId); 
         insert new List<Account>{paTest, paTest2, adminAcct};  

        //Add new users, territory, and user territory assignments 
        addSetUpObjects(); 

        //Query for a random territory: 
        Territory2 territory = [SELECT ID FROM Territory2 WHERE Program_Code__c != null LIMIT 1]; 

        //Insert test Opportunity Records: 
        Id DegreeProgramProspectRTId = OpportunityService.DegreeProgramProspectRTId; 
        //Create Opportunities (with no PA)
        Opportunity testOppNoPA = new Opportunity(Name = 'Test Oppty No PA', AccountId = adminAcct.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', RecordTypeId = DegreeProgramProspectRTId); 
        Opportunity testOppWithPA = new Opportunity(Name = 'Test Oppty with PA', AccountId = adminAcct.id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', 
                                                        Program_Account__c = paTest2.Id, Assistant_Director__c = UserInfo.getUserId(), Recruitment_Officer__c = UserInfo.getUserId(), 
                                                        Territory2Id = territory.Id, IsExcludedFromTerritory2Filter = true, RecordTypeId = DegreeProgramProspectRTId); 
        insert new List<Opportunity>{testOppNoPA, testOppWithPA}; 
    }

    /**
     * @description future method to add new users, 
     * territory, and user assignments in the setup method 
     */
    @future
    private static void addSetUpObjects(){
        //Query for Recruitment and Admissions Profile: 
        Profile profileRA = [SELECT ID FROM PROFILE WHERE NAME = 'System Administrator' LIMIT 1]; 
        //Create Test User Profiles: 
        List<user> usersToInsert = new List<User>(); 
        for(Integer i= 0; i < 4; i++){
            User testUser = new User(
                                    alias = 'test'+i, 
                                    email = 'test'+i+'@testuser.com', 
                                    emailencodingkey = 'UTF-8', 
                                    firstName = 'test',
                                    lastName = 'User'+i, 
                                    userName = 'test'+i+'@testuser.com.otl', 
                                    profileId = profileRA.Id, 
                                    timeZoneSidKey = 'America/Los_Angeles', 
                                    LocaleSidKey = 'en_US', 
                                    LanguageLocaleKey = 'en_US'); 
            usersToInsert.add(testUser); 
        }
        insert usersToInsert; 

        //Query for Specialized Program Territory Type: 
        Territory2Type spType = [SELECT Id FROM Territory2Type WHERE DeveloperName = 'Specialized_Programs' LIMIT 1]; 
        
        //Query for Active Territory Model:
        Territory2Model territoryModel = [SELECT ID, DeveloperName FROM Territory2Model WHERE State = 'Active']; 

        //Create new Territory record: 
        Territory2 testTerritory = new Territory2(DeveloperName = 'Test_Program_Territory', Name = 'Test Program Territory', Program_Code__c  = programCode, Territory2ModelId = territoryModel.id, Territory2TypeId = spType.Id ); 
        Territory2 testTerritory2 = new Territory2(DeveloperName = 'Test_Program_Territory_No_Assigments', Name =' Test Program Territory - No Assignments', Program_code__c = 'Test800', Territory2ModelId = territoryModel.Id, Territory2TypeId = spType.Id);
        insert new List<Territory2>{testTerritory, testTerritory2}; 
        //Create User Assignments
        List<UserTerritory2Association> userAssignments = new List<UserTerritory2Association>(); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[0].Id, RoleInTerritory2='Assistant Director')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[1].Id, RoleInTerritory2='Assistant Director')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[2].Id, RoleInTerritory2='Recruitment Officer')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[3].Id, RoleInTerritory2='Recruitment Officer')); 
        insert userAssignments; 
    }

    /**
     * @description Create a new opportunity record that is related to an account 
     * that has a program territory assigned--Opportunity should be created with a 
     * program account value. 
     * Assert that territory lookup field on opportunity is populated
     * Assert that Admissions Director lookup field on opportunity is populated
     * Assert that Recruitment Officer lookup field on opportunity is populated
     * 
     */
    @isTest
    public static void testCreateOpp(){
        //Query for admin account: 
        Account adminAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1]; 

        //Query for test territory: 
        Territory2 testTerritory = [SELECT Id, Program_Code__c FROM Territory2 WHERE Program_Code__c = :programAccount.Program_Code__c AND DeveloperName = 'Test_Program_Territory' LIMIT 1]; 

        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = adminAccount.Id, Territory2Id = testTerritory.Id, AssociationCause = 'Territory2Manual');
        //Create an opportunity with a related accountId (with territories) & program app
        Opportunity opp = new Opportunity(Name = 'Test Oppty PA', AccountId = adminAccount.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', 
                                            Program_Account__c = programAccount.Id, RecordTypeId = OpportunityService.DegreeProgramProspectRTId);

        Test.startTest(); 
            insert acctAssignment;
            insert opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT Id, Assistant_Director__c, Recruitment_Officer__c, Territory2Id, Assign_Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 
        //Query for all user assignments of test terriority: 
        List<UserTerritory2Association> userAssignments = [SELECT ID,RoleInTerritory2, UserId FROM UserTerritory2Association WHERE Territory2Id = :testTerritory.Id]; 
        Set<Id> ADUsers = new Set<Id>(); 
        Set<Id> ROUsers = new Set<Id>(); 

        for(UserTerritory2Association u :userAssignments){
            if(u.RoleInTerritory2 == 'Assistant Director'){
                ADUsers.add(u.UserId); 
            }
            else if(u.RoleInTerritory2 == 'Recruitment Officer'){
                ROUsers.add(u.UserId); 
            }
        }

        //Assert that territory was populated: 
        System.assertEquals(testTerritory.Id, oppAfter.Territory2Id); 
        //Assert that AD was populated: 
        System.assert(ADUsers.contains(oppAfter.Assistant_Director__c), 'Assistant Director was not populated correctly on the opportunity: ' + oppAfter ); 
        //Assert that RO was populated: 
        System.assert(ROUsers.contains(oppAfter.Recruitment_Officer__c), 'Recruitment Officer was not populated correctly on the opportunity: ' + oppAfter); 
    }

    /**
     * @description Create a new executive program prospect opportunity record that is related to an account 
     * that has a program territory assigned--Opportunity should be created with a 
     * program account value. --Logic should not run for Non-Prospect opps
     * Assert that territory lookup field on opportunity was not populated
     * Assert that Admissions Director lookup field on opportunity was not populated
     * Assert that Recruitment Officer lookup field on opportunity was not populated
     * 
     */
    @isTest
    public static void testCreateNon_ProspectOpp(){
        //Query for admin account: 
        Account adminAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1]; 

        //Query for test territory: 
        Territory2 testTerritory = [SELECT Id, Program_Code__c FROM Territory2 WHERE Program_Code__c = :programAccount.Program_Code__c AND DeveloperName = 'Test_Program_Territory' LIMIT 1]; 

        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = adminAccount.Id, Territory2Id = testTerritory.Id, AssociationCause = 'Territory2Manual');
        //Create an opportunity with a related accountId (with territories) & program app
        Opportunity opp = new Opportunity(Name = 'Test Oppty PA', AccountId = adminAccount.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', 
                                            Program_Account__c = programAccount.Id, RecordTypeId = OpportunityService.ProgramOfferingRTId);

        Test.startTest(); 
            insert acctAssignment;
            insert opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT Id, Assistant_Director__c, Recruitment_Officer__c, Territory2Id FROM Opportunity WHERE Id = :opp.Id]; 

        //Assert that territory was not populated: 
        System.assertEquals(null, oppAfter.Territory2Id); 
        //Assert that AD was not populated: 
        System.assertEquals(null, oppAfter.Assistant_Director__c); 
        //Assert that RO was populated: 
        System.assertEquals(null, oppAfter.Recruitment_Officer__c); 
    }

    /**
     * @description Create a new opportunity record that is related to an account 
     * that does not have a program territory assigned--Opportunity should be created with a 
     * program account value. 
     * Assert that territory lookup field on opportunity is not populated
     * Assert that Admissions Director lookup field on opportunity is not populated
     * Assert that Recruitment Officer lookup field on opportunity is not populated
     * 
     */
    @isTest
    public static void testCreateOpp_Neg(){
        //Query for admin account: 
        Account adminAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for program account: 
        Account programAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1]; 

        //Create an opportunity with a related account Id (with no territories) & program app
        Opportunity opp = new Opportunity(Name = 'Test Oppty PA', AccountId = adminAccount.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', 
                                            Program_Account__c = programAccount.Id, RecordTypeId = OpportunityService.DegreeProgramProspectRTId);

        Test.startTest(); 
            insert opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT Id, Assistant_Director__c, Recruitment_Officer__c, Territory2Id FROM Opportunity WHERE Id = :opp.Id]; 

        //Assert that no territory was populated 
        System.assertEquals(null, oppAfter.Territory2Id, 'Territory should not be assigned to opp'); 
        //Assert AD was not assigned 
        System.assertEquals(null, oppAfter.Assistant_Director__c, 'AD should not be assigned to opp'); 
        //Assert RO was not assigned 
        System.assertEquals(null, oppAfter.Recruitment_Officer__c, 'RO should not be assigned to opp'); 
    }

    /**
     * @description Create a new opportunity record that is related to an account 
     * that has a program territory assigned--Opportunity should be created without a 
     * program account value. 
     * Assert that territory lookup field on opportunity is not populated
     * Assert that Admissions Director lookup field on opportunity is not populated
     * Assert that Recruitment Officer lookup field on opportunity is not populated
     */
    @isTest
    public static void testCreateOpp_NoPA(){
        //Query for admin account: 
        Account adminAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1];

        //Query for test territory: 
        Territory2 testTerritory = [SELECT Id, Program_Code__c FROM Territory2 WHERE Program_Code__c = :programAccount.Program_Code__c AND DeveloperName = 'Test_Program_Territory' LIMIT 1]; 

        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = adminAccount.Id, Territory2Id = testTerritory.Id, AssociationCause = 'Territory2Manual');

        //Create an opportunity with a related accountId (with territories): 
        Opportunity opp = new Opportunity(Name = 'Test Oppty PA', AccountId = adminAccount.Id, CloseDate = Date.newInstance(2021, 1, 1), 
                                            StageName = 'Inquiry', RecordTypeId = OpportunityService.DegreeProgramProspectRTId);

        Test.startTest(); 
            insert opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT Id, Assistant_Director__c, Recruitment_Officer__c, Territory2Id FROM Opportunity WHERE Id = :opp.Id]; 

        //Assert that no territory was populated 
        System.assertEquals(null, oppAfter.Territory2Id, 'Territory should not be assigned to opp'); 
        //Assert AD was not assigned 
        System.assertEquals(null, oppAfter.Assistant_Director__c, 'AD should not be assigned to opp'); 
        //Assert RO was not assigned 
        System.assertEquals(null, oppAfter.Recruitment_Officer__c, 'RO should not be assigned to opp');
    }
     /**
     * @description update an opportunity record that is related to an account 
     * that has a program territory assigned--Opportunity should be updated with a 
     * program account value. 
     * Assert that territory lookup field on opportunity is populated
     * Assert that Admissions Director lookup field on opportunity is populated
     * Assert that Recruitment Officer lookup field on opportunity is populated
     */
    @isTest
    public static void testUpdateOpp_WithPA(){
        //Query for admin account: 
        Account adminAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1];

        //Query for test territory: 
        Territory2 testTerritory = [SELECT Id, Program_Code__c FROM Territory2 WHERE Program_Code__c = :programAccount.Program_Code__c AND DeveloperName = 'Test_Program_Territory' LIMIT 1]; 

        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = adminAccount.Id, Territory2Id = testTerritory.Id, AssociationCause = 'Territory2Manual');

        Opportunity opp = [SELECT ID, Territory2Id, Assistant_Director__c, Recruitment_Officer__c FROM Opportunity WHERE AccountId = :adminAccount.Id AND Program_Account__c = null LIMIT 1];

        Test.startTest(); 
            insert acctAssignment; 
            opp.Program_Account__c = programAccount.Id; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT Id, Assistant_Director__c, Recruitment_Officer__c, Territory2Id FROM Opportunity WHERE Id = :opp.Id]; 
        //Query for all user assignments of test terriority: 
        List<UserTerritory2Association> userAssignments = [SELECT ID,RoleInTerritory2, UserId FROM UserTerritory2Association WHERE Territory2Id = :testTerritory.Id]; 
        Set<Id> ADUsers = new Set<Id>(); 
        Set<Id> ROUsers = new Set<Id>(); 

        for(UserTerritory2Association u :userAssignments){
            if(u.RoleInTerritory2 == 'Assistant Director'){
                ADUsers.add(u.UserId); 
            }
            else if(u.RoleInTerritory2 == 'Recruitment Officer'){
                ROUsers.add(u.UserId); 
            }
        }

        //Assert that territory was populated: 
        System.assertNotEquals(opp.Territory2Id, oppAfter.Territory2Id); 
        System.assertEquals(testTerritory.Id, oppAfter.Territory2Id); 
        //Assert that AD was populated: 
        System.assertNotEquals(opp.Assistant_Director__c, oppAfter.Assistant_Director__c); 
        System.assert(ADUsers.contains(oppAfter.Assistant_Director__c), 'Assistant Director was not populated correctly on the opportunity: ' + oppAfter); 
        //Assert that RO was populated: 
        System.assertNotEquals(opp.Recruitment_Officer__c, oppAfter.Recruitment_Officer__c); 
        System.assert(ROUsers.contains(oppAfter.Recruitment_Officer__c), 'Recruitment Officer was not populated correctly on the opportunity: ' + oppAfter); 
    }

     /**
     * @description Test that users are assigned fairly on insertion of related multiple 
     * opportunities 
     * Assert that territory lookup field on opportunity is populated
     * Assert that opportunities are assigned different admissions directors
     * Assert that opportunities are assigned different recruitment officers 
     */
    @isTest
    public static void testEffortAssignment(){
        //Query for admin account: 
        Account adminAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1];
        
        //Query for test territory: 
        Territory2 testTerritory = [SELECT Id, Program_Code__c FROM Territory2 WHERE Program_Code__c = :programAccount.Program_Code__c AND DeveloperName = 'Test_Program_Territory' LIMIT 1]; 

        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = adminAccount.Id, Territory2Id = testTerritory.Id, AssociationCause = 'Territory2Manual');

        List<Opportunity> oppsToInsert = new List<Opportunity>(); 
        oppsToInsert.add(new Opportunity(Name = 'Test Oppty PA1', AccountId = adminAccount.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', Program_Account__c = programAccount.Id, RecordTypeId = OpportunityService.DegreeProgramProspectRTId, IsExcludedFromTerritory2Filter = False));

        Test.startTest(); 
            insert acctAssignment; 
            insert oppsToInsert; 
        Test.stopTest(); 

        Set<Id> oppIds = new Set<Id>(); 
        for(Opportunity opp :oppsToInsert){
            oppIds.add(opp.Id); 
        }

        List<Opportunity> oppsAfter = [SELECT ID, Territory2Id, Assistant_Director__c, Recruitment_Officer__c, Level_Of_Effort__c FROM Opportunity WHERE Id IN :oppIds]; 
        Set<Id> adUsers = new Set<Id>(); 
        Set<Id> roUsers = new Set<Id>(); 

        for(Opportunity opp :oppsAfter){
            if(opp.Assistant_Director__c != null) adUsers.add(opp.Assistant_Director__c); 
            if(opp.Recruitment_Officer__c != null) roUsers.add(opp.Recruitment_Officer__c); 
            //Assert that territory was assigned: 
            System.assert(opp.Territory2Id == testTerritory.Id, 'Territory was not assigned properly'); 
        }

        //Assert that each opp was assigned different ROs and ADs: 
        System.assert(adUsers.size() == oppsAfter.size(), 'ADs were not assigned properly' + oppsAfter); 
        System.assert(roUsers.size() == oppsAfter.size(), 'ROs were not assigned properly'); 
    }

     /**
     * @description When updating an opportunity with a program account that has no 
     * associated program territories, territory, AD, and RO should reset 
     * Assert that territory lookup field on opportunity is blank
     * Assert that opportunity's admissions director field is blank
     * Assert that opportunity's recruitment officer field is blank 
     */
    @isTest
    public static void testUpdatePA_NullFields(){
        //Query for an opportunity with program account: 
        Opportunity opp = [SELECT ID, AccountId, Territory2Id, Assistant_Director__c, Recruitment_Officer__c, Program_Account__c FROM Opportunity 
                            WHERE Program_Account__c != null 
                            AND Assistant_Director__c != null 
                            AND Recruitment_Officer__c != null 
                            AND Territory2Id != null
                            LIMIT 1];

        //Query for another program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null AND Id != :opp.Program_Account__c LIMIT 1];

        //Query for territory not associated with program account: 
        Territory2 territory = [SELECT ID FROM Territory2 WHERE Program_Code__c != null AND Program_Code__c != :programAccount.Program_Code__c LIMIT 1]; 
        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = opp.AccountId, Territory2Id = territory.Id, AssociationCause = 'Territory2Manual');

        //Update program account value on opportunity: 
        Test.startTest(); 
            insert acctAssignment; 
            opp.IsExcludedFromTerritory2Filter = false; 
            opp.Program_Account__c = programAccount.Id; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT ID, Territory2Id, Assistant_Director__c, Recruitment_Officer__c, Program_Account__c FROM Opportunity WHERE Id = :opp.Id]; 

        //Assert that Territory, AD, and RO fields have all been nulled out: 
        System.assertEquals(null, oppAfter.Territory2Id); 
        System.assertEquals(null, oppAfter.Assistant_Director__c); 
        System.assertEquals(null, oppAfter.Recruitment_Officer__c); 
    }

     /**
     * @description Test that when an opportunity is updated with a program account 
     * whose related program territory has no assigned RO and AD users, all RO
     * and AD fields are nulled out
     * Assert that opportunity's admissions director field is blank
     * Assert that opportunity's recruitment officer field is blank 
     */
    @isTest
    public static void testUpdatePA_NullUsers(){
        //Query for an opportunity with program account: 
        Opportunity opp = [SELECT ID, AccountId, Territory2Id, Assistant_Director__c, Recruitment_Officer__c, Program_Account__c FROM Opportunity 
                            WHERE Program_Account__c != null 
                            AND Assistant_Director__c != null 
                            AND Recruitment_Officer__c != null 
                            AND Territory2Id != null
                            LIMIT 1];

        //Query for another program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null AND Id != :opp.Program_Account__c LIMIT 1];

        //Query for territory not associated with program account: 
        Territory2 territory = [SELECT ID, Program_Code__c FROM Territory2 WHERE DeveloperName = 'Test_Program_Territory_No_Assigments' LIMIT 1]; 
 
        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = opp.AccountId, Territory2Id = territory.Id, AssociationCause = 'Territory2Manual');
        ObjectTerritory2Association acctAssignment_old = new ObjectTerritory2Association(ObjectId = opp.AccountId, Territory2Id = opp.Territory2Id, AssociationCause = 'Territory2Manual');

        //Update program account value on opportunity: 
        Test.startTest(); 
            insert new List<ObjectTerritory2Association>{acctAssignment, acctAssignment_old}; 
            //update program account's program code to match territory's program code: 
            programAccount.program_Code__c = territory.program_code__c; 
            update programAccount; 
            //Update opp's program account: 
            opp.IsExcludedFromTerritory2Filter = false; 
            opp.Program_Account__c = programAccount.Id; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT ID, Territory2Id, Assistant_Director__c, Recruitment_Officer__c, Program_Account__c FROM Opportunity WHERE Id = :opp.Id]; 

        //Assert that Territory has been updated & AD and RO fields have all been nulled out: 
        System.assertEquals(territory.Id, oppAfter.Territory2Id); 
        System.assertEquals(null, oppAfter.Assistant_Director__c); 
        System.assertEquals(null, oppAfter.Recruitment_Officer__c);
    }

     /**
     * @description Tests that ADs and ROs are assigned based on their 
     * existing level of efforts 
     * Assert that territory lookup field on opportunity is populated
     * Assert that opportunities are assigned different admissions directors
     * Assert that opportunities are assigned different recruitment officers 
     */
    @isTest
    public static void testEffortAssignment_ExistingOpps(){
        //Query for admin account: 
        Account adminAccount = [SELECT ID FROM Account WHERE RecordtypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1];
        
        //Query for test territory: 
        Territory2 testTerritory = [SELECT Id, Program_Code__c FROM Territory2 WHERE Program_Code__c = :programAccount.Program_Code__c AND DeveloperName = 'Test_Program_Territory' LIMIT 1]; 

        //Query for all users assigned to testTerritory: 
        List<UserTerritory2Association> userAssignments = [SELECT ID, UserId,RoleInTerritory2 FROM UserTerritory2Association WHERE Territory2Id = :testTerritory.Id]; 
        //Map user role to list of users: 
        Map<String, List<Id>> roleToUsers = new Map<String, List<Id>>(); 
        for(UserTerritory2Association user :userAssignments){
            if(roleToUsers.containsKey(user.RoleInTerritory2)){
                roleToUsers.get(user.RoleInTerritory2).add(user.userId); 
            } 
            else{
                roleToUsers.put(user.RoleInTerritory2, new List<Id>{user.userId}); 
            }
        }
        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = adminAccount.Id, Territory2Id = testTerritory.Id, AssociationCause = 'Territory2Manual');

        //List<Opportunity> oppsToInsertFirst = new List<Opportunity>(); 
        Opportunity firstBatchOpp = new Opportunity(Name = 'Test Oppty PA1', AccountId = adminAccount.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', Assistant_Director__c =roleToUsers.get('Assistant Director')[0], Recruitment_Officer__c = roleToUsers.get('Recruitment Officer')[0]);

        Opportunity secondBatchOpp = new Opportunity(Name = 'Test Oppty PA2', AccountId = adminAccount.Id, CloseDate = Date.newInstance(2021, 1, 1), StageName = 'Inquiry', Program_Account__c = programAccount.Id, RecordtypeId = OpportunityService.DegreeProgramProspectRTId); 
        
        Test.startTest(); 
            insert acctAssignment; 
            insert firstBatchOpp; 
            insert secondBatchOpp; 
        Test.stopTest(); 

        Set<Id> oppIds = new Set<Id>{firstBatchOpp.Id, secondBatchOpp.Id}; 

        Opportunity oppsAfter = [SELECT ID, Territory2Id, Assistant_Director__c, Recruitment_Officer__c, Level_Of_Effort__c FROM Opportunity WHERE Id = :secondBatchOpp.Id]; 
        //Assert that ADs and ROs were distributed properly:
        System.assert(oppsAfter.Assistant_Director__c != null, 'Assistant Director was not updated'); 
        System.assert(oppsAfter.Assistant_Director__c != firstBatchOpp.Assistant_Director__c, 'Assistant Directors are the same'); 
        System.assert(oppsAfter.Recruitment_Officer__c != null, 'Recruitment_Officer__c was not updated'); 
        System.assert(oppsAfter.Recruitment_Officer__c != firstBatchOpp.Recruitment_Officer__c, 'Recruitment_Officer__c are the same'); 
    }

    /**
     * @description Assert that when an opportunity is updated to a program account 
     * that does not have an associated program code, all territory, AD, and RO fields are 
     * nulled out
     * Assert that territory lookup field on opportunity is not populated
     * Assert that Admissions Director lookup field on opportunity is not populated
     * Assert that Recruitment Officer lookup field on opportunity is not populated
     */
    @isTest
    public static void testUpdateOpp_WithPA_Null(){ 
        //Query for program account: 
        Account programAccount = [SELECT ID, Program_Code__c FROM Account WHERE RecordtypeId = :AccountService.AcademicProgramRTId  AND Program_Code__c != null LIMIT 1];

        //Query for an opportunity with program account: 
        Opportunity opp = [SELECT ID, AccountId, Territory2Id, Assistant_Director__c, Recruitment_Officer__c, Program_Account__c FROM Opportunity 
                            WHERE Program_Account__c != null 
                            AND Assistant_Director__c != null 
                            AND Recruitment_Officer__c != null 
                            AND Territory2Id != null
                            LIMIT 1];

        //Query for test territory whose program code does not match with the updated Program Account : 
        Territory2 testTerritory = [SELECT Id, Program_Code__c FROM Territory2 WHERE Program_Code__c != null AND Program_Code__c != :programAccount.Program_Code__c LIMIT 1]; 

        //Create Territory Assignment on Admin Account: 
        ObjectTerritory2Association acctAssignment = new ObjectTerritory2Association(ObjectId = opp.AccountId, Territory2Id = testTerritory.Id, AssociationCause = 'Territory2Manual');

        Test.startTest(); 
            insert acctAssignment; 
            //Remove program account code: 
            programAccount.program_Code__c = null; 
            update programAccount; 

            opp.IsExcludedFromTerritory2Filter = false;
            opp.Program_Account__c = programAccount.Id; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT Id, Assistant_Director__c, Recruitment_Officer__c, Territory2Id FROM Opportunity WHERE Id = :opp.Id]; 

        //Assert that territory, AD, and RO fields have reset: 
        System.assertEquals(null, oppAfter.Territory2Id); 
        System.assertEquals(null, oppAfter.Assistant_Director__c); 
        System.assertEquals(null, oppAfter.Recruitment_Officer__c); 
    }
}