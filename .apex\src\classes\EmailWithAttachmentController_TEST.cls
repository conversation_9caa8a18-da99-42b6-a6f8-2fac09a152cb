@isTest
private class EmailWithAttachmentController_TEST {
	@TestSetup
	private static void testDataSetup() {
		Account testAccount = new Account(Name = 'Test Account');
		insert testAccount;

		Contact testContact = new Contact(FirstName = 'John', LastName = 'Doe', Email = '<EMAIL>', AccountId = testAccount.Id);
		insert testContact;

		/*		insert new User(
						alias = 'test2',
						communityNickname = 'test123',
						contactId = testContact.Id,
						email = '<EMAIL>',
						emailencodingkey = 'UTF-8',
						firstName = 'testCommunity2',
						lastName = 'User',
						userName = '<EMAIL>',
						profileId = [SELECT ID FROM PROFILE WHERE NAME = 'Applicant Community User' LIMIT 1].Id,
						timeZoneSidKey = 'America/Los_Angeles',
						LocaleSidKey = 'en_US',
						LanguageLocaleKey = 'en_US'
				);*/
		String cookieName = String.valueOf(dateTime.now());

		pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
		cart.pymt__Cart_UID__c = cookieName;
		insert cart;

		evt__Special_Event__c event = new evt__Special_Event__c();
		event.Name = 'Special event';
		event.Price__c = '$43.00 + HST - IN-PERSON: includes a copy of the book, meet-and-greet with book signing by the author';
		event.Start_Local__c = Date.today().addDays(30);
		event.End_Local__c = Date.today().addDays(31);
		event.evt__Event_Time_Zone__c = '(GMT-04:00) Eastern Daylight Time (America/Toronto)';
		event.evt__Registration_Deadline__c = Date.today().addDays(29);
		event.evt__By_Invitation__c = false;
		event.Venue_Type__c = 'In-Person';
		event.evt__Publish_To__c = 'Public Events';
		event.evt__Event_Type__c = 'Session Event';
		event.evt__Status__c = 'Published';
		event.Tags__c = 'Strategic Communications';
		event.evt__Web_Meeting_Join_URL__c = 'https://zoom.us/j/123456789';

		List<evt__Special_Event__c> listOfEventToInsert = new List<evt__Special_Event__c>();
		listOfEventToInsert.add(event);

		insert listOfEventToInsert;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = listOfEventToInsert[0].Id;
		fee.evt__Amount__c = 0.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = testContact.Id;
		item.Event_Fee__c = fee.Id;
		item.pymt__Unit_Price__c = 0.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';

		insert item;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = testContact.Id;
		at.evt__Invitation_Status__c = 'Registered';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.Shopping_Cart_Item__c = item.Id;
		at.evt__Event__c = listOfEventToInsert[0].Id;
		at.evt__Reg_Email__c = '<EMAIL>';

		insert at;

		shopping_cart_item_details__c testCartItemDetail = new shopping_cart_item_details__c();
		testCartItemDetail.SC_event__c = listOfEventToInsert[0].Id;
		testCartItemDetail.Event_Fee__c = fee.Id;
		testCartItemDetail.Contact__c = testContact.Id;
		testCartItemDetail.Shopping_Cart_Item__c = item.Id;
		testCartItemDetail.Attendee__c = at.Id;
		testCartItemDetail.Item_Unit_Price__c = 5.0;
		testCartItemDetail.Item_Quantity__c = 1.0;
		testCartItemDetail.Item_Discount_Amount__c = 0.0;
		testCartItemDetail.Item_Gross_Amount__c = 5.0;
		testCartItemDetail.Item_Tax_Amount__c = 0.0;
		testCartItemDetail.Item_Total_Amount__c = 5.0;

		insert testCartItemDetail;

		shopping_cart_item_details__c testCartItemDetail1 = new shopping_cart_item_details__c();
		testCartItemDetail1.SC_event__c = listOfEventToInsert[0].Id;
		testCartItemDetail1.Event_Fee__c = fee.Id;
		testCartItemDetail1.Contact__c = testContact.Id;
		testCartItemDetail1.Shopping_Cart_Item__c = item.Id;
		testCartItemDetail1.Attendee__c = at.Id;
		testCartItemDetail1.Item_Unit_Price__c = 5.0;
		testCartItemDetail1.Item_Quantity__c = 1.0;
		testCartItemDetail1.Item_Discount_Amount__c = 0.0;
		testCartItemDetail1.Item_Gross_Amount__c = 5.0;
		testCartItemDetail1.Item_Tax_Amount__c = 0.0;
		testCartItemDetail1.Item_Total_Amount__c = 5.0;
		testCartItemDetail1.Void_ticket__c = true;

		insert testCartItemDetail1;

		insert new pymt__PaymentX__c (
				pymt__Contact__c    = testContact.Id,
				Name                = 'TEST Payment',
				pymt__Status__c     = 'Online Checkout',
				pymt__Billing_First_Name__c = 'Test',
				pymt__Billing_Last_Name__c = 'Student',
				pymt__Billing_Street__c = 'Test St',
				pymt__Billing_City__c = 'Testing',
				pymt__Billing_State__c = 'Tester',
				pymt__Billing_Country__c = 'Canada',
				pymt__Billing_Postal_Code__c = '12345',
				pymt__Amount__c = 500
		);

	}

	@isTest
	static void testSendEmailWithAttachment() {
		pymt__PaymentX__c payment = [SELECT Id FROM pymt__PaymentX__c LIMIT 1];
		pymt__Shopping_Cart_Item__c shoppingCartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		shoppingCartItem.pymt__Payment__c = payment.Id;
		shoppingCartItem.pymt__Payment_Completed__c = true;
		update shoppingCartItem;

		Contact contact = [SELECT Id FROM Contact LIMIT 1];

		Test.startTest();
		EmailWithAttachmentController.sendEmailWithAttachment(shoppingCartItem.Id, contact.Id);
		Test.stopTest();
	}

	@isTest
	static void testSendCancelledEmailWithAttachment() {
		pymt__PaymentX__c payment = [SELECT Id FROM pymt__PaymentX__c LIMIT 1];
		pymt__Shopping_Cart_Item__c shoppingCartItem = [SELECT Id FROM pymt__Shopping_Cart_Item__c LIMIT 1];
		shoppingCartItem.pymt__Payment__c = payment.Id;
		shoppingCartItem.pymt__Payment_Completed__c = true;
		update shoppingCartItem;

		Contact contact = [SELECT Id FROM Contact LIMIT 1];
		shopping_cart_item_details__c testItemDetail = [SELECT Id FROM shopping_cart_item_details__c Where Void_ticket__c = true LIMIT 1];

		String[] testAttIds = new String[]{testItemDetail.Id};

		Test.startTest();
		EmailWithAttachmentController.sendCancelledEmailWithAttachment(shoppingCartItem.Id, contact.Id, testAttIds);
		Test.stopTest();
	}

	@isTest
	static void testIsValidURL() {
		Test.startTest();
		Boolean result1 = EmailWithAttachmentController.isValidURL('');
		Boolean result2 = EmailWithAttachmentController.isValidURL('https://www.example.com');
		Test.stopTest();
		System.assertEquals(false, result1);
		System.assertEquals(true, result2);
	}
}