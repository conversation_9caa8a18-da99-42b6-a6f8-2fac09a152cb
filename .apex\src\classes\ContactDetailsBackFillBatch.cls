/**
 * @description Application to update Program and Term details from latest application by Contact.
 * <AUTHOR>
 * @version 1.0
 * @created 2021-10-29
 */
public with sharing class ContactDetailsBackFillBatch implements Database.Batchable<sObject> {
    /**
     * @description Start Method to query required Data
     * @return  Database.QueryLocater
     * @param Standard Batchable Context 
     */
    Set<Id> successRecord = new Set<Id>();
    Set<Id> failRecord = new Set<Id>();

    public Database.QueryLocator start(Database.BatchableContext BC) {
        String query = 'SELECT Id, Program_Recently_Applied_To__c, Term_Recently_Applied_To__c, App_Status_Recently_Applied_To__c FROM Contact';
        return Database.getQueryLocator(query);
    }

    /**
     * @description Execute Method to perform operation
     * @return void 
     * @param Standard Batchable Context and List of Record to process
     */
    public void execute(Database.BatchableContext context,List<Contact> listOfContactsrRecords) {
        Map<Id, List<hed__Application__c>> mapOfIdToListOfApplication = new Map<Id, List<hed__Application__c>>();
        for(Contact con : listOfContactsrRecords) {
            mapOfIdToListOfApplication.put(con.Id, new List<hed__Application__c>());
        }
        List<hed__Application__c> listOfApplications = [SELECT Id, Term_Name__c, hed__Applying_To__r.Name, hed__Application_Date__c, LastModifiedDate,
                                                        hed__Applicant__c, hed__Application_Status__c
                                                        FROM hed__Application__c
                                                        WHERE hed__Applicant__c IN: mapOfIdToListOfApplication.keySet()
                                                        AND RecordType.DeveloperName != 'Executive_Programs'];
        for(hed__Application__c app : listOfApplications) {
            if(mapOfIdToListOfApplication.containsKey(app.hed__Applicant__c)){
                mapOfIdToListOfApplication.get(app.hed__Applicant__c).add(app);
            }
        }
        Map<Id, hed__Application__c> mapOfContactIdToApplication = ContactBackFillDetailUtility.getTheMostRecentRecord(mapOfIdToListOfApplication);
        for(Contact con : listOfContactsrRecords) {
            if(mapOfContactIdToApplication.get(con.Id) != null) {
                con.Program_Recently_Applied_To__c = mapOfContactIdToApplication.get(con.Id).hed__Applying_To__r.Name;
                con.Term_Recently_Applied_To__c = mapOfContactIdToApplication.get(con.Id).Term_Name__c;
                con.App_Status_Recently_Applied_To__c = mapOfContactIdToApplication.get(con.Id).hed__Application_Status__c;
            }
        }

        if(!listOfContactsrRecords.isEmpty()) {
            Database.SaveResult[] srList = Database.update(listOfContactsrRecords, false);

            for (Database.SaveResult sr : srList) {
                if (sr.isSuccess()) {
                    // Operation was successful, so get the ID of the record that was processed
                    successRecord.add(sr.id);
                } else {
                    for(Database.Error err : sr.getErrors()) {
                        failRecord.add(sr.id);
                    }   
                }
            }
        }
    }

    /**
     * @description Finish Method to perform operation after batch run
     * @return  
     * @param Standard Batchable Context 
     */
    public void finish(Database.BatchableContext BC) {
        // Get the ID of the AsyncApexJob representing this batch job
        // from Database.BatchableContext.
        // Query the AsyncApexJob object to retrieve the current job's information.
        AsyncApexJob a = [SELECT Id, Status, NumberOfErrors, JobItemsProcessed,
                          TotalJobItems, CreatedBy.Email FROM AsyncApexJob WHERE Id = :BC.getJobId()];
        // Send an email to the Apex job's submitter notifying of job completion.
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        String[] toAddresses = new String[] {a.CreatedBy.Email};
        mail.setToAddresses(toAddresses);
        mail.setSubject('Account and contact update' + a.Status);
        mail.setPlainTextBody('The batch Apex job processed ' + a.TotalJobItems + ' batches with '+ a.NumberOfErrors + ' failures. SuccessRecordids: '+ successRecord + ' , FailRecordids: '+ failRecord);
    }
}