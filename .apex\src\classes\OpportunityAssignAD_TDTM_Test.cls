/**
* @description    Test class for OpportunityAssignAD_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-04-07
* @modified 2020-04-07
*/
@isTest
public class OpportunityAssignAD_TDTM_Test {
    /**
     * Class Variables 
     */
    private static final String programCode = '999999'; 

    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
         //retrieve default EDA trigger handler
         List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
         //create trigger handler for OpportunityAssignAD_TDTM class
         tokens.add(new hed.TDTM_Global_API.TdtmToken('OpportunityAssignAD_TDTM', 'Opportunity', 'BeforeInsert;BeforeUpdate', 2.00)); 
         //pass trigger handler config to set method
         hed.TDTM_Global_API.setTdtmConfig(tokens);
        //Create Program and Administrative Accounts 
        Account paTest = new Account(Name = 'TestPA', Program_Code__c = programCode, RecordTypeId = AccountService.AcademicProgramRTId); 
        Account adminAcct = new Account(Name = 'Admin1 Account', RecordTypeId = AccountService.AdminRTId); 
        insert new List<Account>{paTest, adminAcct}; 

        //Add test users, territory, and user territory assignments: 
        addSetUpObjects(); 

        Opportunity opp = new Opportunity(Name = 'Test Oppty', 
                                            AccountId = adminAcct.Id, 
                                            CloseDate = Date.newInstance(2021, 1, 1), 
                                            StageName = 'Lead', 
                                            RecordTypeId = OpportunityService.DegreeProgramProspectRTId); 
        Opportunity opp_AssignAD = new Opportunity(Name = 'Test Oppty Assign AD', 
                                            AccountId = adminAcct.Id, 
                                            CloseDate = Date.newInstance(2021, 1, 1), 
                                            StageName = 'Inquiry', 
                                            RecordTypeId = OpportunityService.DegreeProgramProspectRTId); 
        insert new List<Opportunity>{opp, opp_AssignAD}; 
    }

    /**
     * @description future method to add new users, 
     * territory, and user assignments in the setup method 
     */
    @future
    private static void addSetUpObjects(){
        //Query for Recruitment and Admissions Profile: 
        Profile profileRA = [SELECT ID FROM PROFILE WHERE NAME = 'System Administrator' LIMIT 1]; 
        //Create Test User Profiles: 
        List<user> usersToInsert = new List<User>(); 
        for(Integer i= 0; i < 4; i++){
            User testUser = new User(
                                    alias = 'test'+i, 
                                    email = 'test'+i+'@testuser.com', 
                                    emailencodingkey = 'UTF-8', 
                                    firstName = 'test',
                                    lastName = 'User'+i, 
                                    userName = 'test'+i+'@testuser.com.ad', 
                                    profileId = profileRA.Id, 
                                    timeZoneSidKey = 'America/Los_Angeles', 
                                    LocaleSidKey = 'en_US', 
                                    LanguageLocaleKey = 'en_US'); 
            usersToInsert.add(testUser); 
        }
        insert usersToInsert; 

        //Query for Specialized Program Territory Type: 
        Territory2Type spType = [SELECT Id FROM Territory2Type WHERE DeveloperName = 'Specialized_Programs' LIMIT 1]; 
        
        //Query for Active Territory Model:
        Territory2Model territoryModel = [SELECT ID, DeveloperName FROM Territory2Model WHERE State = 'Active']; 

        //Create new Territory record: 
        Territory2 testTerritory = new Territory2(DeveloperName = 'Test_Program_Territory', Name = 'Test Program Territory', Program_Code__c  = programCode, Territory2ModelId = territoryModel.id, Territory2TypeId = spType.Id ); 
        insert testTerritory; 
        //Create User Assignments
        List<UserTerritory2Association> userAssignments = new List<UserTerritory2Association>(); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[0].Id, RoleInTerritory2='Assistant Director')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[1].Id, RoleInTerritory2='Assistant Director')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[2].Id, RoleInTerritory2='Recruitment Officer')); 
        userAssignments.add(new UserTerritory2Association(Territory2Id = testTerritory.Id, UserId = usersToInsert[3].Id, RoleInTerritory2='Recruitment Officer')); 
        insert userAssignments; 
    }

    /**
     * @description insert an opportunity record where Assign_Assistant_Director_Manual__c is checked 
     * Assert that Assistant Director was assigned 
     * 
     */
    @isTest
    public static void testADManual(){
        //Query for admin account: 
        Account adminAcct = [SELECT ID FROM Account WHERE RecordTypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for territory: 
        Territory2 testTerritory = [SELECT ID FROM Territory2 WHERE Program_Code__c = :programCode LIMIT 1]; 

        Set<Id> validADIds = new Set<Id>(); 
        for(UserTerritory2Association uta : [SELECT ID, UserId FROM UserTerritory2Association WHERE Territory2Id = :testTerritory.Id AND RoleInTerritory2 = 'Assistant Director']){
            validADIds.add(uta.UserId); 
        }


        Opportunity opp = new Opportunity(Name = 'Test Oppty No PA', 
                                            AccountId = adminAcct.Id, 
                                            CloseDate = Date.newInstance(2021, 1, 1), 
                                            StageName = 'Lead', 
                                            RecordTypeId = OpportunityService.DegreeProgramProspectRTId, 
                                            Territory2Id = testTerritory.Id,
                                            Assign_Assistant_Director_Manual__c = true ); 
        
        Test.startTest(); 
            insert opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 

        System.Assert(oppAfter.Assistant_Director__c != null, 'Assistant Director did not get assigned: ' + oppAfter); 
        System.Assert(validADIds.contains(oppAfter.Assistant_Director__c), 'Assistant Director was not assigned correctly: ' + oppAfter);
    }

    /**
     * @description insert an opportunity record where Assign_Assistant_Director__c evaluates to true 
     * but isExcludedFromTerritory2Filter is checked
     * Assert that Assistant Director was not assigned 
     * 
     */
    @isTest
    public static void testOppInquiry_NoAD(){
        //Query for admin account: 
        Account adminAcct = [SELECT ID FROM Account WHERE RecordTypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for territory: 
        Territory2 testTerritory = [SELECT ID FROM Territory2 WHERE Program_Code__c = :programCode LIMIT 1]; 

        Opportunity opp = new Opportunity(Name = 'Test Oppty No PA', 
                                            AccountId = adminAcct.Id, 
                                            CloseDate = Date.newInstance(2021, 1, 1), 
                                            StageName = 'Inquiry', 
                                            RecordTypeId = OpportunityService.DegreeProgramProspectRTId, 
                                            Territory2Id = testTerritory.Id,
                                            IsExcludedFromTerritory2Filter = true ); 
        
        Test.startTest(); 
            insert opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 

        System.Assert(oppAfter.Assistant_Director__c == null, 'Assistant Director got assigned: ' + oppAfter); 
    }

    /**
     * @description insert an opportunity record where territory is assigned but Assign_Assistant_Director__c is false
     * Assert that Assistant Director was not assigned 
     * 
     */
    @isTest
    public static void testOpp_NoAD(){
        //Query for admin account: 
        Account adminAcct = [SELECT ID FROM Account WHERE RecordTypeId = :AccountService.AdminRTId LIMIT 1]; 
        //Query for territory: 
        Territory2 testTerritory = [SELECT ID FROM Territory2 WHERE Program_Code__c = :programCode LIMIT 1]; 

        Opportunity opp = new Opportunity(Name = 'Test Oppty No PA', 
                                            AccountId = adminAcct.Id, 
                                            CloseDate = Date.newInstance(2021, 1, 1), 
                                            StageName = 'Lead', 
                                            RecordTypeId = OpportunityService.DegreeProgramProspectRTId, 
                                            Territory2Id = testTerritory.Id); 
        
        Test.startTest(); 
            insert opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 

        System.Assert(oppAfter.Assistant_Director__c == null, 'Assistant Director got assigned: ' + oppAfter); 

    }

    /**
     * @description Assign opportunity to a territory (where Assign_Assistant_Director__c is not true)
     * Assert that Assistant Director was not assigned 
     * 
     */
    @isTest 
    public static void testUpdateTerritory_NoAD(){
        //Query for Opportunity
        Opportunity opp = [SELECT ID FROM Opportunity WHERE Assign_Assistant_Director__c != true AND Territory2Id = null LIMIT 1]; 
        //Query for territory: 
        Territory2 testTerritory = [SELECT ID FROM Territory2 WHERE Program_Code__c = :programCode LIMIT 1]; 

        //Assign test territory: 
        Test.startTest(); 
            opp.territory2Id = testTerritory.Id; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfter = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 

        System.Assert(oppAfter.Assistant_Director__c == null, 'Assistant Director got assigned: ' + oppAfter); 
    }

    /**
     * @description Update opportunity's Assign_Assistant_Director_Manual__c to true 
     * Assert that Assistant Director is assigned 
     */
    @isTest  
    public static void testUpdateAD_Manual(){
         //Query for Opportunity
         Opportunity opp = [SELECT ID FROM Opportunity WHERE Assign_Assistant_Director__c != true AND Territory2Id = null LIMIT 1]; 
         //Query for territory: 
         Territory2 testTerritory = [SELECT ID FROM Territory2 WHERE Program_Code__c = :programCode LIMIT 1]; 

        Set<Id> validADIds = new Set<Id>(); 
        for(UserTerritory2Association uta : [SELECT ID, UserId FROM UserTerritory2Association WHERE Territory2Id = :testTerritory.Id AND RoleInTerritory2 = 'Assistant Director']){
            validADIds.add(uta.UserId); 
        }
 
         //Assign test territory: 
        opp.territory2Id = testTerritory.Id; 
        update opp; 
        Opportunity oppAfter = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 
 
         System.Assert(oppAfter.Assistant_Director__c == null, 'Assistant Director got assigned: ' + oppAfter); 

         //Update Assign_Assistant_Director_Manual__c: 
         Test.startTest(); 
            opp.Assign_Assistant_Director_Manual__c = true; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfterAssign = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 
        System.Assert(oppAfterAssign.Assistant_Director__c != null, 'Assistant Director did not get assigned: ' + oppAfterAssign); 
        System.Assert(validADIds.contains(oppAfterAssign.Assistant_Director__c), 'Assistant Director was not assigned correctly: ' + oppAfterAssign);
    }

     /**
     * @description Update opportunity so that Assign_Assistant_Director__c is true 
     * Assert that Assistant Director is assigned 
     */
    @isTest  
    public static void testUpdateOppStage(){
        //Query for Opportunity
        Opportunity opp = [SELECT ID FROM Opportunity WHERE Assign_Assistant_Director__c != true AND Territory2Id = null LIMIT 1]; 
        //Query for territory: 
        Territory2 testTerritory = [SELECT ID FROM Territory2 WHERE Program_Code__c = :programCode LIMIT 1]; 

        Set<Id> validADIds = new Set<Id>(); 
        for(UserTerritory2Association uta : [SELECT ID, UserId FROM UserTerritory2Association WHERE Territory2Id = :testTerritory.Id AND RoleInTerritory2 = 'Assistant Director']){
            validADIds.add(uta.UserId); 
        }
 
         //Assign test territory: 
        opp.territory2Id = testTerritory.Id; 
        update opp; 
        Opportunity oppAfter = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 
 
         System.Assert(oppAfter.Assistant_Director__c == null, 'Assistant Director got assigned: ' + oppAfter); 

         //Update Assign_Assistant_Director_Manual__c: 
         Test.startTest(); 
            opp.StageName = 'Inquiry'; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfterAssign = [SELECT ID, Assistant_Director__c, Assign_Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 
        System.Assert(oppAfterAssign.Assistant_Director__c != null, 'Assistant Director did not get assigned: ' + oppAfterAssign); 
        System.Assert(validADIds.contains(oppAfterAssign.Assistant_Director__c), 'Assistant Director was not assigned correctly: ' + oppAfterAssign);
    }

    /**
     * @description Assign opportunity to a territory (where Assign_Assistant_Director__c true)
     * Assert that Assistant Director is assigned 
     * 
     */
    @isTest
    public static void testUpdateOppTerritory_AD(){
        //Query for Opportunity
        Opportunity opp = [SELECT ID FROM Opportunity WHERE Assign_Assistant_Director__c = true AND Territory2Id = null LIMIT 1]; 
        //Query for territory: 
        Territory2 testTerritory = [SELECT ID FROM Territory2 WHERE Program_Code__c = :programCode LIMIT 1]; 
 
        Set<Id> validADIds = new Set<Id>(); 
        for(UserTerritory2Association uta : [SELECT ID, UserId FROM UserTerritory2Association WHERE Territory2Id = :testTerritory.Id AND RoleInTerritory2 = 'Assistant Director']){
            validADIds.add(uta.UserId); 
        }
  
        //Assign test territory: 
        Test.startTest(); 
            opp.territory2Id = testTerritory.Id; 
            update opp; 
        Test.stopTest(); 

        Opportunity oppAfterAssign = [SELECT ID, Assistant_Director__c FROM Opportunity WHERE Id = :opp.Id]; 
        System.Assert(oppAfterAssign.Assistant_Director__c != null, 'Assistant Director did not get assigned: ' + oppAfterAssign); 
        System.Assert(validADIds.contains(oppAfterAssign.Assistant_Director__c), 'Assistant Director was not assigned correctly: ' + oppAfterAssign);
    }
}