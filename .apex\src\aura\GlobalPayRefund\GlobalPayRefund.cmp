<aura:component controller="RefundController" implements="force:lightningQuickActionWithoutHeader,force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes" access="global">
    
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:attribute name="messageToDisplay" type="String" default="" />
    <aura:attribute name="maxRefund" type="Decimal" default="0.00" />
    <aura:attribute name="disableBtn" type="Boolean" default="true" />
    <aura:attribute name="showSpinner" type="Boolean" default="false" />
    <aura:attribute name="showRefundCMP" type="Boolean" default="false" />
	<aura:attribute name="paymentObj" type="object"/>
    
    <aura:attribute name="RefundAmount" type="Decimal" default="0.00" />
    <aura:attribute name="adminFee" type="Decimal" default="0.00" />
    <aura:attribute name="RefundReason" type="String" default="" />
    
    <aura:html tag="style">
        .cuf-content { 
        padding: 0 0rem !important;
        }
        .slds-p-around--medium {
        padding: 0rem !important;
        }       
        .slds-modal__content{
        overflow-y:hidden !important;
        height:unset !important;
        max-height:unset !important;
        }
    </aura:html>
    
    <aura:if isTrue="{!v.showSpinner}">
        <lightning:spinner variant="brand" size="large" alternativeText="Saving" />
    </aura:if>
    
    <div class="modal-header slds-modal__header slds-size_1-of-1">
        <h4 class="title slds-text-heading--medium" >Refund Payment</h4>
    </div>
    
    <div class="slds-modal__content slds-p-around_large slds-size_1-of-1 slds-is-relative" aura:id="modalbody" id="modalbody">
        <div class="slds-p-around_large">
			<p>Cart Amount: {!v.paymentObj.pymt__Amount__c}</p>
            <p>Amount Paid: {!v.paymentObj.Amount_Paid__c}</p>
            <p>Amount Refunded: {!v.paymentObj.Amount_Refunded__c}</p>
            <p>Max Refund allowed: {!v.maxRefund}</p>
        </div>
        <p style="font-size: 16px;color:red;" class="slds-p-around_medium">{!v.messageToDisplay}</p>
        <aura:if isTrue="{!v.showRefundCMP}">
            <lightning:input aura:id="fieldId" type="number" required="true" label="Refund Amount"
                             Name="Refund Amount" formatter="currency" step="0.01"
                             placeholder="0.00" value="{!v.RefundAmount}" min="0.01"
                             messageWhenStepMismatch="Max 2 decimal places is allowed."
                             messageWhenRangeUnderflow="Amount should be greater than 0."/>
            
            <lightning:input aura:id="fieldId" type="number" required="true" label="Administration Fee"
                             Name="Administration Fee" formatter="currency" step="0.01"
                             placeholder="0.00" value="{!v.adminFee}" 
                             messageWhenStepMismatch="Max 2 decimal places is allowed."/> 
            
            <lightning:input aura:id="fieldId" Name="RefundReason" required="true"
                             label="Refund Reason" placeholder="Reason for refund" value="{!v.RefundReason}" />
        </aura:if>        
    </div>
    
    <div class="modal-footer slds-modal__footer slds-size_1-of-1">
        <div class="forceChangeRecordTypeFooter">
            <lightning:button variant="neutral" label="Cancel" onclick="{!c.closePopup}" />
            <lightning:button variant="brand" label="Refund" onclick="{!c.handleRefund}" disabled="{!v.disableBtn}" />
        </div>
    </div>
    
</aura:component>