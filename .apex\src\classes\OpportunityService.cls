/**
* @description   Service class for Opportunity Object 
* <AUTHOR>
* @version        1.0 
* @created 2020-03-12
* @modified 2020-04-09
*/ 
public inherited sharing class OpportunityService {
    /**
     * Class Variables 
     */
    private static final Map<String, Schema.RecordTypeInfo> OpportunityTypeMap = Opportunity.SObjectType.getDescribe().getRecordTypeInfosByDeveloperName();
    //Degree Program Prospect Record Type Id: 
    public static final Id DegreeProgramProspectRTId = OpportunityTypeMap.get('Degree_Program_Prospect').RecordTypeId;
    //Executive Program Prospect Record Type Id: 
    public static final Id ExecutiveProgramProspectRTId = OpportunityTypeMap.get('Executive_Program_Prospect').RecordTypeId; 
    //Executive Program Offering Record Type Id: 
    public static final Id ProgramOfferingRTId = OpportunityTypeMap.get('Executive_Program_Offering').RecordTypeId; 

    /**
     * @description query specific fields of opportunity records 
     * @return List<Opportunity> list of Opportunity records
     * @param oppIds set of opportunity IDs
     * @param fieldsToQuery set of api names of fields to query 
     */
    public static List<Opportunity> queryFieldsInOpps(Set<Id> oppIds, Set<String> fieldsToQuery){
        return Database.query('SELECT ' + String.join(new List<String>(fieldsToQuery), ',') + ' FROM Opportunity WHERE ID IN :oppIds ' ); 
    }
}