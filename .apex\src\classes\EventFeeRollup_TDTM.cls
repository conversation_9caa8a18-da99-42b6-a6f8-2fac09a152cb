/**
 * @description Stamps Student Price and Min Active Price fields on the parent event object 
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-06
 * @modified 2020-08-06
 */
global class EventFeeRollup_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for the object 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();
        Set<Id> eventIds = new Set<Id>(); 

        //AFTER INSERT CONTEXT: 
        if(triggerAction ==  hed.TDTM_Runnable.Action.AfterInsert){
            for(evt__Event_Fee__c fee :(List<evt__Event_Fee__c>)newList){
                if(fee.evt__Active__c){
                    eventIds.add(fee.evt__Event__c); 
                }
            }
            if(eventIds.size() > 0){
                //Event list: 
                List<evt__Special_Event__c> events= getEvents(eventIds); 
                //Map event Id to Map of fee type to min price: 
                Map<Id, Map<String, Decimal>> evtIdToMinPriceByTypeMap = getMinPriceMap(events);
                //Set of events to Update: 
                Set<evt__Special_Event__c> eventsToUpdate = calculatePrices(events, evtIdToMinPriceByTypeMap); 
                

                if(eventsToUpdate.size() > 0) dmlWrapper.objectsToUpdate.addAll(new List<evt__Special_Event__c>(eventsToUpdate)); 
            } //AFTER UPDATE CONTEXT: 
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            Map<Id, evt__Event_Fee__c> oldMap = new Map<Id, evt__Event_Fee__c>((List<evt__Event_Fee__c>)oldList); 
            for(evt__Event_Fee__c fee :(List<evt__Event_Fee__c>)newList){
                /* Only update fields on parent event record if one of the following fields are updated: evt__Active__c, Start_Date__c, 
                   End_Date__c, Type__c, Current_Usage__c, evt__Limit__c, evt__Amount__c*/
                evt__Event_Fee__c oldFee = oldMap.get(fee.Id); 
                if(fee.evt__Active__c != oldFee.evt__Active__c || fee.Start_Date__c != oldFee.Start_Date__c || fee.End_Date__c != oldFee.End_Date__c 
                    || fee.Type__c != oldFee.Type__c || fee.evt__Amount__c != oldFee.evt__Amount__c 
                    || (fee.At_Limit__c && fee.Current_Usage__c != oldFee.Current_Usage__c) || fee.evt__Limit__c != oldFee.evt__Limit__c){
                    
                     eventIds.add(fee.evt__Event__c); 
                }
            }
            if (eventIds.size() > 0){
                //Retrieve the list of events and event fees: 
                List<evt__Special_Event__c> events= getEvents(eventIds);
                //Map event Id to Map of fee type to min price: 
                Map<Id, Map<String, Decimal>> evtIdToMinPriceByTypeMap = getMinPriceMap(events); 
                //Set of events to Update: 
                Set<evt__Special_Event__c> eventsToUpdate = calculatePrices(events, evtIdToMinPriceByTypeMap); 
                
                if(eventsToUpdate.size() > 0) dmlWrapper.objectsToUpdate.addAll(new List<evt__Special_Event__c>(eventsToUpdate));
            } //AFTER DELETE CONTEXT
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterDelete){
            //re-calculate Student_Price__c and Minumum_Active_Price__c fields
            for(evt__Event_Fee__c fee : (List<evt__Event_Fee__c>)oldList){
                eventIds.add(fee.evt__Event__c); 
            }
            if(eventIds.size() > 0){
                //Retrieve the list of events and event fees: 
                List<evt__Special_Event__c> events= getEvents(eventIds);
                //Map event Id to Map of fee type to min price: 
                Map<Id, Map<String, Decimal>> evtIdToMinPriceByTypeMap = getMinPriceMap(events); 
                //Set of events to Update: 
                Set<evt__Special_Event__c> eventsToUpdate = calculatePrices(events, evtIdToMinPriceByTypeMap); 
                
                if(eventsToUpdate.size() > 0) dmlWrapper.objectsToUpdate.addAll(new List<evt__Special_Event__c>(eventsToUpdate));
            } //AFTER UNDELETE CONTEXT
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterUndelete){
            //re-calculate Student_Price__c and Minumum_Active_Price__c fields
            for(evt__Event_Fee__c fee : (List<evt__Event_Fee__c>)newList){
                eventIds.add(fee.evt__Event__c); 
            }
            if(eventIds.size() > 0){
                //Retrieve the list of events and event fees: 
                List<evt__Special_Event__c> events= getEvents(eventIds);
                //Map event Id to Map of fee type to min price: 
                Map<Id, Map<String, Decimal>> evtIdToMinPriceByTypeMap = getMinPriceMap(events); 
                //Set of events to Update: 
                Set<evt__Special_Event__c> eventsToUpdate = calculatePrices(events, evtIdToMinPriceByTypeMap); 
                
                if(eventsToUpdate.size() > 0) dmlWrapper.objectsToUpdate.addAll(new List<evt__Special_Event__c>(eventsToUpdate));
            } 
        }
        return dmlWrapper; 
    }

    /**
     * @description returns list of event records 
     * @param eventIds set of event Ids to query for
     * @return List<evt__Special_Event__c> list of event records
     */
    private List<evt__Special_Event__c> getEvents(Set<Id> eventIds){
        List<evt__Special_Event__c> events = [SELECT Id, Student_Price__c, Minimum_Active_Price__c,
                    (SELECT Id, Start_Date__c, End_Date__c, Type__c, Current_Usage__c, evt__Limit__c, evt__Amount__c 
                    FROM evt__Event_Fees__r 
                    WHERE evt__Active__c = true
                    AND (Start_Date__c = null OR Start_Date__c >= TODAY)
                    AND (End_Date__c = null OR End_Date__c <= TODAY)
                    AND evt__Amount__c != null 
                    AND at_Limit__c = false 
                    ORDER BY evt__Amount__c ASC)
                FROM evt__Special_Event__c
                WHERE Id IN :eventIds]; 
        return events; 
    }
    /**
     * @description Map event Id to map of event fee type (student or standard) to minimum price 
     * @param events list of events
     * @return Map<Id, Map<String, Decimal>>
     */
    private Map<Id, Map<String, Decimal>> getMinPriceMap(List<evt__Special_Event__c> events){
        Map<Id, Map<String, Decimal>> evtIdToMinPriceByTypeMap = new Map<Id, Map<String, Decimal>>(); 

        for(evt__Special_Event__c event :events){
            if(event.evt__Event_Fees__r.size() > 0){
                for(evt__Event_Fee__c fee :event.evt__Event_Fees__r){
                    if(!evtIdToMinPriceByTypeMap.containsKey(event.Id)){
                        if(fee.type__c == 'Student'){
                            evtIdToMinPriceByTypeMap.put(event.Id, new Map<String, Decimal>{'Student' => fee.evt__Amount__c}); 
                        }else{
                            evtIdToMinPriceByTypeMap.put(event.Id, new Map<String, Decimal>{'Standard' => fee.evt__Amount__c}); 
                        }
                    }else{
                        if(fee.type__c== 'Student'){
                            if(evtIdToMinPriceByTypeMap.get(event.Id).containsKey('Student')){
                                if(evtIdToMinPriceByTypeMap.get(event.Id).get('Student') > fee.evt__Amount__c) evtIdToMinPriceByTypeMap.get(event.Id).put('Student', fee.evt__Amount__c); 
                            }else{
                                evtIdToMinPriceByTypeMap.get(event.Id).put('Student', fee.evt__Amount__c); 
                            }
                        }else{
                            if(evtIdToMinPriceByTypeMap.get(event.Id).containsKey('Standard')){
                                if(evtIdToMinPriceByTypeMap.get(event.id).get('Standard') > fee.evt__Amount__c) evtIdToMinPriceByTypeMap.get(event.Id).put('Standard', fee.evt__Amount__c); 
                            }else{
                                evtIdToMinPriceByTypeMap.get(event.Id).put('Standard', fee.evt__Amount__c); 
                            }
                        }
                    }
                }
            }
        }
        return evtIdToMinPriceByTypeMap; 
    }
    /**
     * @description Calculates Student_Price__c and Minimum_Active_Price__c fields on Special Event Object
     * @param events list of events
     * @param evtIdToMinPriceByTypeMap map of event Id to map of event fee type (student or standard) to minimum price
     * @return Set<evt__Special_Event__c> set of events to update 
     */
    private Set<evt__Special_Event__c> calculatePrices(List<evt__Special_Event__c> events, Map<Id, Map<String, Decimal>> evtIdToMinPriceByTypeMap){
        Set<evt__Special_Event__c> eventsToUpdate = new Set<evt__Special_Event__c>(); 
        for(evt__Special_Event__c event :events){ 
            if(evtIdToMinPriceByTypeMap.containsKey(event.Id)){
                //Student Price: 
                if(evtIdToMinPriceByTypeMap.get(event.Id).containsKey('Student')){
                    //Update Student_Price__c if new minumum student price is not equal to the current field value 
                    if(event.Student_Price__c != evtIdToMinPriceByTypeMap.get(event.Id).get('Student')){
                        event.Student_Price__c = evtIdToMinPriceByTypeMap.get(event.Id).get('Student'); 
                        eventsToUpdate.add(event); 
                    }  
                }
                if(evtIdToMinPriceByTypeMap.get(event.Id).containsKey('Standard')){
                    //Update Minimum_Active_Price__c if new minumum price is not equal to the current field value
                    if(event.Minimum_Active_Price__c != evtIdToMinPriceByTypeMap.get(event.Id).get('Standard')){
                        event.Minimum_Active_Price__c = evtIdToMinPriceByTypeMap.get(event.Id).get('Standard'); 
                        eventsToUpdate.add(event); 
                    }
                }
            }else{
                //Reset Student Price and Minimum Price: 
                if(event.Student_Price__c != null){
                    event.Student_Price__c = null;
                    eventsToUpdate.add(event); 
                }
                if(event.Minimum_Active_Price__c != null){
                    event.Minimum_Active_Price__c = null; 
                    eventsToUpdate.add(event); 
                }
            }
        }
        return eventsToUpdate; 
    }
}