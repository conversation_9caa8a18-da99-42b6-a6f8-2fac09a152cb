public class ShoppingCartController {
    
    public MAP<string,cartWrapper> ListItems{get;set;}
    public MAP<string,cartWrapper> cart{get;set;}
    public boolean displayDialog{get;set;}
    public static string itemName{get;set;}
    public class cartWrapper{
        public string description{get;set;}
        public double price{get;set;}
        public string item{get;set;}
        public integer qty{get;set;}
        public double total{get;set;}
        public evt__Special_Event__c event{get;set;}
    }
    @AuraEnabled
    public static MAP<string,cartWrapper> ShoppingMap(){
         MAP<string,cartWrapper> ListItems = new  MAP<string,cartWrapper>{};
        //displayDialog = false;
        MAP<string,cartWrapper> cart = new MAP<string,cartWrapper>();
        
        for(integer i = 1;i<=4;i++){
            cartWrapper cartItem = new cartWrapper();
            cartItem.description = 'Course'+i+' description';
            cartItem.price = double.valueOf(i*99.00);
            cartItem.item = 'Course'+i;
            system.debug('cartItem== '+ cartItem);
            ListItems.put(cartItem.item, cartItem);
        }
        system.debug('ListItems='+ListItems);
        return ListItems;
    }
     @AuraEnabled
    public static MAP<id,evt__Special_Event__c> getEvents(){
        MAP<ID,evt__Special_Event__c> events = new Map<ID, evt__Special_Event__c>([SELECT Id, Name,evt__Short_Description__c, (SELECT  evt__Amount__c From  evt__Event_Fees__r) from evt__Special_Event__c]);
    return events;
    }
    
    
    public static PageReference getcartDetails(){
        
        MAP<string,cartWrapper> cart = new MAP<string,cartWrapper>();
        system.debug('itemName== '+ itemName);
        //system.debug('itemName== '+ itemName);
        cartWrapper cartItem = new cartWrapper();
        cartItem.description = 'c4 description';
        cartItem.price = double.valueOf('199');
        cartItem.item = 'c4';
        system.debug('cartItem== '+ cartItem);
        cart.put(cartItem.item, cartItem);
        system.debug('cart'+cart);
        return null;
    }
    public PageReference openDialog()
    {            		
        displayDialog = true;
        return null;      
    }
    @AuraEnabled
    public static Id createPayment(Id event,string amount){
        Id contactid = '0034c000001xfyeAAA';
        //evt__Special_Event__c spEvent = []//evt__Event__c
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        pymt.pymt__Contact__c = '0034c000001xfyeAAA';
        pymt.Name = 'Special Event Payment_'+event;
        pymt.evt__Event__c = event;
        pymt.pymt__Status__c = 'Scheduled';
        pymt.pymt__Amount__c = Double.valueOF(amount);
        insert pymt;
        return pymt.Id;
    }
}