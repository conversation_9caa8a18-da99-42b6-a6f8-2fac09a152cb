/**
* @description    Test class for EventFeeRollup_TDTM class
* <AUTHOR>
* @version        1.0 
* @created 2020-08-16
* @modified 2020-08-16
*/
@isTest  
public class EventFeeRollup_TDTM_Test {
    /**
     * @description setup method used to load initial data for the test
     */
    @TestSetup
    static void setup(){
        //retrieve default EDA trigger hanlder 
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //Create trigger handlers for test score objects for TestScoreCalculator_TDTM class 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('EventFeeRollup_TDTM', 'evt__Event_Fee__c', 'AfterInsert;AfterUpdate;AfterDelete;AfterUndelete', 1.00)); 
        //pass trigger handler config to set method 
        hed.TDTM_Global_API.setTdtmConfig(tokens); 

        //Insert event:
        evt__Special_Event__c event = new evt__Special_Event__c(Name='Rotman Business Career Fair', evt__Event_Type__c ='Conference'); 
        insert event; 

        //Insert event fees: 
        //Create event fees: 
        List<evt__Event_Fee__c> fees = new List<evt__Event_Fee__c>(); 
        fees.add(new evt__Event_Fee__c(Name='Student Fee', evt__Event__c=event.Id, evt__Amount__c = 200.00, evt__Active__c= true, type__c = 'Student')); 
        fees.add(new evt__Event_Fee__c(Name='Student Fee', evt__Event__c=event.Id, evt__Amount__c = 250.00, evt__Active__c= true, type__c = 'Student')); 
        fees.add(new evt__Event_Fee__c(Name='General Admissions Fee', evt__Event__c=event.Id, evt__Amount__c = 300.00, evt__Active__c= true, type__c = 'Standard')); 
        insert fees; 
    }

    /**
     * @description Insert a list of event fees 
     * Assert that Student Price was calculated correctly
     * Assert that Minimum Active Price was calculated correctly 
     */
    @isTest  
    static void testInsertEventFees(){
        evt__Special_Event__c event = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c LIMIT 1]; 

        List<evt__Event_Fee__c> feesToInsert = new List<evt__Event_Fee__c>(); 
        feesToInsert.add(new evt__Event_Fee__c(Name='Student Fee', evt__Event__c=event.Id, evt__Amount__c = 20.00, evt__Active__c= true, type__c = 'Student')); 
        feesToInsert.add(new evt__Event_Fee__c(Name='General Admissions Fee', evt__Event__c=event.Id, evt__Amount__c = 30.00, evt__Active__c= true, type__c = 'Standard')); 

        Test.startTest(); 
            insert feesToInsert; 
        Test.stopTest(); 

        evt__Special_Event__c eventAfter = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c WHERE Id = :event.Id]; 

        //Assert student price has updated: 
        System.assert(event.Student_Price__c != eventAfter.Student_Price__c,'Student price did not update: old event: ' + event + ' updated event: ' + eventAfter); 
        System.assert(eventAfter.Student_Price__c == 20.00, 'Student price did not update: old event: ' + event + ' updated event: ' + eventAfter);
        //Assert Active minimum price has updated: 
        System.assert(event.Minimum_Active_Price__c != eventAfter.Minimum_Active_Price__c,'Minimum_Active_Price__c did not update: old event: ' + event + ' updated event: ' + eventAfter); 
        System.assert(eventAfter.Minimum_Active_Price__c == 30.00, 'Minimum_Active_Price__c did not update: old event: ' + event + ' updated event: ' + eventAfter);
    }

     /**
     * @description Update event fee 
     * Assert that Student Price is re-calculated correctly
     * Assert that Minimum Active Price is re-calculated correctly 
     */
    @isTest  
    static void testUpdateEventFee(){
        evt__Special_Event__c event = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c LIMIT 1]; 
        evt__Event_Fee__c  fee = [SELECT ID, evt__Amount__c, type__c FROM Evt__Event_Fee__c WHERE evt__Amount__c = :event.Student_Price__c LIMIT 1]; 

        Test.startTest(); 
            fee.type__c = null; 
            update fee; 
        Test.stopTest(); 

        evt__Special_Event__c eventAfter = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c WHERE Id = :event.Id]; 

        //Assert student price has updated: 
        System.assert(event.Student_Price__c != eventAfter.Student_Price__c,'Student price did not update: old event: ' + event + ' updated event: ' + eventAfter); 
        System.assert(eventAfter.Student_Price__c == 250.00, 'Student price did not update: old event: ' + event + ' updated event: ' + eventAfter);
         //Assert Active minimum price has updated: 
         System.assert(event.Minimum_Active_Price__c != eventAfter.Minimum_Active_Price__c,'Minimum_Active_Price__c did not update: old event: ' + event + ' updated event: ' + eventAfter); 
         System.assert(eventAfter.Minimum_Active_Price__c == 200.00, 'Minimum_Active_Price__c did not update: old event: ' + event + ' updated event: ' + eventAfter);
    }


    /**
     * @description Delete event fee 
     * Assert that Student Price is re-calculated correctly
     * Assert that Minimum Active Price s re-calculated correctly 
     */
    @isTest
    static void testDeleteEventFee(){
        evt__Special_Event__c event = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c LIMIT 1]; 
        evt__Event_Fee__c  fee = [SELECT ID, evt__Amount__c, type__c FROM Evt__Event_Fee__c WHERE evt__Amount__c = :event.Student_Price__c LIMIT 1]; 

        Test.startTest(); 
            delete fee; 
        Test.stopTest(); 

        evt__Special_Event__c eventAfter = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c WHERE Id = :event.Id]; 

        //Assert student price has been updated: 
        System.assert(event.Student_Price__c != eventAfter.Student_Price__c,'Student price did not update: old event: ' + event + ' updated event: ' + eventAfter); 
        System.assert(eventAfter.Student_Price__c == 250.00, 'Student price did not update: old event: ' + event + ' updated event: ' + eventAfter);
    }

    /**
     * @description Undelete event fee 
     * Assert that Student Price is re-calculated correctly
     * Assert that Minimum Active Price s re-calculated correctly 
     */
    @isTest 
    static void testUndeleteEventFee(){
        evt__Special_Event__c event = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c LIMIT 1]; 
        evt__Event_Fee__c  fee = [SELECT ID, evt__Amount__c, type__c FROM Evt__Event_Fee__c WHERE evt__Amount__c = :event.Student_Price__c LIMIT 1]; 

        Test.startTest(); 
            delete fee; 
            undelete fee; 
        Test.stopTest(); 

        evt__Special_Event__c eventAfter = [SELECT Id, Student_Price__c, Minimum_Active_Price__c FROM evt__Special_Event__c WHERE Id = :event.Id]; 

        //Assert student price && Minimum Active Price has not been updated: 
        System.assert(event.Student_Price__c == eventAfter.Student_Price__c,'Student price was update: old event: ' + event + ' updated event: ' + eventAfter); 
        //Assert Active minimum price has updated: 
        System.assert(event.Minimum_Active_Price__c == eventAfter.Minimum_Active_Price__c,'Minimum_Active_Price__c was update: old event: ' + event + ' updated event: ' + eventAfter); 
    }
}