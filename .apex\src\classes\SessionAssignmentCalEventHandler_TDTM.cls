/**
 * @description Calls CalendarService class methods to create Events to be inserted into Session Assignment's grand-parent (Event's) owner's calendar
  * <AUTHOR>
 * @version 1.0
 * @created 24-AUG-2020 
 */
global class SessionAssignmentCalEvent<PERSON>andler_TDTM extends hed.TDTM_Runnable{

	/**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Session Assignment records from trigger new 
     * @param oldList the list of Session Assignment records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, Before Update)
     * @param objResult the describe for Session Assignment 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        Map<Id, evt__Session_Assignment__c> oldMap = new Map<Id, evt__Session_Assignment__c>();
        List<Event> eventList = new List<Event>();
        List<evt__Session_Assignment__c> sesAssignList = new List<evt__Session_Assignment__c>();
        
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
            
            //Filter newList to search for records where Status = 'Confirmed'
            for(evt__Session_Assignment__c rec: (List<evt__Session_Assignment__c>)newlist){
            	if(rec.evt__Status__c == 'Confirmed')
            		sesAssignList.add(rec);
            }
            
            //create Event records for above filtered records
            if(sesAssignList.size() >0)
                eventList = CalendarService.createEvent(sesAssignList, 'evt_Start__c', 'evt_End__c', 'Event_Owner__c', 'ContactId_Attendee__c', 'Event_Location__c', null);
            //Create Event records
            if(eventList.size() >0)
                insert eventList;
            
        }
        
        if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            
        	for(evt__Session_Assignment__c rec : (List<evt__Session_Assignment__c>)oldList)
            	oldMap.put(rec.Id, rec); 
            
            //Filter newList to search for records where Status has changed to 'Confirmed'
            for(evt__Session_Assignment__c rec: (List<evt__Session_Assignment__c>)newlist){
            	if(rec.evt__Status__c != (oldMap.get(rec.Id)).evt__Status__c && rec.evt__Status__c == 'Confirmed')
            		sesAssignList.add(rec);
            }
            
            //create Event records for above filtered records
            if(sesAssignList.size() >0)
                eventList = CalendarService.createEvent(sesAssignList, 'evt_Start__c', 'evt_End__c', 'Event_Owner__c', 'ContactId_Attendee__c', 'Event_Location__c', null);
            //Create Event records
            if(eventList.size() >0)
                insert eventList;
            
            sesAssignList.clear();
            eventList.clear();
            //Filter newList to search for records where Status has changed from 'Confirmed' to something else
            for(evt__Session_Assignment__c rec: (List<evt__Session_Assignment__c>)newlist){
                if(rec.evt__Status__c != (oldMap.get(rec.Id)).evt__Status__c && (oldMap.get(rec.Id)).evt__Status__c == 'Confirmed')
                    sesAssignList.add(rec);
            }
            
            //Find Event records for above filtered records
            if(sesAssignList.size() >0)
                eventList = CalendarService.findEvent(sesAssignList, 'evt_Start__c', 'evt_End__c', 'Event_Owner__c', 'ContactId_Attendee__c', 'Event_Location__c', null);
            system.debug('eventList = ' + eventList);
            //Delete Event records
			if(eventList.size() >0)
                delete eventList;
            
        }
	return null;
    }
}