global class Test_ChecklistIndicator_TDTM extends hed.TDTM_Runnable {

    global static Boolean alreadyRan = false;

    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
 
        //Stamp date fields on the application
        if ( triggerAction == hed.TDTM_Runnable.Action.AfterInsert && !Test_ChecklistIndicator_TDTM.alreadyRan ) {

            //This thing has a bad habit of running twice. Need to log a case with SF
            updateRelatedCis( (List<hed__Test__c>)newList );

        } else if ( triggerAction == hed.TDTM_Runnable.Action.AfterUpdate && !Test_ChecklistIndicator_TDTM.alreadyRan ) {

            //This thing has a bad habit of running twice. Need to log a case with SF
            List<hed__Test__c> verifiedTests = new List<hed__Test__c>();
            for ( Integer i=0; i < newList.size(); i++ )
                if ( newList[i].get('hed__Source__c') == 'Official' && oldList[i].get('hed__Source__c') != 'Official' )
                    verifiedTests.add((hed__Test__c)newList[i]);

            if ( verifiedTests.size() > 0 )
                updateRelatedCis( (List<hed__Test__c>)newList );

        }

        return dmlWrapper;
    }

    private void updateRelatedCis ( List<hed__Test__c> tests ) {
 
        Application_ChecklistManager_TDTM.alreadyRan = true;
        ApplicationChecklistService acs = new ApplicationChecklistService ( tests, 'hed__Test__c' );

    }

}