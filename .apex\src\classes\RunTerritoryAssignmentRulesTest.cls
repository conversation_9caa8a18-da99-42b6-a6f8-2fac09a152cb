@isTest
public class RunTerritoryAssignmentRulesTest {
    @testSetup
    static void testSetup () {

        Contact con = new Contact ( 
            FirstName = 'test',
            LastName  = 'test',
            email = '<EMAIL>'
        );
        insert con;

        Account adminAcct = new Account(Name = 'Test', RecordTypeId = AccountService.AdminRTId, hed__Primary_Contact__c = con.Id);
        insert adminAcct;

        Map<String, Object> paMap = new Map<String, Object>{
                'Program_Code__c' => '9',
                'RecordTypeId' => AccountService.AcademicProgramRTId
        };
        Account paTest = (Account) TestFactory.createSObject(new Account(), paMap);
        insert paTest;

        List<User> usersToInsert = (List<User>) TestFactory.createSObjectList(new User(), 2);
        insert usersToInsert;

        //Insert related opp with AD and RO assignments:
        Map<String, Object> oppMap = new Map<String, Object>{
                'AccountId' => adminAcct.Id,
                'Contact__c' => con.Id,
                'Program_Account__c' => paTest.Id
        };
        Opportunity testOpp_withAssignments = (Opportunity)TestFactory.createSObject(new Opportunity(Assistant_Director__c = usersToInsert[0].Id,Recruitment_Officer__c = usersToInsert[1].Id,RecordTypeId = OpportunityService.DegreeProgramProspectRTId),oppMap);

        Opportunity testOpp_woAssignments = (Opportunity) TestFactory.createSObject(new Opportunity(RecordTypeId = OpportunityService.ExecutiveProgramProspectRTId, StageName = 'Lead', IsExcludedFromTerritory2Filter=false), oppMap);

        insert new List<Opportunity>{testOpp_withAssignments, testOpp_woAssignments};

        //Insert test app related to an opportunity with no AD and RO assignments
        hed__Application__c app = new hed__Application__c(RecordTypeId = ApplicationService.FTandSpecializedRTId,
                hed__Applicant__c = con.Id,
                hed__Applying_To__c = paTest.Id,
                Opportunity__c = testOpp_woAssignments.Id);
        //Insert test app related to an opportunity with AD and RO assignments
        hed__Application__c app_withAssignments = new hed__Application__c(RecordTypeId = ApplicationService.FTandSpecializedRTId,
                hed__Applicant__c = con.Id,
                hed__Applying_To__c = paTest.Id,
                Opportunity__c = testOpp_withAssignments.Id,
                Assistant_Director__c = testOpp_withAssignments.Assistant_Director__c,
                Recruitment_Officer__c = testOpp_withAssignments.Recruitment_Officer__c);
        insert new List<hed__Application__c>{app, app_withAssignments};
    }
    
    @isTest
    static void testTerritoryRules() {
        Test.startTest();
			Test.setMock(HttpCalloutMock.class, new RunTerritoryAssignmentRulesMockCallout());
        	RunTerritoryAssignmentRules updateJob = new RunTerritoryAssignmentRules();
        	ID jobID = System.enqueueJob(updateJob);
        Test.stopTest();
    }
    
    @isTest
    static void testAuthenticateByUsernamePassword() {
        Test.startTest();
			Test.setMock(HttpCalloutMock.class, new RunTerritoryAssignmentRulesMockCallout());
        	HttpResponse res = RunTerritoryAssignmentRules.authenticateByUsernamePassword('<EMAIL>', 'test#123');
        Test.stopTest();
        System.assertNotEquals(null, res);
    }
    
    @isTest
    static void testGetSessionId() {
        Test.startTest();
			Test.setMock(HttpCalloutMock.class, new RunTerritoryAssignmentRulesMockCallout());
        	RunTerritoryAssignmentRules updateJob = new RunTerritoryAssignmentRules();
        	String response = updateJob.getSessionId('<EMAIL>', 'test#123');
        Test.stopTest();
        System.assertNotEquals(null, response);
    }
}