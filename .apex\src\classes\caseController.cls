public with sharing class caseController {
    @AuraEnabled
    public static Case getCaseWithContact(Id caseId) {
        return [SELECT Id, ContactId, Contact_Full_Name__c FROM Case WHERE Id = :caseId];
    }

    @AuraEnabled
    public static List<Task> getTasksByCase(Id caseId) {
        return [
                SELECT Id, Subject, Status, Priority, ActivityDate
                FROM Task
                WHERE WhatId = :caseId AND TaskSubtype <> 'Email' ORDER BY ActivityDate DESC
        ];
    }

    @AuraEnabled
    public static List<sfal__Appointment__c> getAppointmentsByCase (Id caseId) {
        return [
                SELECT Id, Name, sfal__Topic__c, sfal__StartDateTime__c, sfal__EndDateTime__c, Owner.Name
                FROM sfal__Appointment__c
                WHERE sfal__RelatedCase__c = :caseId ORDER BY CreatedDate DESC
        ];
    }
}