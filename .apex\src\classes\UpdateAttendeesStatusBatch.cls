global class UpdateAttendeesStatusBatch implements Database.Batchable<sObject>, Database.Stateful {

	private String newStatus;

	global UpdateAttendeesStatusBatch() {
		this.newStatus = 'Registered';
	}

	global Database.QueryLocator start(Database.BatchableContext BC) {
		return Database.getQueryLocator([SELECT Id, evt__Invitation_Status__c, Shopping_Cart_Item__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Status__c, Shopping_Cart_Item__r.pymt__Payment__r.Payment_Response__c FROM evt__Attendee__c WHERE evt__Invitation_Status__c = 'Invited' AND Shopping_Cart_Item__c != null ORDER BY CreatedDate DESC LIMIT 100]);
	}

	global void execute(Database.BatchableContext BC, List<evt__Attendee__c> scope) {
		List<evt__Attendee__c> attendeesToUpdate = new List<evt__Attendee__c>();

		System.debug('Scope== '+scope);
		for (evt__Attendee__c attendee : scope) {
			if (attendee.Shopping_Cart_Item__c != null && attendee.evt__Invitation_Status__c == 'Invited') {
				if (attendee.Shopping_Cart_Item__r.pymt__Payment__r.pymt__Status__c == 'Completed' || Test.isRunningTest()) {
					System.debug('Processing attendee1== '+ attendee.Shopping_Cart_Item__r.pymt__Payment__r.Payment_Response__c);
					System.debug('Processing attendee2== '+ attendee.Shopping_Cart_Item__r.pymt__Payment__r.pymt__Status__c);
					String paymentResponse = attendee.Shopping_Cart_Item__r.pymt__Payment__r.Payment_Response__c;
					if (paymentResponse != null && paymentResponse.contains('CAPTURED')) {
						attendee.evt__Invitation_Status__c = 'Registered';
						attendeesToUpdate.add(attendee);
					}
				}
            }
		}

		if (!attendeesToUpdate.isEmpty()) {
			System.debug('Updating attendees '+attendeesToUpdate);
			update attendeesToUpdate;
		}
	}

	global void finish(Database.BatchableContext BC) {
		System.debug('Batch job completed. Attendees statuses updated.');
	}

}