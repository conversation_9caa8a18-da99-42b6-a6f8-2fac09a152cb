public class createShoppingCartController {
    @AuraEnabled
    public static string response{get;set;}
    public static Id recId{get;set;}
    //@AuraEnabled
    //public static list<cartWrapper> cart= new list<cartWrapper>{};
    public createShoppingCartController(){
        recId= ApexPages.currentpage().getparameters().get('id');
    }
    
    public class cartWrapper{
        @AuraEnabled public string description{get;set;}
        @AuraEnabled public double price{get;set;}
        @AuraEnabled  public string item{get;set;}
        @AuraEnabled public integer qty{get;set;}
        @AuraEnabled  public double total{get;set;}
        @AuraEnabled  public string cid{get;set;}
        @AuraEnabled  public string User{get;set;}
        //public evt__Special_Event__c event{get;set;}
    }
    /*public static void vfMethod(){
        recId = ApexPages.currentpage().getparameters().get('id');
        system.debug('id--> '+recId);
        if(recId != Null){
            addToCart(recId); 
            system.debug('response--'+response);
        }
    }*/
    @AuraEnabled
    public static string getUserDetails(){
        id userId = UserInfo.getUserId();
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        return u.FirstName;
    }
    @AuraEnabled
    public static list<cartWrapper> addToCart(Id EventCourseId){
        //list<cartWrapper> cart = new list<cartWrapper>{};
        system.debug('id--> '+recId);
        //cartWrapper wrap = new cartWrapper();
        id userId = UserInfo.getUserId();
        system.debug('EventCourseId='+EventCourseId);
        system.debug('userId='+userId);
        Contact con;
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        if(u.ContactId != null){
            con  = [SELECT Id, Name From Contact WHERE Id=: u.ContactId LIMIT 1];
        }
        else{
            con  = [SELECT Id, Name From Contact WHERE Name=: u.Name LIMIT 1];
        }
        system.debug('id= '+recId);
        if(recId != null && EventCourseId ==Null){
            system.debug('id= '+recId);
            EventCourseId = recId;
        } 
        if(EventCourseId != null){
            
            system.debug('objectname='+EventCourseId.getSObjectType().getDescribe().getName());
            if(EventCourseId.getSObjectType().getDescribe().getName() == 'hed__Course_Offering__c'){
                hed__Course_Offering__c course = [SELECT 
                                                  Name,Registration_Fee__c,hed__Course__r.hed__Description__c,
                                                  (SELECT Id, Name,pymt__Quantity__c From Shopping_Cart_Items__r)
                                                  FROM hed__Course_Offering__c
                                                  WHERE Id=:EventCourseId
                                                 ];
                
                system.debug('course='+course);
                if(course.Shopping_Cart_Items__r.size() > 0){
                    //wrap.description = course.name;
                    //wrap.price = course.Registration_Fee__c;
                    system.debug('In CartItem');
                    LIST<pymt__Shopping_Cart_Item__c> cartItem = [SELECT 
                                                                  Id, Name,pymt__Quantity__c
                                                                  FROM pymt__Shopping_Cart_Item__c
                                                                  WHERE pymt__Contact__c=: con.id AND Course_Offering__c =: EventCourseId
                                                                  LIMIT 1];
                    if(!cartItem.isEmpty()){
                        cartItem[0].pymt__Quantity__c +=1;
                        try{
                            update cartItem;
                            response = 'Shopping Cart Updated';
                            //wrap.qty = integer.valueOf(cartItem[0].pymt__Quantity__c);
                            // wrap.item = cartItem[0].name;
                            //wrap.total = double.valueOf(course.Registration_Fee__c * wrap.qty);
                        } 
                        catch(exception e){
                            response = 'Error: '+e.getMessage();
                        }
                    }
                    
                }
                else{
                    system.debug('In else');
                    pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
                    cartItem.Name = course.Name;
                    cartItem.Course_Offering__c = EventCourseId;
                    cartItem.pymt__Quantity__c =1;
                    cartItem.pymt__Contact__c = con.id;
                    cartItem.pymt__Description__c = course.hed__Course__r.hed__Description__c;
                    if(cartItem.pymt__Unit_Price__c != null){
                        cartItem.pymt__Unit_Price__c += course.Registration_Fee__c;
                    }
                    else{
                        cartItem.pymt__Unit_Price__c = course.Registration_Fee__c;
                    }
                    
                    try{
                        insert cartItem;
                        response = 'Item Added to Shopping Cart ';
                    }
                    catch(exception e){
                        response = 'Error: '+e.getMessage();
                    }                
                }
                
            }
            else IF(EventCourseId.getSObjectType().getDescribe().getName() == 'evt__Special_Event__c'){
                evt__Special_Event__c course = [SELECT 
                                                Name,evt__Short_Description__c,
                                                (SELECT Id, Name,pymt__Quantity__c From Shopping_Cart_Items__r),
                                                (SELECT  evt__Amount__c From  evt__Event_Fees__r)
                                                FROM evt__Special_Event__c
                                                WHERE Id=:EventCourseId
                                               ]; 
                
                if(course.Shopping_Cart_Items__r.size() > 0){
                    system.debug('In Event CartItem');
                    LIST<pymt__Shopping_Cart_Item__c> cartItem = [SELECT 
                                                                  Id, Name,pymt__Quantity__c,pymt__Unit_Price__c
                                                                  FROM pymt__Shopping_Cart_Item__c
                                                                  WHERE pymt__Contact__c=: con.id  AND Special_Event__c =: EventCourseId
                                                                  LIMIT 1    ];
                    // wrap.description = course.name;
                    if(!cartItem.isEmpty()){
                        
                        cartItem[0].pymt__Quantity__c +=1;
                        system.debug('fee= '+course.evt__Event_Fees__r );
                        system.debug('amount= '+course.evt__Event_Fees__r[0].evt__Amount__c);
                        if(course.evt__Event_Fees__r.size() > 0 ){
                            if(cartItem[0].pymt__Unit_Price__c != null){
                                
                            }
                            else{
                                cartItem[0].pymt__Unit_Price__c = course.evt__Event_Fees__r[0].evt__Amount__c;	
                            }
                            
                        }
                        
                        try{
                            update cartItem;
                            response = 'Shopping Cart Updated';
                        } 
                        catch(exception e){
                            response = 'Error: '+e.getMessage();
                        }
                    }
                    
                }
                else{
                    system.debug('In else');
                    pymt__Shopping_Cart_Item__c cartItem = new pymt__Shopping_Cart_Item__c();
                    cartItem.Name = course.Name;
                    cartItem.Special_Event__c = EventCourseId;
                    cartItem.pymt__Quantity__c =1;
                    cartItem.pymt__Contact__c = con.id;
                    cartItem.pymt__Description__c = course.evt__Short_Description__c;
                    system.debug('c: '+ course.evt__Event_Fees__r);
                    system.debug('c: '+ course.evt__Event_Fees__r[0].evt__Amount__c);
                    cartItem.pymt__Unit_Price__c = course.evt__Event_Fees__r[0].evt__Amount__c;
                    try{
                        insert cartItem;
                        
                        //   wrap.qty = integer.valueOf(cartItem.pymt__Quantity__c);
                        //    wrap.description = course.name;
                        //    wrap.total = double.valueOf(wrap.price * wrap.qty);
                        response = 'Item Added to Shopping Cart ';
                    }
                    catch(exception e){
                        response = 'Error: '+e.getMessage();
                    }                
                }
                
            }
         }
        list<cartWrapper> cart = new list<cartWrapper>{};
            
            //cart.add(wrap);
            //system.debug('cart='+cart);
            List<pymt__Shopping_Cart_Item__c> shopCartList = [SELECT 
                                                              Name, id,Special_Event__c,Special_Event__r.Name,pymt__Quantity__c,
                                                              Course_Offering__c,pymt__Description__c,
                                                              Course_Offering__r.Name,
                                                              Course_Offering__r.Registration_Fee__c
                                                              FROM pymt__Shopping_Cart_Item__c
                                                              WHERE pymt__Contact__c=: con.id
                                                             ];
        List<id> eventIds = new List<id>{};
            MAP<Id,double> amountMap = new MAP<Id,double>{};
                for(pymt__Shopping_Cart_Item__c sitem: shopCartList){
                    if(sitem.Special_Event__c != null){
                        eventIds.add(sitem.Special_Event__c);
                    }
                }
        if(!eventIds.isEmpty()){
            List<evt__Event_Fee__c> fee = [SELECT evt__Amount__c,evt__Event__c FROM evt__Event_Fee__c 
                                           WHERE evt__Event__c IN: eventIds];
            for(evt__Event_Fee__c fees: fee){
                amountMap.put(fees.evt__Event__c,fees.evt__Amount__c);
            }
        }
        for(pymt__Shopping_Cart_Item__c item: shopCartList){
            cartWrapper Cartitem = new cartWrapper();
            cartitem.user = u.firstName;
            Cartitem.item = item.Name;
            Cartitem.qty = integer.valueOf(item.pymt__Quantity__c);
            Cartitem.description = item.pymt__Description__c;
            if(item.Special_Event__c != null){
                //item.Special_Event__r.Name;
                Cartitem.cid = string.valueOf(item.Special_Event__c);
                if(amountMap.containsKey(item.Special_Event__c)){
                    Cartitem.price = amountMap.get(item.Special_Event__c);
                }
                else{
                    Cartitem.price = 0.00;
                }
                system.debug('price='+  Cartitem.price);
                system.debug('amount= '+amountMap.get(item.Special_Event__c));
            }
            else if(item.Course_Offering__c != null){
                //Cartitem.description = '';//item.Course_Offering__r.Name;
                Cartitem.cid = string.valueOf(item.Course_Offering__c);
                Cartitem.price = item.Course_Offering__r.Registration_Fee__c;
            }
            Cartitem.total =   Cartitem.price * Cartitem.qty;       
            //Cartitem.qty
            
            cart.add(Cartitem);
        }
        return cart;
        //return response;
    }
    @AuraEnabled
    public static string PaymentId(Id event){
        //Id event= ApexPages.currentpage().getparameters().get('id');
        system.debug('event= '+ event);
        //User u = [select id, contactId from User where id = : userId];
        Contact con = [SELECT Id, Name From Contact WHERE Name='Peter Dupre'];
        LIST<pymt__Shopping_Cart_Item__c> course = [SELECT 
                                                    Name, id,pymt__Unit_Price__c, pymt__Payment__c
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE (Special_Event__c=: event OR Course_Offering__c=:event )
                                                   ];
        system.debug('course= '+ course);
        if(!course.isEmpty() && course[0].pymt__Payment__c != null){
            return string.valueOF(course[0].pymt__Payment__c);
        }
        LIST<pymt__PaymentX__c> payment = [SELECT Id 
                                           FROM pymt__PaymentX__c 
                                           WHERE pymt__Contact__c =: con.Id AND pymt__Status__c = 'Scheduled'
                                           LIMIT 1
                                          ];
        system.debug('payment= '+ payment);
        If(payment.isEmpty() && !course.isEmpty()){
            pymt__PaymentX__c pymt = new pymt__PaymentX__c();
            pymt.pymt__Contact__c = con.Id;
            pymt.Name = 'Payment_'+event;
            pymt.evt__Event__c = event;
            pymt.pymt__Status__c = 'Scheduled';
            pymt.pymt__Amount__c = course[0].pymt__Unit_Price__c;//Double.valueOF('90');
            insert pymt;
            course[0].pymt__Payment__c = pymt.id;
            update course[0];
            system.debug('pymt= '+ pymt);
            return string.valueOF(pymt.Id);
        }
        else{
            system.debug('pymtId= '+ payment[0].Id);
            return string.valueOF( payment[0].Id);
        }
        //Id contactid = '0034c000001xfyeAAA';
        //evt__Special_Event__c spEvent = []//evt__Event__c
        //return null;
    }
    
    
    @AuraEnabled
    public static list<cartWrapper> removeFromCart(Id EventCourseId){
        list<cartWrapper> cart = new list<cartWrapper>{};
            Contact con = [SELECT Id, Name From Contact WHERE Name='Peter Dupre'];
        system.debug('EventCourseId-> '+EventCourseId);
        LIST<pymt__Shopping_Cart_Item__c> course = [SELECT 
                                                    Name, id
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE (Special_Event__c=: EventCourseId OR Course_Offering__c=:EventCourseId )
                                                   ];
        if(!course.isEmpty()){
            try {
                delete course;
                //return 'Deleted';
            }
            catch(exception e){
                //return 'Error';
            }
        }
        List<pymt__Shopping_Cart_Item__c> shopCartList = [SELECT 
                                                          Name, id,Special_Event__c,Special_Event__r.Name,pymt__Quantity__c,
                                                          Course_Offering__c,
                                                          Course_Offering__r.Name,
                                                          Course_Offering__r.Registration_Fee__c
                                                          FROM pymt__Shopping_Cart_Item__c
                                                          WHERE pymt__Contact__c=: con.id
                                                         ];
        List<id> eventIds = new List<id>{};
            MAP<Id,double> amountMap = new MAP<Id,double>{};
                for(pymt__Shopping_Cart_Item__c sitem: shopCartList){
                    if(sitem.Special_Event__c != null){
                        eventIds.add(sitem.Special_Event__c);
                    }
                }
        system.debug('eventIds-> '+eventIds);
        if(!eventIds.isEmpty()){
            List<evt__Event_Fee__c> fee = [SELECT evt__Amount__c,evt__Event__c FROM evt__Event_Fee__c 
                                           WHERE evt__Event__c IN: eventIds];
            for(evt__Event_Fee__c fees: fee){
                amountMap.put(fees.evt__Event__c,fees.evt__Amount__c);
            }
        }
        
        for(pymt__Shopping_Cart_Item__c item: shopCartList){
            cartWrapper Cartitem = new cartWrapper();
            Cartitem.item = item.Name;
            Cartitem.qty = integer.valueOf(item.pymt__Quantity__c);
            system.debug('special= '+item.Special_Event__c);
            if(item.Special_Event__c != null){
                Cartitem.description = item.Special_Event__r.Name;
                Cartitem.cid = string.valueOf(item.Special_Event__c);
                if(amountMap.containsKey(item.Special_Event__c)){
                    Cartitem.price = amountMap.get(item.Special_Event__c);
                }
                else{
                    Cartitem.price = 0.00;
                }
                
                system.debug('price='+  Cartitem.price);
                system.debug('amount= '+amountMap.get(item.Special_Event__c));
            }
            else if(item.Course_Offering__c != null){
                Cartitem.description = item.Course_Offering__r.Name;
                Cartitem.cid = string.valueOf(item.Course_Offering__c);
                Cartitem.price = item.Course_Offering__r.Registration_Fee__c;
            }
            Cartitem.total =   Cartitem.price * Cartitem.qty;       
            //Cartitem.qty
            cart.add(Cartitem);
        }
        system.debug('cart= '+cart);
        return cart;
    }
    
    @AuraEnabled
    public static list<cartWrapper> continueCartSave(list<cartWrapper> cartItems){
        Map<id,integer> itemQtyMap = new Map<id,integer>{};
        for(cartWrapper item: cartItems ){
            system.debug('id-'+item.cid);
            system.debug('qty-'+item.qty);
            itemQtyMap.put(item.cid,item.qty);
        }
		system.debug('itemQtyMap='+itemQtyMap);
        if(!itemQtyMap.isEmpty()){
            LIST<pymt__Shopping_Cart_Item__c> course = [SELECT 
                                                    Name, id,pymt__Quantity__c,Special_Event__c,Course_Offering__c
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE (Special_Event__c IN: itemQtyMap.keySet() OR Course_Offering__c IN:itemQtyMap.keySet() )
                                                   ];
            system.debug('course= '+ course);
            if(!course.isEmpty()){
                for(pymt__Shopping_Cart_Item__c cartItem: course ){
                    system.debug('qty-'+cartItem.pymt__Quantity__c);
                    if(cartItem.Special_Event__c != Null && itemQtyMap.containsKey(cartItem.Special_Event__c)){
                        cartItem.pymt__Quantity__c = itemQtyMap.get(cartItem.Special_Event__c);
                    }
                    if(cartItem.Course_Offering__c != Null && itemQtyMap.containsKey(cartItem.Course_Offering__c)){
                        cartItem.pymt__Quantity__c = itemQtyMap.get(cartItem.Course_Offering__c);
                    }
                    //cartItem.pymt__Quantity__c = itemQtyMap.get('');
                }
                update course;
            }
        }
        return cartItems;
        
    }
    
    @AuraEnabled
    public static list<cartWrapper> noRecord(){
        Contact con;
        id userId = UserInfo.getUserId();
        User u = [select id,Name,firstName, contactId from User where id = : userId];
        if(u.ContactId != null){
            con  = [SELECT Id, Name From Contact WHERE Id=: u.ContactId LIMIT 1];
        }
        else{
            con  = [SELECT Id, Name From Contact WHERE Name=: u.Name LIMIT 1];
        }
        list<cartWrapper> cart = new list<cartWrapper>{};
            
            //cart.add(wrap);
            //system.debug('cart='+cart);
            List<pymt__Shopping_Cart_Item__c> shopCartList = [SELECT 
                                                              Name, id,Special_Event__c,Special_Event__r.Name,pymt__Quantity__c,
                                                              Course_Offering__c,pymt__Description__c,
                                                              Course_Offering__r.Name,
                                                              Course_Offering__r.Registration_Fee__c
                                                              FROM pymt__Shopping_Cart_Item__c
                                                              WHERE pymt__Contact__c=: con.id
                                                             ];
        List<id> eventIds = new List<id>{};
            MAP<Id,double> amountMap = new MAP<Id,double>{};
                for(pymt__Shopping_Cart_Item__c sitem: shopCartList){
                    if(sitem.Special_Event__c != null){
                        eventIds.add(sitem.Special_Event__c);
                    }
                }
        if(!eventIds.isEmpty()){
            List<evt__Event_Fee__c> fee = [SELECT evt__Amount__c,evt__Event__c FROM evt__Event_Fee__c 
                                           WHERE evt__Event__c IN: eventIds];
            for(evt__Event_Fee__c fees: fee){
                amountMap.put(fees.evt__Event__c,fees.evt__Amount__c);
            }
        }
        for(pymt__Shopping_Cart_Item__c item: shopCartList){
            cartWrapper Cartitem = new cartWrapper();
            cartitem.user = u.firstName;
            Cartitem.item = item.Name;
            Cartitem.qty = integer.valueOf(item.pymt__Quantity__c);
            Cartitem.description = item.pymt__Description__c;
            if(item.Special_Event__c != null){
                //item.Special_Event__r.Name;
                Cartitem.cid = string.valueOf(item.Special_Event__c);
                if(amountMap.containsKey(item.Special_Event__c)){
                    Cartitem.price = amountMap.get(item.Special_Event__c);
                }
                else{
                    Cartitem.price = 0.00;
                }
                system.debug('price='+  Cartitem.price);
                system.debug('amount= '+amountMap.get(item.Special_Event__c));
            }
            else if(item.Course_Offering__c != null){
                //Cartitem.description = '';//item.Course_Offering__r.Name;
                Cartitem.cid = string.valueOf(item.Course_Offering__c);
                Cartitem.price = item.Course_Offering__r.Registration_Fee__c;
            }
            Cartitem.total =   Cartitem.price * Cartitem.qty;       
            //Cartitem.qty
            
            cart.add(Cartitem);
        }
        return cart;
    }
}