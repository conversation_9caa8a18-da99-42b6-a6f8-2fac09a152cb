@isTest
public class RemoveEventControllerTest {
      @TestSetup
    static void setupTestData() {
        Calendar_View__c testCalendrViewObject = new Calendar_View__c();
        testCalendrViewObject.Link_to_Calendar_Item__c = 'https://dev01-rotman.cs138.force.com/events/s/special-event/{!itemId}';
        testCalendrViewObject.Name = 'Events Home Page Calendar View';
        insert testCalendrViewObject;        
       
        //Create Special Event
        evt__Special_Event__c Event = new evt__Special_Event__c(Name = 'Big Ideas Speaker Series at Rotman - Efosa Ojomo');
        insert Event;
        
        //Create shopping Cart

        pymt__Shopping_Cart__c testShoppingCart = new pymt__Shopping_Cart__c();
        insert testShoppingCart;
        
        //Create shopping Cart itme

        pymt__Shopping_Cart_Item__c testShoppingCartItem = new pymt__Shopping_Cart_Item__c();
        testShoppingCartItem.Name = 'Test Shopping Item';
        testShoppingCartItem.Special_Event__c = Event.Id;
        testShoppingCartItem.pymt__Shopping_Cart__c = testShoppingCart.Id;
        insert testShoppingCartItem;
        
        /*pymt__Shopping_Cart_Item__c testShoppingCartItem1 = new pymt__Shopping_Cart_Item__c();
        testShoppingCartItem1.Name = 'Test Shopping Item';
        testShoppingCartItem1.Special_Event__c = Event.Id;
        testShoppingCartItem1.pymt__Shopping_Cart__c = testShoppingCart.Id;
        insert testShoppingCartItem1;*/
    }

    @isTest
    static void testDeleteCartItemsAndCart() {
        pymt__Shopping_Cart__c testShoppingCart = [SELECT Id FROM pymt__Shopping_Cart__c];
        pymt__Shopping_Cart_Item__c item = [SELECT Id FROM pymt__Shopping_Cart_Item__c WHERE pymt__Shopping_Cart__c =: testShoppingCart.Id limit 1 ];
        Test.startTest();
            String testUrl = RemoveEventController.deleteCartItemsAndCart(item.Id,testShoppingCart.Id);
        Test.stopTest();
        System.assertNotEquals(testUrl, null, 'URL should not be null');
    }

}