@isTest
public class paymentTerminalTest {
    @testSetup
    static void setupMethod(){
        Gateway_Setting__c gs = new Gateway_Setting__c();
        gs.Endpoint__c = 'https://esqa.moneris.com/gateway2/servlet/MpgRequest';
        gs.API_token__c = 'yesguy';
        gs.Store_Id__c = 'store3';
        insert gs;
        Contact con = new contact();
        con.FirstName = 'Test';
        con.lastName = 'Contact';
        insert con;
        
        pymt__PaymentX__c pymt = new pymt__PaymentX__c();
        pymt.Name = 'payment_'+string.valueOf(datetime.now());
        pymt.Order_Id__c = 'Payment:'+string.valueOf(datetime.now());
        pymt.pymt__Transaction_Type__c = 'Payment';
        pymt.pymt__Status__c = 'Scheduled';
        pymt.pymt__Amount__c = 10.12;
        pymt.Payment_Response__c = '';
        pymt.pymt__Billing_First_Name__c = 'Test';
        pymt.pymt__Billing_Last_Name__c = 'Lname';
        pymt.pymt__Transaction_Id__c = 'tnx123';
        pymt.Administration_Fee__c = 5.00;
        pymt.pymt__Last_4_Digits__c='';
        //pymt.Payment_Response__c = '';
        insert pymt;
        system.debug('payment--> '+pymt.pymt__Amount__c);
    } 
    static testMethod void checkPayment() {
        
        pymt__PaymentX__c pymt = [select id,Name,pymt__Payment_Processor__c, pymt__Tax__c, pymt__Shipping__c, pymt__Amount__c,
                                  pymt__Currency_ISO_Code__c, pymt__Invoice_Number__c, pymt__Date__c,pymt__Transaction_Id__c,
                                  pymt__PO_Number__c, pymt__Card_Type__c, pymt__Billing_First_Name__c,Payment_Response__c,
                                  pymt__Billing_Last_Name__c, pymt__Billing_Street__c,Order_Id__c,Administration_Fee__c,
                                  pymt__Billing_City__c, pymt__Billing_Country__c,pymt__Status__c,pymt__Contact__c,
                                  pymt__Billing_State__c, pymt__Billing_Postal_Code__c, pymt__Billing_Email__c,
                                  Refund_Reason__c,pymt__Last_4_Digits__c
                                  From pymt__PaymentX__c WHERE pymt__Status__c= 'Scheduled' LIMIT 1 ];
        test.startTest();
        test.setMock(HttpCalloutMock.class, new MonerisMockResponse());
        CardWrapperClass cardDetails = PaymentTerminalController.getPaymentDetails(pymt.Id);
        cardDetails.Amount = 10.12;
        cardDetails.pymt = pymt;
        cardDetails.cardNo = '****************';
        system.debug('card--'+ cardDetails);
        PaymentTerminalController.getPaymentDone(cardDetails, pymt.Id);
        test.stopTest();
    }
    static testMethod void checkRefund() {
        
        pymt__PaymentX__c pymt = [select Name,pymt__Payment_Processor__c, pymt__Tax__c, pymt__Shipping__c, pymt__Amount__c,
                                  pymt__Currency_ISO_Code__c, pymt__Invoice_Number__c, pymt__Date__c,pymt__Transaction_Id__c,
                                  pymt__PO_Number__c, pymt__Card_Type__c, pymt__Billing_First_Name__c,Payment_Response__c,
                                  pymt__Billing_Last_Name__c, pymt__Billing_Street__c,Order_Id__c,Administration_Fee__c,
                                  pymt__Billing_City__c, pymt__Billing_Country__c,pymt__Status__c,pymt__Contact__c,
                                  pymt__Billing_State__c, pymt__Billing_Postal_Code__c, pymt__Billing_Email__c,
                                  Refund_Reason__c                                  
                                  From pymt__PaymentX__c WHERE pymt__Status__c= 'Scheduled' LIMIT 1 ];
        test.startTest();
        test.setMock(HttpCalloutMock.class, new MonerisMockResponse());
        CardWrapperClass cardDetails = PaymentTerminalController.getPaymentDetails(pymt.Id);
        cardDetails.Amount = 10.12;
        cardDetails.pymt = pymt;
        system.debug('card--'+ cardDetails);
        PaymentTerminalController.getRefundPayment(cardDetails, pymt.Id);
        test.stopTest();
    }
    static testMethod void checkVoid() {
        
        pymt__PaymentX__c pymt = [select id,Order_Id__c,pymt__Transaction_Type__c,pymt__Amount__c,Payment_Response__c From pymt__PaymentX__c WHERE pymt__Status__c= 'Scheduled' LIMIT 1 ];
        test.startTest();
        test.setMock(HttpCalloutMock.class, new MonerisMockResponse());
        CardWrapperClass cardDetails = PaymentTerminalController.getPaymentDetails(pymt.Id);
        cardDetails.Amount = 10.12;
        system.debug('card--'+ cardDetails);
        PaymentTerminalController.getVoidPayment(cardDetails, pymt.Id);
        test.stopTest();
    }
    static testMethod void checkContact(){
        pymt__PaymentX__c pymt = [select id,Order_Id__c,pymt__Transaction_Type__c,pymt__Amount__c,Payment_Response__c From pymt__PaymentX__c WHERE pymt__Status__c= 'Scheduled' LIMIT 1 ];
        ApexPages.CurrentPage().getParameters().put('id',pymt.Id);
        ApexPages.Standardcontroller sc = new ApexPages.Standardcontroller(pymt);
        PaymentTerminalController ptc = new PaymentTerminalController(sc);
        Contact con = [SELECT Id, Name from Contact where FirstName = 'Test'];
        test.startTest();
        PaymentTerminalController.NewContact(con.Id);
        test.stopTest();
    }
    static testMethod void checkRefundCash() {
        
        pymt__PaymentX__c pymt = [select Name,pymt__Payment_Processor__c, pymt__Tax__c, pymt__Shipping__c, pymt__Amount__c,
                                  pymt__Currency_ISO_Code__c, pymt__Invoice_Number__c, pymt__Date__c,pymt__Transaction_Id__c,
                                  pymt__PO_Number__c, pymt__Card_Type__c, pymt__Billing_First_Name__c,Payment_Response__c,
                                  pymt__Billing_Last_Name__c, pymt__Billing_Street__c,Order_Id__c,Administration_Fee__c,
                                  pymt__Billing_City__c, pymt__Billing_Country__c,pymt__Status__c,pymt__Contact__c,
                                  pymt__Billing_State__c, pymt__Billing_Postal_Code__c, pymt__Billing_Email__c,
                                  Refund_Reason__c                                  
                                  From pymt__PaymentX__c WHERE pymt__Status__c= 'Scheduled' LIMIT 1 ];
        test.startTest();
        test.setMock(HttpCalloutMock.class, new MonerisMockResponse());
        CardWrapperClass cardDetails = PaymentTerminalController.getPaymentDetails(pymt.Id);
        cardDetails.Amount = 10.12;
        cardDetails.pymt = pymt;
        system.debug('card--'+ cardDetails);
        PaymentTerminalController.getRefundPaymentcashCheck(cardDetails, pymt.Id);
        test.stopTest();
    }
    
}