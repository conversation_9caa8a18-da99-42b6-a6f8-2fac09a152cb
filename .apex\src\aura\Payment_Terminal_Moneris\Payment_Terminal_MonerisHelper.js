({
    getPaymentWrapper: function(component, event, helper) {
        //console.log('***In helper');
        var a= component.get("v.recordId");
        console.log('***In helper'+a);
        //call apex class method
       // debugger;
        if(a){
             
            var action = component.get('c.getPaymentDetails');
            action.setParams({
                pid : a//component.get("v.recordId")
            });
            action.setCallback(this, function(response) {
                //store state of response
                var state = response.getState();
                if (state === "SUCCESS") {
                    console.log("resp--> "+response.getReturnValue());
                    //set response value in wrapperList attribute on component.
                    component.set('v.paymentCardWrapper', response.getReturnValue());
                    component.set("v.TotalAmount",response.getReturnValue().pymt.pymt__Amount__c);
                }
            });
            $A.enqueueAction(action);
        }
        
    },
    MakePayment: function(component,action) {
        var wrapper = component.get("v.paymentCardWrapper");
        console.log('Card--> '+wrapper.cardNo);
        /* var action = component.get("c.getPaymentDone");
        action.setParams({
            cardDetails : component.get("v.paymentCardWrapper"),
            pid : component.get("v.recordId")
        });
        action.setCallback(this, function(response) {
            //store state of response
            
            var ReceiptId = response.getReturnValue().response.receipt.ReceiptId;
            //console.log("rece--> "+response.Message);ReceiptId
            //var responseRec = JSON.parse(response);
            //console.log("responseRec--> "+responseRec);
            //var state = response.getState();
         if (ReceiptId === null || ReceiptId === "null") {
                //console.log("resp--> "+response);
                //var resp = response.getReturnValue();
                //console.log("respval--> "+resp);
                component.find('notifLib').showNotice({
                    "variant": "error",
                    "header": "Error",
                    "message": response.getReturnValue().response.receipt.Message,
                    closeCallback: function() {
                       // var dismissActionPanel = $A.get("e.force:closeQuickAction");
       					// dismissActionPanel.fire();
                       //$A.get("e.force:refreshView").fire();
                    }
                });
            }
            else{
                component.find('notifLib').showNotice({
                    "variant": "Success",
                    "header": "Payment was Successful!",
                    "message": 'Payment Reference Number: '+response.getReturnValue().response.receipt.ReferenceNum,
                    closeCallback: function() {
                          //var dismissActionPanel = $A.get("e.force:closeQuickAction");
       					 //dismissActionPanel.fire();
                       //$A.get("e.force:refreshView").fire();
                    }
                });
            } 
        });
        $A.enqueueAction(action); */
        return new Promise(function(resolve, reject) { 
            action.setCallback(this, 
                               function(response) {
                                   var state = response.getState();
                                   if (state === "SUCCESS") {
                                       resolve(response.getReturnValue());
                                   } else {
                                       reject(new Error(response.getError()));
                                   }
                               }); 
            $A.enqueueAction(action);
        });
        
        
    },
    closeModal: function(component, event, helper){
        var dismissActionPanel = $A.get("e.force:closeQuickAction");
        dismissActionPanel.fire();
    },
})