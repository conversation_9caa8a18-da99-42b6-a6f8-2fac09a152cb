/**
 * @description Sums the total of all shoppping cart items to the payment objects
 * <AUTHOR>
 * @version 1.0
 * @created 2020-08-25
 * @modified 2020-08-25
 */
global class ShoppingCartSumService_TDTM extends hed.TDTM_Runnable{

    public static final List<String> amountFields = new List<String>{ 'Gross_Amount__c', 'Discount_Amount__c', 'Tax_Amount__c', 'Total_Amount__c' };

    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        Set<Id> pymtsToCalc     = new Set<Id>();
        Set<Id> pymtsToHold     = new Set<Id>();
        Set<Id> pymtsToUnhold   = new Set<Id>();

        Set<Id> invoicesToCalc      = new Set<Id>();

        if ( triggerAction ==  hed.TDTM_Runnable.Action.AfterInsert ) {
            //Only apply taxes to new shopping cart items
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList) {

                if( !sci.pymt__Payment_Completed__c && sci.pymt__Payment__c != null ) {
                    pymtsToCalc.add( sci.pymt__Payment__c ); 
                }

                if ( !sci.pymt__Payment_Completed__c && sci.pymt__Payment__c != null && (sci.Discount_Status__c.contains('Pending Approval') || sci.Discount_Status__c.contains('Discount Denied')) )
                    pymtsToHold.add( sci.pymt__Payment__c );

                invoicesToCalc.add( sci.Invoice__c );
            }


        } else if ( triggerAction == hed.TDTM_Runnable.Action.AfterUpdate ) {
            Map<Id, pymt__Shopping_Cart_Item__c> oldMap = new Map<Id, pymt__Shopping_Cart_Item__c>((List<pymt__Shopping_Cart_Item__c>)oldList);
            //Recalculate taxes on all taxable items where the address has changed or taxable status has changed
            for ( pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)newList ) {

                if ( !sci.pymt__Payment_Completed__c ) {

                    //Check to see if any sum fields on the shopping cart item object have changed. 
                    for ( String field : ShoppingCartSumService_TDTM.amountFields )
                        if ( sci.get(field) != oldMap.get(sci.Id).get(field) )
                            pymtsToCalc.add( sci.pymt__Payment__c ); 
                            invoicesToCalc.add( sci.Invoice__c );

                    //If the payment record has changed, recalculate both payment records
                    if ( sci.pymt__Payment__c != oldMap.get(sci.Id).pymt__Payment__c ) {
                        pymtsToCalc.add( sci.pymt__Payment__c ); 
                        pymtsToCalc.add( oldMap.get(sci.Id).pymt__Payment__c ); 
                    }

                    //If the invoice record has changed, recalculate both invoice records
                    if ( sci.Invoice__c != oldMap.get(sci.Id).Invoice__c ) {
                        invoicesToCalc.add( sci.Invoice__c ); 
                        invoicesToCalc.add( oldMap.get(sci.Id).Invoice__c ); 
                    }

                    if ( sci.pymt__Payment__c != null && (sci.Discount_Status__c.contains('Pending Approval') || sci.Discount_Status__c.contains('Discount Denied')) && (oldMap.get(sci.Id).Discount_Status__c.contains('Pending Approval') || oldMap.get(sci.Id).Discount_Status__c.contains('Discount Denied')) ) {
                        pymtsToHold.add( sci.pymt__Payment__c );
                    }
                    else if ( sci.pymt__Payment__c != null && sci.Discount_Status__c.contains('Discount Approved') && !oldMap.get(sci.Id).Discount_Status__c.contains('Discount Approved') && !pymtsToHold.contains(sci.pymt__Payment__c) )
                        pymtsToUnhold.add( sci.pymt__Payment__c );

                } else if ( sci.pymt__Payment_Completed__c )
                    invoicesToCalc.add( sci.Invoice__c );
            }

        } else if ( triggerAction == hed.TDTM_Runnable.Action.AfterDelete ) {
            for(pymt__Shopping_Cart_Item__c sci : (List<pymt__Shopping_Cart_Item__c>)oldList) {
                pymtsToCalc.add( sci.pymt__Payment__c ); 
                invoicesToCalc.add( sci.Invoice__c );
            }
        }

        Map<Id, pymt__PaymentX__c> pymts = new Map<Id, pymt__PaymentX__c>(); //List of all payment records which will be updated
        Map<Id, Invoice__c> invoices = new Map<Id, Invoice__c>(); //List of all invoice records which will be updated

        if ( pymtsToCalc.size() > 0 ) {


            //Since in after context, check for totals of all payment records which have been updated
            List<AggregateResult> ars = [ SELECT pymt__Payment__c pymt, SUM( Gross_Amount__c ) ga, SUM( Total_Amount__c ) ta, SUM( Tax_Amount__c ) taxa, SUM( Discount_Amount__c ) da, SUM( Program_Balance_Amount__c ) pba
                                          FROM pymt__Shopping_Cart_Item__c
                                          WHERE pymt__Payment__c IN :pymtsToCalc AND pymt__Payment__c != null AND pymt__Payment__r.pymt__Status__c != 'Completed' 
                                          GROUP BY pymt__Payment__c ];

            //Update each payment with the sum from the child shopping cart items
            for ( AggregateResult ar : ars ) {
                pymts.put((Id)ar.get('pymt'),  new pymt__PaymentX__c (
                    Id                  = (Id)ar.get('pymt'),
                    Gross_Amount__c     = (Decimal)ar.get('ga'),
                    pymt__Amount__c     = (Decimal)ar.get('ta'),
                    pymt__Discount__c   = (Decimal)ar.get('da'),
                    pymt__Tax__c        = (Decimal)ar.get('taxa'),
                    Program_Balance_Amount__c = (Decimal)ar.get('pba')
                ) );  
                //If payment total amount == $0 -- set status to "Completed"
                if((Decimal)ar.get('ta') == 0){ 
                    pymts.get((Id)ar.get('pymt')).pymt__Status__c = 'Completed';     
                    pymts.get((Id)ar.get('pymt')).pymt__Date__c = Date.Today();    
                }
            }
            

            //For EP Only: Update Deposit amount paid: 
            List<AggregateResult> arsDeposit = [ SELECT pymt__Payment__c pymt, SUM( Deposit_Amount_Paid__c ) depa
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE pymt__Payment__c IN :pymtsToCalc AND pymt__Payment__c != null AND pymt__Payment__r.pymt__Status__c != 'Completed' 
                                                    AND Deposit_Paid__c = true
                                                    AND Type__c = 'EP Program Balance'
                                                    GROUP BY pymt__Payment__c ];

            //Update each payment with the sum from the child shopping cart items
            for ( AggregateResult ar : arsDeposit ) {
                pymts.get((Id)ar.get('pymt')).Deposit_Amount_Paid__c = (Decimal)ar.get('depa');
            }
        }

        if ( invoicesToCalc.size() > 0 ) {
            //Since in after context, check for totals of all payment records which have been updated
            List<AggregateResult> arsInvoices = [ SELECT Invoice__c inv, SUM( Total_Amount__c ) ta, SUM( Tax_Amount__c ) taxa
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE Invoice__c IN :invoicesToCalc AND Invoice__c != null AND Type__c NOT IN ('Manual', 'Deposit Credit', null)// AND Type__c NOT IN ('EP Program Deposit', 'Deposit Credit')
                                                    GROUP BY Invoice__c ];


            //Update each invoice with the sum from the child shopping cart items
            for ( AggregateResult ar : arsInvoices ) {
                invoices.put((Id)ar.get('inv'),  new Invoice__c (
                    Id                      = (Id)ar.get('inv'),
                    Total_Amount_SCI__c     = (Decimal)ar.get('ta'),
                    //Gross_Amount__c         = (Decimal)ar.get('ga'),
                    //Discount_Amount__c      = (Decimal)ar.get('da')
                    Tax_Amount__c           = (Decimal)ar.get('taxa')
                ) );   
            }
            //Set invoice gross amount (all shopping cart items except EP Program Deposit): 
            List<AggregateResult> arsGrossAmount = [ SELECT Invoice__c inv, SUM( Gross_Amount__c ) ga
                                                    FROM pymt__Shopping_Cart_Item__c
                                                    WHERE Invoice__c IN :invoicesToCalc AND Invoice__c != null AND Type__c NOT IN ('EP Program Deposit', 'Manual', 'Deposit Credit', null)
                                                    GROUP BY Invoice__c ];
            
            for ( AggregateResult ar : arsGrossAmount ) {
                invoices.get((Id)ar.get('inv')).Gross_Amount__c = (Decimal)ar.get('ga'); 
            }
            
            //Update invoice if discount status applies with Discount Amount
            List<AggregateResult> arsDiscount = [ SELECT Invoice__c inv, SUM( Discount_Amount__c ) da
                                          FROM pymt__Shopping_Cart_Item__c
                                          WHERE Invoice__c IN :invoicesToCalc AND Invoice__c != null AND (Discount_Status__c LIKE '%Applied Automatically%' OR Discount_Status__c LIKE '%Discount Approved%')
                                          GROUP BY Invoice__c ];
            for ( AggregateResult ar : arsDiscount ) {
                if(ar.get('da') != null){
                    invoices.get((Id)ar.get('inv')).Discount_Amount__c = (Decimal)ar.get('da');    
                }
            }
            
            List<AggregateResult> arsProgramBalance = [ SELECT Invoice__c inv, pymt__Payment_Completed__c pymtcom, SUM( Total_Amount__c ) ta
                                                        FROM pymt__Shopping_Cart_Item__c
                                                        WHERE Invoice__c IN :invoicesToCalc AND Invoice__c != null AND pymt__Payment_Completed__c = true 
                                                        AND discount__c != null AND discount__r.type__c = 'Program Balance'
                                                        GROUP BY Invoice__c, pymt__Payment_Completed__c];


            for ( AggregateResult arp : arsProgramBalance ) { 
                invoices.get((Id)arp.get('inv')).Program_Balance_Amount__c = Math.abs((Decimal)arp.get('ta')); 
            }

            List<AggregateResult> arsPaid = [ SELECT Invoice__c inv, pymt__Payment_Completed__c pymtcom, SUM( Gross_Amount__c ) ga, SUM( Total_Amount__c ) ta, SUM( Tax_Amount__c ) taxa, SUM( Discount_Amount__c ) da
                                            FROM pymt__Shopping_Cart_Item__c
                                            WHERE Invoice__c IN :invoicesToCalc AND Invoice__c != null AND pymt__Payment_Completed__c = true
                                            GROUP BY Invoice__c, pymt__Payment_Completed__c];


            for ( AggregateResult arp : arsPaid ) { 
                invoices.get((Id)arp.get('inv')).Paid_Amount_SCI__c = (Decimal)arp.get('ta') + (invoices.get((Id)arp.get('inv')).Program_Balance_Amount__c != null ? invoices.get((Id)arp.get('inv')).Program_Balance_Amount__c : 0) ; 
            }
                
            /*List<AggregateResult> arsDeposit = [ SELECT Invoice__c inv, SUM( Gross_Amount__c ) ga
                                                 FROM pymt__Shopping_Cart_Item__c 
                                                 WHERE Invoice__c IN :invoicesToCalc AND Invoice__c != null AND Type__c IN ('EP Program Deposit')
                                                 GROUP BY Invoice__c ];

            for ( AggregateResult ard : arsDeposit ) { 
                invoices.put((Id)ard.get('inv'),  new Invoice__c (
                    Id                      = (Id)ard.get('inv'),
                    Program_Deposit__c      = (Decimal)ard.get('ga')
                ) ); 
            }*/
        }

        if ( pymtsToHold.size() > 0 ) {
            for ( Id pId : pymtsToHold )
                if ( pymts.containsKey(pId) )
                    pymts.get( pId ).Pending_Approval__c = true;
                else
                    pymts.put( pId, new pymt__PaymentX__c( ID = pId, Pending_Approval__c = true ) );
        }
        if ( pymtsToUnhold.size() > 0 ) {
            for ( Id pId : pymtsToUnhold )
                if ( pymts.containsKey(pId) )
                    pymts.get( pId ).Pending_Approval__c = false;
                else
                    pymts.put( pId, new pymt__PaymentX__c( ID = pId, Pending_Approval__c = false ) );
        }

        if ( pymts.values().size() > 0 )
            update pymts.values();

        if ( invoices.values().size() > 0 )
            update invoices.values();
        
        return dmlWrapper; 
    }

}