global without sharing class PaymentComplete_TDTM extends hed.TDTM_Runnable {

    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper();

        Set<Id> pymts               = new Set<Id>();
        Set<Id> invoicesToCalc      = new Set<Id>();

        if( triggerAction == hed.TDTM_Runnable.Action.AfterInsert || triggerAction == hed.TDTM_Runnable.Action.AfterUpdate ) {
            for( pymt__PaymentX__c pymt : ( List<pymt__PaymentX__c> )newList ) {
                //System.debug(' *** pymts status: *** ' + pymt.pymt__Status__c);
                if ( pymt.pymt__Status__c == 'Completed' ) 
                    pymts.add( pymt.Id );
                    invoicesToCalc.add( pymt.Invoice__c );
            }
        }
        //System.debug(' *** pymts size: *** ' + pymts.size());
        Map<Id, Invoice__c> invoices = new Map<Id, Invoice__c>(); //List of all invoice records which will be updated
        if ( pymts.size() > 0 ) {
            
            for ( Invoice__c inv : [SELECT Id, Invoice_Status__c , (SELECT Id FROM shopping_cart_items__r WHERE pymt__Payment_Completed__c = false) 
                                    FROM Invoice__c 
                                    WHERE Id IN :invoicesToCalc AND Invoice_Status__c IN ('Open-Personal','Open-Group')] ) {
                
                if ( inv.shopping_cart_items__r.size() == 0 ) {
                    if ( inv.Invoice_Status__c == 'Open-Personal' )  
                        invoices.put(inv.id,  new Invoice__c (
                            Id                  = inv.id,
                            Invoice_Status__c   = 'Closed-Personal'
                        ) );
                    else if ( inv.Invoice_Status__c == 'Open-Group' )
                        invoices.put(inv.id,  new Invoice__c (
                            Id                  = inv.id,
                            Invoice_Status__c   = 'Closed-Group'
                        ) );
                }
            }
            
        }

        //System.debug(' *** invoices size: *** ' + invoices.values().size());
        if ( invoices.values().size() > 0 )
            update invoices.values();
        
        return dmlWrapper; 
    }
}