/**
 * @description Maps Contact's "Other Address" to related Account's Billing Address
 * <AUTHOR>
 * @version 1.0
 * @created 2020-04-01
 * @modified 2020-04-01
 */
global without sharing class SyncAddresses_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of records from trigger new 
     * @param oldList the list of records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Object
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        //List of Accounts to check for further Updates:
        List<Account> acctsToCheck = new List<Account>();  
        //Set of related contact Ids
        Set<Id> contIds = new Set<Id>(); 
        //Set of related Account Ids
        Set<Id> acctIds = new Set<Id>(); 
        //List of Contacts to check for re-mapping: 
        List<Contact> contsToCheck = new List<Contact>(); 

        //BEFORE INSERT CONTEXT 
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert){
            //for all admin accounts with related contacts: map the contact's "Other Address" to the account's "Billing Address"
            for(Account acct : (List<Account>) newList){
                if(acct.recordTypeId == AccountService.AdminRTId && acct.hed__Primary_Contact__c != null){
                    acctsToCheck.add(acct); 
                    contIds.add(acct.hed__Primary_Contact__c); 
                }
            }
        }

        //AFTER UPDATE CONTEXT: if "Other Address" on the contact is updated, update related account's "Billing Address"
        if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){ 
            //Map of contact Id to old contact record (state from before the update): 
            Map<Id, Contact> oldMap = new Map<Id, Contact>((List<Contact>) oldList); 
            //for all contacts that have updated their "Other Address", Sync "Other Address" to related Admin Account's "Billing Address"
            for(Contact cont : (List<Contact>) newList){
                Contact oldCont = oldMap.get(cont.Id); 
                if(cont.AccountId != null && (cont.OtherStreet != oldCont.OtherStreet || cont.OtherCity != oldCont.OtherCity || cont.OtherState != oldCont.OtherState || cont.OtherPostalCode != oldCont.OtherPostalCode || cont.OtherCountry != oldCont.OtherCountry)){
                    contsToCheck.add(cont); 
                    acctIds.add(cont.AccountId); 
                }
            }
        }

        if(acctsToCheck.size() > 0){
            //contact fields to query: 
            Set<String> contFieldsToQuery = new Set<String>{'OtherAddress', 'OtherStreet', 'OtherCity', 'OtherState', 'OtherPostalCode', 'OtherCountry'}; 
            //Map contact Id to contact record: 
            Map<Id, Contact> contIdToContactMap = new Map<Id, Contact>(ContactService.queryFieldsInContacts(contIds, contFieldsToQuery)); 

            //Update account's billing address if contact's "other address" is not null: 
            for(Account acct :acctsToCheck){
                Contact relatedCont = contIdToContactMap.get(acct.hed__Primary_Contact__c); 
                if(relatedCont.OtherAddress != null){
                    mapOtherAddressToBillingAddress(acct, relatedCont); 
                }
            }
        }

        if(acctIds.size() > 0){
            //account fields to query: 
            Set<String> acctFieldsToQuery = new Set<String>{'BillingStreet', 'BillingCity', 'BillingState', 'BillingPostalCode', 'BillingCountry', 'IsExcludedFromRealign', 'Run_Territory_Assignment__c', 'RecordTypeId'}; 
            //Map account Id to account record: 
            Map<Id, Account> acctIdToAccountMap = new Map<Id, Account>(AccountService.queryFieldsInAccounts(acctIds, acctFieldsToQuery)); 
 
            for(Contact cont :contsToCheck){
                Account acct = acctIdToAccountMap.get(cont.AccountId); 
                //Only update if this is admin account 
                if(acct.RecordTypeId == AccountService.AdminRTId){
                    //Update Run_Territory_Assignment__c to true if BillingCountry or Billing Province has been updated and IsExcludedFromRealign is is false: 
                    if(!acct.IsExcludedFromRealign && (acct.BillingCountry != cont.OtherCountry || acct.BillingState  != cont.OtherState)){
                        acct.Run_Territory_Assignment__c = true; 
                    }
                    //Update account's billing address:
                    mapOtherAddressToBillingAddress(acct, cont); 
                }
            }

            dmlWrapper.objectsToUpdate.addAll(acctIdToAccountMap.values()); 
        }

        return dmlWrapper; 
    }
    /**
     * @description map Contact's "Other Address" to Account's "Billing Address"
     * @param acct Account record 
     * @param relatedCont Contact record 
     */
    private void mapOtherAddressToBillingAddress(Account acct, Contact relatedCont){
        acct.BillingStreet = relatedCont.OtherStreet; 
        acct.BillingCity = relatedCont.OtherCity; 
        acct.BillingState = relatedCont.OtherState; 
        acct.BillingPostalCode = relatedCont.OtherPostalCode; 
        acct.BillingCountry = relatedCont.OtherCountry; 
    }
}