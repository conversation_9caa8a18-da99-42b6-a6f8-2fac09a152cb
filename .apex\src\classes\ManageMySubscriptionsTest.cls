@isTest
private class ManageMySubscriptionsTest {
    
    // Test email search
    @isTest
    static void validateLoad_NoParameters() {
        
        Test.startTest();
        
        // Instantiate controller
        ManageMySubscriptions mms = new ManageMySubscriptions();
        
        // Test when email is not found
        mms.emailSearch = '<EMAIL>';
        //mms.findContact();
        
        // Test when email is found
        mms.emailSearch = '<EMAIL>';
        //mms.findContact();
        
        Test.stopTest();
    }
    
    // Test when contact ID is passed-in
    @isTest
    static void validateLoad_ContactIdParam() {
        
        // Grab IDs for URL parameters
        String hashId = [SELECT Id, HASH_Id__c FROM Contact WHERE LastName = :'TestLast'].HASH_Id__c;
        String listId = [SELECT Id FROM Subscription__c WHERE Subscription_ID__c = :'Test1'].Id;
        
        Test.startTest();
        
        // Set URL parameters
        Test.setCurrentPageReference(new PageReference('Page.ManageMySubscriptions'));
        System.currentPageReference().getParameters().put('cId', hashId);
        System.currentPageReference().getParameters().put('sId', listId);
        
        // Instantiate controller
        ManageMySubscriptions mms = new ManageMySubscriptions();
        
        // Loop over memberships and toggle subscription
        for (Subscription_Membership__c lm:mms.subscrItems.values()) {
            if (lm.Id != null) lm.Subscription_Status__c = 'Subscribed';
            else lm.Subscription_Status__c = 'Unsubscribed';
        }
        
        // Give reason and update subscriptions
        mms.unsubscribeReason = 'test reason';
        mms.unsubscribeFromAll();
        mms.updateSubscription();
        
        mms.selectedGroup = 'Group1';
        mms.updateSubscriptionList();
        
        Test.stopTest();
    }
    
    
    // Test when contact ID is passed-in
    @isTest
    static void validateLoad_LeadIdParam() {
        
        // Grab IDs for URL parameters
        String hashId = [SELECT Id, HASH_Id__c FROM Lead WHERE LastName = :'TestLastLead'].HASH_Id__c;
        String listId = [SELECT Id FROM Subscription__c WHERE Subscription_ID__c = :'Test1'].Id;
        
        Test.startTest();
        
        // Set URL parameters
        Test.setCurrentPageReference(new PageReference('Page.ManageMySubscriptions'));
        System.currentPageReference().getParameters().put('lId', hashId);
        System.currentPageReference().getParameters().put('sId', listId);
        
        // Instantiate controller
        ManageMySubscriptions mms = new ManageMySubscriptions();
        
        
        // Give reason and update subscriptions
        mms.unsubscribeReason = 'test reason';
        
        Test.stopTest();
    }
    
    
    // Test when contact and subscription URL parameters are passed-in
    @isTest
    static void validateLoad_ContactAndSubscriptionIdParams() {
        
        // Grab IDs for URL parameters
        String hashId = [SELECT Id, HASH_Id__c FROM Contact WHERE LastName = :'TestLast'].HASH_Id__c;
        String listId = [SELECT Id FROM Subscription__c WHERE Subscription_ID__c = :'Test1'].Id;
        
        Test.startTest();
        
        // Set URL parameters
        Test.setCurrentPageReference(new PageReference('Page.ManageMySubscriptions'));
        System.currentPageReference().getParameters().put('cId', hashId);
        System.currentPageReference().getParameters().put('sId', listId);
        
        // Instantiate controller
        ManageMySubscriptions mms = new ManageMySubscriptions();
        
        // Call update methods
        mms.updateSubscription();
        mms.updatePreferences();
        mms.globalUnsubscribe();
        
        Test.stopTest();
    }
    
    // Test invalid contact id
    @isTest
    static void validateLoad_ContactIdParam_Error() {
        
        Test.startTest();
        
        // Set URL parameter
        Test.setCurrentPageReference(new PageReference('Page.ManageMySubscriptions'));
        System.currentPageReference().getParameters().put('cId', 'abc');
        
        // Instantiate controller
        ManageMySubscriptions mms = new ManageMySubscriptions();
        
        Test.stopTest();
    }
    
    // Test other parts of subscription center not covered in earlier tests
    @isTest
    static void testOther() {
        
        Test.startTest();
        
        // Instantiate controller and call getUnsubReasons method
        ManageMySubscriptions mms = new ManageMySubscriptions();
        mms.getUnsubReasons();
        
        Test.stopTest();
    }
    
    // Set-up test data
    @testSetup
    static void setup() {
        
        // Create/insert contact
        Contact con = new Contact(FirstName = 'TestFirst', LastName = 'TestLast', Email = '<EMAIL>', HASH_Id__c = 'test');
        insert con;
        
        Lead ld = new Lead(FirstName = 'TestFirst', LastName = 'TestLastLead', Email = '<EMAIL>', HASH_Id__c = 'test', Company = 'TestCo');
        insert ld;
        
        // Create lists
        Subscription__c lst1 = new Subscription__c(Subscription_ID__c = 'Test1', Name = 'Test1', Active__c = true, Group__c = 'Executive Programs');
        Subscription__c lst2 = new Subscription__c(Subscription_ID__c = 'Test2', Name = 'Test2', Active__c = true, Group__c = 'Recruitment and Admissions');
        
        insert new List<Subscription__c>{lst1, lst2}; // Insert lists
        
        // Create list membership for second list
        Subscription_Membership__c lm = new Subscription_Membership__c(Subscription__c = lst2.Id, Contact__c = con.Id, Subscribed_Date__c = Date.TODAY().addDays(-1), Subscription_Status__c = 'Unsubscribed', Unsubscribed_Date__c = Date.TODAY());
        insert lm;
        
        Subscription_Membership__c lm2 = new Subscription_Membership__c(Subscription__c = lst2.Id, Lead__c = ld.Id, Subscribed_Date__c = Date.TODAY().addDays(-1), Subscription_Status__c = 'Unsubscribed', Unsubscribed_Date__c = Date.TODAY());
        insert lm2;
    }
}