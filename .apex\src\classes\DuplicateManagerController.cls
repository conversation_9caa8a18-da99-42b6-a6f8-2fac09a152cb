public with sharing class DuplicateManagerController {

	public class FieldWrapper {

		@AuraEnabled public String fieldName;
		@AuraEnabled public String label;
		@AuraEnabled public String fieldType;

		private Schema.DescribeFieldResult field;

		public FieldWrapper( String fieldName, String sObjectName ) {

 			this.field = Schema.getGlobalDescribe().get( sObjectName ).getDescribe().fields.getMap().get( fieldName ).getDescribe();

 			this.fieldName	= this.field.getName();
			this.label 		= this.field.getLabel();
			this.fieldType 	= this.field.getType().name();

		}

    }

    @AuraEnabled
    public static String checkPair ( String recordId ) {

        return [SELECT Status__c FROM Object_Merge_Pair__c WHERE ID = :recordId].Status__c;

    }

    @AuraEnabled
    public static List<FieldWrapper> getColumns ( String fields, String lookupField, String sObjectName ) {
        
        //Need to get the target SObject API name
        String targetSObjectName = lookupField == null ?
                                   sObjectName : 
                                   Schema.getGlobalDescribe().get( sObjectName ).getDescribe().fields.getMap().get( lookupField ).getDescribe().getReferenceTo()[0].getDescribe().getName();

        List<FieldWrapper> columns = new List<FieldWrapper>();

        for ( String field : fields.split(',') )
            columns.add( new FieldWrapper( field, targetSObjectName ) );

        return columns;


    }

    @AuraEnabled
    public static List<SObject> getDuplicates ( String fields, String lookupField, String recordId, String sObjectName ) {
        
        //If there is no lookup field specified, the target record is the record the component is on. Otherwise, target component is in lookup field and Id can be gathered by querying it
        Id targetRecordId = lookupField == null ? 
                            recordId :
                            (Id) database.query('SELECT ' + lookupField + ' FROM ' + sObjectName + ' WHERE ID = :recordId' )[0].get( lookupField );

        //If lookup is null, return empty list
        if ( targetRecordId == null ) 
            return new List<SObject>();

        //Get all duplicate records from any duplicate record sets containing this ID
        List<DuplicateRecordItem> duplicateSets = [SELECT DuplicateRecordSetId FROM DuplicateRecordItem WHERE RecordId = :targetRecordId ];
        Set<Id> duplicateSetIds = new Set<Id>();
        for ( DuplicateRecordItem ds : duplicateSets )
            duplicateSetIds.add( ds.DuplicateRecordSetId );

        List<DuplicateRecordItem> duplicates = [SELECT RecordId FROM DuplicateRecordItem WHERE DuplicateRecordSetId IN :duplicateSetIds ];
        Set<Id> duplicateIds = new Set<Id>();

        //If there are no duplicates of the given object, return an empty list
        if ( duplicates.size() == 0 )
            return new List<SObject>(); 

        //Getting the target SOBject type by matching duplicate record sets
        String targetSObjectName = lookupField == null ?
                                   sObjectName : 
                                   Schema.getGlobalDescribe().get( sObjectName ).getDescribe().fields.getMap().get( lookupField ).getDescribe().getReferenceTo()[0].getDescribe().getName();

        //Add all duplicate records from the duplicate record set to a set so it can be added to a query
        for ( DuplicateRecordItem dri : duplicates )
            duplicateIds.add( dri.recordId );

        //Query for all records meeting the criteria
        return database.query( 'Select Id, Name, ' + fields + ' FROM ' + targetSObjectName + ' WHERE Id IN :duplicateIds' );

    }

    @AuraEnabled
    public static Object_Merge_Pair__c mergeRecords ( Id victimId, Id masterId ) {

        Object_Merge_Pair__c pairToInsert = new Object_Merge_Pair__c ( Master_Id__c = masterId, 
                                                                       Victim_Id__c = victimId,
                                                                       Error_Reason__c = '',
                                                                       Status__c = '' );

		pairToInsert.Pair_Unique_Id__c = String.valueOf(pairToInsert.Master_ID__c) + String.valueOf(pairToInsert.Victim_ID__c); 
        
        if ( new List<Object_Merge_Pair__c>([SELECT Id FROM Object_Merge_Pair__c WHERE Pair_Unique_Id__c = :pairToInsert.Pair_Unique_Id__c]).size() > 0)
            pairToInsert.Status__c = 'Retry';

        Database.upsert( pairToInsert, Object_Merge_Pair__c.fields.Pair_Unique_Id__c );
        Object_Merge_Pair__c insertedPair = [SELECT Id, Error_Reason__c, Status__c FROM Object_Merge_Pair__c WHERE Id = :pairToInsert.Id ];

        return insertedPair;
        
    }


}