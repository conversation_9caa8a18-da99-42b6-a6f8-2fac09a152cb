@IsTest
private class EventCartController_TEST {
	@TestSetup
	private static void testDataSetup() {
		// Create a test Account
		Account account = new Account(
				Name = 'Test Account'
		);
		insert account;

		// Create a test contact
		Contact contact = new Contact(
				FirstName = 'Test',
				LastName = 'Contact',
				Email = '<EMAIL>',
				AccountId = account.Id
		);
		insert contact;

		evt__Special_Event__c specialEvent = new evt__Special_Event__c(
				Name = 'Test Event',
				evt__Agenda_HTML__c = 'Test Agenda',
				evt__Publish_To__c = 'Public Events',
				Start_Local__c = Datetime.now(),
				End_Local__c = Datetime.now().addDays(1)
		);
		insert specialEvent;

		evt__Event_Fee__c fee = new evt__Event_Fee__c();
		fee.Name = 'special event fee';
		fee.evt__Event__c = specialEvent.Id;
		fee.evt__Amount__c = 0.0;
		fee.evt__Active__c = true;
		fee.Type__c = 'Standard';

		insert fee;

		evt__Session__c testSession1 = new evt__Session__c(Name = 'Test Session 1', evt__Max_Attendees__c = 5, evt__Event__c = specialEvent.Id);
		evt__Session__c testSession2 = new evt__Session__c(Name = 'Test Session 2', evt__Max_Attendees__c = 5, evt__Event__c = specialEvent.Id);
		insert new List<evt__Session__c>{testSession1, testSession2};

		evt__Speaker__c speaker = new evt__Speaker__c(
				Name = 'Test Speaker',
				evt__Event__c = specialEvent.Id,
				Contact__c = contact.Id,
				evt__Title__c = 'Test Title',
				evt__Company__c = 'Test Company',
				evt__Short_Bio__c = 'Test Bio'
		);
		insert speaker;

		pymt__Shopping_Cart_Item__c item = new pymt__Shopping_Cart_Item__c();
		item.Name = 'Test Item';
		item.pymt__Contact__c = contact.Id;
		item.Event_Fee__c = fee.Id;
		item.pymt__Unit_Price__c = 0.0;
		item.Event_Discount_Amount__c = 0.0;
		item.pymt__Quantity__c = 1;
		item.type__c = 'Event Registration';

		insert item;

		evt__Attendee__c at = new evt__Attendee__c();
		at.evt__Event_Fee__c = fee.Id;
		at.evt__Contact__c = contact.Id;
		at.evt__Invitation_Status__c = 'Registered';
		at.evt__Registration_Type__c = 'General: In-Person + Book Admission';
		at.Shopping_Cart_Item__c = item.Id;
		at.evt__Event__c = specialEvent.Id;
		at.evt__Reg_Email__c = '<EMAIL>';
		at.Industry__c = 'Technology';

		insert at;
	}

	@IsTest
	static void testGetEventDetailsAndSpeakerInfo() {
		evt__Special_Event__c specialEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];
		evt__Speaker__c speaker = [SELECT Id FROM evt__Speaker__c LIMIT 1];

		Test.startTest();
		Map<String, Object> result = EventCartController.getEventDetailsAndSpeakerInfo(specialEvent.Id);
		Test.stopTest();
		System.assertEquals(specialEvent.Id, ((evt__Special_Event__c)result.get('eventDetails')).Id);
		System.assertEquals(speaker.Id, ((List<evt__Speaker__c>)result.get('speakerInfo'))[0].Id);
	}

	@IsTest
	static void testGetEventSessions() {
		evt__Special_Event__c testEvent = [SELECT Id FROM evt__Special_Event__c LIMIT 1];

		// Call the method being tested
		List<evt__Session__c> result = EventCartController.getEventSessions(testEvent.Id);

		// Verify the results
		System.assertEquals(2, result.size(), 'Incorrect number of sessions returned');
	}

	@IsTest
	static void testGetSessionSeats() {
		evt__Session__c testSession = [SELECT Id, Name FROM evt__Session__c LIMIT 1];
		EventCartController.EventSessionWrapper testSessionWrapper = new EventCartController.EventSessionWrapper();
		testSessionWrapper.sessionId = testSession.Id;
		testSessionWrapper.sessionName = testSession.Name;
		List<EventCartController.EventSessionWrapper> sessionList = new List<EventCartController.EventSessionWrapper>();
		sessionList.add(testSessionWrapper);

		// Call the method being tested
		List<EventCartController.EventSessionWrapper> result = EventCartController.getSessionsSeats(sessionList);
		System.debug('result: ' + result);
	}

	@isTest
	static void testPositiveCases() {
		// Positive test cases
		EventCartController.EventSessionWrapper session = new EventCartController.EventSessionWrapper();
		session.sessionId = 'ABC123';
		session.sessionName = 'Sample Session';
		session.remainingSeats = 50.0;
		session.selectedSeats = 0;
	}

	@IsTest
	static void testGetAccessibilityValues() {
		// Create a test record
		evt__Attendee__c attendee = [SELECT Id, Accessibility_Requirements__c FROM evt__Attendee__c LIMIT 1];

		Test.startTest();
		List<String> accessibilityValues = EventCartController.getAccessibilityValues();
		Test.stopTest();
	}

	@IsTest
	static void testGetDietaryValues() {
		evt__Attendee__c attendee = [SELECT Id, Dietary_Restrictions__c FROM evt__Attendee__c LIMIT 1];

		Test.startTest();
		List<String> dietaryValues = EventCartController.getDietaryValues();
		Test.stopTest();
	}
}