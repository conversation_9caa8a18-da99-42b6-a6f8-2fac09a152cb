/**
 * @description Self-Registration Controller
 * <AUTHOR>
 * @version 1.0
 * @modified 2020-06-10
 * <AUTHOR> @Modified: 2020-07-01 : UTR-1846 : Add Custom Contact Fields to the Self-Registration Form, @version 2
 */
global without sharing class LightningSelfRegisterController {

    // Class variables 
    public static final String CHECKEMAILURL = 'CheckPasswordResetEmail';
    public static final String SELFREGISTER = 'SelfRegister';
    public static final String FORGOTPASSWORD = 'ForgotPassword';
    public static final String DUPERRORMESSAGE = 'We found a duplicate contact entry.  <NAME_EMAIL> for assistance in activating your account.';
    public static final List<String> profileNames = new List<String>{'Applicant Community User', 'Student Community User'}; 
    
    /**
     * @description Response Wrapper Class
     */
    public class Response{
        @AuraEnabled public Boolean hasError = false; 
        @AuraEnabled public String validationMessage = ''; 
        @AuraEnabled public String redirectURL = ''; 
        @AuraEnabled public Boolean displayValidation = false; 
        @AuraEnabled public String verificationID = ''; 
        @AuraEnabled public Contact relatedContact = new Contact(); 
        @AuraEnabled public User user = new User(); 
        @AuraEnabled public String verificationAccount = ''; 
        @AuraEnabled public String verificationMethod = '';
    }

    /**
     * @description User Wrapper Class
     */
    public class UserWrapper{
        @AuraEnabled public String firstName; 
        @AuraEnabled public String lastName; 
        @AuraEnabled public String email; 
        @AuraEnabled public String phone; 
        @AuraEnabled public String timeZone; 
        @AuraEnabled public String password; 
        @AuraEnabled public String confirmPassword; 
        @AuraEnabled public String contactAddnlFields; 
        @AuraEnabled public String cookieId; 
    }

    
    /**
     * @description registers user to community
     * @param uw user wrapper containing user information from LWC
     */
    @AuraEnabled
    public static Response selfRegister(String uwString) {
        Response res = new Response(); 
        UserWrapper uw = (UserWrapper) JSON.deserialize(uwString, UserWrapper.class); 
        Savepoint sp = Database.setSavepoint();

        try {  
            String networkId = Network.getNetworkId();
    
            //Validate input fields from client: 
            validateRegistration(uw, res); 
            if(res.hasError) return res; 
 
            //Check to see if user already exists in Salesforce: 
            List<User> duplicateUsers = [SELECT ID, email, isActive, FederationIdentifier FROM User WHERE Email = :uw.email AND Profile.Name IN :profileNames ORDER BY isActive DESC, CreatedDate DESC LIMIT 1]; 

            /*If the user record exists in Salesforce: 
             * Case (1): If user is active and has a federation ID, redirect to SSO Page
             * Case (2): If User is active but does not have a federation ID, re-direct user to Forgot Password Page 
             *If user record exists in Salesforce: 
             * Case(3): If user is inactive, activate user and redirect to forgot password page 
             */ 
            if(duplicateUsers.size() > 0){
                if(duplicateUsers[0].isActive){
                    handleActiveUser(duplicateUsers[0], res); 
                }else{
                    handleInactiveUser(duplicateUsers[0], res);
                } 
            /* If there are no duplicate users, Check for duplicate contact records:
             * Case(4): If there is a duplicate contact with related user record, if user is active and has a federation ID, redirect to SSO Page
             * Case(5): If there is a duplicate contact with related user record, if user is active but does not have a federation ID, redirect to Forgot Password Page
             * Case(6): If there is a duplicate contact, with related user record, if user is inactive, 
             *          activate user and redirect to forgot password page TODO: May need to add some sort of verification step
             * Case(7): If there is a duplicate contact record with no related user, create new community user and associated it to the recently created contact record and log user into communities TODO: look into this scenario
             */
            }else{
                 //Check if there are duplicate contacts with the same email address: 
                Contact con = new Contact(FirstName = uw.firstname, LastName = uw.lastname, phone = uw.phone, hed__AlternateEmail__c=uw.email ,hed__WorkEmail__c= uw.email, hed__UniversityEmail__c= uw.email, Email= uw.email);
				Contact conToInsert = new Contact(FirstName = uw.firstname, LastName = uw.lastname, phone = uw.phone, hed__AlternateEmail__c=uw.email, Email= uw.email);
			   
                //Map duplicate contact Id to duplicate contact
                Map<Id, Contact>duplicateContactMap = findDuplicateContacts(con);  
        
                //If there are duplicate contact records, Check if there are related community user records to these contacts: 
                if(duplicateContactMap.size() > 0){
                    //check if there are any other unique emails related to the duplicate contact records: 
                    if(duplicateContactMap.values().size() > 0) getDupContact(duplicateContactMap.values(), uw, res); 
                    
                    //Handle duplicate contact scenarios and redirect page as appropriate 
                    handleDuplicateContacts( res, uw, networkId, uw.cookieId); 
                
                /* Case(8): If there are no duplicate contacts or users, create new contact record and community user
                 */
                }else{ 
                    //Set preferred email on new contact record:
                    conToInsert.hed__Preferred_Email__c = 'Alternate Email'; 
                    //Update any additional contact field from self-registration form: 
                    if(!String.isBlank(uw.contactAddnlFields) && uw.contactAddnlFields != '""') handleContactAddnlFields(conToInsert, uw);
                    insert conToInsert; 
                    res.relatedContact = conToInsert; 
                    User u =  createNewUser(uw, networkId, res);  

                    //VALIDATE PASSWORD to ensure it follows org-wide or profile-based password policies in the current user's org:
                    validatePassword(u, uw.password, uw.confirmPassword);

                    //Send Verification Code to Email: 
                    res.verificationID = System.UserManagement.initSelfRegistration(Auth.VerificationMethod.EMAIL, u);
                    res.verificationMethod = 'EMAIL'; 
                    res.verificationAccount = uw.email; 
                    res.displayValidation = true; //Display validation code fields 
                }                   
            }
        }
        catch (Exception ex) {
            Database.rollback(sp);
            res.hasError = true; 
            res.validationMessage = ex.getMessage();           
        }

        return res; 
    }

    @AuraEnabled
    public static Response sendVerificationCode(String userJSON, String verificationMethod){
        Response res = new Response(); 
        System.debug(userJSON); 
        User u = (User)JSON.deserialize(userJSON, User.class); 
        System.debug(u); 
        System.debug(u.profileID); 
        try{      
            //res.verificationID =  verificationMethod == 'EMAIL' ? System.UserManagement.initSelfRegistration(Auth.VerificationMethod.EMAIL, u) : System.UserManagement.initSelfRegistration(Auth.VerificationMethod.SMS, u);
            res.verificationID =  System.UserManagement.initSelfRegistration(Auth.VerificationMethod.EMAIL, u);
        }catch(Exception ex){
            res.hasError = true; 
            res.validationMessage = ex.getMessage(); 
        }
        return res; 
    }

    /**
     * @description Server-side field validations 
     * @param uw user wrapper containing user information from LWC
     * @param res Response of the controller
     */ 
    public static void validateRegistration(UserWrapper uw, Response res){
        if (uw.firstname == null || String.isEmpty(uw.firstname)) {
            responseHasError(res, Label.Site_FirstName_is_Required);
         }else if (uw.lastname == null || String.isEmpty(uw.lastname)) {
            responseHasError(res,Label.Site.lastname_is_required);
        }else if (uw.email == null || String.isEmpty(uw.email)) {
            responseHasError(res,Label.Site.email_is_required);
        }else if (uw.phone == null || String.isEmpty(uw.phone)) {
            responseHasError(res,Label.Site_Phone_is_Required);
        }else{ 
            if (uw.password == null || String.isEmpty(uw.password))
                responseHasError(res,Label.Site_Password_is_Required); 
            else if(uw.confirmPassword == null || String.isEmpty(uw.confirmPassword)) 
                responseHasError(res,Label.Site_ConfirmPassword_is_Required);
            else if(!String.isEmpty(uw.password) && !String.isEmpty(uw.confirmPassword)){
                if (!isValidPassword(uw.password, uw.confirmPassword)) {
                    responseHasError(res,Label.site.passwords_dont_match);
                }
            }
        } 
    }

    /**
     * @description Stamps error message to response 
     * @param res Response 
     * @param errorMessage validation message 
     */ 
    private static void responseHasError(Response res, String errorMessage){
        res.hasError = true; 
        res.validationMessage = errorMessage; 
    } 

     /**
     * @description validate that password and confirm password match 
     * @return boolean
     * @param password 
     * @param confirmPassword 
     */
    private static boolean isValidPassword(String password, String confirmPassword) {
        return password == confirmPassword;
    }

     /**
     * @description Handles cases where Community user exists in Salesforce and is active  
     * @param activeUser active user record
     * @param res response 
     */
    private static void handleActiveUser(User activeUser, Response res){
        //If User is active but does not have a federation ID, re-direct user to Forgot Password Page 
        if(String.isBlank(activeUser.FederationIdentifier)){
            res.redirectURL =  Site.getPathPrefix() + '/login/' + FORGOTPASSWORD;
        }else{
            res.redirectURL = Label.Community_SSO; 
        }
    }

     /**
     * @description Handles cases where Community user exists in Salesforce and is inactive  
     * @param inactiveUser inactive user record
     * @param res response 
     */
    private static void handleInactiveUser(User inactiveUser, Response res){
        //Update 10/11/2021: If the user is inactive, display a message to contact support.
        responseHasError(res,DUPERRORMESSAGE);
        //if the user is inactive, activate the user and redirect user to Forgot Password Page: 
        /*inactiveUser.isActive = true; 
        Update inactiveUser; 
        res.redirectURL =  Site.getPathPrefix() + '/login/' + FORGOTPASSWORD;*/
    }

     /**
     * @description checks if there are any duplicate contact records in Salesforce
     * @return Map<Id, Contact> map of duplicate contact id to contact record 
     * @param student user that is registering 
     */
    private static Map<Id, Contact> findDuplicateContacts(Contact student){
        Map<Id, Contact> duplicateContactMap = new Map<Id, Contact>(); 

        for(Datacloud.FindDuplicatesResult findDupeResult :Datacloud.FindDuplicates.findDuplicates(new List<Contact>{student})){
            for(Datacloud.DuplicateResult dupeResult: findDupeResult.getDuplicateResults()){
                for(Datacloud.MatchResult matchResult :dupeResult.getMatchResults()){
                    for(Datacloud.MatchRecord matchRecord: matchResult.getMatchRecords()){
                        duplicateContactMap.put(matchRecord.getRecord().Id, (Contact)matchRecord.getRecord()); 
                    }
                }
            }
        }
        return duplicateContactMap; 
    }

     /**
     * @description group together a list of email addresses from potential duplicate contacts 
     * @param dupContacts list of duplicate contacts 
     * @param email
     * @param res response 
     */
    public static void getDupContact(List<Contact> dupContacts, UserWrapper uw, Response res){

        for(Contact cont :dupContacts){
            System.debug('CONTACT: ' + cont);
            if(cont.email == uw.email || cont.phone == uw.phone){
                res.relatedContact = cont; 
                break; 
            }
        }
    }

     /**
     * @description handle scenarios where are there duplicate contacts found from user's input 
     * @param res Response 
     * @param uw user wrapper containing user information from LWC
     * @param networkID 
     * @param cookieId 
     */
    private static void handleDuplicateContacts(Response res, UserWrapper uw, String networkId, String cookieId){
        //If there are more than one user queried from the list, select the active user, if there are no active users from the list, select recently created user 
        List<User> dupUsers = [SELECT ID, isActive, FederationIdentifier FROM User WHERE ContactId = :res.relatedContact.Id AND Profile.Name IN :profileNames ORDER BY isActive DESC, CreatedDate DESC LIMIT 1]; 
        
        if(dupUsers.size() > 0){
            if(dupUsers[0].isActive){
                handleActiveUser(dupUsers[0], res);     
            }else{
                handleInactiveUser(dupUsers[0], res);          
            }
        }else{ 
            //If there are duplicate contact records but no duplicate community user records, create community user and associated it to the recently created contact record and log user into communities: 
            //Create community user associated to the contact: 
            User u = createNewUser(uw, networkId, res); 

            //VALIDATE PASSWORD TODO: validate password before user record is created
            validatePassword(u, uw.password, uw.confirmPassword);

            //Send Verification Code to Email if email matches duplicate contact's email: 
            //if(uw.email == res.relatedContact.email){
            res.verificationID = System.UserManagement.initSelfRegistration(Auth.VerificationMethod.EMAIL, u);
            res.verificationMethod = 'EMAIL'; 
            res.verificationAccount = uw.email;
            /*}else{//Send verification code to phone number
                res.verificationID = System.UserManagement.initSelfRegistration(Auth.VerificationMethod.SMS, u); 
                res.verificationMethod = 'SMS';
                res.verificationAccount = uw.phone;  
            }*/
            res.displayValidation = true; //Display validation code fields

            //Update contact record with additional fields from self-registration form: 
            handleContactAddnlFields(res.relatedContact, uw); 
            update res.relatedContact; 
        }
    }
    
    /**
     * @description validate that the community uses Site.com pages 
     * @return boolean
     * @param communityURL
     */
    @TestVisible
    private static boolean siteAsContainerEnabled(String communityUrl) {
        Auth.AuthConfiguration authConfig = new Auth.AuthConfiguration(communityUrl,'');
        return authConfig.isCommunityUsingSiteAsContainer();
    }

    /**
     * @description Indicates whether a given password meets the requirement 
     * specified by org-wide or profile-based password policies in the current user's org and encrypt password
     * @param u user 
     * @param password 
     * @param password 
     */ 
    private static void validatePassword(User u, String password, String confirmPassword) {
        if(!Test.isRunningTest()) {
            Site.validatePassword(u, password, confirmPassword);
        }
    }

    /**
     * @description formats new user record 
     * @param uw user wrapper containing user information from LWC
     * @param dupContact duplicate contact record ID
     * @param networkId
     */
    private static User createNewUser(UserWrapper uw, String networkId, Response res){
        //Query for Applicant Community Id: 
        Profile applicantCommunity = [SELECT Id FROM Profile WHERE Name = 'Applicant Community User' LIMIT 1]; 
        //Create community user associated to the contact: 
        User u = new User(UserName = uw.email, 
                          email = uw.email, 
                          FirstName = uw.firstname, 
                          LastName = uw.lastname, 
                          Phone = uw.phone, 
                          MobilePhone = uw.phone,
                          profileId = applicantCommunity.Id,
                          alias = String.valueOf(uw.firstname.substring(0, uw.firstname.length() >= 3 ? 3 : uw.firstname.length()) + uw.lastname.substring(0, uw.lastname.length() >= 3 ? 3 : uw.lastname.length())), 
                          //TimeZoneSidKey= uw.timeZone != null ? uw.timeZone : 'America/New_York', 
                          TimeZoneSidKey= 'America/New_York', 
                          LocaleSidKey = 'en_CA', 
                          LanguageLocaleKey='en_US', 
                          EmailEncodingKey = 'UTF-8', 
                          ContactId = res.relatedContact.Id);
        
        System.debug(u); 
        
        // If using site to host the community the user should not hit s1 after logging in from mobile.
        if(networkId != null && siteAsContainerEnabled(Network.getLoginUrl(networkId))) u.put('UserPreferencesHideS1BrowserUI',true);

        String nickname = ((uw.firstname != null && uw.firstname.length() > 0) ? uw.firstname.substring(0,1) : '' ) + uw.lastname.substring(0,1);
        nickname += String.valueOf(Crypto.getRandomInteger()).substring(1,7);
        u.put('CommunityNickname', nickname);

        res.user = u; 

        return u; 
    }
    /**
     * @description updates additional field-values on contact related to the user
     * Added for UTR-1846
     */ 
    public static void    handleContactAddnlFields(Contact relatedContact, UserWrapper uw){
        //Add any additional contact fields speciofied in the controller
        if ( !String.isBlank(uw.contactAddnlFields) && uw.contactAddnlFields != '""') {
            for( Object o : (List<Object>)JSON.deserializeUntyped(uw.contactAddnlFields) ) {
                Map<String, Object> conFieldMap = (Map<String, Object>)o;
            
                if(conFieldMap.get('selectedVal') != '' && conFieldMap.get('selectedVal') != null && conFieldMap.get('selectedVal') != 'undefined' ){
                    if ( conFieldMap.get('type') == 'checkbox' )
                        relatedContact.put(String.valueOf(conFieldMap.get('fieldName')), Boolean.valueOf(conFieldMap.get('selectedVal')) );
                    else 
                        relatedContact.put(String.valueOf(conFieldMap.get('fieldName')), String.valueOf(conFieldMap.get('selectedVal')) );
                }
            }
        }
    }
    
    /**
     * @description reassign shopping cart from newly registered user to contact
     * @param Id userId
     * @param String contactAddnlFields
     * @param String cookieId: needed to migrate all shopping carts from the user to the new contact
     * Added for UTR-1846
     */ 
    public static void reassignShoppingCart(User u, String cookieId){

        //For all shopping carts belonging to the newly registered user, reassign to the contact 
        if ( !String.isBlank(cookieId) ) {
            List<pymt__Shopping_Cart__c> scs = database.query('SELECT Id, Store_Configuration__c FROM pymt__Shopping_Cart__c WHERE Cart_Key__c LIKE \'' + cookieId + '.%\'');
            for ( pymt__Shopping_Cart__c sc : scs ) {
                sc.Cart_Key__c      = u.ContactId + '.' + sc.Store_Configuration__c;
                sc.pymt__Contact__c = u.ContactId;
                sc.Guest_Cart__c    = false;
                sc.OwnerId          = u.Id;
            }
            update scs;

            List<pymt__Shopping_Cart_Item__c> cartItems = database.query('SELECT ID FROM pymt__Shopping_Cart_Item__c WHERE pymt__SHopping_Cart__c IN :scs');
            for (pymt__Shopping_Cart_Item__c ci : cartItems )
                ci.OwnerId = u.Id;
            update cartItems;
        }
    }

    @AuraEnabled
    public static Response registerUser(String uwString, String verificationID, String code, String contactId, String verificationMethod, String sitecoreURLParam){
        System.debug(contactId); 
        Response res = new Response();
        try{
            
            UserWrapper uw = (UserWrapper) JSON.deserialize(uwString, UserWrapper.class);
            
            //if verification method = email, send an email with verification code, else send SMS 
            Auth.VerificationResult verificationResponse = System.UserManagement.verifySelfRegistration(Auth.VerificationMethod.EMAIL, verificationID, code, null); 
            System.debug(verificationResponse); 
            //if the account has been verified, log in as user: 
            if(verificationResponse.success == true || Test.isRunningTest()){
                System.debug(uw); 
                //Query for user record: 
                User u = [SELECT Id, ContactId from User where ContactId = :contactId LIMIT 1];
                //Reassign shopping cart/shopping cart items to contact: 
                reassignShoppingCart(u, uw.cookieId); 
                //Set user password: 
                System.setPassword(u.Id, uw.password); 
                System.debug('setting the password'); 
                //Login as user:
                //System.debug('*** sitecoreURLParam: *** ' + sitecoreURLParam);
                if( sitecoreURLParam == 'mbaapp' ) {
                    ApexPages.PageReference lgn = Site.login(uw.email, uw.password, '/s/apply');
                    //System.debug(' Login URL: *** ' + lgn.getURL());
                    res.redirectURL = lgn.getURL();    
                } else {
                    ApexPages.PageReference lgn = Site.login(uw.email, uw.password, null); 
                    res.redirectURL = lgn.getURL();	//return null;
                }
                
                
            }
            else if(verificationResponse.message == 'Invalid or expired value for the identifier field.' ||
                    verificationResponse.message == 'Token not valid')
            {
                res.hasError = true; 
                res.validationMessage = 'The verification code you have entered does not match our records. Please try again, or request a new code.'; 
            }
            else{
                res.hasError = true; 
                res.validationMessage = verificationResponse.message; 
            }
        }
        catch(Exception ex){
            res.hasError = true; 
            res.validationMessage = ex.getMessage(); 
        }
        return res; 
    }
}