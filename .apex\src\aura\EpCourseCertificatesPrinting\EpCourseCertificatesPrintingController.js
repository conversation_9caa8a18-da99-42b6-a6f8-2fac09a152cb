({
	printCertificates : function(component, event, helper) {
        component.set("v.showSuccessMessage",'Job is in progress and will be ready soon.');
        var action = component.get("c.printCourseCertificates");
        action.setParams({ "cOfferingRecordId": component.get("v.recordId")});
        action.setCallback(this, function(response) {
            var state = response.getState();
            if (state == 'SUCCESS') {
                component.set("v.showSuccess",true);
            } 
            else {
                console.log(state);
            }
        });
        $A.enqueueAction(action);
    }
})