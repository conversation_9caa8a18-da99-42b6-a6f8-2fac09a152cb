public class ManageMySubscriptions {
    
    public Contact con {get;set;}
    public Lead ld {get;set;}
    public Contact pageCon {get;set;}
    public Id subscriptionId {get;set;}
    public string groupName {get;set;}
    public map<Id, Subscription__c> subscrs {get;set;}
    public map<Id, Subscription_Membership__c> subscrItems {get;set;}
    public map<Id, memberWrp> subscrWrpItems {get;set;}
    public map<string, list<Subscription__c>> subscrsByGroup {get;set;}
    public map<string, string> groupNames {get;set;}
    
    public boolean displaySearch {get;set;}
    public string openPanel {get;set;}
    public string emailSearch {get;set;}
    public boolean displayConfirmation {get;set;}
    
    // unsubscription reasons
    public boolean emailComm {get;set;}
    public boolean phoneComm {get;set;}
    public string unsubscribeReason {get;set;} //picklist response
    public string singleUnsubscribeReason {get;set;} //picklist response
    public map<string, string> unsubComments {get;set;}
    public string unsubscribeComments {get;set;} //text response
    
    // page params
    public string selectedGroup {get;set;}
    
    public list<SelectOption> getUnsubReasons() {
        list<SelectOption> options = new list<SelectOption>();
        Schema.DescribeFieldResult fieldResult = Subscription_Membership__c.Unsubscribed_Reason__c.getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        
        for (Schema.PicklistEntry f : ple) {
            options.add(new SelectOption(f.getLabel(), f.getValue()));
        }
        return options;
    }
    
    public Subscription_Center_Setting__mdt getSetting() {
        Subscription_Center_Setting__mdt thisSetting = [SELECT Id, Background_Color__c, Body_Font_Size__c, Font_Family__c, Page_Header_Font_Size__c, Panel_Header_Font_Size__c,
                Primary_Color__c, Secondary_Color__c, Subscription_Center_Heading__c, Tertiary_Color__c, First_Name__c, Last_Name__c, Email__c, Phone__c, Confirmation_Message__c, 
                Show_Logo__c, Logo__c 
                FROM Subscription_Center_Setting__mdt WHERE Active__c = TRUE LIMIT 1];
        return thisSetting;
    }
    
    public ManageMySubscriptions() {
        ld = new Lead();
        con = new Contact();
        
        // retrieve subscription Id
        subscriptionId = ApexPages.currentPage().getParameters().get('sId');
        if (!string.isBlank(subscriptionId)) openPanel = 'item1';
        else openPanel = 'item2';
        
        // retrieve contact by Id or search by email
        if (string.isBlank(ApexPages.currentPage().getParameters().get('cId'))) { // && string.isBlank(ApexPages.currentPage().getParameters().get('e'))) {
            displaySearch = true;
        } else {
            list<Contact> cons = new list<Contact>();
            list<Lead> leads = new list<Lead>();
            if (string.isNotBlank(ApexPages.currentPage().getParameters().get('cId'))) {
                string cId = ApexPages.currentPage().getParameters().get('cId');
                cons = [SELECT Id, FirstName, LastName, Email, Phone, DoNotCall, HasOptedOutOfEmail, hed__Do_Not_Contact__c, Needs_MC_Subscription_Update__c  FROM Contact WHERE Hash_Id__c = :cId OR Id = :cId];
            } else if (string.isNotBlank(ApexPages.currentPage().getParameters().get('lId'))) {
                string lId = ApexPages.currentPage().getParameters().get('lId');
                leads = [SELECT Id, FirstName, LastName, Email, Phone, DoNotCall, HasOptedOutOfEmail, Do_Not_Contact__c, Needs_MC_Subscription_Update__c FROM Lead WHERE Hash_ID__c =:lId OR Id = :lId]; 
            }
            
            if (cons.size() > 0 || leads.size() > 0) {
                if (cons.size() > 0) {
                    con = cons[0];
                    pageCon = con.clone(false, true, false, false);
                } else if (leads.size() > 0) {
                    ld = leads[0];
                    pageCon = new Contact(FirstName = ld.FirstName, LastName = ld.LastName, Email = ld.Email, Phone = ld.Phone, HasOptedOutOfEmail = ld.HasOptedOutOfEmail, DoNotCall = ld.DoNotCall);
                }
                getSubscriptionsAndMemberships();
                displaySearch = false;
                getCommMethods();
            } else displaySearch = true;
        }
        displayConfirmation = FALSE;
    }
    
    public void getSubscriptionsAndMemberships() {
        groupName = '';
        subscrWrpItems = new map<Id, memberWrp>();
        
        if (string.isBlank(ApexPages.currentPage().getParameters().get('group'))) {
            list<Subscription__c> ls = [SELECT Id, Group_ID__c FROM Subscription__c WHERE Id = :subscriptionId];
            if (ls.size() > 0) groupName = ls[0].Group_ID__c;
        } else {
            groupName = ApexPages.currentPage().getParameters().get('group');
        }
        
        // retrieve all subscription memberships
        subscrItems = new map<Id, Subscription_Membership__c>();
        if (!string.isBlank(con.Id)) {
            for (Subscription_Membership__c lm : [SELECT Id, Subscription__c, Subscription__r.Subscription_Id__c, Subscribed_Date__c, Subscription_Status__c, Unsubscribed_Comments__c, Unsubscribed_Date__c, Unsubscribed_Reason__c FROM Subscription_Membership__c WHERE Contact__c = :con.Id AND Subscription__r.Active__c = TRUE]) {
                subscrItems.put(lm.Subscription__c, lm);
                subscrWrpItems.put(lm.Subscription__c, new memberWrp(lm));
            }
        } else if (!string.isBlank(ld.Id)) {
            for (Subscription_Membership__c lm : [SELECT Id, Subscription__c, Subscription__r.Subscription_Id__c, Subscribed_Date__c, Subscription_Status__c, Unsubscribed_Comments__c, Unsubscribed_Date__c, Unsubscribed_Reason__c FROM Subscription_Membership__c WHERE Lead__c = :ld.Id AND Subscription__r.Active__c = TRUE]) {
                subscrItems.put(lm.Subscription__c, lm);
                subscrWrpItems.put(lm.Subscription__c, new memberWrp(lm));
            }
        }
        
        list<Subscription__c> lsts = new list<Subscription__c>();
        if (string.isBlank(groupName)) {
            lsts = [SELECT Id, Public_Subscription_Label__c, Group_ID__c, Public_Group_Label__c, Description__c FROM Subscription__c 
                WHERE Active__c = TRUE ORDER BY public_Subscription_Label__c ASC];
        } else {
            lsts = [SELECT Id, Public_Subscription_Label__c, Group_ID__c, Public_Group_Label__c, Description__c FROM Subscription__c 
                WHERE Active__c = TRUE AND Group_ID__c = :groupName ORDER BY Public_Subscription_Label__c ASC];
        }
        
        // retrieve all subscriptions; if a subscription membership does not already exist, then create it.
        subscrs = new map<Id, Subscription__c>();
        subscrsByGroup = new map<string, list<Subscription__c>>();
        groupNames = new map<string, string>();
        unsubComments = new map<string, string>();
        
        for (Subscription__c l : lsts) {
            subscrs.put(l.Id, l);
            if (!subscrItems.containsKey(l.Id)) {
                Subscription_Membership__c sm = new Subscription_Membership__c(Subscription__c = l.Id, Contact__c = con.Id, Subscribed_Date__c = system.now(), Subscription_Status__c = 'Subscribed');
                subscrItems.put(l.Id, sm);
                subscrWrpItems.put(sm.Subscription__c, new memberWrp(sm));
            }
            
            // organize subscriptions by group
            if (!subscrsByGroup.containsKey(l.Group_ID__c)) subscrsByGroup.put(l.Group_ID__c, new list<Subscription__c>());
            subscrsByGroup.get(l.Group_ID__c).add(l);
            
            if (!groupNames.containsKey(l.Group_ID__c)) {
                groupNames.put(l.Group_ID__c, l.Public_Group_Label__c);
                unsubComments.put(l.Group_ID__c, '');
            }
        }
        
        // validate the subscription id
        if (!subscrItems.containsKey(subscriptionId)) {
            subscriptionId = null;
            openPanel = 'item2';
        } else {
            openPanel = 'item1';
        }
        
    }
    
    public void getCommMethods() {
        emailComm = true;
        phoneComm = true;
        if (con.DoNotCall || ld.DoNotCall) phoneComm = false;
        if (con.HasOptedOutOfEmail || ld.HasOptedOutOfEmail) emailComm = false;
    }
    
    public void setCommMethods() {
        if (!string.isBlank(con.Id)) {
            if (!phoneComm) con.DoNotCall = true;
            if (!emailComm) con.HasOptedOutOfEmail = true;
            update con;
        } else if (!string.isBlank(ld.Id)) {
            if (!phoneComm) ld.DoNotCall = true;
            if (!emailComm) ld.HasOptedOutOfEmail = true;
            update ld;
        }
    }
    
    public pageReference updateSubscription() { // panel 1 - update single subscription
        if (!string.isBlank(subscriptionId)) {
            subscrItems.get(subscriptionId).Subscription_Status__c = 'Unsubscribed';
            subscrItems.get(subscriptionId).Unsubscribed_Date__c = system.now();
            subscrItems.get(subscriptionId).Unsubscribed_Reason__c = singleUnsubscribeReason;
            subscrItems.get(subscriptionId).Unsubscribed_Comments__c = unsubscribeComments;
            
            upsert subscrItems.get(subscriptionId);
        }
        openPanel = 'item1';
        displayConfirmation = TRUE;
        ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Your subscriptions have been updated.'));
        return null;
    }
    
    public pageReference updateSubscriptionList() {        
        list<Subscription_Membership__c> lmsToUpsert = new list<Subscription_Membership__c>();
        if (subscrsByGroup.containsKey(selectedGroup)) {
            for (Subscription__c lst : subscrsByGroup.get(selectedGroup)) {
                Subscription_Membership__c li = subscrItems.get(lst.Id);
                boolean subscribed = subscrWrpItems.get(lst.Id).subscribed;
                system.debug('--- list id: ' + lst.Id);
                system.debug('--- subscribed: ' + subscribed);
                
                if (subscribed) {
                    //li.SubscribedDate__c = system.today();
                    li.Subscription_Status__c = 'Subscribed';
                    li.Unsubscribed_Date__c = null;
                    li.Unsubscribed_Comments__c = '';
                    li.Unsubscribed_Reason__c = '';
                } else {
                    li.Subscription_Status__c = 'Unsubscribed';
                    if (string.isBlank(li.Unsubscribed_Reason__c)) li.Unsubscribed_Reason__c = unsubscribeReason;
                    if (string.isBlank(li.Unsubscribed_Comments__c)) li.Unsubscribed_Comments__c = unsubComments.get(lst.Group_ID__c);
                    if (li.Unsubscribed_Date__c == null) li.Unsubscribed_Date__c = system.now();
                    //con.unsubscribes__c += li.Subscription__c + ';';
                }
                
                lmsToUpsert.add(li);
            }
        }
        
        if (lmsToUpsert.size() > 0) upsert lmsToUpsert;
        
        openPanel = 'item2';
        displayConfirmation = TRUE;
        ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Your subscriptions have been updated.'));
        return null;
    }
    
    public pageReference unsubscribeFromAll() {
        system.debug('--- selected group id: ' + selectedGroup);
        
        list<Subscription_Membership__c> lmsToUpsert = new list<Subscription_Membership__c>();
        if (subscrsByGroup.containsKey(selectedGroup)) {
            for (Subscription__c lst : subscrsByGroup.get(selectedGroup)) {
                Subscription_Membership__c li = subscrItems.get(lst.Id);
                
                li.Subscription_Status__c = 'Unsubscribed';
                li.Unsubscribed_Date__c = system.now();
                if (string.isBlank(li.Unsubscribed_Reason__c)) li.Unsubscribed_Reason__c = unsubscribeReason;
                if (string.isBlank(li.Unsubscribed_Comments__c)) li.Unsubscribed_Comments__c = unsubComments.get(lst.Group_ID__c); //unsubscribeComments;
                
                lmsToUpsert.add(li);
            }
        }
        
        if (lmsToUpsert.size() > 0) upsert lmsToUpsert;
        
        openPanel = 'item2';
        displayConfirmation = TRUE;
        ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Your subscriptions have been updated.'));
        return null;
    }
    
    public pageReference updatePreferences() {
        if (!string.isBlank(con.Id)) {
            if (!string.isBlank(pageCon.Email)) con.Email = pageCon.Email;
            if (!string.isBlank(pageCon.FirstName)) con.FirstName = pageCon.FirstName;
            if (!string.isBlank(pageCon.LastName)) con.LastName = pageCon.LastName;
            if (!string.isBlank(pageCon.Phone)) con.Phone = pageCon.Phone;
            update con;
        } else if (!string.isBlank(ld.Id)) {
            if (!string.isBlank(pageCon.Email)) ld.Email = pageCon.Email;
            if (!string.isBlank(pageCon.FirstName)) ld.FirstName = pageCon.FirstName;
            if (!string.isBlank(pageCon.LastName)) ld.LastName = pageCon.LastName;
            if (!string.isBlank(pageCon.Phone)) ld.Phone = pageCon.Phone;
            update ld;
        }
        setCommMethods();
        
        openPanel = 'item3';
        displayConfirmation = TRUE;
        ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'Your preferences have been updated.'));
        return null;
    }
    
    public pageReference globalUnsubscribe() {
        if (!string.isBlank(con.Id)) {
            con.DoNotCall = TRUE;
            con.HasOptedOutOfEmail = TRUE;
            update con;
        } else if (!string.isBlank(ld.Id)) {
            ld.HasOptedOutOfEmail = TRUE;
            ld.DoNotCall = TRUE;
            update ld;
        }
        setCommMethods();
        displayConfirmation = TRUE;
        ApexPages.addmessage(new ApexPages.message(ApexPages.severity.CONFIRM,'You have unsubscribed from all emails.'));
        return null;
    }
    
    public class memberWrp {
        public Subscription_Membership__c sm {get;set;}
        public boolean subscribed {get;set;}
        
        public memberWrp(Subscription_Membership__c thisSM) {
            sm = thisSM;
            if (sm.Subscription_Status__c == 'Subscribed') subscribed = true;
            else subscribed = false;
        }
        
    }
}