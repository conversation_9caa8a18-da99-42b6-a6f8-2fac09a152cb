/** 
* @description    When all modules are completed, update parent course connection to complete
* <AUTHOR> 
* @version        1.0 
*/
global class EP_ModuleHandler_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
    * @param newList the list of course connection records from trigger new 
     * @param oldList the list of course connection records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc...)
     * @param objResult the describe for course connection 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<SObject> newlist, List<SObject> oldlist, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult) {
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DmlWrapper();
        Set<Id> relatedCourseOfferings = new Set<Id>(); 
        Set<Id> relatedApps = new Set<Id>(); 
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert || triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            for(hed__Course_Enrollment__c ce : (List<hed__Course_Enrollment__c>)newList){
                if(ce.hed__Status__c == 'Complete' && ce.hed__Course_Offering__c != null && ce.Application__c != null){
                    relatedCourseOfferings.add(ce.hed__Course_Offering__c);
                    relatedApps.add(ce.Application__c); 
                }
            }    
        }else if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            Map<Id, hed__Course_Enrollment__c> oldMap = new Map<Id, hed__Course_Enrollment__c>(((List<hed__Course_Enrollment__c>) oldList)); 
            for(hed__Course_Enrollment__c ce: (List<hed__Course_Enrollment__c>)newList){
                if(oldMap.get(ce.Id).hed__Status__c != ce.hed__Status__c && ce.hed__Status__c == 'Complete' && ce.hed__Course_Offering__c != null && ce.Application__c != null){
                    relatedCourseOfferings.add(ce.hed__Course_Offering__c); 
                    relatedApps.add(ce.Application__c); 
                }
            }
        }

        //Query for course offering:
        List<hed__Course_Offering__c> relatedCos = new List<hed__Course_Offering__c>(); 
        if(relatedCourseOfferings.size() > 0) relatedCos = [SELECT Id, Parent_Course__c FROM hed__Course_Offering__c WHERE Id IN: relatedCourseOfferings AND Parent_Course__c != null]; 

        if (relatedCos.size() > 0){
            Set<Id> parentCoIds = new Set<Id>(); 
            for(hed__Course_Offering__c co : relatedCos){
                parentCoIds.add(co.Parent_Course__c); 
            }

            List<hed__Course_Enrollment__c> parentCourseConnectionToUpdate = updateParentCourseEnrollmentStatus(parentCoIds, relatedApps); 
            if(parentCourseConnectionToUpdate.size() > 0) dmlWrapper.objectsToUpdate.addAll(parentCourseConnectionToUpdate); 
        }

        return dmlWrapper;
    }

    private List<hed__Course_Enrollment__c> updateParentCourseEnrollmentStatus(Set<Id> parentCoIds, Set<Id> relatedApps){
        List<hed__Course_Enrollment__c> ccToUpdate = new List<hed__Course_Enrollment__c>(); 

        List<hed__Course_Enrollment__c> moduleCEList = [SELECT Id, hed__Status__c, hed__Course_Offering__r.Parent_Course__c, hed__Course_Offering__r.Parent_Course__r.hed__course__c, Application__c 
                                                        FROM hed__Course_Enrollment__c 
                                                        WHERE Application__c IN: relatedApps
                                                        AND hed__Course_Offering__c != null
                                                        ]; 
        
        //Map modules to parent course: 
        Map<Id, List<hed__Course_Enrollment__c>> courseIdToCompletedModules= new Map<Id, List<hed__Course_Enrollment__c>>();
        Map<Id, hed__Course_Enrollment__c> appIdToParentCC = new Map<Id, hed__Course_Enrollment__c>(); 

        for(hed__Course_Enrollment__c ce : moduleCEList){
            //If course enrollment is related to main program course offering: 
            if(ce.hed__Course_Offering__r.Parent_Course__c == null && ce.hed__Status__c != 'Complete'){
                appIdToParentCC.put(ce.Application__c, ce); 
            }else if(ce.hed__Course_Offering__r.Parent_Course__c != null && ce.hed__Status__c == 'Complete' && ce.hed__Course_Offering__r.Parent_Course__r.hed__course__c != null){
                if(!courseIdToCompletedModules.containsKey(ce.hed__Course_Offering__r.Parent_Course__r.hed__course__c)){
                    courseIdToCompletedModules.put(ce.hed__Course_Offering__r.Parent_Course__r.hed__course__c, new List<hed__Course_Enrollment__c>{ce});
                }else{
                    courseIdToCompletedModules.get(ce.hed__Course_Offering__r.Parent_Course__r.hed__course__c).add(ce);
                }
            }
        }

        //Map list of children course offering to parent course offering: 
        Map<Id, hed__Course_Offering__c> parentCOMap = new Map<Id, hed__Course_Offering__c>([SELECT Id, hed__Course__c,
                                                                                            (SELECT Id FROM Child_Course_Offerings__r)
                                                                                            FROM hed__Course_Offering__c 
                                                                                            WHERE Id IN: parentCoIds]); 

        for(Id pId : parentCoIds){
            if(parentCOMap.containsKey(pId) && courseIdToCompletedModules.containsKey(parentCOMap.get(pId).hed__Course__c)){
                if(courseIdToCompletedModules.get(parentCOMap.get(pId).hed__Course__c).size() == parentCOMap.get(pId).Child_Course_Offerings__r.size() ){
                    if(appIdToParentCC.containsKey(courseIdToCompletedModules.get(parentCOMap.get(pId).hed__Course__c)[0].Application__c)){
                        ccToUpdate.add(new hed__Course_Enrollment__c(Id = appIdToParentCC.get(courseIdToCompletedModules.get(parentCOMap.get(pId).hed__Course__c)[0].Application__c).Id, hed__Status__c = 'Complete')); 
                    }
                }
            }
        }
        return ccToUpdate; 
    }
}