/**
 * @description test score calculator to calculate Total Test Score and update Source value (when applicable) on the Test object
 * <AUTHOR>
 * @version 1.0
 * @created 2020-03-03
 * @modified 2020-03-03
 */
global class TestScoreCalculator_TDTM extends hed.TDTM_Runnable{
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Test records from trigger new 
     * @param oldList the list of Test records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Tests 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        Set<Id> testIds = new Set<Id>(); 
        Set<String> testNames = new Set<String>(); 

        //AFTER INSERT CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterInsert){
            for(hed__Test_Score__c ts :(List<hed__Test_Score__c>) newList){
                //calculate total test score if score field has a value: 
                if(ts.hed__Score__c != null || (ts.Source__c != null && ts.Source__c == 'Official')){
                    testIds.add(ts.hed__Test__c); 
                    testNames.add(ts.hed__Test_Type__c); 
                }
            }
        }
        //AFTER UPDATE CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.AfterUpdate){
            Map<Id, hed__Test_Score__c> oldMap = new Map<Id, hed__Test_Score__c>((List<hed__Test_Score__c>)oldList); 
            for(hed__Test_Score__c ts:(List<hed__Test_Score__c>)newList){
                //re-calculate total test score if score field or test look-up has been updated or source is updated to 'Official'
                if(ts.hed__Score__c != oldMap.get(ts.Id).hed__Score__c || ts.hed__Test__c != oldMap.get(ts.Id).hed__Test__c|| (ts.Source__c != null && ts.Source__c == 'Official' && ts.Source__c != oldMap.get(ts.Id).Source__c)){
                    testIds.add(ts.hed__Test__c); 
                    testNames.add(ts.hed__Test_Type__c); 
                }
            }
        }

        if(testIds.size() > 0){
            //Set of Test records to Update: 
            Set<hed__Test__c> testRecordsToUpdateSet = new Set<hed__Test__c>(); 

            //Query for related test records: 
            List<hed__Test__c> testRecords = TestService.queryFieldsInTests(testIds, new Set<String>{'Composite_Score__c', 'hed__Source__c', 'hed__Test_Type__c','Converted_GMAT_Total_Score__c'}); 
            //Query for related test setting custom metadata records and map test name to a map of subject to weight 
            Map<String, Map<String, Decimal>> testToSubjectWeightMap = TestService.mapTestNameToSubjectWeight(testNames); 

            //Map of test records to list of related test scores: 
            Map<Id, List<hed__Test_Score__c>> testToScoresMap = TestService.mapTestIdToScores(testIds, new Set<String>{'hed__Score__c', 'hed__Subject_Area__c', 'Source__c'}); 

            //Calculate test scores: 
            List<hed__Test__c> updateTestScores = RollupService.calculateTestScores(testRecords, testToScoresMap, testToSubjectWeightMap); 
            if(updateTestScores.size() > 0) testRecordsToUpdateSet.addAll(updateTestScores); 

            //Update test's source value to 'Official' if all related test scores' sources are 'Official': 
            List<hed__Test__c> updateSources = updateToOfficial(testRecords, testToScoresMap); 
            if(updateSources.size() > 0) testRecordsToUpdateSet.addAll(updateSources); 

            //Add all records to update to dmlWrapper 
            if(testRecordsToUpdateSet.size() > 0){
                List<hed__Test__c> testsToUpdateList = new List<hed__Test__c>(testRecordsToUpdateSet); 
                dmlWrapper.objectsToUpdate.addAll(testsToUpdateList); 
            } 
        }
        return dmlWrapper;
    }

    /**
    * @description update all test records' source to "Official" if all related children 
    * test score records' sources are "Official" 
    * @param testRecords List of test records 
    * @param testToScoresMap map test Ids to their related test scores 
    */
    private List<hed__Test__c> updateToOfficial(List<hed__Test__c> testRecords, Map<Id, List<hed__Test_Score__c>> testToScoresMap){
        List<hed__Test__c> testsToUpdate = new List<hed__Test__c>(); 
        Boolean updateSourceToOfficial = true; 

        for(hed__Test__c t :testRecords){
            if(t.hed__Source__c != 'Official' && testToScoresMap.get(t.Id) != null){
                for(hed__Test_Score__c ts :testToScoresMap.get(t.Id)){
                    if(ts.Source__c != 'Official'){
                        //Do not update source
                        updateSourceToOfficial = false; 
                        break; 
                    }
                }
                if(updateSourceToOfficial){
                    t.hed__Source__c = 'Official'; 
                    testsToUpdate.add(t); 
                }
            }
        }
        return testsToUpdate; 
    }
}