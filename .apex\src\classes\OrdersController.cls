/*
 * <AUTHOR> <PERSON>
 *
 * @description :
 *
 * @group : Rotman School of Management
 *
 * @last modified on : 07-16-2023
 * @last modified by : <EMAIL>
 */
public without sharing class OrdersController {
	@AuraEnabled
	public static List<Shopping_Cart_Item_Details__c> getOrders(String searchAttendees,
																String searchBuyer,
																Integer lastMonths,
																String recordId,
																Boolean isCommunitySite) {
		Date threeMonthsAgo = Date.today().addMonths(-lastMonths);
		String currentContactId = '';

		id userId = UserInfo.getUserId();
		User myuser = [SELECT Id, Email FROM User WHERE Id = :userId];
		List<Contact> mycc = [SELECT Id FROM Contact WHERE Email = :myuser.Email];
		if (mycc.size() > 0) {
			currentContactId = mycc[0].Id;
		}

		System.debug('==currentContactId: ' + currentContactId);
		String queryString =
				'SELECT Id, Name, Shopping_Cart_Item__r.Name, SC_event__r.Name, SC_event__r.Thumbnail_Image__c, SC_event__r.Event_Date_Time__c, SC_event__r.Registration_Confirmation__c, Contact__r.Name, Contact__r.Email, Item_Total_Amount__c, CreatedDate, Attendee__r.Check_In_QR_Code__c, Attendee__r.Check_In_QR_Code_URL__c, Attendee__r.evt__Reg_First_Name__c, Attendee__r.evt__Reg_Last_Name__c, Attendee__r.evt__Reg_Email__c, Attendee__r.CreatedDate, Attendee__r.evt__Contact__c, Attendee__r.Attendee_Name__c, Attendee__r.evt__Event__c, Attendee__r.evt__Event_Fee__r.Name, Attendee__r.Customize_FieldValue_1__c, Attendee__r.Customize_FieldValue_2__c, Attendee__r.Customize_FieldValue_3__c, Attendee__r.Customize_FieldValue_4__c, Attendee__r.Customize_FieldValue_5__c, Attendee__r.evt__Mailing_Street__c , Attendee__r.evt__Mailing_City__c , Attendee__r.evt__Mailing_State__c , Attendee__r.evt__Mailing_Country__c , Attendee__r.evt__Mailing_Postal_Code__c , Attendee__r.evt__Invitation_Status__c,  Shopping_Cart_Item__r.pymt__Payment__r.pymt__Payment_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Last_4_Digits__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Card_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.Payment_Date__c FROM Shopping_Cart_Item_Details__c';

		if (recordId != null) {
			queryString += ' WHERE SC_event__c = :recordId AND CreatedDate >= :threeMonthsAgo';
		} else {
			queryString += ' WHERE CreatedDate >= :threeMonthsAgo';
		}

		//queryString += ' AND SC_event__r.End_Date__c > TODAY AND Void_ticket__c = false';
		queryString += ' AND Void_ticket__c = false';
		if (searchAttendees != '' || searchAttendees != null) {
			String searchString = '%' + searchAttendees + '%';
			queryString +=
					' AND (Attendee__r.evt__Reg_Email__c LIKE :searchString OR Attendee__r.evt__Reg_First_Name__c LIKE :searchString OR Attendee__r.evt__Reg_Last_Name__c LIKE :searchString)';
		}
		if (searchBuyer != '' || searchBuyer != null) {
			String searchBuyerString = '%' + searchBuyer + '%';
			queryString += ' AND (Contact__r.Name LIKE :searchBuyerString)';
		}
		queryString += ' ORDER BY CreatedDate DESC LIMIT 1000';
		System.debug('==queryRecordId==: ' + recordId);
		System.debug('==queryString==: ' + queryString);
		List<Shopping_Cart_Item_Details__c> results = Database.query(queryString);
		System.debug('results: ' + results);

		return results;
	}

	@AuraEnabled
	public static List<Shopping_Cart_Item_Details__c> getMyOrders(String searchAttendees,
																  String searchBuyer,
																  Integer lastMonths,
																  String recordId,
																  Boolean isCommunitySite) {
		Date threeMonthsAgo = Date.today().addMonths(-lastMonths);
		//System.debug('==threeMonthsAgo: ' + threeMonthsAgo);
		//System.debug('==searchAttendees: ' + searchAttendees);
		//System.debug('==searchBuyer: ' + searchBuyer);
		//System.debug('==lastMonths: ' + lastMonths);
		//System.debug('==recordId: ' + recordId);
		//System.debug('==isCommunitySite: ' + isCommunitySite);

		String currentContactId = '';

		id userId = UserInfo.getUserId();
		User myuser = [SELECT Id, Email FROM User WHERE Id = :userId];
		List<Contact> mycc = [SELECT Id FROM Contact WHERE Email = :myuser.Email];
		if (mycc.size() > 0) {
			currentContactId = mycc[0].Id;
		}

		//System.debug('==currentContactId: ' + currentContactId);
		String queryString =
				'SELECT Id, Name, Void_ticket__c, Shopping_Cart_Item__r.Name, SC_event__r.Name, SC_event__r.Thumbnail_Image__c, SC_event__r.Event_Date_Time__c, Contact__r.Name, Contact__r.Email, Item_Total_Amount__c, CreatedDate, Attendee__r.Check_In_QR_Code__c, Attendee__r.Check_In_QR_Code_URL__c, Attendee__r.evt__Reg_First_Name__c, Attendee__r.evt__Reg_Last_Name__c, Attendee__r.evt__Reg_Email__c, Attendee__r.CreatedDate, Attendee__r.evt__Contact__c, Attendee__r.Attendee_Name__c, Attendee__r.evt__Event__c, Attendee__r.evt__Event_Fee__r.Name, Attendee__r.evt__Mailing_Street__c , Attendee__r.evt__Mailing_City__c , Attendee__r.evt__Mailing_State__c , Attendee__r.evt__Mailing_Country__c , Attendee__r.evt__Mailing_Postal_Code__c , Attendee__r.evt__Invitation_Status__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Payment_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Last_4_Digits__c, Shopping_Cart_Item__r.pymt__Payment__r.pymt__Card_Type__c, Shopping_Cart_Item__r.pymt__Payment__r.Payment_Date__c';
		queryString += ' FROM Shopping_Cart_Item_Details__c';
		queryString += ' WHERE Contact__c = :currentContactId AND Void_ticket__c = false AND CreatedDate >= :threeMonthsAgo';
		//queryString += ' WHERE Void_ticket__c = false AND Contact__c = :currentContactId AND CreatedDate >= :threeMonthsAgo';
		//queryString += ' AND SC_event__r.End_Date__c > TODAY AND Attendee__r.evt__Invitation_Status__c=\'Registered\'';
		//queryString += ' AND Attendee__r.evt__Invitation_Status__c=\'Registered\'';
		queryString += ' ORDER BY CreatedDate DESC LIMIT 1000';
		//System.debug('==queryString==: ' + queryString);
		List<Shopping_Cart_Item_Details__c> results = Database.query(queryString);
		//System.debug('results: ' + results);

		return results;
	}

	@AuraEnabled
	public static Map<String, String> updateSelectedOrder(String[] attendees, String reason) {
		Map<String, String> mapRes = new Map<String, String>();
		Decimal totalAmount = 0;
		Id shopCartItemId;
		Id contactId;
		String[] cancelledAttendees = new String[]{};
		Id paymentId;
		Id eventId;
		String eventName = '';
		List<evt__Attendee__c> lstFreeAttendee = new List<evt__Attendee__c>();
		List<evt__Attendee__c> lstPaidAttendee = new List<evt__Attendee__c>();
		List<evt__Attendee__c> lstAttendee = [SELECT Id, evt__Invitation_Status__c, evt__Reg_Email__c FROM evt__Attendee__c WHERE Id IN :attendees];

		if (lstAttendee.size() > 0) {
			List<Shopping_Cart_Item_Details__c> lstOrder = [SELECT Id, SC_event__c, SC_event__r.Name, Attendee__c, Item_Total_Amount__c, Contact__c, Shopping_Cart_Item__c, Shopping_Cart_Item__r.pymt__Payment__c FROM Shopping_Cart_Item_Details__c WHERE Attendee__c IN :attendees];
			Map<Id, Decimal> mapAttendeeIdToTotalAmount = new Map<Id, Decimal>();
			for (Shopping_Cart_Item_Details__c order : lstOrder) {
				if (order.Shopping_Cart_Item__r.pymt__Payment__c != null) {
					paymentId = order.Shopping_Cart_Item__r.pymt__Payment__c;
				}
				shopCartItemId = order.Shopping_Cart_Item__c;
				contactId = order.Contact__c;
				eventId = order.SC_event__c;
				eventName = order.SC_event__r.Name;
				totalAmount = totalAmount + order.Item_Total_Amount__c;
				mapAttendeeIdToTotalAmount.put(order.Attendee__c, order.Item_Total_Amount__c);
			}
			for (evt__Attendee__c attendee : lstAttendee) {
				if (mapAttendeeIdToTotalAmount.get(attendee.Id) == 0) {
					attendee.evt__Invitation_Status__c = 'Cancelled';
					lstFreeAttendee.add(attendee);
					cancelledAttendees.add(attendee.Id);
				} else {
					attendee.evt__Invitation_Status__c = 'Cancelled';
					lstPaidAttendee.add(attendee);
					cancelledAttendees.add(attendee.Id);
				}
			}
			if (lstFreeAttendee.size() > 0) {
				try {
					update lstFreeAttendee;
					mapRes.put('Cancelled', 'success');

					//update shopping cart item details record to set the void ticket field to true
					Set<Id> attIds = new Set<Id>();
					for (evt__Attendee__c attendee : lstFreeAttendee) {
						attIds.add(attendee.Id);
					}
					List<Shopping_Cart_Item_Details__c> currentShoppingCartItemDetails = [SELECT Id, Void_ticket__c FROM Shopping_Cart_Item_Details__c WHERE Attendee__c IN :attIds AND Void_ticket__c = false];
					List<Shopping_Cart_Item_Details__c> lstUpdateSCID = new List<Shopping_Cart_Item_Details__c>();
					if (currentShoppingCartItemDetails.size() > 0) {
						for (Shopping_Cart_Item_Details__c shoppingCartItemDetails : currentShoppingCartItemDetails) {
							shoppingCartItemDetails.Void_ticket__c = true;
							lstUpdateSCID.add(shoppingCartItemDetails);
						}

						if (lstUpdateSCID.size() > 0) {
							update lstUpdateSCID;
						}
					}
				} catch (Exception ex) {
					System.debug('Exception: ' + ex.getMessage());
					mapRes.put('error', ex.getMessage());
				}
			}
			if (lstPaidAttendee.size() > 0 && paymentId != null) {
				pymt__PaymentX__c payment, refundPayment;
				Boolean isSuccess = false;

				payment = [SELECT Id, OwnerId, Name, pymt__Transaction_Id__c, pymt__Amount__c, pymt__Card_Type__c, pymt__Billing_Email__c,
				pymt__Billing_First_Name__c, pymt__Billing_Last_Name__c, pymt__Billing_Street__c, Application__c,
				pymt__Billing_City__c, pymt__Billing_State__c, pymt__Billing_Country__c, pymt__Contact__c,
				pymt__Billing_Postal_Code__c, pymt__Last_4_Digits__c, pymt__Reference_Id__c, Amount_Paid__c, Amount_Refunded__c
				FROM pymt__PaymentX__c WHERE ID = :paymentId];
				System.debug('payment: ' + payment);

				refundPayment = new pymt__PaymentX__c(pymt__Parent_Transaction__c = paymentId,
						pymt__Transaction_Type__c = 'Refund',
						pymt__Amount__c = totalAmount,
						Refund_Reason__c = reason,
						evt__Event__c = eventId,
						pymt__Payment_Type__c = 'Credit Card',
						Type__c = 'Event Registration',
						pymt__Payment_Processor__c = 'Global Pay');
				refundPayment.pymt__Billing_First_Name__c = payment.pymt__Billing_First_Name__c;
				refundPayment.pymt__Billing_Last_Name__c = payment.pymt__Billing_Last_Name__c;
				refundPayment.pymt__Billing_Street__c = payment.pymt__Billing_Street__c;
				refundPayment.pymt__Billing_City__c = payment.pymt__Billing_City__c;
				refundPayment.pymt__Billing_State__c = payment.pymt__Billing_State__c;
				refundPayment.pymt__Billing_Country__c = payment.pymt__Billing_Country__c;
				refundPayment.pymt__Billing_Postal_Code__c = payment.pymt__Billing_Postal_Code__c;
				refundPayment.pymt__Last_4_Digits__c = payment.pymt__Last_4_Digits__c;
				refundPayment.pymt__Card_Type__c = payment.pymt__Card_Type__c;
				refundPayment.pymt__Reference_Id__c = payment.pymt__Reference_Id__c;
				refundPayment.pymt__Billing_Email__c = payment.pymt__Billing_Email__c;
				refundPayment.pymt__Contact__c = payment.pymt__Contact__c;
				refundPayment.Application__c = payment.Application__c;
				refundPayment.OwnerId = payment.OwnerId;
				String pName = 'Refund-'+ eventName;
				pName = pName.left(80);
				refundPayment.Name = pName;

				//System.debug('processRefund pymt__Transaction_Id__c ' + payment.pymt__Transaction_Id__c);
				String sourceIp;
				if (test.isRunningTest()) {
					sourceIp = '127.0.0.1';
				}else{
					sourceIp = Auth.SessionManagement.getCurrentSession().get('SourceIp');
				}
				refundPayment.pymt__IP_Address__c = sourceIp;
				try {
					HTTPResponse response =
							GlobalPayConnect.processRefund(payment.pymt__Transaction_Id__c, totalAmount);

					//System.debug('response.getStatusCode ' + response.getStatusCode());
					GP_PaymentResponseWrapper wrapper = (GP_PaymentResponseWrapper) JSON.deserialize(response.getBody(),
									GP_PaymentResponseWrapper.class);
					//System.debug('wrapper ' + wrapper);
					//System.debug('wrapper error_code ' + wrapper.detailed_error_description);
					//System.debug('wrapper status ' + wrapper.status);

					if (String.isNotBlank(wrapper.detailed_error_description)) {
						refundPayment.pymt__Status__c = 'Error';
						mapRes.put('Error', wrapper.detailed_error_description);
					} else if (response.getStatusCode() == 200 && wrapper.action.result_code == 'SUCCESS' &&
									wrapper.status == 'CAPTURED') {
						refundPayment.pymt__Status__c = 'Reversed';
						refundPayment.pymt__Date__c = Date.today();
						refundPayment.pymt__Batch_Id__c = wrapper.batch_id;
						refundPayment.Amount_Refunded__c = totalAmount;
						payment.Amount_Refunded__c += totalAmount;
						mapRes.put('Refunded', 'SUCCESS');
						isSuccess = true;
					} else {
						String errMsg =
								'We\'re sorry, but we cannot complete your refund. Please refresh this page and try again.';
						refundPayment.pymt__Status__c = 'Error';
						mapRes.put('Error', errMsg);
					}
					if (String.isNotBlank(wrapper.id)) {
						refundPayment.pymt__Transaction_Id__c = wrapper.id;
					}
					refundPayment.Payment_Response__c = DateTime.now() + ' ->' + String.valueOf(response.getBody());

					if (isSuccess) {
						insert refundPayment;
						update payment;
						update lstPaidAttendee;

						//update shopping cart item details record to set the void ticket field to true
						Set<Id> attIds = new Set<Id>();
						for (evt__Attendee__c attendee : lstPaidAttendee) {
							attIds.add(attendee.Id);
						}
						List<Shopping_Cart_Item_Details__c> currentShoppingCartItemDetails = [SELECT Id, Void_ticket__c FROM Shopping_Cart_Item_Details__c WHERE Attendee__c IN :attIds AND Void_ticket__c = false];
						List<Shopping_Cart_Item_Details__c> lstUpdateSCID = new List<Shopping_Cart_Item_Details__c>();
						if (currentShoppingCartItemDetails.size() > 0) {
							for (Shopping_Cart_Item_Details__c shoppingCartItemDetails : currentShoppingCartItemDetails) {
								shoppingCartItemDetails.Void_ticket__c = true;
								lstUpdateSCID.add(shoppingCartItemDetails);
							}

							if (lstUpdateSCID.size() > 0) {
								update lstUpdateSCID;
							}
						}
					}
				} catch (Exception ex) {
					System.debug('Exception: ' + ex.getMessage());
					mapRes.put('error', ex.getMessage());
				}
			}
			if (cancelledAttendees.size() > 0) {
				EmailWithAttachmentController.sendCancelledEmailWithAttachment(shopCartItemId, contactId,
						cancelledAttendees);
			}
		}

		return mapRes;
	}

	@AuraEnabled
	public static String ResendOrder(String scId, String contactId) {
		String result = 'Success';
		try {
			EmailWithAttachmentController.sendEmailWithAttachment(scId, contactId);
		} catch (Exception ex) {
			System.debug('Exception: ' + ex.getMessage());
			result = ex.getMessage();
		}
		return result;
	}

	@AuraEnabled(cacheable = true)
	public static List<evt__Event_Fee__c> getEventFeeTypes(String eventId) {
		List<evt__Event_Fee__c> ef = new List<evt__Event_Fee__c>();

		ef = [SELECT Id, Name, evt__Limit__c, Current_Usage__c, Mailing_Enabled__c, Fields_Recruitment_Admissions__c,  evt__Amount__c, evt__Event__c, Taxable__c, Show_Customize_Field_1__c, Show_Customize_Field_2__c, Show_Customize_Field_3__c, Show_Customize_Field_4__c, Show_Customize_Field_5__c, Customize_FieldName_1__c, Customize_FieldName_2__c, Customize_FieldName_3__c, Customize_FieldName_4__c, Customize_FieldName_5__c, evt__Description__c, evt__Limit_Per_Purchase__c FROM evt__Event_Fee__c WHERE evt__Event__c = :eventId];

		return ef;
	}

	@AuraEnabled
	public static String updateRefundEventFeeType(String eventId,
												  String attendeeId,
												  String shoppingCartId,
												  String oldEventFeeTypeId,
												  String newEventFeeTypeId) {
		String result = 'Success';

		//update attendee record with new event fee type
		evt__Attendee__c attendee = [SELECT Id, evt__Event_Fee__c FROM evt__Attendee__c WHERE Id = :attendeeId AND evt__Event__c = :eventId AND evt__Event_Fee__c = :oldEventFeeTypeId];
		attendee.evt__Event_Fee__c = newEventFeeTypeId;
		update attendee;


		return result;
	}

	@AuraEnabled
	public static evt__Special_Event__c getEventInfo(String eventId) {
		evt__Special_Event__c evt = new evt__Special_Event__c();

		evt = [SELECT Id, Name, Thumbnail_Image__c, Event_Date_Time__c FROM evt__Special_Event__c WHERE Id = :eventId];

		return evt;
	}

	@AuraEnabled
	Public static String TicketRefundTransfer(String attendeeId,
											  String eventId,
											  String newEventFeeId,
											  String oldEventFeeId,
											  Decimal amountToRefund,
											  Decimal adminFee) {
		String res = 'Success';

		Shopping_Cart_Item_Details__c currentShoppingCartItemDetails = [SELECT Id, Shopping_Cart_Item__c, Item_Quantity__c, Item_Unit_Price__c, Item_Discount_Amount__c, Item_Tax_Amount__c, Item_Gross_Amount__c, Item_Total_Amount__c, SC_event__c, Event_Fee__c, Attendee__c, Contact__c, Order_Note__c, Void_ticket__c FROM Shopping_Cart_Item_Details__c WHERE Attendee__c = :attendeeId AND SC_event__c = :eventId AND Event_Fee__c = :oldEventFeeId AND Void_ticket__c = false limit 1];

		pymt__Shopping_Cart_Item__c currentShoppingCartItem = [SELECT Id, Event_Quantity__c, Event_Gross_Amount__c, Event_Discount_Amount__c, Event_Tax_Amount__c, Event_Total_Amount__c, pymt__Contact__c, pymt__Payment__c, pymt__Payment__r.Payment_Type__c, pymt__Payment__r.pymt__Transaction_Id__c, pymt__Payment__r.pymt__Status__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :currentShoppingCartItemDetails.Shopping_Cart_Item__c LIMIT 1];
		evt__Event_Fee__c newEventFee = [SELECT Id, evt__Amount__c, evt__Event__r.Taxable__c FROM evt__Event_Fee__c WHERE Id = :newEventFeeId];

		Map<String, String> refundResult = RefundController.processRefund(currentShoppingCartItem.pymt__Payment__c, amountToRefund, adminFee,
				'Difference in ticket type refund');

		if (refundResult.get('status') == 'Success') {
			evt__Attendee__c currentAttendee = [SELECT Id, evt__Event_Fee__c FROM evt__Attendee__c WHERE Id = :attendeeId AND evt__Event__c = :eventId];
			currentAttendee.evt__Event_Fee__c = newEventFeeId;
			update currentAttendee;


			currentShoppingCartItemDetails.Void_ticket__c = true;
			update currentShoppingCartItemDetails;

			Shopping_Cart_Item_Details__c newShoppingCartItemDetails = new Shopping_Cart_Item_Details__c();
			newShoppingCartItemDetails.Shopping_Cart_Item__c = currentShoppingCartItemDetails.Shopping_Cart_Item__c;
			newShoppingCartItemDetails.Item_Quantity__c = currentShoppingCartItemDetails.Item_Quantity__c;
			newShoppingCartItemDetails.Item_Unit_Price__c = newEventFee.evt__Amount__c;
			newShoppingCartItemDetails.Item_Discount_Amount__c = 0;
			newShoppingCartItemDetails.Item_Tax_Amount__c =	newEventFee.evt__Event__r.Taxable__c ? newEventFee.evt__Amount__c * 0.13 : 0;
			newShoppingCartItemDetails.Item_Gross_Amount__c = newShoppingCartItemDetails.Item_Unit_Price__c * newShoppingCartItemDetails.Item_Quantity__c;
			newShoppingCartItemDetails.Item_Total_Amount__c = newShoppingCartItemDetails.Item_Gross_Amount__c + newShoppingCartItemDetails.Item_Tax_Amount__c;
			newShoppingCartItemDetails.SC_event__c = currentShoppingCartItemDetails.SC_event__c;
			newShoppingCartItemDetails.Event_Fee__c = newEventFeeId;
			newShoppingCartItemDetails.Attendee__c = currentShoppingCartItemDetails.Attendee__c;
			newShoppingCartItemDetails.Contact__c = currentShoppingCartItemDetails.Contact__c;
			newShoppingCartItemDetails.Order_Note__c = currentShoppingCartItemDetails.Order_Note__c;
			newShoppingCartItemDetails.Void_ticket__c = false;

			insert newShoppingCartItemDetails;

			List<Shopping_Cart_Item_Details__c> lstSCID = [SELECT Id, Item_Quantity__c, Item_Discount_Amount__c, Item_Tax_Amount__c, Item_Gross_Amount__c, Item_Total_Amount__c FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItem.Id AND Void_ticket__c = false];
			Decimal totalQuantity = 0;
			Decimal totalDiscount = 0;
			Decimal totalTax = 0;
			Decimal totalGross = 0;
			Decimal totalAmount = 0;
			for (Shopping_Cart_Item_Details__c sci : lstSCID) {
				totalQuantity += sci.Item_Quantity__c == null ? 0 : sci.Item_Quantity__c;
				totalDiscount += sci.Item_Discount_Amount__c == null ? 0 : sci.Item_Discount_Amount__c;
				totalTax += sci.Item_Tax_Amount__c == null ? 0 : sci.Item_Tax_Amount__c;
				totalGross += sci.Item_Gross_Amount__c == null ? 0 : sci.Item_Gross_Amount__c;
				totalAmount += sci.Item_Total_Amount__c == null ? 0 : sci.Item_Total_Amount__c;
			}

			currentShoppingCartItem.Event_Quantity__c = totalQuantity;
			currentShoppingCartItem.Event_Discount_Amount__c = totalDiscount;
			currentShoppingCartItem.Event_Tax_Amount__c = totalTax;
			currentShoppingCartItem.Event_Gross_Amount__c = totalGross;
			currentShoppingCartItem.Event_Total_Amount__c = totalAmount;
			update currentShoppingCartItem;
		}else {
			res = refundResult.get('message');
		}

		return res;
	}

	@AuraEnabled
	public static String PaymentTransfer(String attendeeId,
										 String eventId,
										 String newEventFeeId,
										 String oldEventFeeId,
										 String cardName,
										 String cardNo,
										 String cvv,
										 String expMonth,
										 String expYear,
										 String accountName,
										 Decimal subTotal,
										 Decimal discount,
										 Decimal tax,
										 Decimal total,
										 String attendeeCustomize1,
										 String attendeeCustomize2,
										 String attendeeCustomize3,
										 String attendeeCustomize4,
										 String attendeeCustomize5,
										 String mailingStreet,
										 String mailingCity,
										 String mailingState,
										 String mailingPostalCode,
										 String mailingCountry) {
		String res = 'Success';

		//query shopping cart item details record
		List<Shopping_Cart_Item_Details__c> currentShoppingCartItemDetails = [SELECT Id, Shopping_Cart_Item__c, Item_Quantity__c, Item_Unit_Price__c, Item_Discount_Amount__c, Item_Tax_Amount__c, Item_Gross_Amount__c, Item_Total_Amount__c, SC_event__c, Event_Fee__c, Attendee__c, Contact__c, Order_Note__c, Void_ticket__c FROM Shopping_Cart_Item_Details__c WHERE Attendee__c = :attendeeId AND SC_event__c = :eventId AND Event_Fee__c = :oldEventFeeId AND Void_ticket__c = false limit 1];
		if (currentShoppingCartItemDetails.size() == 0) {
			res = 'Error';
			return res;
		} else {
			pymt__Shopping_Cart_Item__c currentShoppingCartItem = [SELECT Id, Event_Quantity__c, Event_Gross_Amount__c, Event_Discount_Amount__c, Event_Tax_Amount__c, Event_Total_Amount__c, pymt__Contact__c, pymt__Payment__c, pymt__Payment__r.Payment_Type__c, pymt__Payment__r.pymt__Transaction_Id__c, pymt__Payment__r.pymt__Status__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :currentShoppingCartItemDetails[0].Shopping_Cart_Item__c LIMIT 1];

			pymt__PaymentX__c newpymnt = new pymt__PaymentX__c();

			String pname = 'Event Ticket Type Change Checkout - ' + currentShoppingCartItemDetails[0].Id;
			newpymnt.Name = pname.left(80);
			newpymnt.pymt__Transaction_Type__c = 'Payment';
			newpymnt.pymt__Payment_Type__c = 'Credit Card';
			newpymnt.pymt__Status__c = 'Online Checkout';
			newpymnt.pymt__Contact__c = currentShoppingCartItemDetails[0].Contact__c;
			newpymnt.pymt__Amount__c = total;
			newpymnt.Gross_Amount__c = subTotal;
			newpymnt.pymt__Tax__c = tax;
			newpymnt.pymt__Discount__c = discount;
			newpymnt.Type__c = 'Event Registration';
			newpymnt.pymt__Payment_Processor__c = 'Global Pay';
			newpymnt.pymt__Parent_Transaction__c =
					currentShoppingCartItem.pymt__Payment__c == null ? null : currentShoppingCartItem.pymt__Payment__c;
			newpymnt.pymt__Billing_First_Name__c = cardName;
			newpymnt.pymt__Billing_Last_Name__c = '';
			newpymnt.pymt__Billing_Street__c = mailingStreet;
			newpymnt.pymt__Billing_City__c = mailingCity;
			newpymnt.pymt__Billing_State__c = mailingState;
			newpymnt.pymt__Billing_Country__c = mailingCountry;
			newpymnt.pymt__Billing_Postal_Code__c = mailingPostalCode;
			newpymnt.pymt__Date__c = Date.today();

			insert newpymnt;

			currentShoppingCartItem.pymt__Payment__c = newpymnt.Id;
			update currentShoppingCartItem;

			Map<String, String> resMap =
					CartController.processPayment(newpymnt, cardNo, cvv, expYear, expMonth, accountName);

			evt__Attendee__c currentAttendee = [SELECT Id, evt__Event_Fee__c FROM evt__Attendee__c WHERE Id = :attendeeId AND evt__Event__c = :eventId];
			currentAttendee.evt__Event_Fee__c = newEventFeeId;
			update currentAttendee;

			evt__Event_Fee__c newEventFee = [SELECT Id, evt__Amount__c, evt__Event__r.Taxable__c FROM evt__Event_Fee__c WHERE Id = :newEventFeeId];

			currentShoppingCartItemDetails[0].Void_ticket__c = true;
			update currentShoppingCartItemDetails;

			Shopping_Cart_Item_Details__c newShoppingCartItemDetails = new Shopping_Cart_Item_Details__c();
			newShoppingCartItemDetails.Shopping_Cart_Item__c = currentShoppingCartItemDetails[0].Shopping_Cart_Item__c;
			newShoppingCartItemDetails.Item_Quantity__c = currentShoppingCartItemDetails[0].Item_Quantity__c;
			newShoppingCartItemDetails.Item_Unit_Price__c = newEventFee.evt__Amount__c;
			newShoppingCartItemDetails.Item_Discount_Amount__c = 0;
			newShoppingCartItemDetails.Item_Tax_Amount__c =
			newEventFee.evt__Event__r.Taxable__c ? newEventFee.evt__Amount__c * 0.13 : 0;
			newShoppingCartItemDetails.Item_Gross_Amount__c = newShoppingCartItemDetails.Item_Unit_Price__c * newShoppingCartItemDetails.Item_Quantity__c;
			newShoppingCartItemDetails.Item_Total_Amount__c =
					(newShoppingCartItemDetails.Item_Gross_Amount__c + newShoppingCartItemDetails.Item_Tax_Amount__c);
			newShoppingCartItemDetails.SC_event__c = currentShoppingCartItemDetails[0].SC_event__c;
			newShoppingCartItemDetails.Event_Fee__c = newEventFeeId;
			newShoppingCartItemDetails.Attendee__c = currentShoppingCartItemDetails[0].Attendee__c;
			newShoppingCartItemDetails.Contact__c = currentShoppingCartItemDetails[0].Contact__c;
			newShoppingCartItemDetails.Order_Note__c = currentShoppingCartItemDetails[0].Order_Note__c;
			newShoppingCartItemDetails.Void_ticket__c = false;

			insert newShoppingCartItemDetails;

			List<Shopping_Cart_Item_Details__c> lstSCID = [SELECT Id, Item_Quantity__c, Item_Discount_Amount__c, Item_Tax_Amount__c, Item_Gross_Amount__c, Item_Total_Amount__c FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItem.Id AND Void_ticket__c = false];
			Decimal totalQuantity = 0;
			Decimal totalDiscount = 0;
			Decimal totalTax = 0;
			Decimal totalGross = 0;
			Decimal totalAmount = 0;
			for (Shopping_Cart_Item_Details__c sci : lstSCID) {
				totalQuantity += sci.Item_Quantity__c == null ? 0 : sci.Item_Quantity__c;
				totalDiscount += sci.Item_Discount_Amount__c == null ? 0 : sci.Item_Discount_Amount__c;
				totalTax += sci.Item_Tax_Amount__c == null ? 0 : sci.Item_Tax_Amount__c;
				totalGross += sci.Item_Gross_Amount__c == null ? 0 : sci.Item_Gross_Amount__c;
				totalAmount += sci.Item_Total_Amount__c == null ? 0 : sci.Item_Total_Amount__c;
			}

			currentShoppingCartItem.Event_Quantity__c = totalQuantity;
			currentShoppingCartItem.Event_Discount_Amount__c = totalDiscount;
			currentShoppingCartItem.Event_Tax_Amount__c = totalTax;
			currentShoppingCartItem.Event_Gross_Amount__c = totalGross;
			currentShoppingCartItem.Event_Total_Amount__c = totalAmount;
			update currentShoppingCartItem;
		}

		return res;
	}

	@AuraEnabled
	public static Map<String, String> createInputContact(String contactFName,
														 String contactLName,
														 String contactEmail,
														 String eventName,
														 Decimal subTotal,
														 Decimal discount,
														 Decimal tax,
														 Decimal total,
														 String eventId,
														 Integer quantity) {
		System.debug('contactFName: ' + contactFName);
		System.debug('contactLName: ' + contactLName);
		System.debug('contactEmail: ' + contactEmail);
		System.debug('eventName: ' + eventName);
		System.debug('subTotal: ' + subTotal);
		System.debug('discount: ' + discount);
		System.debug('tax: ' + tax);
		System.debug('total: ' + total);
		System.debug('eventId: ' + eventId);
		System.debug('quantity: ' + quantity);

		Map<String, String> result = new Map<String, String>();
		String cId = '';
		String sId = '';

		Taxing_Authority__c ta = [SELECT Id FROM Taxing_Authority__c WHERE Name = 'Ontario' LIMIT 1];
		evt__Special_Event__c es = [SELECT Taxable__c FROM evt__Special_Event__c WHERE Id = :eventId LIMIT 1];

		List<Contact> lstCon = [SELECT Id, FirstName, LastName, Email FROM Contact WHERE Email = :contactEmail ORDER BY CreatedDate DESC LIMIT 1];
		System.debug('lstCon==: ' + lstCon);
		if (lstCon.size() > 0) {
			cId = lstCon[0].Id;
			result.put('contactId', cId);
		} else {
			Contact newContact = new Contact();
			newContact.FirstName = contactFName;
			newContact.LastName = contactLName;
			newContact.Email = contactEmail;
			newContact.hed__Preferred_Email__c = 'Alternate Email';
			newContact.hed__AlternateEmail__c = contactEmail;
			try {
				insert newContact;
				cId = newContact.Id;
				result.put('contactId', cId);
			} catch (Exception e) {
				result.put('error', e.getMessage());
			}
		}

		if (cId != '' && cId != null) {
			String cookieName = String.valueOf(dateTime.now());
			pymt__Shopping_Cart__c cart = new pymt__Shopping_Cart__c();
			cart.pymt__Cart_UID__c = cookieName;
			insert cart;

			String citmeName = 'Manually Register - ' + eventName;

			pymt__Shopping_Cart_Item__c newShoppingCartItem = new pymt__Shopping_Cart_Item__c();
			newShoppingCartItem.Name = citmeName.left(80);
			newShoppingCartItem.pymt__Contact__c = cId;
			newShoppingCartItem.Type__c = 'Event Registration';
			newShoppingCartItem.pymt__Shopping_Cart__c = cart.Id;
			newShoppingCartItem.Special_Event__c = eventId;
			newShoppingCartItem.Event_Gross_Amount__c = subTotal;
			newShoppingCartItem.Event_Discount_Amount__c = discount;
			newShoppingCartItem.Event_Tax_Amount__c = tax;
			newShoppingCartItem.Event_Total_Amount__c = total;
			newShoppingCartItem.Event_Quantity__c = quantity;
			//newShoppingCartItem.pymt__Taxable__c = es.Taxable__c;
			newShoppingCartItem.Taxing_Authority__c = ta.Id;
			try {
				insert newShoppingCartItem;
				sId = newShoppingCartItem.Id;
				result.put('shoppingCartItemId', sId);
			} catch (Exception e) {
				result.put('error', e.getMessage());
			}
		}
		System.debug('result: ' + result);
		return result;
	}

	@AuraEnabled
	public static pymt__PaymentX__c createNewAttendeeRecords(String sciId,
															 String eventName,
															 String contactId,
															 List<evt__Attendee__c> listOfAttendees,
															 pymt__PaymentX__c pymnt,
															 Decimal subTotal,
															 Decimal discount,
															 Decimal tax,
															 Decimal total,
															 String eventId,
															 List<EventFeeCartWrapper> listOfEventFees,
															 Integer quantity,
															 String paymentMethod) {
		//String res = 'Success';
		List<Shopping_Cart_Item_Details__c> lstSCID = new List<Shopping_Cart_Item_Details__c>();
		pymt__PaymentX__c newpymnt = new pymt__PaymentX__c();

		//System.debug('eventName: ' + eventName);
		//System.debug('contactId: ' + contactId);
		System.debug('listOfAttendees: ' + listOfAttendees);
		//System.debug('subTotal: ' + subTotal);
		//System.debug('discount: ' + discount);
		//System.debug('tax: ' + tax);
		//System.debug('total: ' + total);
		//System.debug('eventId: ' + eventId);
		//System.debug('listOfEventFees: ' + listOfEventFees);
		//System.debug('quantity: ' + quantity);

		for (EventFeeCartWrapper wrapperObj : listOfEventFees) {
			if (wrapperObj.quantity > 0) {
				for (Integer j = 1; j <= wrapperObj.quantity; j++) {
					Shopping_Cart_Item_Details__c scid = new Shopping_Cart_Item_Details__c();
					scid.Shopping_Cart_Item__c = sciId;
					scid.Event_Fee__c = wrapperObj.eventFeeId;
					scid.SC_event__c = wrapperObj.eventId;
					scid.Item_Quantity__c = 1;
					scid.Item_Unit_Price__c = wrapperObj.price;
					scid.Item_Gross_Amount__c = wrapperObj.price;
					scid.Item_Tax_Amount__c = wrapperObj.price * wrapperObj.tax;
					scid.Item_Total_Amount__c = wrapperObj.price + (wrapperObj.price * wrapperObj.tax);
					lstSCID.add(scid);
				}
			}
		}
		System.debug('lstSCID===: ' + lstSCID);

		List<evt__Attendee__c> newAttendees = new List<evt__Attendee__c>();
		for (evt__Attendee__c att : listOfAttendees) {
			System.debug('att: ' + att);
			List<Contact> c = [SELECT Id, Email, FirstName, LastName FROM Contact WHERE Email = :att.evt__Reg_Email__c];
			String cId = '';
			if (c.size() > 0) {
				cId = c[0].Id;
			} else {
				Contact newContact = new Contact();
				String attLastName = '';
				String attFirstName = '';
				if (att.evt__First_Name__c != null) {
					attFirstName = att.evt__First_Name__c;
				}else if(att.evt__Reg_First_Name__c != null) {
					attFirstName = att.evt__Reg_First_Name__c;
				}
				if (att.evt__Last_Name__c != null) {
					attLastName = att.evt__Last_Name__c;
				}else if(att.evt__Reg_Last_Name__c != null) {
					attLastName = att.evt__Reg_Last_Name__c;
				}
				newContact.FirstName = attFirstName;
				newContact.LastName = attLastName;
				newContact.Email = att.evt__Reg_Email__c;
				newContact.hed__Preferred_Email__c = 'Alternate Email';
				newContact.hed__AlternateEmail__c = att.evt__Reg_Email__c;
				System.debug('newContact: ' + newContact);
				insert newContact;
				cId = newContact.Id;
			}

			//System.debug('cId: ' + cId);

			evt__Attendee__c newAttendee = new evt__Attendee__c();
			newAttendee.evt__Reg_First_Name__c = att.evt__First_Name__c == null ? '' : att.evt__First_Name__c;
			newAttendee.evt__Reg_Last_Name__c = att.evt__Last_Name__c == null ? '' : att.evt__Last_Name__c;
			newAttendee.evt__Reg_Email__c = att.evt__Reg_Email__c == null ? '' : att.evt__Reg_Email__c;
			newAttendee.evt__Event__c = eventId;
			newAttendee.evt__Event_Fee__c = att.evt__Event_Fee__c == null ? '' : att.evt__Event_Fee__c;
			newAttendee.evt__Registration_Type__c =	att.evt__Registration_Type__c == null ? '' : att.evt__Registration_Type__c;
			if (paymentMethod == 'paid with credit/debit card') {
				newAttendee.evt__Invitation_Status__c = 'Invited';
			} else {
				newAttendee.evt__Invitation_Status__c = 'Registered';
			}
			newAttendee.evt__Contact__c = cId;
			newAttendee.evt__Reg_Street__c = att.evt__Reg_Street__c == null ? '' : att.evt__Reg_Street__c;
			newAttendee.evt__Reg_City__c = att.evt__Reg_City__c == null ? '' : att.evt__Reg_City__c;
			newAttendee.evt__Reg_State__c = att.evt__Reg_State__c == null ? '' : att.evt__Reg_State__c;
			newAttendee.evt__Reg_Country__c = att.evt__Reg_Country__c == null ? '' : att.evt__Reg_Country__c;
			newAttendee.evt__Reg_Postal_Code__c =
					att.evt__Reg_Postal_Code__c == null ? '' : att.evt__Reg_Postal_Code__c;
			newAttendee.evt__Registration_Type__c =
					att.evt__Registration_Type__c == null ? '' : att.evt__Registration_Type__c;
			newAttendee.evt__Category__c = 'Attendee';
			newAttendee.Shopping_Cart_Item__c = sciId;
			newAttendee.Customize_FieldName_1__c =
					att.Customize_FieldName_1__c == null ? '' : att.Customize_FieldName_1__c;
			newAttendee.Customize_FieldName_2__c =
					att.Customize_FieldName_2__c == null ? '' : att.Customize_FieldName_2__c;
			newAttendee.Customize_FieldName_3__c =
					att.Customize_FieldName_3__c == null ? '' : att.Customize_FieldName_3__c;
			newAttendee.Customize_FieldName_4__c =
					att.Customize_FieldName_4__c == null ? '' : att.Customize_FieldName_4__c;
			newAttendee.Customize_FieldName_5__c =
					att.Customize_FieldName_5__c == null ? '' : att.Customize_FieldName_5__c;
			newAttendee.Customize_FieldValue_1__c =
					att.Customize_FieldValue_1__c == null ? '' : att.Customize_FieldValue_1__c;
			newAttendee.Customize_FieldValue_2__c =
					att.Customize_FieldValue_2__c == null ? '' : att.Customize_FieldValue_2__c;
			newAttendee.Customize_FieldValue_3__c =
					att.Customize_FieldValue_3__c == null ? '' : att.Customize_FieldValue_3__c;
			newAttendee.Customize_FieldValue_4__c =
					att.Customize_FieldValue_4__c == null ? '' : att.Customize_FieldValue_4__c;
			newAttendee.Customize_FieldValue_5__c =
					att.Customize_FieldValue_5__c == null ? '' : att.Customize_FieldValue_5__c;
			if (att.Industry__c != null && att.Industry__c != ''){
				newAttendee.Industry__c = (String) att.Industry__c;
			}
			if (att.Where_did_you_hear_about_this_event__c != null && att.Where_did_you_hear_about_this_event__c != ''){
				newAttendee.Where_did_you_hear_about_this_event__c = (String) att.Where_did_you_hear_about_this_event__c;
			}
 			//System.debug('Academic_Program__c: ' + att?.Academic_Program__c);
 			//System.debug('Academic_Term__c: ' + att?.Academic_Term__c);
 			//System.debug('Accessibility_Requirements__c: ' + att?.Accessibility_Requirements__c);
 			//System.debug('Accessibility_Requirements_Other__c: ' + att?.Accessibility_Requirements_Other__c);
            //System.debug('Dietary_Restrictions__c: ' + att?.Dietary_Restrictions__c);
  		  	//System.debug('Dietary_Restictions_Other__c: ' + att?.Dietary_Restictions_Other__c);
			if (att?.Academic_Program__c != null){
				newAttendee.Academic_Program__c = (Id) att.Academic_Program__c;
			}
			if (att?.Academic_Term__c != null){
				newAttendee.Academic_Term__c = (Id) att.Academic_Term__c;
			}
			if (att?.Accessibility_Requirements__c != null){
				newAttendee.Accessibility_Requirements__c = (String) att.Accessibility_Requirements__c ;
			}
			if (att?.Accessibility_Requirements_Other__c != null){
				newAttendee.Accessibility_Requirements_Other__c = (String) att.Accessibility_Requirements_Other__c;
			}
			if (att?.Dietary_Restrictions__c != null){
				newAttendee.Dietary_Restrictions__c = (String) att.Dietary_Restrictions__c;
			}
			if (att?.Dietary_Restictions_Other__c != null){
				newAttendee.Dietary_Restictions_Other__c = (String) att.Dietary_Restictions_Other__c;
			}

			newAttendees.add(newAttendee);
		}

		if (newAttendees.size() > 0) {
			Database.DMLOptions dmlOptions = new Database.DMLOptions();
			dmlOptions.duplicateRuleHeader.allowSave = true;
			for (evt__Attendee__c newAttendee : newAttendees) {
				newAttendee.setOptions(dmlOptions);
			}
			//System.debug('newAttendees: ' + newAttendees);
			Database.SaveResult[] sr = Database.insert(newAttendees, false);
			//System.debug('sr====: ' + sr);
			Set<Id> attendeeIds = new Set<Id>();
			for (Database.SaveResult s : sr) {
				if (s.isSuccess()) {
					System.debug('Attendee created successfully');
					attendeeIds.add(s.getId());
				} else {
					for (Database.Error err : s.getErrors()) {
						System.debug('Error: ' + err.getMessage());
					}
					//res = 'Error: Attendee not created '+s.getErrors();
				}
			}
			if (!attendeeIds.isEmpty()) {
				//System.debug('attendeeIds: ' + attendeeIds);
				List<evt__Attendee__c> newAttendeeList = [
						SELECT Id, evt__Event__c, evt__Event_Fee__c, evt__Contact__c
						FROM evt__Attendee__c
						WHERE Id IN :attendeeIds
				];
				for (evt__Attendee__c et : newAttendeeList) {
					//System.debug('et: ' + et);
					for (Shopping_Cart_Item_Details__c sc : lstSCID) {
						if (sc.Event_Fee__c == et.evt__Event_Fee__c && sc.SC_event__c == et.evt__Event__c &&
										sc.Attendee__c == null) {
							sc.Attendee__c = et.Id;
							sc.Contact__c = contactId;
							break;
						}
					}
				}
				if (!lstSCID.isEmpty()) {
					//System.debug('lstSCID: ' + lstSCID);
					insert lstSCID;
				}
				//create a payment record
				//Create invoice record, associate contact with invoice.
				Invoice__c inv = new Invoice__c();
				inv.Invoice_Status__c = 'Open-Personal';
				inv.Type__c = 'Events';
				inv.Tax_Amount__c = tax;
				inv.Gross_Amount__c = subTotal;
				inv.Total_Amount_SCI__c = total;
				inv.Contact__c = contactId;
				insert inv;

				switch on paymentMethod {
					when 'paid with cheque'{
						newpymnt.pymt__Payment_Type__c = 'Check or Money Order';
					}
					when 'paid with cash'{
						newpymnt.pymt__Payment_Type__c = 'Cash';
					}
					when 'paid with voucher'{
						newpymnt.pymt__Payment_Type__c = 'Voucher';
					}
					when 'complementary'{
						newpymnt.pymt__Payment_Type__c = 'Complementary';
					}
					when 'paid with credit/debit card'{
						newpymnt.pymt__Payment_Type__c = 'Credit Card';
						newpymnt.pymt__Payment_Processor__c = 'Global Pay';
					}
					when 'no payment necessary'{
						newpymnt.pymt__Payment_Type__c = 'No Payment Necessary';
					}
					when 'other'{
						newpymnt.pymt__Payment_Type__c = 'Other';
					}
				}
				newpymnt.Name = 'Event Manually Register Checkout';
				newpymnt.pymt__Transaction_Type__c = 'Payment';
				newpymnt.pymt__Status__c = 'Online Checkout';
				newpymnt.pymt__Contact__c = contactId != null ? contactId : null;
				newpymnt.pymt__Amount__c = total;
				newpymnt.Gross_Amount__c = subTotal;
				newpymnt.pymt__Tax__c = tax;
				newpymnt.pymt__Discount__c = discount;
				newpymnt.Type__c = 'Event Registration';
				newpymnt.evt__Event__c = eventId;
				newpymnt.Invoice__c = inv.Id;
				newpymnt.pymt__Billing_First_Name__c = pymnt.pymt__Billing_First_Name__c;
				newpymnt.pymt__Billing_Last_Name__c = pymnt.pymt__Billing_Last_Name__c;
				newpymnt.pymt__Billing_Street__c = pymnt.pymt__Billing_Street__c;
				newpymnt.pymt__Billing_City__c = pymnt.pymt__Billing_City__c;
				newpymnt.pymt__Billing_State__c = pymnt.pymt__Billing_State__c;
				newpymnt.pymt__Billing_Country__c = pymnt.pymt__Billing_Country__c;
				newpymnt.pymt__Billing_Postal_Code__c = pymnt.pymt__Billing_Postal_Code__c;

				insert newpymnt;

				List<pymt__Shopping_Cart_Item__c> sciList = [SELECT Id, Name, pymt__Payment__c FROM pymt__Shopping_Cart_Item__c WHERE Id = :sciId];
				//System.debug('sciList==: ' + sciList);
				List<pymt__Shopping_Cart_Item__c> sciListToUpdate = new List<pymt__Shopping_Cart_Item__c>();
				if (sciList.size() > 0) {
					for (pymt__Shopping_Cart_Item__c sci : sciList) {
						sci.pymt__Payment__c = newpymnt.Id;
						sciListToUpdate.add(sci);
					}
					update sciListToUpdate;
				}
			}
		}

		//System.debug('newpymnt: ' + newpymnt);
		return newpymnt;
	}

	/*
 * @description Processes Payment
 *
 * @param payment - Payment record being processed
 * @param cardNo - Credit Card number being processed
 * @param cvv - Security code of the credit card being processed
 *
 * @return MonerisResponseWrapper with details about payment
 */
/*
	@AuraEnabled
	public static Map<String, String> processPayment(pymt__PaymentX__c clientPymt,
													 String cardNo,
													 String cvd,
													 String expYr,
													 String expMo,
													 String accountName) {
		Map<String, String> responseMap = new Map<String, String>();
		String returnResponse = null;
		Boolean hasError = false;
		Id currentPaymentId = clientPymt.Id;
		String currentEventId = null;
		String currentContactId = null;
		String currentShoppingCartItemId = null;
		System.debug('clientPymt ' + clientPymt);
		System.debug('cardNo ' + cardNo);
		System.debug('cvd ' + cvd);
		System.debug('expYr ' + expYr);
		System.debug('expMo ' + expMo);
		System.debug('accountName ' + accountName);
		system.debug(clientPymt.Id);

		Organization[] orgData = [SELECT Id, InstanceName, IsSandbox, Name, OrganizationType FROM Organization];
		if (orgData.size() > 0) {
			if (orgData[0].IsSandbox == true)
				accountName = 'transaction_processing';
		}
		accountName = 'transaction_processing';
		//Compare the amount being acknowledged by the user and record to ensure that the payment is valid
		pymt__PaymentX__c payment;
		try {
			System.debug('PaymentId: ' + clientPymt.Id);
			payment = [
					SELECT Id, pymt__Amount__c, Payment_Response__c, Amount_Paid__c, evt__Event__c, pymt__Contact__c
					FROM pymt__PaymentX__c
					WHERE Id = :clientPymt.Id
			];
			System.debug('New Payment: ' + payment);
			if (payment.evt__Event__c != null) {
				currentEventId = payment.evt__Event__c;
			}
			if (payment.pymt__Contact__c != null) {
				currentContactId = payment.pymt__Contact__c;
			}
			pymt__Shopping_Cart_Item__c shoppingCartItem = [
					SELECT Id
					FROM pymt__Shopping_Cart_Item__c
					WHERE pymt__Payment__c = :payment.Id
			];
			if (shoppingCartItem != null) {
				currentShoppingCartItemId = shoppingCartItem.Id;
			}
			// AND pymt__Amount__c = :clientPymt.pymt__Amount__c
			//String sourceIp = Auth.SessionManagement.getCurrentSession().get('SourceIp');
			String sourceIp = '';
			payment.pymt__IP_Address__c = sourceIp;

			System.debug('imhere 1 ');
			System.debug('pmt billing --- ' + clientPymt.pymt__Billing_First_Name__c);
			System.debug('pmt lastname --- ' + clientPymt.pymt__Billing_Last_Name__c);
			//Update address information from the payment component
			payment.pymt__Billing_First_Name__c = clientPymt.pymt__Billing_First_Name__c;
			payment.pymt__Billing_Last_Name__c = clientPymt.pymt__Billing_Last_Name__c;
			payment.pymt__Billing_Street__c = clientPymt.pymt__Billing_Street__c;
			payment.pymt__Billing_City__c = clientPymt.pymt__Billing_City__c;
			payment.pymt__Billing_State__c = clientPymt.pymt__Billing_State__c;
			payment.pymt__Billing_Country__c = clientPymt.pymt__Billing_Country__c;
			payment.pymt__Billing_Postal_Code__c = clientPymt.pymt__Billing_Postal_Code__c;
			payment.Gross_Amount__c = clientPymt.Gross_Amount__c;
			payment.pymt__Amount__c = clientPymt.pymt__Amount__c;
			payment.pymt__Tax__c = clientPymt.pymt__Tax__c;
			payment.pymt__Discount__c = clientPymt.pymt__Discount__c;
			payment.pymt__Payment_Type__c = clientPymt.pymt__Payment_Type__c;
			payment.pymt__Transaction_Type__c = clientPymt.pymt__Transaction_Type__c;
			payment.pymt__Status__c = clientPymt.pymt__Status__c;
			payment.Name = clientPymt.Name;
			payment.Invoice__c = clientPymt.Invoice__c;
			payment.pymt__Payment_Processor__c = clientPymt.pymt__Payment_Processor__c;

			if (payment.Payment_Response__c == NULL) {
				payment.Payment_Response__c = '';
			}

			System.debug('imhere 2 ');
			System.debug('payment ' + payment);
			HTTPResponse response = GlobalPayConnect.processPayment(clientPymt, cardNo, cvd, expYr, expMo, accountName);
			System.debug('response: ' + response);
			//System.debug('response.getStatusCode '+response.getStatusCode());
			GP_PaymentResponseWrapper wrapper =
					(GP_PaymentResponseWrapper) JSON.deserialize(response.getBody(), GP_PaymentResponseWrapper.class);
			System.debug('wrapper ' + wrapper);
			//System.debug('wrapper.action.result_code '+wrapper.action.result_code);
			System.debug('wrapper error_code ' + wrapper.detailed_error_description);
			System.debug('wrapper status ' + wrapper.status);

			//Error in response
			if (String.isNotBlank(wrapper.detailed_error_description)) {
				returnResponse = wrapper.detailed_error_description;
				payment.pymt__Status__c = 'Error';
				responseMap.put('Status', 'ERROR');
				responseMap.put('ErrMessage', wrapper.detailed_error_description);
				hasError = true;
			} else if (response.getStatusCode() == 200 && wrapper.status == 'CAPTURED') {
				System.debug('Payment_methodbrand ' + wrapper.Payment_method.Card.brand);
				System.debug('imhere 1-1');
				returnResponse = wrapper.status;
				payment.pymt__Status__c = 'Completed';
				payment.pymt__Date__c = Date.today();
				payment.pymt__Batch_Id__c = wrapper.batch_id;
				payment.pymt__Reference_Id__c = wrapper.reference;
				payment.pymt__Last_4_Digits__c = cardNo.right(4);
				if (payment.Amount_Paid__c == null) {
					payment.Amount_Paid__c = 0;
				}
				payment.Amount_Paid__c += clientPymt.pymt__Amount__c;
				payment.pymt__Card_Type__c = CartController.getCardType(wrapper.Payment_method.Card.brand);
				responseMap.put('Status', 'CAPTURED');
				hasError = false;
			} else if (response.getStatusCode() == 200 && wrapper.status == 'DECLINED') {
				System.debug('imhere 1-2');
				returnResponse = wrapper.status;
				payment.pymt__Status__c = 'Declined';
				payment.pymt__Batch_Id__c = wrapper.batch_id;
				payment.pymt__Reference_Id__c = wrapper.reference;
				payment.pymt__Last_4_Digits__c = cardNo.right(4);
				payment.pymt__Card_Type__c = CartController.getCardType(wrapper.Payment_method.Card.brand);
				responseMap.put('Status', 'DECLINED');
				hasError = true;
			} else {
				System.debug('imhere 1-3');
				payment.pymt__Status__c = 'Error';
				String errMsg =
						'We\'re sorry, but we cannot complete your payment. Please refresh this page and try again.';
				responseMap.put('Status', 'ERROR');
				responseMap.put('ErrMessage', errMsg);
				hasError = true;
			}
			if (String.isNotBlank(wrapper.id)) {
				payment.pymt__Transaction_Id__c = wrapper.id;
			}
			payment.Payment_Response__c += '\n\n' + DateTime.now() + ' ->' + String.valueOf(response.getBody());
			System.debug('imhere 2');
			if (hasError) {
				System.debug('imhere 2-1 ');
				payment.pymt__Status__c = 'Error';
			}
			System.debug('=====payment===== ' + payment);
			update payment;
		} catch (Exception e) {
			System.debug('imhere 3 ');
			System.debug('e.getMessage ' + e.getMessage());
			if (payment != null) {
				payment.pymt__Status__c = 'Error';
				payment.Payment_Response__c += '\n\n' + DateTime.now() + ' ->' + e.getMessage();
				try {
					System.debug('imhere 3-1');
					System.debug('payment ' + payment);
					update payment;
				} catch (Exception ex1) {
					System.debug('ex1.getMessage ' + ex1.getMessage());
				}
			}
			responseMap.put('Status', 'ERROR');
			responseMap.put('ErrMessage', e.getMessage() + ':' + e.getStackTraceString());

			//throw new AuraHandledException(e.getMessage());
		}
		if (hasError) {
			//Rollback the changes
			List<evt__Attendee__c> attendees = [SELECT Id, evt__Invitation_Status__c FROM evt__Attendee__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItemId AND evt__Event__c = :currentEventId];
			if (attendees.size() > 0) {
				for (evt__Attendee__c attendee : attendees) {
					attendee.evt__Invitation_Status__c = 'Payment Pending';
				}
				System.debug('attendees=== ' + attendees);
				update attendees;
			}

			List<Shopping_Cart_Item_Details__c> lstDetails = [SELECT Id, Void_ticket__c FROM Shopping_Cart_Item_Details__c WHERE Shopping_Cart_Item__c = :currentShoppingCartItemId AND SC_event__c = :currentEventId AND Contact__c = :currentContactId];
			if (lstDetails.size() > 0) {
				for (Shopping_Cart_Item_Details__c detail : lstDetails) {
					detail.Void_ticket__c = true;
				}
				update lstDetails;
			}
		}
		return responseMap;
	}
*/

	@AuraEnabled
	public static List<String> getIndustryValues() {
		List<String> industryValues = new List<String>();

		List<Schema.PicklistEntry> entries = evt__Attendee__c.Industry__c.getDescribe().getPickListValues();
		for (Schema.PicklistEntry entry : entries) {
			if (entry.isActive()) {
				industryValues.add(entry.getLabel());
			}
		}
		return industryValues;
	}

	@AuraEnabled
	public static List<String> getWhereHeardValues() {
		List<String> whereHeardValues = new List<String>();

		List<Schema.PicklistEntry> entries = evt__Attendee__c.Where_did_you_hear_about_this_event__c.getDescribe().getPickListValues();
		for (Schema.PicklistEntry entry : entries) {
			if (entry.isActive()) {
				whereHeardValues.add(entry.getLabel());
			}
		}
		return whereHeardValues;
	}

	@AuraEnabled
	public static List<Map<String, String>> getProgramValues(String eventId) {
		List<Map<String, String>> programMaps = new List<Map<String, String>>();

		Map<String, String> proMaps = new Map<String, String>();
		List<String> proNames = new List<String>();
		List<Program_Term_Availability__c> programs = [SELECT Program__r.Name, Program__c, Available_for_RFI__c FROM Program_Term_Availability__c];
		if (programs.size() > 0) {
			for (Program_Term_Availability__c pro : programs) {
				proMaps.put(pro.Program__r.Name, pro.Program__c);
				proNames.add(pro.Program__r.Name);
			}
		}
		System.debug('proMaps: ' + proMaps);
		System.debug('proNames: ' + proNames);

		List<evt__Special_Event__c> tags = [SELECT Tags__c FROM evt__Special_Event__c WHERE Id =: eventId];
		if (tags.size() > 0) {
			String[] mytags = tags[0].Tags__c.split(';');
			for (String tag : mytags) {
				if (proNames.contains(tag)) {
					Map<String, String> programMap = new Map<String, String>();
					programMap.put(proMaps.get(tag), tag);
					programMaps.add(programMap);
				}
			}
		}
		System.debug('programMaps1: ' + programMaps);
		if (programMaps.size() == 0) {
			for (Program_Term_Availability__c pro : programs) {
				if (pro.Available_for_RFI__c == true){
					Map<String, String> programMap = new Map<String, String>();
					programMap.put(pro.Program__c, pro.Program__r.Name);
					programMaps.add(programMap);
				}
			}
		}
		System.debug('programMaps2: ' + programMaps);
		return programMaps;
	}

	@AuraEnabled
	public static List<Map<String, String>> getProgramTermValues(String programId) {
		List<Map<String, String>> termValues = new List<Map<String, String>>();

		List<Program_Term_Availability__c> terms = [SELECT Term__r.Name, Term__c, Available_for_RFI__c FROM Program_Term_Availability__c WHERE Program__c =: programId AND Available_for_RFI__c = true ORDER BY CreatedDate DESC];
		if (terms.size() > 0) {
			for (Program_Term_Availability__c t : terms) {
				Map<String, String> termMap = new Map<String, String>();
				termMap.put(t.Term__c, t.Term__r.Name);
				termValues.add(termMap);
			}
		}

		return termValues;
	}
}