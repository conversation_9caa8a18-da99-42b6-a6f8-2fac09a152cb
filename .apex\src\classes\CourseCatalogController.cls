global without sharing class CourseCatalogController {
	@AuraEnabled
    public static List<CourseItem> allCourses(String pagesize, String termName, String titleSectionId) {
        List<CourseItem> lstCourse = new List<CourseItem>();
        //ci.term='2019A';
        //lstCourse.add(ci);
        String pagesizeValue = '';
        String termNameValue = '';
        String termNameTitleSectionId = '';
        if(termName!=null && String.isNotBlank(termName) && termName!='None'){
            termNameValue = 'where hed__Term__r.Name=\''+termName+'\'';
            termNameTitleSectionId = ' and hed__Term__r.Name=\''+termName+'\'';
        }
        if(titleSectionId!=null && String.isNotBlank(titleSectionId)){
            termNameValue = 'where (hed__Course__r.Name like '+'\'%'+titleSectionId+'%\' or hed__Section_ID__c like '+'\'%'+titleSectionId+'%\')';
            if(termNameTitleSectionId!=null && String.isNotBlank(termNameTitleSectionId)){
                termNameValue = termNameValue + termNameTitleSectionId;
            }
        }
        if(pagesize!=null && String.isNotBlank(pagesize)){
            if(pagesize=='All'){
                pagesizeValue = '';
            } else {
        		pagesizeValue = ' Limit '+pagesize;
            }
        } else {
            pagesizeValue = ' Limit 50';
        }
        if(termName!=null && String.isNotBlank(termName) && termName=='None'){
            pagesizeValue = ' Limit 50';
        }
        String sortingOrder = ' ORDER BY hed__Term__r.hed__End_Date__c ASC, hed__Course__r.Name ASC';
        String soqlQuery = 'SELECT Id,hed__Term__r.Name,hed__Term__r.hed__End_Date__c,hed__Section_ID__c,hed__Course__r.Name,hed__Course__r.hed__Extended_Description__c,hed__Faculty__r.Name,Registration_Status__c,hed__Course__r.hed__Credit_Hours__c from hed__Course_Offering__c '+termNameValue+sortingOrder+pagesizeValue;
        //List<hed__Course_Offering__c> coffrngLst = [SELECT Id,hed__Term__r.Name,hed__Section_ID__c,hed__Course__r.Name,hed__Faculty__r.Name,Registration_Status__c,hed__Course__r.hed__Credit_Hours__c from hed__Course_Offering__c Limit 50];
        System.Debug('Full Query'+soqlQuery);
        List<hed__Course_Offering__c> coffrngLst = database.query(soqlQuery);
        if(coffrngLst!=null && coffrngLst.size()>0){
            for(hed__Course_Offering__c coffrng : coffrngLst){
                CourseItem ci = new CourseItem();
                ci.Id = coffrng.Id;
                ci.term = coffrng.hed__Term__r.Name;
                ci.sectionId = coffrng.hed__Section_ID__c;
                ci.courseTitle = coffrng.hed__Course__r.Name;
                ci.instructor = coffrng.hed__Faculty__r.Name;
                ci.status = coffrng.Registration_Status__c;
                ci.cu = coffrng.hed__Course__r.hed__Credit_Hours__c;
                ci.description = coffrng.hed__Course__r.hed__Extended_Description__c;
                lstCourse.add(ci);
            }
        }
        return lstCourse;
    }
    global class CourseItem{
        @AuraEnabled public string Id;
        @AuraEnabled public string term;
        @AuraEnabled public string sectionId;
        @AuraEnabled public string courseTitle;
        @AuraEnabled public string instructor;
        @AuraEnabled public string max;
        @AuraEnabled public string status;
        @AuraEnabled public Decimal cu;
        @AuraEnabled public String description;
    }
    @AuraEnabled
    public static List<TermNames> allTermNames() {
        List<TermNames> termnameLst = new List<TermNames>();
        TermNames tnamesAll = new TermNames();
        tnamesAll.label = 'None';
        tnamesAll.value = 'None';
        termnameLst.add(tnamesAll);
        List<hed__Term__c> termLst = [Select Id,Name from hed__Term__c where hed__End_Date__c>Today ORDER BY hed__End_Date__c ASC];
        if(termLst!=null && termLst.size()>0)
            for(hed__Term__c trm : termLst){
                TermNames tnames = new TermNames();
                tnames.label = trm.Name;
                tnames.value = trm.Name;
                termnameLst.add(tnames);
            }
        return termnameLst;
    }
    global class TermNames{
        @AuraEnabled public string label;
        @AuraEnabled public string value;
    }
}