/**
* @description   Runs Enterprise Territory Management Assingment rules for Accounts 
* <AUTHOR>
* @version        1.0 
* @created 2020-04-02
* @modified 2020-04-02
*/ 
public class RunTerritoryAssignmentRules implements Queueable, Database.AllowsCallouts{

    @TestVisible
    private static HttpResponse authenticateByUsernamePassword(String userName, String password) {
        String uri = Url.getOrgDomainUrl().toExternalForm() + '/services/Soap/u/51.0';
        String body =
            '<Envelope xmlns="http://schemas.xmlsoap.org/soap/envelope/"><Header/><Body><login xmlns="urn:partner.soap.sforce.com"><username>' +
            userName +
            '</username><password>' +
            password +
            '</password></login></Body></Envelope>';
        Http h = new Http();
        HttpRequest hRqst = new HttpRequest();
        hRqst.setEndpoint(uri);
        hRqst.setMethod('POST');
        hRqst.setHeader('SOAPAction', 'VALUE');
        hRqst.setHeader('Accept', 'text/xml');
        hRqst.setHeader('Content-type', 'text/xml');
        hRqst.setHeader('charset', 'UTF-8');
        hRqst.setBody(body);
        return h.send(hRqst);
    }
	@TestVisible
    private string getSessionId(string username, string password) {
        HttpResponse response = authenticateByUsernamePassword(username, password);
        if (response.getStatusCode() == 200) {
            Dom.XmlNode resultElmt = response.getBodyDocument()
                .getRootElement()
                .getChildElement('Body', 'http://schemas.xmlsoap.org/soap/envelope/')
                .getChildElement('loginResponse', 'urn:partner.soap.sforce.com')
                .getChildElement('result', 'urn:partner.soap.sforce.com');
            return resultElmt.getChildElement('sessionId', 'urn:partner.soap.sforce.com').getText();
        }
        return null;
    }
    public Set<Id> accntIds = null; 
    String accountTag = '<urn:sObjects> '+
                            '<urn1:type>Account</urn1:type>  '+
                                '<urn1:Id>{ACCID}</urn1:Id>   '+
                                '<Run_Territory_Assignment__c>false</Run_Territory_Assignment__c>   ' +
                        '</urn:sObjects> ' ;    

    String requestTemplate = '<soapenv:Envelope '+'xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"'+' xmlns:urn="urn:partner.soap.sforce.com"'+' xmlns:urn1="urn:sobject.partner.soap.sforce.com">'+
                                '<soapenv:Header> '+
                                    '<urn:AssignmentRuleHeader> '+ 
                                        '<urn:useDefaultRule>true</urn:useDefaultRule> '+
                                        '<urn:assignmentRuleId></urn:assignmentRuleId> '+
                                    '</urn:AssignmentRuleHeader>  '+
                                    '<urn:SessionHeader> '+
                                        '<urn:sessionId>{SESSID}</urn:sessionId> '+
                                    '</urn:SessionHeader> '+
                                '</soapenv:Header> '+
                                '<soapenv:Body> '+
                                    '<urn:update> '+
                                        ' {ACCLISTS}'+ 
                                    '</urn:update> '+
                                '</soapenv:Body> '+
                            '</soapenv:Envelope>';

    public void execute(QueueableContext context) {

        // Get the intergartion user credentials
        profile sysAdminProfile = [SELECT id FROM profile WHERE name='System Administrator'];
        Territory_Assignment__c ta = Territory_Assignment__c.getInstance(sysAdminProfile.id);
   
        String sessionId;
        // If the user is a guest user then log in as Integration user and get the session id else get the logged in user's session id
        sessionId = UserInfo.getSessionId();
        if (sessionId == null){
            sessionId = this.getSessionId(ta.Integration_User_Username__c, ta.Password__c);
        }

        //Formatting the request:   
        List<String> lstAccString = new List<String>();
        if(accntIds != null){
            for(Id accId:accntIds){
                lstAccString.add(accountTag.replace('{ACCID}', (String)accId)); 
            }
        } 
        requestTemplate = requestTemplate.replace('{ACCLISTS}', String.join(lstAccString, ' '));
        requestTemplate = requestTemplate.replace('{SESSID}', sessionId);

        HttpRequest request = new HttpRequest();
        request.setEndpoint(System.URL.getSalesforceBaseUrl().toExternalForm()+
                    '/services/Soap/u/41.0/'+UserInfo.getOrganizationId());
        request.setMethod('POST');
        request.setHeader('Content-Type', 'text/xml;charset=UTF-8');
        request.setHeader('SOAPAction', '""');
        request.setBody(requestTemplate);
        
        //Send request:
        HttpResponse res = new HttpResponse();  
        if(!Test.isRunningTest()){ 
            if (sessionId != null) {
                res = new Http().send(request); 
                System.debug(res); 
                System.debug(res.getBody()); 
                System.debug(res.getStatus());
            }
    	}
        if(res.getStatusCode() == 200 || Test.isRunningTest()){
            Set<Id> prospectRTs = new Set<Id>{OpportunityService.DegreeProgramProspectRTId, OpportunityService.ExecutiveProgramProspectRTId};
            if(Test.isRunningTest()){
                Account testacc = [SELECT Id FROM Account WHERE Name='Test'];
                if (accntIds == null){
                    accntIds = new Set<Id>();
                    accntIds.add(testacc.Id);
                }
            }
                //Query for related prospect opportunities for FTMBA (since territories change by region) :
            List<Opportunity> oppsWithOutTerr = [SELECT ID, Territory2Id, Recruitment_Officer__c, Program_Account__c, isExcludedFromTerritory2Filter,
                                                     AccountId, Level_of_Effort__c, Assistant_Director__c FROM Opportunity 
                                                     WHERE AccountId IN :accntIds 
                                                     //AND Territory2Id = null 
                                                     AND StageName IN ('Inquiry', 'Application Started', 'Lead', 'Application Submitted')
                                                     AND Program_Account__c != null
                                                     AND Program_Account__r.Program_Code__c = '9'
                                                     AND isExcludedFromTerritory2Filter != true
                                                     AND RecordtypeId IN :prospectRTs];

            //Set of program Ids: 
            Set<Id> programIds = new Set<Id>(); 
            for(Opportunity opp :oppsWithOutTerr){
                programIds.add(opp.Program_Account__c); 
            }
            
            //Map parent account to program account's program code:
            Map<Id, String> acctIdToProgramCode = new Map<Id, String>(); 
            Map<Id, Account> idToProgramAcct = new Map<id, Account>([SELECT ID, Program_Code__c FROM Account WHERE Id IN :programIds AND program_code__c != null]); 
            
            for(Opportunity opp: oppsWithOutTerr){
                if(idToProgramAcct.containsKey(opp.Program_Account__c)){
                    acctIdToProgramCode.put(opp.AccountId, idToProgramAcct.get(opp.Program_Account__c).Program_Code__c); 
                }
            } 
            
            System.debug('01 - '+accntIds);
            System.debug('02 - '+prospectRTs);
            System.debug('03 - '+oppsWithOutTerr);
            System.debug('04 - '+acctIdToProgramCode);
            //Assign all related opps with no territory assignments
            if(oppsWithOutTerr.size() > 0){
                List<Opportunity> oppsToUpdate = OpportunityTerritoryAssignmentLogic.oppTerrAndROAssignment(acctIdToProgramCode, oppsWithOutTerr);
                System.debug('05 - '+oppsToUpdate);
                //update TerritoryId and Assistant Director/Recruitment Officer in opportunity
                if(oppsToUpdate.size() > 0) update oppsToUpdate;

                //update Assistant Director/Recruitment Officer in Application
                List<hed__Application__c> appsToUpdate = new List<hed__Application__c>();
                if (oppsToUpdate.size() > 0){
                    for(Opportunity opp1 : oppsToUpdate){
                        for(hed__Application__c app :[SELECT ID, Assistant_Director__c, Recruitment_Officer__c, Opportunity__c FROM hed__Application__c WHERE Opportunity__c = : opp1.Id]){
                            if(opp1.Assistant_Director__c != null){
                                app.Assistant_Director__c = opp1.Assistant_Director__c;
                                appsToUpdate.add(app);
                            }
                            if(opp1.Recruitment_Officer__c != null){
                                app.Recruitment_Officer__c = opp1.Recruitment_Officer__c;
                                appsToUpdate.add(app);
                            }
                        }
                    }
                    Map<Id, hed__Application__c> appMap = new Map<Id, hed__Application__c>();
                    appMap.putall(appsToUpdate);
                    if (appMap.size()>0){
                        System.debug('06 - '+appsToUpdate);
                        System.debug('07 - '+appMap);
                        update appMap.values();
                    }
                    /*if (appsToUpdate.size()>0){
                        System.debug('06 - '+appsToUpdate);
                        update appsToUpdate;
                    }*/
                }
            }
        }
    }
}