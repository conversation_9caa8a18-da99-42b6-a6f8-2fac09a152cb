@isTest
public with sharing class PaymentService_TDTM_Test {

    @TestSetup
    static void setup(){

        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        tokens.add(new hed.TDTM_Global_API.TdtmToken('PaymentService_TDTM', 'pymt__PaymentX__c', 'BeforeInsert;', 1.00)); 
        hed.TDTM_Global_API.setTdtmConfig(tokens);

    }
    
    @isTest
    public static void testShipping() {

        pymt__PaymentX__c pymt = new pymt__PaymentX__c(
            Name                    = 'New Payment',
            pymt__Payment_Type__c   = 'Credit Card'
        );

        Test.startTest();
            insert pymt;
        Test.stopTest();

        pymt__PaymentX__c pymtInserted = [ SELECT Id, pymt__Ship_To_Street__c, pymt__Ship_To_City__c, pymt__Ship_To_State__c, pymt__Ship_To_Country__c, pymt__Ship_To_Postal_Code__c
                                           FROM pymt__PaymentX__c 
                                           WHERE ID = :pymt.Id ];

        System.assertEquals( pymtInserted.pymt__Ship_To_Street__c,      '105 St. George St',    'Ship To Address was incorrect');
        System.assertEquals( pymtInserted.pymt__Ship_To_City__c,        'Toronto',              'Ship To Address was incorrect');
        System.assertEquals( pymtInserted.pymt__Ship_To_State__c,       'ON',                   'Ship To Address was incorrect');
        System.assertEquals( pymtInserted.pymt__Ship_To_Country__c,     'Canada',               'Ship To Address was incorrect');
        System.assertEquals( pymtInserted.pymt__Ship_To_Postal_Code__c, 'M5S 3E6',              'Ship To Address was incorrect');

    }
}