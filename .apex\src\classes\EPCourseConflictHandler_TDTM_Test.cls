/**
 * @description Test Class for EPCourseConflictHandler_TDTM, ConflictCheckService class
 * <AUTHOR>
 * @version 1.0
 * @created 08-AUG-2020
 * @modifed 14-DEC-2020
  */
@isTest
public class EPCourseConflictHandler_TDTM_Test {

	@testSetup
    static void testSetup (){
        //retrieve default EDA trigger handler
        List<hed.TDTM_Global_API.TdtmToken> tokens = hed.TDTM_Global_API.getTdtmConfig(); 
        //create trigger handler for AttendeeConflictHandler_TDTM class
        tokens.add(new hed.TDTM_Global_API.TdtmToken('EPCourseConflictHandler_TDTM', 'Course_Enrollment__c', 'AfterInsert;AfterUpdate', 1.00)); 
        //pass trigger handler config to set method
        hed.TDTM_Global_API.setTdtmConfig(tokens);
        
        RecordType EPRT = [Select Id, Name from RecordType where SObjectType = 'hed__Course__c' AND Name = 'EP Course'];
        Account a = (Account)TestFactory.createSObject(new Account(RecordTypeId = AccountService.AcademicProgramRTId));
        insert a;
        
        List<hed__Course__c> corList = new List<hed__Course__c>();
        corList.add( new hed__Course__c (RecordTypeId = EPRT.Id, Name = 'Test Course1', hed__Account__c = a.Id));
        corList.add( new hed__Course__c (RecordTypeId = EPRT.Id, Name = 'Test Course2', hed__Account__c = a.Id));
        insert corList;
        
        List<Contact> cList = new List<Contact>();
        Contact c1 = (Contact)TestFactory.createSObject(new Contact());
        cList.add(c1); 
        Insert cList;
    
        hed__Term__c t = new hed__Term__c(Name = 'Test Term1', hed__Account__c = a.Id);
        insert t;
                    
        List<hed__Course_Offering__c> coList = new List<hed__Course_Offering__c>();
        coList.add(new hed__Course_Offering__c(Name = 'Test CO1', hed__Course__c = corList[0].Id, hed__Term__c = t.Id, hed__Start_Date__c = Date.newInstance(2025,1,1), hed__End_Date__c = Date.newInstance(2025,1,15)));
        coList.add(new hed__Course_Offering__c(Name = 'Test CO2', hed__Course__c = corList[1].Id, hed__Term__c = t.Id, hed__Start_Date__c = Date.newInstance(2025,2,1), hed__End_Date__c = Date.newInstance(2025,2,15)));
        insert coList;
		
	}
    
	@isTest
    static void EPCourseConflictInsertTest(){ 
        
        List<hed__Course_Offering__c> coList = [Select Id, Name from hed__Course_Offering__c];
        List<Contact> cList = [Select Id , Name from Contact];

        List<hed__Course_Enrollment__c> ceList = new List<hed__Course_Enrollment__c>();
		ceList.add(new hed__Course_Enrollment__c(hed__Contact__c = cList[0].Id, hed__Course_Offering__c = coList[0].Id));
        ceList.add(new hed__Course_Enrollment__c(hed__Contact__c = cList[0].Id, hed__Course_Offering__c = coList[1].Id));
        ceList.add(new hed__Course_Enrollment__c(hed__Contact__c = cList[0].Id, hed__Course_Offering__c = coList[1].Id));
        
        Test.startTest();
        try{
            insert ceList;

        } catch(Exception e){
            System.assert(e.getMessage().contains(EPCourseConflictHandler_TDTM.ERROR_MESSAGE));
        }
        Test.stopTest();
    }

    @isTest
    static void EPCourseConflictUpdateTest(){ 
        
        List<hed__Course_Offering__c> coList = [Select Id, Name from hed__Course_Offering__c];
        List<Contact> cList = [Select Id , Name from Contact];

        List<hed__Course_Enrollment__c> ceList = new List<hed__Course_Enrollment__c>();
		ceList.add(new hed__Course_Enrollment__c(hed__Contact__c = cList[0].Id, hed__Course_Offering__c = coList[0].Id));
        ceList.add(new hed__Course_Enrollment__c(hed__Contact__c = cList[0].Id, hed__Course_Offering__c = coList[1].Id));
        insert ceList;
        
        Test.startTest();
        try{
            ceList[0].Role__c = 'Enrolled';
            update ceList;

        } catch(Exception e){
            System.assert(e.getMessage().contains(EPCourseConflictHandler_TDTM.ERROR_MESSAGE));
        }
        Test.stopTest();
    }

}