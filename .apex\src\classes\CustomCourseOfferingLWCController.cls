global without sharing  class CustomCourseOfferingLWCController {


     /**
     * @description Returned when calendar initially loaded, includes initial items and calendar config
     */
    public class CalendarConfig {

        @AuraEnabled public Calendar_View__c cv;
        @AuraEnabled public hed__Course_Offering__c courseOfferingDetails;
        @AuraEnabled public List<Map<String, String>> filters;

        public CalendarConfig ( Calendar_View__c cv ) {

            this.cv = cv;
           /* this.filters = new List<Map<String, String>>();

            //Get the possible values of the filter for this field
            if ( !String.isBlank(this.cv.Filter_Field__c) ) 
                for ( Schema.PicklistEntry ple : Schema.getGlobalDescribe().get( cv.SObject_API_Name__c ).getDescribe().fields.getMap().get( cv.Filter_Field__c ).getDescribe().getPicklistValues() ) 
                    filters.add( new Map<String, String>{ 'label' => ple.getLabel(), 'value' => ple.getValue() });*/

        }

    }

    @AuraEnabled
    public static CalendarConfig getCalendarConfig (String quercusSectionID , String view) {
        Calendar_View__c cv;
        try {
                cv = [ SELECT Id , Name , Link_to_Calendar_Item__c , SObject_API_Name__c
                FROM Calendar_View__c 
                WHERE Name = :view ];       
        } catch (Exception e) {
                throw new AuraHandledException('Course Offerning Button View Not Found'); 
        }
        CalendarConfig cc = new CalendarConfig( cv );
        cc.courseOfferingDetails = CustomCourseOfferingLWCController.getCourseOfferingRecordDetails(cv , quercusSectionID);
        return cc;
    }

    private static hed__Course_Offering__c getCourseOfferingRecordDetails(Calendar_View__c cv , String quercusSectionID) {
        hed__Course_Offering__c courseOffList;
        try {
                courseOffList = [SELECT Id , Name , Quercus_Section_ID__c , hed__Start_Date__c , hed__End_Date__c , Full_Registration_Fee__c
                                                        FROM hed__Course_Offering__c 
                                                        WHERE Quercus_Section_ID__c = :quercusSectionID];

                /*Datetime dt = courseOffList.hed__Start_Date__c;
                String startDate = dt.format('MMM dd');
                system.debug('*** Start Date: *** ' +  startDate);*/
        } catch (Exception e) {
                throw new AuraHandledException(e.getMessage());
        }
            return courseOffList;
    }

}