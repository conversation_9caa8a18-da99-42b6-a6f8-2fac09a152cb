/**
 * @description Opportunity Territory Logic 
 * <AUTHOR>
 * @version 1.0
 * @created 2020-03-12
 * @modified 2021-01-25 
 */
global class OpportunityTerritoryLogic_TDTM extends hed.TDTM_Runnable{
    String EXECUTIVEPROGRAMCODE = '99999'; //Program Code of EP Territories in Territory Model
    /**
     * @description run method for TDTM 
     * @return dmlWrapper 
     * @param newList the list of Op    portunity records from trigger new 
     * @param oldList the list of Opportunity records from trigger old 
     * @param triggerAction describes the trigger context (Before Insert, AfterInsert, etc..)
     * @param objResult the describe for Opportunity 
     */
    global override hed.TDTM_Runnable.DmlWrapper run(List<sObject> newList, List<sObject> oldList, hed.TDTM_Runnable.Action triggerAction, Schema.DescribeSObjectResult objResult){
        hed.TDTM_Runnable.dmlWrapper dmlWrapper = new hed.TDTM_Runnable.DMLWrapper(); 
        Set<Id> programIds = new Set<Id>(); 
        List<Opportunity> oppsToAssign = new List<Opportunity>(); 

        //BEFORE INSERT CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeInsert){
            for(Opportunity opp :(List<Opportunity>) newList){
                //Assign Opportunity Territory for Prospect Opps (Degree Program Prospect or Executive Program Prospect) with Program Accounts and not excluded from territory assignments
                if(((opp.RecordTypeId == OpportunityService.DegreeProgramProspectRTId && opp.Program_Account__c != null) || opp.RecordTypeId == OpportunityService.ProgramOfferingRTId)  
                    && !opp.IsExcludedFromTerritory2Filter){
                    oppsToAssign.add(opp); 
                    programIds.add(opp.Program_Account__c); 
                }
            }
        }
        //BEFORE UPDATE CONTEXT
        if(triggerAction == hed.TDTM_Runnable.Action.BeforeUpdate){
            Map<Id, Opportunity> oldMap = new Map<Id, Opportunity>((List<Opportunity>)oldList); 
            for(Opportunity opp :(List<Opportunity>) newList){
                //Assign Opportunity Territory for Prospect Opps (Degree Program Prospect or Executive Program Prospect) with Program Accounts and not excluded from territory assignments: 
                if(((opp.RecordTypeId == OpportunityService.DegreeProgramProspectRTId && opp.Program_Account__c != null && (opp.Program_Account__c != oldMap.get(opp.Id).Program_Account__c) )
                    || opp.RecordTypeId == OpportunityService.ProgramOfferingRTId && opp.Territory2Id == null) && !opp.IsExcludedFromTerritory2Filter){
                        oppsToAssign.add(opp); 
                        programIds.add(opp.Program_Account__c); 
                }
            }
        }
        
        if(oppsToAssign.size() > 0){
            Map<Id, String> acctIdToProgramCode = new Map<Id, String>(); 
            Map<Id, Account> idToProgramAcct = new Map<id, Account>([SELECT ID, Program_Code__c FROM Account WHERE Id IN :programIds AND program_code__c != null]); 
            //Map account Id to program code: 
            for(Opportunity opp: oppsToAssign){
                if(opp.RecordTypeId == OpportunityService.ProgramOfferingRTId){
                    acctIdToProgramCode.put(opp.AccountId, EXECUTIVEPROGRAMCODE); 
                }
                else if(opp.RecordTypeId == OpportunityService.DegreeProgramProspectRTId && idToProgramAcct.containsKey(opp.Program_Account__c)){
                    acctIdToProgramCode.put(opp.AccountId, idToProgramAcct.get(opp.Program_Account__c).Program_Code__c); 
                }
            }
            //Assign Opportunity Territory and Recruitment Officer 
            OpportunityTerritoryAssignmentLogic.oppTerrAndROAssignment(acctIdToProgramCode, oppsToAssign); 
        }
        return dmlWrapper; 
    }
}