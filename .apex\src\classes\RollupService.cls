/**
 * @description Service class for all custom rollup summary operations 
 * <AUTHOR>
 * @version 1.0
 * @created 2020-03-02
 * @modified 2020-03-02
 */
public without sharing class RollupService {
    
    /**
     * @description calculates number of years between two different date ranges of children records before a certain date range (optional)
     *  and updates parent field with the calculated value (to the nearest month)
     * @param parentObject the parent object
     * @param parentToChildrenMap parent object mapped to related children records that need to be rolled up 
     * @param startDateField the start date api field name of the child object
     * @param endDdateField the end date api field name of the child object
     * @param parentFieldToUpdate the name of the parent field to update with the final calculation 
     */
    public static List<sObject> calculateConsecutiveYears(List<sObject> parentList, Map<Id, List<sObject>> parentToChildrenMap, Map<Id, Date> parentToDateLimit, String startDateField, String endDateField, String parentFieldToUpdate){
        List<sObject> sObjectsToUpdate = new List<sObject>(); 
        /*Requirements: 
         * (1) All children records are sorted by startDate<PERSON>ield ascending (earliest start date first)
         */
        //for each parent record: 
        for(sObject parent :parentList){
            //if parent record has related work history records, calculate roll-up field: 
            if(parentToChildrenMap.get(parent.Id) != null){
                Decimal calculatedYears = null; 

                //calculate consecutive years
                //parentToDateLimit is null or parent ID does not have a date limit: 
                if(parentToDateLimit == null || !parentToDateLimit.containsKey(parent.Id)){ 
                    calculatedYears = calculateYears(parentToChildrenMap.get(parent.Id), startDateField, endDateField, null); 
                }
                else if(parentToDateLimit.containsKey(parent.Id)){
                    calculatedYears = calculateYears(parentToChildrenMap.get(parent.Id), startDateField, endDateField, parentToDateLimit.get(parent.Id)); 
                }

                //only update contact record if calculated roll-up value is different from current parentFieldToUpdate value: 
                if(parent.get(parentFieldToUpdate) != calculatedYears){
                    //updated contact record: 
                    parent.put(parentFieldToUpdate, calculatedYears); 
                    sObjectsToUpdate.add(parent); 
                }
            }
        }
        return sObjectsToUpdate; 
    }
    
    /**
     * @description calculates number of years between two different date ranges of children records before a certain date range (optional)
     * @param childrenRecord the list of related children records 
     * @param startDateField the start date api field name of the child object
     * @param endDdateField the end date api field name of the child object
     * @param dateLimit calculates the number of years before this date 
     */
    private static Decimal calculateYears(List<sObject> childrenRecords, String startDateField, String endDateField, Date dateLimit){
        /*Requirements: 
         * (1) All children records are sorted by startDateField ascending (earliest start date first)
         */ 
        Date earliestStartDate = null; 
        Date latestEndDate = null; 
        Decimal totalMonths = 0; 
        Integer monthsInBetween = null; 
        Boolean calculateMonthsBetween = false;
        Boolean addToTotalMonths = false; 

        //iterate through each related work history record: 
        for(sObject so: childrenRecords){
            //calculation for all records that have a start and end date field where, if applicable, start date is before the dateLimit date 
            if(so.get(startDateField) != null && so.get(endDateField) != null && (dateLimit == null || (Date) so.get(startDateField) < dateLimit)){
                //set the initial values of earliestStartDate and latestEndDate: 
                if(earliestStartDate == null && latestEndDate == null){
                    earliestStartDate = (Date) so.get(startDateField); 
                     //if dateLimit is not null and record end date is greater than the date limit, set latest end date to the date limit -- else, set the end date to the record's end date 
                    latestEndDate = dateLimit != null && (Date) so.get(endDateField) > dateLimit ? dateLimit : (Date)so.get(endDateField); 
                    //calculate months between earliest start date and latest end date: 
                    calculateMonthsBetween = true; 
                }
                //if start date is between earliestStartDate and latestEndDate, but end date is greater than latestEndDate, re-assign latestEndDate: 
                else if((Date)so.get(startDateField) >= earliestStartDate && (Date) so.get(startDateField) < latestEndDate && (Date)so.get(endDateField) > latestEndDate){
                    //Re-assign latestEndDate: 
                     //if dateLimit is not null and record end date is greater than the date limit, set latest end date to the date limit -- else, set the end date to the record's end date 
                     latestEndDate = dateLimit != null && (Date) so.get(endDateField) > dateLimit ? dateLimit : (Date)so.get(endDateField); 
                     //re-calculate months between earliest start date and latest end date: 
                     calculateMonthsBetween = true; 
                }
                //if start date is greater than or equal to end date, re-assign earliestStartDate, latestEndDate
                else if((Date) so.get(startDateField) >= latestEndDate){
                    earliestStartDate = (Date) so.get(startDateField);
                    //add current monthsInBetween value to totalMonths
                    addToTotalMonths = true;  
                     //if dateLimit is not null and record end date is greater than the date limit, set latest end date to the date limit -- else, set the end date to the record's end date 
                    latestEndDate = dateLimit != null && (Date) so.get(endDateField) > dateLimit ? dateLimit : (Date)so.get(endDateField); 
                    //re-calculate months between earliest start date and latest end date: 
                    calculateMonthsBetween = true; 

                }
            }
             //calculate total months: 
             if(addToTotalMonths){
                totalMonths += monthsInBetween; 
                //reset flag: 
                addToTotalMonths = false; 
            } 

            //calculate months between earliest start date and latest end date: 
            if(calculateMonthsBetween){
                monthsInBetween = earliestStartDate.monthsBetween(latestEndDate); 
                //reset flag
                calculateMonthsBetween = false; 
            }             
        }
        if(monthsInBetween != null){
            totalMonths += monthsInBetween; 
        }

        //convert total months to years: 
        Decimal monthsToYear = (Decimal) totalMonths/12; 
        if(monthsToYear > 0){
            return monthsToYear.setScale(1, RoundingMode.HALF_UP); 
        }
        else{
            return null; 
        }
    }

    /**
     * @description calculates test scores
     * @return List<hed__Test__c> list of test records to update 
     * @param testRecords List of test records 
     * @param testIdsToScores map of test Id to list of related test score records
     * @param testSubjectToWeightMap map of test to a map of subject category of the test and its weight towards the total score 
     */
    public static List<hed__Test__c> calculateTestScores(List<hed__Test__c> testRecords, Map<Id, List<hed__Test_Score__c>> testIdsToScores, Map<String, Map<String, Decimal>> testSubjectToWeightMap ){
        List<hed__Test__c> testScoresToUpdate = new List<hed__Test__c>(); 

        //Calculate test score for each unofficial test record: 
        for(hed__test__c t :testRecords){
            if(t.hed__Source__c != 'Official'){
                //total score values could be nulled out if test_type__c has been updated to null 
                Decimal calculatedValue = null; 
                Decimal convertedGMATValue = null;
                //for all test records where test type != null and test type has related test setting metadata records: 
                if(t.hed__Test_Type__c != null && testSubjectToWeightMap.containsKey(t.hed__Test_Type__c)){
                    calculatedValue = calculateTestScore(testIdsToScores.get(t.Id), testSubjectToWeightMap.get(t.hed__Test_Type__c));
                    convertedGMATValue = calculateConvertGMATScore(testIdsToScores.get(t.Id), testSubjectToWeightMap.get(t.hed__Test_Type__c));
                    if (t.hed__Test_Type__c == 'GRE'){
                        convertedGMATValue -= 2080.75;
                        convertedGMATValue = ((convertedGMATValue/10).round(System.RoundingMode.HALF_UP))*10;
                    }
                }
                //Only update total score calculation if numbers differ and there are test setting metadata records for that specific test: 
                if((calculatedValue != null && t.composite_Score__c != calculatedValue) || (convertedGMATValue != null && t.Converted_GMAT_Total_Score__c != convertedGMATValue)){
                    t.composite_Score__c = calculatedValue;
                    t.Converted_GMAT_Total_Score__c = convertedGMATValue;
                    testScoresToUpdate.add(t); 
                } 
            }
        }

        return testScoresToUpdate; 
    }

    /**
     * @description calculates total test score based on weight of the test score's subject area 
     * @return Decimal total test score calculation 
     * @param testScores list of test score records
     * @param subjectToWeightMap map of subject area to weight of the subject area towards the total test score
     */
    private static Decimal calculateTestScore(List<hed__Test_Score__c> testScores, Map<String, Decimal> subjectToWeightMap){
        Decimal totalScore = 0; 
        for(hed__Test_Score__c ts :testScores){
            //if there are test setting custom metadata records related to the test type 
            if(ts.hed__Subject_Area__c != null && subjectToWeightMap.containsKey(ts.hed__Subject_Area__c)){              
                    // total score = sum(individual test score * weight)
                    totalScore += ts.hed__Score__c * subjectToWeightMap.get(ts.hed__Subject_Area__c); 
            }
        }
        return totalScore; 
    }

    /**
     * @description calculates converted GMAT total test score based on weight of the test score's subject area 
     * @return Decimal total test score calculation 
     * @param testScores list of test score records
     * @param subjectToWeightMap map of subject area to weight of the subject area towards the total test score
     */
    private static Decimal calculateConvertGMATScore(List<hed__Test_Score__c> testScores, Map<String, Decimal> subjectToWeightMap){
        Decimal convertedGMATScore = 0; 
        for(hed__Test_Score__c ts :testScores){
            //if there are test setting custom metadata records related to the test type 
            if(ts.hed__Subject_Area__c != null && subjectToWeightMap.containsKey(ts.hed__Subject_Area__c)){    
                if (ts.hed__Subject_Area__c == 'Quantitative Reasoning'){
                    convertedGMATScore += ts.hed__Score__c * 10.62;
                }
                else if (ts.hed__Subject_Area__c == 'Verbal Reasoning'){
                    convertedGMATScore += ts.hed__Score__c * 6.38;
                }
            }
        }
        return convertedGMATScore; 
    }
    
    public static List<Opportunity> calculateOpportunityEngagementScore(Map<Id, List<Staging_Table__c>> opportunityIdToInteractionMap){
        List<Opportunity> oppLstToUpdate = new List<Opportunity>();
        Map<Id, List<Staging_Table__c>> opportunityIdToAllInteractionMap = new Map<Id, List<Staging_Table__c>>();
        List<Staging_Table__c> sttbleLst = [Select Id,Engagement_Score__c,Engagement_Key__c,Opportunity__c from Staging_Table__c where Opportunity__c=:opportunityIdToInteractionMap.keySet()];
        Map<ID, Opportunity> idToOppMap = new Map<ID, Opportunity>([SELECT Id,Engagement_Score__c FROM Opportunity  WHERE Id IN :opportunityIdToInteractionMap.keySet()]);
        if(sttbleLst.size()>0){    
        	for(Staging_Table__c sttble : sttbleLst){
                if(opportunityIdToAllInteractionMap.containsKey(sttble.Opportunity__c)) {
                    opportunityIdToAllInteractionMap.get(sttble.Opportunity__c).add(sttble);
                } else {
                    opportunityIdToAllInteractionMap.put(sttble.Opportunity__c, new List<Staging_Table__c> { sttble });
                }
            }
        }
        for(String oppId : opportunityIdToInteractionMap.keySet()){
            Map<String,Decimal> engmntKeytoScoreMap = new Map<String,Decimal>();
            Decimal totalScore = 0;
            if(opportunityIdToAllInteractionMap.containsKey(oppId)){
                for(Staging_Table__c stbl : opportunityIdToAllInteractionMap.get(oppId)){
                    if(engmntKeytoScoreMap.containsKey(stbl.Engagement_Key__c)) {
                        if(engmntKeytoScoreMap.get(stbl.Engagement_Key__c)<stbl.Engagement_Score__c){
							engmntKeytoScoreMap.put(stbl.Engagement_Key__c, stbl.Engagement_Score__c);
						}
                    } else {
                        engmntKeytoScoreMap.put(stbl.Engagement_Key__c, stbl.Engagement_Score__c);
                    }
                }
            }
            if(engmntKeytoScoreMap.values().size()>0){
                for(Decimal score : engmntKeytoScoreMap.values()){
                    totalScore+=score;
                }
            }
            system.debug(idToOppMap);
            Opportunity oppToUpdate = idToOppMap.get(oppId);
            oppToUpdate.Engagement_Score__c = totalScore;
            oppLstToUpdate.add(oppToUpdate);
        }
        return oppLstToUpdate;
    }
    public static List<Plan_Requirement_Fulfillment__c> createPlanRequirementFullFillment(List<hed__Program_Enrollment__c> pgrmEnrollmentLst){
    	System.Debug('<<<<<pgrmEnrollmentLst>>>>>'+pgrmEnrollmentLst);
        Map<Id, List<hed__Plan_Requirement__c>> programPlanIdToPlanRequirementMap = new Map<Id, List<hed__Plan_Requirement__c>>();
        Map<Id, List<Plan_Requirement_Fulfillment__c>> contactIdToFullfillmentMap = new Map<Id, List<Plan_Requirement_Fulfillment__c>>();
        List<Plan_Requirement_Fulfillment__c> pplnLstToreturn = new List<Plan_Requirement_Fulfillment__c>();
        List<Plan_Requirement_Fulfillment__c> pplnLstToDelete = new List<Plan_Requirement_Fulfillment__c>();
        Set<Id> programPlanIds = new Set<Id>();
        Set<Id> programEnrollmentIds = new Set<Id>();
        for(hed__Program_Enrollment__c prgrmenrlmnt : pgrmEnrollmentLst){
            programPlanIds.add(prgrmenrlmnt.hed__Program_Plan__c);
            programEnrollmentIds.add(prgrmenrlmnt.Id);
        }
        //pgrmEnrollmentLst = [Select Id from Plan_Requirement_Fulfillment__c where Program_Enrollment__c =:programEnrollmentIds];
        pplnLstToDelete = [Select Id from Plan_Requirement_Fulfillment__c where Program_Enrollment__c =:programEnrollmentIds];
        List<hed__Plan_Requirement__c> plnRequirement = new List<hed__Plan_Requirement__c>();
        plnRequirement = [Select Id,hed__Plan_Requirement__c,hed__Plan_Requirement__r.hed__Program_Plan__c,hed__Course__c,hed__Plan_Requirement__r.hed__Category__c,hed__Category__c,hed__Program_Plan__c from hed__Plan_Requirement__c where hed__Program_Plan__c =:programPlanIds or hed__Plan_Requirement__r.hed__Program_Plan__c =:programPlanIds];
        System.Debug('<<<<<plnRequirement>>>>>'+plnRequirement);
        for(hed__Plan_Requirement__c plnrqrmnt : plnRequirement){
            if(plnrqrmnt.hed__Plan_Requirement__c==null){
                if(programPlanIdToPlanRequirementMap.containsKey(plnrqrmnt.hed__Program_Plan__c)) {
                    programPlanIdToPlanRequirementMap.get(plnrqrmnt.hed__Program_Plan__c).add(plnrqrmnt);
                } else {
                    programPlanIdToPlanRequirementMap.put(plnrqrmnt.hed__Program_Plan__c, new List<hed__Plan_Requirement__c> { plnrqrmnt });
                }
            } else if(plnrqrmnt.hed__Plan_Requirement__c!=null){
                if(programPlanIdToPlanRequirementMap.containsKey(plnrqrmnt.hed__Plan_Requirement__r.hed__Program_Plan__c)) {
                    programPlanIdToPlanRequirementMap.get(plnrqrmnt.hed__Plan_Requirement__r.hed__Program_Plan__c).add(plnrqrmnt);
                } else {
                    programPlanIdToPlanRequirementMap.put(plnrqrmnt.hed__Plan_Requirement__r.hed__Program_Plan__c, new List<hed__Plan_Requirement__c> { plnrqrmnt });
                }
            }
        }
        System.Debug('<<<<<programPlanIdToPlanRequirementMap>>>>>'+programPlanIdToPlanRequirementMap);
        for(hed__Program_Enrollment__c prgrmenrlmnt : pgrmEnrollmentLst){
            if(programPlanIdToPlanRequirementMap.containsKey(prgrmenrlmnt.hed__Program_Plan__c)) {
                for(hed__Plan_Requirement__c plnrqrmnt : programPlanIdToPlanRequirementMap.get(prgrmenrlmnt.hed__Program_Plan__c)){
                    if(plnrqrmnt.hed__Plan_Requirement__c!=null && plnrqrmnt.hed__Plan_Requirement__r.hed__Category__c=='Required'){
                        Plan_Requirement_Fulfillment__c plnrqrmntFlflmnt = new Plan_Requirement_Fulfillment__c();
                        plnrqrmntFlflmnt.Program_Enrollment__c = prgrmenrlmnt.Id;
                        plnrqrmntFlflmnt.Plan_Requirement__c = plnrqrmnt.Id;
                        plnrqrmntFlflmnt.Course__c = plnrqrmnt.hed__Course__c;
                        if(contactIdToFullfillmentMap.containsKey(prgrmenrlmnt.hed__Contact__c)){
                            contactIdToFullfillmentMap.get(prgrmenrlmnt.hed__Contact__c).add(plnrqrmntFlflmnt);
                        } else {
                            contactIdToFullfillmentMap.put(prgrmenrlmnt.hed__Contact__c, new List<Plan_Requirement_Fulfillment__c> { plnrqrmntFlflmnt });
                        }
                        pplnLstToreturn.add(plnrqrmntFlflmnt);
                    } else if(plnrqrmnt.hed__Plan_Requirement__c==null && plnrqrmnt.hed__Course__c!=null && plnrqrmnt.hed__Category__c=='Required'){
                        Plan_Requirement_Fulfillment__c plnrqrmntFlflmnt = new Plan_Requirement_Fulfillment__c();
                        plnrqrmntFlflmnt.Program_Enrollment__c = prgrmenrlmnt.Id;
                        plnrqrmntFlflmnt.Plan_Requirement__c = plnrqrmnt.Id;
                        plnrqrmntFlflmnt.Course__c = plnrqrmnt.hed__Course__c;
                        //plnrqrmntFlflmnt.Contact__c = prgrmenrlmnt.hed__Contact__c;
                        if(contactIdToFullfillmentMap.containsKey(prgrmenrlmnt.hed__Contact__c)){
                            contactIdToFullfillmentMap.get(prgrmenrlmnt.hed__Contact__c).add(plnrqrmntFlflmnt);
                        } else {
                            contactIdToFullfillmentMap.put(prgrmenrlmnt.hed__Contact__c, new List<Plan_Requirement_Fulfillment__c> { plnrqrmntFlflmnt });
                        }
                        pplnLstToreturn.add(plnrqrmntFlflmnt);
                    }
                }
            }
        }
        System.Debug('<<<<<contactIdToFullfillmentMap>>>>>'+contactIdToFullfillmentMap);
        pplnLstToreturn = PlanRqmntToCourseConnect.connectPlanRequirementToCourseConnection(contactIdToFullfillmentMap);
        if(pplnLstToDelete!=null && pplnLstToDelete.size()>0){
            delete pplnLstToDelete;
        }
        System.Debug('<<<<<pplnLstToreturn>>>>>'+pplnLstToreturn);
        return pplnLstToreturn;
    }
}