/**
 * Created by <PERSON><PERSON> on 2022-09-22.
 */

public with sharing class FullCalendarService {

    @AuraEnabled
    public static List<Object> getEventsNearbyDynamic2(String additionalFilter)  {
        String soqlQuery = '';
        System.debug('additional: ' + additionalFilter);
        if (additionalFilter == null || additionalFilter == '') {
            additionalFilter = '';
            soqlQuery += 'SELECT Id, Name, evt__Short_Description__c, Price__c, Event_Location__c, Start_Date__c, Start_Local__c, End_Date__c, End_Local__c, Start_Date_Time_Formula__c, End_Date_Time_Formula__c, evt__Event_Time_Zone__c FROM evt__Special_Event__c WHERE evt__Status__c = \'Published\' AND Start_Date__c <> null AND (End_Date__c <> null AND End_Date__c >= TODAY)';
        }else{
            soqlQuery += 'SELECT Id, Name, evt__Short_Description__c, Price__c, Event_Location__c, Start_Date__c, Start_Time__c, End_Date__c, End_Time__c, Start_Date_Time_Formula__c, End_Date_Time_Formula__c, evt__Event_Time_Zone__c FROM evt__Special_Event__c WHERE evt__Status__c = \'Published\' AND Start_Date__c <> null AND (End_Date__c <> null AND End_Date__c >= TODAY) AND '+additionalFilter;
        }
        System.debug('soqlQuery: '+soqlQuery);

        List<Object> q = Database.query(soqlQuery);
        if (q == null) {
            q = new List<Object>();
        }
        System.debug('results = ' + q.size());
        return q;
    }

    @AuraEnabled(cacheable = true)
    public static List<String> getEventImgSrcUrls() {
        List<String> imgSrcUrls = new List<String>();
        String myPattern = '<img[^>]*src=\"([^\"]+)\"[^>]*>';
        Pattern regex = Pattern.compile(myPattern);
        Integer i = 0;

        List<evt__Special_Event__c> events = [SELECT Id, Thumbnail_Image__c, evt__Publish_To__c FROM evt__Special_Event__c WHERE evt__Status__c = 'Published' Order by CreatedDate Desc limit 100];
        for (evt__Special_Event__c event : events) {
            if (event.Thumbnail_Image__c != null && i < 4) {
                if (event.evt__Publish_To__c != null && event.evt__Publish_To__c.contains('Private Events')) {
                    continue;
                }
                String myImg = event.Thumbnail_Image__c;
                Matcher matcher = regex.matcher(myImg);
                while (matcher.find()) {
                    String imgSrc = matcher.group(1);
                    imgSrcUrls.add(imgSrc);
                }
                i++;
            }
        }
        //System.debug('imgSrcUrls ' + imgSrcUrls);

        return imgSrcUrls;
    }
}