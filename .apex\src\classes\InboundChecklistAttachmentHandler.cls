/**
* Takes attachments emailed by applicants and associates them to related chekclist items
* 
* <AUTHOR> 
* @since   2020-08-19 
*/
global class InboundChecklistAttachmentHandler implements Messaging.InboundEmailHandler {
    
    global Messaging.InboundEmailResult handleInboundEmail(Messaging.InboundEmail email, Messaging.InboundEnvelope envelope) {

	// Create an InboundEmailResult object for returning the result of the Apex Email Service
        Messaging.InboundEmailResult result = new Messaging.InboundEmailResult();

        // Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        // String[] toAddresses = new String[] {'<EMAIL>'}; 
        // mail.setToAddresses(toAddresses);
        // mail.setPlainTextBody(email.plainTextBody);
        // Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });

        //Get the Id from the email being sent to the email service
        try{
            //Extract the App Id from Subject
            String ciId = email.subject.substring( email.subject.lastIndexOf('Item: ') + 6);   //Subject: "RE: Application: <ID HERE>". Use this format, to get APP ID (starting from 0 base)
            system.debug('app Id: '+ ciId);

            List<Application_Checklist_Item__c> cis = [ SELECT Id, Status__c FROM Application_Checklist_Item__c WHERE ID = :ciId AND Status__c = 'Incomplete' AND Request_Upload_from_Applicant__c = true ];

            if ( cis.size() == 1 &&  email.binaryAttachments.size() > 0 ) {

                Application_Checklist_Item__c ci = cis[0];
                ci.Status__c = 'Complete - Unreviewed'; //Set the status for the checklsit item to unreviewed

                List<ContentVersion> cvs        = new List<ContentVersion>();
                List<ContentDocumentLink> cdls  = new List<ContentDocumentLink>();

                //Add all email attachments as attachments to the email
                for ( Integer i=0; i < email.binaryAttachments.size(); i++ )
                    cvs.add( new ContentVersion(
                        VersionData     = email.binaryAttachments[i].body,
                        PathOnClient    = email.binaryAttachments[i].fileName 
                    ));
    
                insert cvs;

                for ( ContentVersion insertedCv : [ SELECT Id, ContentDocumentId FROM ContentVersion WHERE ID IN :cvs ] ) 
                    cdls.add( new ContentDocumentLink(
                        LinkedEntityId      = ci.Id,
                        ContentDocumentId   = insertedCv.ContentDocumentId
                    ));

                insert cdls;
                update ci;

            }
            result.success = true;
        
        }catch(Exception e){
            result.success = false;
        }
        return result;
        
    }

}